﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    public class HomeController : BaseApiController<HomeBll>
    {
        /// <summary>
        /// 查询公告
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询公告")]
        public QueryResult<AnnouncementQuery> QueryAnnouncement([FromQuery] AnnouncementFilter filter)
        {
            var exps = this.NewExps<Announcement>();

            exps.And(p =>
            (!p.StartTime.HasValue && !p.EndTime.HasValue) ||
            (p.StartTime.HasValue && !p.EndTime.HasValue && DateTime.Now > p.StartTime) ||
            (!p.StartTime.HasValue && p.EndTime.HasValue && DateTime.Now < p.EndTime) ||
            (!p.StartTime.HasValue && !p.EndTime.HasValue && DateTime.Now > p.StartTime && DateTime.Now < p.EndTime)
            );

            return this.Repo.GetDynamicQuery<Announcement, AnnouncementQuery>(filter, exps);
        }
    }
}