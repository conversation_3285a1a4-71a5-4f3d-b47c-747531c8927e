﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Common
{
    public static class SdrAuthHelper
    {
        private readonly static Random _random = new Random();

        /// <summary>
        /// 获取128~163的之间的随机数
        /// </summary>
        /// <returns></returns>
        private static int GetRndNum()
        {
            return _random.Next(128, 164);
        }

        private static char NumToChar(int n)
        {
            char c;

            if (n > 137)
            {
                c = (char)(n - 73);
            }
            else
            {
                c = (char)(n - 80);
            }

            return c;
        }

        private static int ChatToNum(char c)
        {
            int n = (int)c;

            if (n > 57)
            {
                n += 73;
            }
            else
            {
                n += 80;
            }

            return n;
        }

        private static (int, char) GetRndNumChar()
        {
            var n = GetRndNum();
            var c = NumToChar(n);

            return (n, c);
        }

        private static string EncryptSdrToken(string sdrCode, int[] n, char[] c, DateTime? day = null)
        {
            day ??= SysDateTime.Today;

            var s1 = $"{n[0]}.{day:yyyyMMdd}.{n[1]}.{sdrCode}.{n[2]}";
            var m1 = s1.ToMD5();

            var m2 = $"{c[0]}{m1.ToMD5()}{m1[14]}{c[1]}{c[2]}";

            return m2;
        }

        public static string EncryptSdrToken(string sdrCode, DateTime? day = null)
        {
            var (n1, c1) = GetRndNumChar();
            var (n2, c2) = GetRndNumChar();
            var (n3, c3) = GetRndNumChar();

            var n = new int[3]
            {
                n1, n2, n3
            };

            var c = new char[3]
            {
                c1,c2,c3
            };

            return EncryptSdrToken(sdrCode, n, c, day);
        }

        public static bool ValdateSdrToken(string sdrCode, string sdrToken, DateTime? day = null)
        {
            if (sdrToken.Length < 36)
            {
                return false;
            }
            else
            {
                var c1 = sdrToken[0];

                var s = sdrToken[^2..];
                var c2 = s[0];
                var c3 = s[1];

                var n1 = ChatToNum(c1);
                var n2 = ChatToNum(c2);
                var n3 = ChatToNum(c3);

                var n = new int[3]
                {
                    n1, n2, n3
                };

                var c = new char[3]
                {
                    c1,c2,c3
                };

                var sdrToken2 = EncryptSdrToken(sdrCode, n, c, day);

                return sdrToken == sdrToken2;
            }
        }
    }
}