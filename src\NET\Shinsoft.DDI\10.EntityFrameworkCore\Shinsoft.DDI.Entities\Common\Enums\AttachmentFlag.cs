﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace Shinsoft.DDI.Entities
{
    /// <summary>
    /// 附件标志枚举
    /// 用于定义附件的特殊属性和权限标记，支持位运算组合
    /// </summary>
    [Flags]
    public enum AttachmentFlag
    {
        /// <summary>
        /// 无标志
        /// 默认值，表示普通附件，无特殊标记
        /// </summary>
        [Description("无")]
        None = 0,
    }
}