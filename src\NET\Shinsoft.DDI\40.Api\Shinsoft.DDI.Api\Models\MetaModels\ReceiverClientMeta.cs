﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class ReceiverClientMeta
    {
        /// <summary>
        /// 经销商名称
        /// </summary>
        [MapFromProperty(typeof(ReceiverClient), ReceiverClient.Foreigns.Receiver, Receiver.Columns.Name)]
        public string? DistributorName { get; set; }

        /// <summary>
        /// 经销商编号
        /// </summary>
        [MapFromProperty(typeof(ReceiverClient), ReceiverClient.Foreigns.Receiver, Receiver.Columns.Code)]
        public string? DistributorCode { get; set; }
    }
}
