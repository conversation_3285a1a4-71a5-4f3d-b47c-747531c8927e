﻿--数据审核内容
CREATE TABLE [dbo].[ReviewData]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL,
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [OriData]                   NVARCHAR(MAX)               NOT NULL,
    [NewData]                   NVARCHAR(MAX)               NOT NULL,
    [ApprovedData]              NVARCHAR(MAX)               NOT NULL,
    CONSTRAINT [PK_ReviewData] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_ReviewData_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_ReviewData_ReviewIndex] FOREIGN KEY ([ID]) REFERENCES [dbo].[ReviewIndex] ([ID]),
)

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'数据审核内容',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewData',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'待审核数据',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewData',
    @level2type = N'COLUMN',
    @level2name = N'NewData'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'已审核数据',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewData',
    @level2type = N'COLUMN',
    @level2name = N'ApprovedData'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'原数据',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewData',
    @level2type = N'COLUMN',
    @level2name = N'OriData'