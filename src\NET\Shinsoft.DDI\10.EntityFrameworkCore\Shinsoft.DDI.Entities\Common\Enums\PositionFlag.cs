﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    /// <summary>
    /// 角色成员类型
    /// </summary>
    [Description("职位标识")]
    [Flags]
    public enum PositionFlag
    {
        None = 0,

        /// <summary>
        /// 主岗
        /// </summary>
        [Description("主岗")]
        Major = 1,

        /// <summary>
        /// 兼岗
        /// </summary>
        [Description("兼岗")]
        Concurrent = 2,

        ///// <summary>
        ///// 一岗一人
        ///// </summary>
        //[Description("一岗一人")]
        //OnePersonPerStation = 4,

        ///// <summary>
        ///// 一人一岗
        ///// </summary>
        //[Description("一人一岗")]
        //OneStationPerPerson = 8
    }
}
