<script setup lang="ts">
import { basicMasterDataApi } from "@/api/basicMasterData";
import { useUserStoreHook } from "@/store/modules/user";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElMessage, ElMessageBox } from "element-plus";
import { mount } from "sortablejs";
import moment from "moment";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;
defineOptions({
  name: "CostCenter:view"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // 标题：查看
  viewTitle: {
    type: String,
    default: "查看分公司"
  },
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "210px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.model?.id
    ? `${t("operate.view")} ${tt("Entity.SubCompany._Entity")} - ${state.model.name}`
    : `${t("operate.view")} ${tt("Entity.SubCompany._Entity")}`;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {}
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  },
  // 按钮权限
  btn: {
    delete: computed(() => {
      return userStore.hasAnyAuth(["SubCompany:Manage", "SubCompany:Manage:Delete"]);
    })
  }
});

/**
 * 当前用户存储
 */
const userStore = useUserStoreHook();
/**
 * 数据变量
 */
const state = reactive({
  id: "",
  model: cfg.default.model as Record<string, any>
});
/**
 * 初始化组件（异步）
 */
const init = () => {
  state.model = cfg.default.model;

  return new Promise<void>(resolve => {
    resolve();
  });
};

/**
 * 获取model数据
 */
const get = (id: string) => {
  if (id) {
    cfg.loading.form = true;
    basicMasterDataApi
      .GetSubCompany(id)
      .then(res => {
        if (res.success) {
          state.model = res.data;
          state.model.openingDate = moment(state.model.openingDate).format("yyyy-MM-DD");
          emit("refresh", state.model);
        }
      })
      .finally(() => {
        cfg.loading.form = false;
      });
  }
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 按钮事件：【删除】
 */
const del = (data?: any) => {
  // 内部调用时，data为空
  // 外部调用时，需要传入{id:""}的对象
  data = data ?? getModel();

  if (data && data.id) {
    ElMessageBox.confirm(
      `${t("operate.confirm.delete")}<br />${t("operate.confirm.deleteHint")}`,
      `${t("operate.title.deleteConfirm")} - ${data.name}`,
      {
        showClose: false,
        closeOnClickModal: false,
        draggable: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: t("operate.ok"),
        cancelButtonText: t("operate.cancel"),
        type: "warning"
      }
    )
      .then(() => {
        cfg.loading.btn = true;
        basicMasterDataApi
          .DeleteSubCompany(data)
          .then(res => {
            // 仅提示
            if (res.success) {
              refresh(t("operate.message.success"), "success");
            }
          })
          .finally(() => {
            cfg.loading.btn = false;
          });
      })
      .catch(() => {
        close();
      });
  }
};

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = async (id: string) => {
  cfg.dialog.visible = true;
  if (id) {
    state.model.id = id;
  }
  init().then(() => {
    if (id) {
      get(id);
    }
  });
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open,
  del
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      v-model:title="title"
      append-to-body
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <div>
        <el-form
          ref="formRef"
          v-loading="cfg.loading.form"
          :model="state.model"
          label-position="right"
          :label-width="formLabelWidth"
          class="el-dialog-form"
        >
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="name" :label="tt('Entity.SubCompany.Name')">
                <span>{{ state.model.name }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="code" :label="tt('Entity.SubCompany.Code')">
                <span>{{ state.model.code }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="shortName" :label="tt('Entity.SubCompany.ShortName')">
                <span>{{ state.model.shortName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="valid" :label="tt('Entity.SubCompany.Valid')">
                <span v-if="state.model.valid">{{ t("operate.yes") }}</span>
                <span v-if="!state.model.valid">{{ t("operate.no") }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.delete"
            class="delete"
            style="float: left"
            :round="btnRound"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.form"
            :icon="useRenderIcon(btnDeleteIcon)"
            @click="del()"
          >
            {{ t("operate.delete") }}
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
