﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Primitives;
using Shinsoft.Core.Caching;
using Shinsoft.DDI.Api.App_Start.Interfaces;
using Shinsoft.DDI.Api.Providers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true, Inherited = true)]
    public class AuthorizeFilter : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            if (context.ActionDescriptor is ControllerActionDescriptor descriptor)
            {
                int? statusCode = null;
                string? contentType = null;
                string? message = null;

                //方法上没有定义权限属性，则取Controller类上的定义
                var allowAnonymous = descriptor.MethodInfo.GetActionAttribute<AllowAnonymousAttribute>(true)
                    ?? descriptor.MethodInfo.GetControllerAttribute<AllowAnonymousAttribute>(true);

                if (allowAnonymous == null)
                {
                    var authorize = descriptor.MethodInfo.GetActionAttribute<AuthorizeAttribute>(true)
                        ?? descriptor.MethodInfo.GetControllerAttribute<AuthorizeAttribute>(true);

                    if (authorize != null)
                    {
                        if (context.IsAuthenticated())
                        {
                            //只对非服务账号（正常登录的用户）进行此判断
                            //判断是否要强制用户重新登录（IdentityCode错误）
                            var userProvider = context.GetRequiredService<IUserProvider, UserProvider>();

                            if (!userProvider.ValidateIdentity(context.HttpContext.User.GetIdentityJson()))
                            {
                                statusCode = (int)HttpStatusCode.Unauthorized;
                            }
                        }
                        else
                        {
                            statusCode = (int)HttpStatusCode.Unauthorized;
                        }
                    }

                    if (!statusCode.HasValue)
                    {
                        var allow = true;

                        //方法上没有定义权限属性，则取Controller类上的定义
                        var authAttrs = descriptor.MethodInfo.GetPriorityAttributes<AuthAttribute>();

                        if (authAttrs.Count != 0)
                        {
                            //已设置了权限属性，需要判断用户是否拥有权限
                            allow = false;

                            if (context.IsAuthenticated())
                            {
                                var user = context.GetOperatorUser<User>();

                                if (user != null)
                                {
                                    allow = user.HasAnyAuth(authAttrs);
                                }
                            }
                        }

                        if (!allow)
                        {
                            statusCode = (int)HttpStatusCode.Forbidden;
                        }
                    }
                }

                #region 验证 SDR Token

                if (!statusCode.HasValue)
                {
                    //方法上没有定义权限属性，则取Controller类上的定义
                    var sdrAuthorize = descriptor.MethodInfo.GetActionAttribute<SdrAuthorizeAttribute>(true)
                        ?? descriptor.MethodInfo.GetControllerAttribute<SdrAuthorizeAttribute>(true);

                    if (sdrAuthorize?.Enable == true)
                    {
                        //需要验证SDR Token

                        var failedResult = new ClientResult()
                        {
                            resHead = new ClientResultInfo()
                        };

                        if (context.HttpContext.Request.Headers.TryGetValue(Config.SDR.CodeHeader, out StringValues codeValue))
                        {
                            // 获取到授权码
                            var sdrCode = codeValue.ToString();

                            failedResult.resHead.resCode = sdrCode;

                            if (context.HttpContext.Request.Headers.TryGetValue(Config.SDR.TokenHeader, out StringValues tokenValue))
                            {
                                // 获取到授权令牌
                                var sdrToken = tokenValue.ToString();

                                if (!SdrAuthHelper.ValdateSdrToken(sdrCode, sdrToken))
                                {
                                    statusCode = HttpStatusCode.OK.GetHashCode();
                                    failedResult.resHead.resMsg = "DDI授权无效";
                                }
                            }
                            else if (!Config.SDR.Debug)
                            {
                                //未传Token, 非debug 模式
                                // debug模式下，不传token则验证通过（主要为了swagger测试方便）

                                statusCode = HttpStatusCode.OK.GetHashCode();
                                failedResult.resHead.resMsg = "缺少DDI授权";
                            }

                            if (!statusCode.HasValue)
                            {
                                var bll = context.GetRequiredService<SysBll>();

                                var receiver = bll.GetEntity<Receiver>(p => p.SdrCode == sdrCode);

                                if (receiver == null)
                                {
                                    statusCode = HttpStatusCode.OK.GetHashCode();
                                    failedResult.resHead.resMsg = "该经销商不存在";
                                }
                                else if (receiver.EnumStatus != ReceiverStatus.Valid)
                                {
                                    statusCode = HttpStatusCode.OK.GetHashCode();
                                    failedResult.resHead.resMsg = "该经销商已终止";
                                }
                                else if (context.Controller is IReceiverController receiverController)
                                {
                                    receiverController.RawReceiver = receiver;
                                }
                            }
                        }
                        else
                        {
                            statusCode = HttpStatusCode.OK.GetHashCode();
                            failedResult.resHead.resMsg = "缺少DDI授权";
                        }

                        if (statusCode.HasValue)
                        {
                            contentType = "text/xml; charset=utf-8";
                            message = failedResult.ToXml();
                        }
                    }
                }

                #endregion 验证 SDR Token

                if (statusCode.HasValue)
                {
                    var result = new ContentResult()
                    {
                        StatusCode = statusCode,
                    };

                    if (!contentType.IsEmpty())
                    {
                        result.ContentType = contentType;
                    }

                    if (!message.IsEmpty())
                    {
                        result.Content = message;
                    }

                    context.Result = result;
                }
            }

            base.OnActionExecuting(context);
        }

        public override void OnResultExecuting(ResultExecutingContext context)
        {
            if (context.ActionDescriptor is ControllerActionDescriptor descriptor)
            {
                //方法上没有定义权限属性，则取Controller类上的定义
                //var allowAnonymous = descriptor.MethodInfo.GetAttribute<AllowAnonymousAttribute>(true)
                //    ?? descriptor.MethodInfo.DeclaringType?.GetAttribute<AllowAnonymousAttribute>(true);

                var jwtIgnore = descriptor.MethodInfo.GetAttribute<JwtIgnoreAttribute>(true)
                    ?? descriptor.MethodInfo.DeclaringType?.GetAttribute<JwtIgnoreAttribute>(true);

                if (jwtIgnore == null && context.HttpContext.User.IsAuthenticated())
                {
                    //只对非服务账号（正常登录的用户）重新生成jwt
                    var userProvider = context.GetRequiredService<IUserProvider, UserProvider>();

                    var identity = userProvider.DeserializeToIdentity(context.HttpContext.User.GetIdentityJson());

                    var token = $"{JwtBearerDefaults.AuthenticationScheme} {identity.GetJwt()}";

                    var exposHeader = context.HttpContext.Response.Headers.AccessControlExposeHeaders.AsString();

                    if (exposHeader.IsEmpty())
                    {
                        exposHeader = "jwt";
                    }
                    else
                    {
                        exposHeader += ",jwt";
                    }

                    context.HttpContext.Response.Headers.Append("jwt", token);
                    context.HttpContext.Response.Headers.Append("Access-Control-Expose-Headers", exposHeader);
                }
            }

            base.OnResultExecuting(context);
        }
    }
}