CREATE VIEW [dbo].[vEmployeeDelegate] AS 


SELECT 
	ed.ID,
	ed.Company<PERSON>d,
	ie.DisplayName AS [EmployeeName],
	ae.DisplayName AS [AgentName],
	ed.<PERSON><PERSON>,
	ed.<PERSON><PERSON><PERSON>,
	ed.<PERSON><PERSON>,
	iu.LoginName AS [EmployeeLoginName],
	au.LoginName AS [AgentLoginName],
	ed.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	ed.AgentId
FROM dbo.EmployeeDelegate AS ed
INNER JOIN dbo.Employee AS ie ON ie.ID = ed.EmployeeId
LEFT JOIN dbo.[User] AS iu ON iu.ID = ie.UserId  
INNER JOIN dbo.Employee AS ae ON ae.ID = ed.AgentId
LEFT JOIN dbo.[User] AS au ON au.ID = ae.UserId  
WHERE ed.Deleted = 0
