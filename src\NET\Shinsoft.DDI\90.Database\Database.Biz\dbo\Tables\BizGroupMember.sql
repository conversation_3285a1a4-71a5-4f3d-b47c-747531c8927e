--业务群成员
CREATE TABLE [dbo].[BizGroupMember]
(
    [ID]					        UNIQUEIDENTIFIER	        NOT NULL    CONSTRAINT [DF_BizGroupMember_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                     UNIQUEIDENTIFIER            NOT NULL,
    [BizGroupId]		            UNIQUEIDENTIFIER	        NOT NULL,		--FK, 公司群组定义ID
    [BizGroupInstId]			    UNIQUEIDENTIFIER	        NULL,		    --业务群实例ID
	[EnumType]				        INT					        NOT NULL,		--成员类型,枚举
    [MemberId]				        UNIQUEIDENTIFIER	        NOT NULL,		--成员ID
    CONSTRAINT [PK_BizGroupMember] PRIMARY KEY CLUSTERED ([ID]), 
    CONSTRAINT [FK_BizGroupMember_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_BizGroupMember_Group_00_Group] FOREIGN KEY ([BizGroupId]) REFERENCES [dbo].[BizGroup] ([ID]),
)
