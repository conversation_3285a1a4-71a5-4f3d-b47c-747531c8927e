﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public partial class CompanyCurrency : IHashCache, IOrder
    {
        #region IHashCache

        [NotMapped, XmlIgnore, JsonIgnore]
        public string Field => this.Currency;

        #endregion IHashCache

        #region IOrder

        IComparable IOrder.Order => this.IsStandard ? "" : this.Currency;

        #endregion IOrder
    }
}