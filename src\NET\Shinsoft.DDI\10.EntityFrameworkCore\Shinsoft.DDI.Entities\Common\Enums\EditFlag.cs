﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    [Flags]
    public enum EditFlag
    {
        /// <summary>
        /// 无限制
        /// </summary>
        [Description("无限制")]
        [EnumGroup(Visible = false)]
        Unlimited = -1,

        None = 0,

        /// <summary>
        /// 可编辑
        /// </summary>
        [Description("可删除")]
        Delete = 1,

        /// <summary>
        /// 编码可编辑
        /// </summary>
        [Description("编码可编辑")]
        Code = 2,

        /// <summary>
        /// 名称可编辑
        /// </summary>
        [Description("名称可编辑")]
        Name = 4,

        /// <summary>
        /// 类型可编辑
        /// </summary>
        [Description("类型可编辑")]
        Type = 8,

        /// <summary>
        /// 标签可编辑
        /// </summary>
        [Description("标签可编辑")]
        Flag = 16,

        /// <summary>
        /// 状态可编辑
        /// </summary>
        [Description("状态可编辑")]
        Status = 32,

        /// <summary>
        /// 备注可编辑
        /// </summary>
        [Description("备注可编辑")]
        Remark = 64,

        /// <summary>
        /// 顺序可编辑
        /// </summary>
        [Description("顺序可编辑")]
        Ordinal = 128,

        /// <summary>
        /// 子对象可维护
        /// </summary>
        [Description("子对象可维护")]
        Children = 1024,
    }
}
