﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class HomeBll : BaseCompanyBll
    {
        #region Constructs

        public HomeBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public HomeBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public HomeBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public HomeBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs
    }
}