import { $t } from "@/plugins/i18n";

export default {
  path: "/error",
  redirect: "/error/403",
  meta: {
    icon: "ri:information-line",
    showLink: false,
    title: $t("menus.pureAbnormal"),
    rank: 999
  },
  children: [
    {
      path: "/error/403",
      name: "err-403",
      component: () => import("@/views/error/403.vue"),
      meta: {
        title: $t("menus.pureFourZeroOne"),
        anonymous: true
      }
    },
    {
      path: "/error/404",
      name: "err-404",
      component: () => import("@/views/error/404.vue"),
      meta: {
        title: $t("menus.pureFourZeroFour"),
        anonymous: true
      }
    },
    {
      path: "/error/500",
      name: "err-500",
      component: () => import("@/views/error/500.vue"),
      meta: {
        title: $t("menus.pureFive"),
        anonymous: true
      }
    }
  ]
} satisfies RouteConfigsTable;
