﻿CREATE TABLE [dbo].[Calendar]
(
	[ID]                        UNIQUEIDENTIFIER            NOT NULL,
	[CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
	[Date]                      DATE                        NOT NULL,
    [EnumType]                  INT                         NOT NULL,
	[Year]						INT							NOT NULL,
	[Month]						INT							NOT NULL,
	[Day]						INT							NOT NULL,
	[DayOfWeek]					INT							NOT NULL,
	[DayOfYear]					INT							NOT NULL,
	[WeekOfMonth]				INT							NOT NULL,
	[WeekOfYear]				INT							NOT NULL,
	[Remark]					NVARCHAR(500)				NOT NULL,
    CONSTRAINT [PK_Calendar] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_Calendar_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),

)
GO

CREATE UNIQUE INDEX [IX_Calendar] ON [dbo].[Calendar]
(
	[CompanyId] ASC,
	[Date] ASC
);

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'日历',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Calendar',
    @level2type = NULL,
    @level2name = NULL
GO

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'年',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Calendar',
    @level2type = N'COLUMN',
    @level2name = N'Year'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'月',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Calendar',
    @level2type = N'COLUMN',
    @level2name = N'Month'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'日',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Calendar',
    @level2type = N'COLUMN',
    @level2name = N'Day'

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'日期',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Calendar',
    @level2type = N'COLUMN',
    @level2name = N'Date'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Calendar',
    @level2type = N'COLUMN',
    @level2name = N'EnumType'