﻿CREATE TABLE [dbo].[RoleMember]
(
    [ID]                            UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_RoleMember_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                     UNIQUEIDENTIFIER            NOT NULL,
    [RoleId]                        UNIQUEIDENTIFIER            NOT NULL,
    [EnumType]                      INT                         NOT NULL,
    [MemberId]                      UNIQUEIDENTIFIER            NOT NULL,
    [Creator]			            NVARCHAR(50)		        NULL, 
    [CreateTime]		            DATETIME			        NULL, 
    [LastEditor]		            NVARCHAR(50)		        NULL, 
    [LastEditTime]		            DATETIME			        NULL, 
    CONSTRAINT [PK_RoleMember] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_RoleMember_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_RoleMember_Role_00_Role_RoleMembers] FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Role] ([ID]),
)

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'角色成员',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RoleMember',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'角色ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RoleMember',
    @level2type = N'COLUMN',
    @level2name = N'RoleId'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'成员类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RoleMember',
    @level2type = N'COLUMN',
    @level2name = N'EnumType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'成员ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RoleMember',
    @level2type = N'COLUMN',
    @level2name = N'MemberId'