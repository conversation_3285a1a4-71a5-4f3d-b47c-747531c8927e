--字典
CREATE TABLE [dbo].[Dict] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_Dict_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [ParentId]                  UNIQUEIDENTIFIER            NULL,
    [EnumFlags]                 INT                         NOT NULL,
    [EnumEditFlags]             INT                         NOT NULL,
    [EnumEditFlags_Child]       INT                         NOT NULL,
    [Code]                      NVARCHAR(50)                NOT NULL,
    [Name]                      NVARCHAR(200)               NOT NULL,
    [ShortName]                 NVARCHAR(50)                NOT NULL,
    [Ordinal]                   INT                         NOT NULL,
    [Remark]                    NVARCHAR(500)               NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_Dict] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Dict_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_Dict_Dict_00_Parent_Children] FOREIGN KEY ([ParentId]) REFERENCES [dbo].[Dict] ([ID])
);

GO


EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'字典',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Dict',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'上级字典ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Dict',
    @level2type = N'COLUMN',
    @level2name = N'ParentId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'编辑标志',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Dict',
    @level2type = N'COLUMN',
    @level2name = N'EnumEditFlags'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'子字典项编辑标志',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Dict',
    @level2type = N'COLUMN',
    @level2name = N'EnumEditFlags_Child'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'编码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Dict',
    @level2type = N'COLUMN',
    @level2name = N'Code'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Dict',
    @level2type = N'COLUMN',
    @level2name = N'Name'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'简称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Dict',
    @level2type = N'COLUMN',
    @level2name = N'ShortName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Dict',
    @level2type = N'COLUMN',
    @level2name = N'Remark'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'排序字段',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Dict',
    @level2type = N'COLUMN',
    @level2name = N'Ordinal'
