﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class DepartmentSelectorFilter
    {
        /// <summary>
        /// 关键词
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(Department), Department.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Department), Department.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }

        /// <summary>
        /// 编码
        /// </summary>
        [Description("编码")]
        [DynamicQueryColumn(typeof(Department), Department.Columns.Code, Operation = Operation.StringIntelligence)]
        public string? Code { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        [DynamicQueryColumn(typeof(Department), Department.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Name { get; set; }

        /// <summary>
        /// 有效性
        /// </summary>
        [Description("有效性")]
        [DynamicQueryColumn(typeof(Department), Department.Columns.Valid, Operation = Operation.In)]
        public List<bool>? Valids { get; set; }

        /// <summary>
        /// 分公司
        /// </summary>
        [Description("分公司")]
        [DynamicQueryColumn(typeof(Department), Department.Foreigns.SubCompany, SubCompany.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Department), Department.Foreigns.SubCompany, SubCompany.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? SubCompanyKeywords { get; set; }

        /// <summary>
        /// 分公司ID
        /// </summary>
        [Description("分公司ID")]
        [DynamicQueryColumn(typeof(Department), Department.Columns.SubCompanyId, Operation = Operation.Equal)]
        public Guid? SubCompanyId { get; set; }
    }
}
