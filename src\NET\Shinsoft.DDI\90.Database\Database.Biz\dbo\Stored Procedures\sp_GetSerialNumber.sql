﻿CREATE PROCEDURE [dbo].[sp_GetSerialNumber]
	@CompanyId UNIQUEIDENTIFIER,
	@Prefix NVARCHAR(10),
	@Date DATETIME = NULL,
	@DateFormat NVARCHAR (20) = N'yyyyMMdd',
	@SeedLength INT = 3,
	@Count INT = 1,
	@OperatorUser NVARCHAR (50) = NULL,
	@SerialNumber NVARCHAR (MAX) OUT
AS

	DECLARE @ID UNIQUEIDENTIFIER,@SeedId UNIQUEIDENTIFIER
	DECLARE @Year INT, @Month INT, @Day INT, @Hour INT, @Min INT, @Sec INT
	DECLARE @Seed INT 


	SET @Count = ISNULL(@Count,1)
	SET @OperatorUser = ISNULL(@OperatorUser,'')
	SET @SerialNumber = ''


	--时间为空时取当前时间
	IF @Date IS NULL
	BEGIN
		SET @Date = GETDATE()
	END

	--取序列号定义
	SELECT
		@ID = sn.ID,
		@DateFormat = sn.DateFormat,
		@SeedLength = sn.SeedLength
	FROM dbo.SerialNumber AS sn
	WHERE sn.CompanyId = @CompanyId
	AND sn.Prefix = @Prefix

	IF @ID IS NULL AND @CompanyId IS NOT NULL AND ISNULL(@DateFormat,'') <> '' AND  ISNULL(@SeedLength,0) > 0
	BEGIN
		--没取到定义，如果有定义项则生成定义
		INSERT INTO dbo.SerialNumber
		(
		    ID,
		    CompanyId,
		    Prefix,
		    DateFormat,
		    SeedLength,
		    Creator,
		    CreateTime,
		    LastEditor,
		    LastEditTime
		) VALUES (
			DEFAULT, -- ID - uniqueidentifier
		    @CompanyId,    -- CompanyId - uniqueidentifier
		    @Prefix,     -- Prefix - nvarchar(50)
		    @DateFormat,     -- DateFormat - nvarchar(50)
		    @SeedLength,       -- SeedLength - int
		    @OperatorUser,    -- Creator - nvarchar(50)
		    GETDATE(),    -- CreateTime - datetime
		    @OperatorUser,    -- LastEditor - nvarchar(50)
		    GETDATE()     -- LastEditTime - datetime
		)

		SELECT
			@ID = sn.ID,
			@DateFormat = sn.DateFormat,
			@SeedLength = sn.SeedLength
		FROM dbo.SerialNumber AS sn
		WHERE sn.CompanyId = @CompanyId
		AND sn.Prefix = @Prefix

	END

	IF @ID IS NOT NULL
	BEGIN
		--开始生成序列号

		IF @DateFormat COLLATE Chinese_PRC_CS_AS LIKE N'%y%'
		BEGIN
			SET @Year = DATEPART(YEAR, @Date)
		END

		IF @DateFormat COLLATE Chinese_PRC_CS_AS LIKE N'%M%'
		BEGIN
			SET @Month = DATEPART(YEAR, @Date)
		END

		IF @DateFormat COLLATE Chinese_PRC_CS_AS LIKE N'%d%'
		BEGIN
			SET @Day = DATEPART(DAY, @Date)
		END

		IF @DateFormat COLLATE Chinese_PRC_CS_AS LIKE N'%H%'
		BEGIN
			SET @Hour = DATEPART(HOUR, @Date)
		END

		IF @DateFormat COLLATE Chinese_PRC_CS_AS LIKE N'%m%'
		BEGIN
			SET @Min = DATEPART(MINUTE, @Date)
		END

		IF @DateFormat COLLATE Chinese_PRC_CS_AS LIKE N'%s%'
		BEGIN
			SET @Sec = DATEPART(SECOND, @Date)
		END


		SELECT 
			@SeedId =  ss.ID,
			@Seed = ss.Seed
		FROM dbo.SerialSeed AS ss
		WHERE ss.Deleted = 0
		AND ss.SerialNumberId = @ID
		AND ISNULL(ss.Year,-1) = ISNULL(@Year,-1)
		AND ISNULL(ss.Month,-1) = ISNULL(@Month,-1)
		AND ISNULL(ss.Day,-1) = ISNULL(@Day,-1)
		AND ISNULL(ss.Hour,-1) = ISNULL(@Hour,-1)
		AND ISNULL(ss.Min,-1) = ISNULL(@Min,-1)
		AND ISNULL(ss.Sec,-1) = ISNULL(@Sec,-1)

		IF @SeedId IS NULL
		BEGIN
			SET @Seed = 0

			INSERT INTO dbo.SerialSeed
			(
			    ID,
			    CompanyId,
			    SerialNumberId,
			    Year,
			    Month,
			    Day,
			    Hour,
			    Min,
			    Sec,
			    Seed,
			    Deleted,
			    Creator,
			    CreateTime,
			    LastEditor,
			    LastEditTime
			) VALUES (
				DEFAULT, -- ID - uniqueidentifier
			    @CompanyId,    -- CompanyId - uniqueidentifier
			    @ID,    -- SerialNumberId - uniqueidentifier
			    @Year,    -- Year - int
			    @Month,    -- Month - int
			    @Day,    -- Day - int
			    @Hour,    -- Hour - int
			    @Min,    -- Min - int
			    @Sec,    -- Sec - int
			    @Seed + @Count,       -- Seed - int
			    0,    -- Deleted - bit
			    @OperatorUser,    -- Creator - nvarchar(50)
			    GETDATE(),    -- CreateTime - datetime
			    @OperatorUser,    -- LastEditor - nvarchar(50)
			    GETDATE()     -- LastEditTime - datetime
			)

		END
		ELSE
		BEGIN

			UPDATE ss SET
				ss.Seed = @Seed + @Count,
				ss.LastEditor = @OperatorUser,
				ss.LastEditTime = GETDATE()
			FROM dbo.SerialSeed AS ss
			WHERE ss.ID = @SeedId

		END

		DECLARE @i INT = 1
		DECLARE @DateSN NVARCHAR(50),@SeedSN NVARCHAR(50),@SN NVARCHAR(150)

		SET @DateSN = FORMAT(@Date,@DateFormat);
	
		WHILE @i <= @Count
		BEGIN
	
			SET @Seed = @Seed + 1
	
			SET @SeedSN = CONVERT(NVARCHAR(50),@Seed)
	
			WHILE @SeedLength > LEN(@SeedSN)
			BEGIN
				SET @SeedSN = '0'+ @SeedSN
			END

			SET @SN = CONCAT(@Prefix,@DateSN,@SeedSN)
	
			IF @i > 1
			BEGIN
				SET @SN = CONCAT( ';',@SN)
			END
	
			SET @SerialNumber = CONCAT(@SerialNumber,@SN)
	
			SET @i = @i + 1
		END

	END



RETURN 0
