﻿CREATE TABLE [dbo].[ColumnMappingTemplate] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_ColumnMappingTemplate_ID]  DEFAULT (NEWSEQUENTIALID()),
    [FieldName]                 NVARCHAR(50)                NOT NULL,
    [DbFiled]                   NVARCHAR(50)                NOT NULL,
    [EnumFieldType]             INT                         NOT NULL,
    [IsRequired]                BIT                         NOT NULL,
    [EnumDDIType]               INT                         NOT NULL,
    [Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL, 
    [CreateTime]                DATETIME                    NULL, 
    [LastEditor]                NVARCHAR(50)                NULL, 
    [LastEditTime]              DATETIME                    NULL, 
    CONSTRAINT [PK_ColumnMappingTemplate] PRIMARY KEY CLUSTERED ([ID] ASC),
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'列映射模版',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMappingTemplate',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMappingTemplate',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'字段名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMappingTemplate',
    @level2type = N'COLUMN',
    @level2name = N'FieldName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'数据库字段名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMappingTemplate',
    @level2type = N'COLUMN',
    @level2name = N'DbFiled'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'格式',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMappingTemplate',
    @level2type = N'COLUMN',
    @level2name = N'EnumFieldType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'DDI类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMappingTemplate',
    @level2type = N'COLUMN',
    @level2name = N'EnumDDIType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否必填',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMappingTemplate',
    @level2type = N'COLUMN',
    @level2name = N'IsRequired'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMappingTemplate',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMappingTemplate',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMappingTemplate',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMappingTemplate',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMappingTemplate',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'

