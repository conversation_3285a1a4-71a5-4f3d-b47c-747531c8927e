﻿CREATE TABLE [dbo].[ReviewAuditor]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_ReviewAuditor_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [ReviewIndexId]             UNIQUEIDENTIFIER            NOT NULL,
    [ReviewTaskId]              UNIQUEIDENTIFIER            NOT NULL,
    [EmployeeId]                UNIQUEIDENTIFIER            NOT NULL,
    [ReviewEmployeeId]          UNIQUEIDENTIFIER            NULL,
    [ReviewTime]                DATETIME                    NULL,
    [ReviewRemark]              NVARCHAR(200)               NOT NULL,
    [EnumReviewStatus]          INT                         NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_ReviewAuditor] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_ReviewAuditor_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_ReviewAuditor_ReviewIndex_00_ReviewIndex_ReviewAuditors] FOREIGN KEY ([ReviewIndexId]) REFERENCES [dbo].[ReviewIndex] ([ID]),
    CONSTRAINT [FK_ReviewAuditor_ReviewTask_00_ReviewTask_ReviewAuditors] FOREIGN KEY ([ReviewTaskId]) REFERENCES [dbo].[ReviewTask] ([ID]),
    CONSTRAINT [FK_ReviewAuditor_Employee_00_Employee] FOREIGN KEY ([EmployeeId]) REFERENCES [dbo].[Employee] ([ID]),

)
