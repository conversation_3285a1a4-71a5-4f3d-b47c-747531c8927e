﻿using Shinsoft.DDI.Entities;
using Shinsoft.Core.DynamicQuery;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    [DynamicQueryEntity(typeof(Employee))]
    public class RoleEmployeeFilter : PagingFilterModel
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        [Required]
        [Description("角色ID")]
        public Guid RoleId { get; set; }

        /// <summary>
        /// 关键词
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(Employee), Employee.Columns.LoginName, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Employee), Employee.Columns.DisplayName, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Employee), Employee.Columns.JobNo, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Employee), Employee.Columns.Email, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Employee), Employee.Columns.Title, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Employee), Employee.Columns.Position, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        [Description("工号")]
        [DynamicQueryColumn(typeof(Employee), Employee.Columns.JobNo, Operation = Operation.StringIntelligence)]
        public string? JobNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [Description("姓名")]
        [DynamicQueryColumn(typeof(Employee), Employee.Columns.DisplayName, Operation = Operation.StringIntelligence)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        [Description("性别")]
        [DynamicQueryColumn(typeof(Employee), Employee.Columns.EnumGender, Operation = Operation.In)]
        public List<Gender>? Genders { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        [DynamicQueryColumn(typeof(Employee), Employee.Columns.EnumStatus, Operation = Operation.In)]
        public List<EmployeeStatus>? Statuses { get; set; }

        /// <summary>
        /// 入职时间
        /// </summary>
        [Description("入职时间")]
        [DynamicQueryColumn(typeof(Employee), Employee.Columns.HireDate, Operation = Operation.BetweenWithMinAndMax)]
        public List<DateTime>? HireDate { get; set; }

        /// <summary>
        /// 离职时间
        /// </summary>
        [Description("离职时间")]
        [DynamicQueryColumn(typeof(Employee), Employee.Columns.LeaveDate, Operation = Operation.BetweenWithMinAndMax)]
        public List<DateTime>? LeaveDate { get; set; }
    }
}
