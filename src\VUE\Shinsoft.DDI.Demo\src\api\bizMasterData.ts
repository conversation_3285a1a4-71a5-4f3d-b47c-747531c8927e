import { useHttpApi } from "./libs/httpApi";

const controller = "BizMasterData";

const api = useHttpApi(controller);

export const bizMasterDataApi = {
  QueryBizDict(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryBizDict", data, config);
  },
  GetBizDict(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetBizDict", { id }, config);
  },
  AddBizDict(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddBizDict", data, config);
  },
  UpdateBizDict(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateBizDict", data, config);
  },
  DelectBizDict(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("DelectBizDict", { id: data.id }, config);
  }
};
