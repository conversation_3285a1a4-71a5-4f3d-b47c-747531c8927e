﻿CREATE PROCEDURE [log].[sp_ClearLogs]
	@CompanyId				NVARCHAR(50),
	@LogTime				DATETIME
AS
	SET NOCOUNT ON;

	IF @CompanyId IS NULL
	BEGIN

		IF @LogTime	IS NULL
		BEGIN

			DELETE FROM [log].[LogException];
			DELETE FROM [log].[LogInterface];
			DELETE FROM [log].[LogTarget];
			DELETE FROM [log].[LogApi];
			DELETE FROM [log].[LogWeb];
			DELETE FROM [log].[Log];

		END
		ELSE
		BEGIN

			DELETE le FROM [log].[LogException] AS  le
			LEFT JOIN [log].[Log] AS l ON l.ID = le.ID
			WHERE l.LogTime < @LogTime;

			DELETE li FROM [log].[LogInterface] AS  li
			LEFT JOIN [log].[Log] AS l ON l.ID = li.ID
			WHERE l.LogTime < @LogTime;

			DELETE lt FROM [log].[LogTarget] AS  lt
			LEFT JOIN [log].[Log] AS l ON l.ID = lt.ID
			WHERE l.LogTime < @LogTime;

			DELETE la FROM [log].[LogApi] AS  la
			LEFT JOIN [log].[Log] AS l ON l.ID = la.ID
			WHERE l.LogTime < @LogTime;

			DELETE lw FROM [log].[LogWeb] AS  lw
			LEFT JOIN [log].[Log] AS l ON l.ID = lw.ID
			WHERE l.LogTime < @LogTime;

			DELETE l FROM [log].[Log] AS  l
			WHERE l.LogTime < @LogTime;

		END
	END
	ELSE
	BEGIN

		IF @LogTime	IS NULL
		BEGIN
			DELETE FROM [log].[LogException] WHERE CompanyId = @CompanyId;
			DELETE FROM [log].[LogInterface] WHERE CompanyId = @CompanyId;
			DELETE FROM [log].[LogTarget] WHERE CompanyId = @CompanyId;
			DELETE FROM [log].[LogApi] WHERE CompanyId = @CompanyId;
			DELETE FROM [log].[LogWeb] WHERE CompanyId = @CompanyId;
			DELETE FROM [log].[Log] WHERE CompanyId = @CompanyId;

		END
		ELSE
		BEGIN

			DELETE le FROM [log].[LogException] AS  le
			INNER JOIN [log].[Log] AS l ON l.ID = le.ID
			WHERE l.CompanyId = @CompanyId
			AND l.LogTime < @LogTime;

			DELETE li FROM [log].[LogInterface] AS  li
			INNER JOIN [log].[Log] AS l ON l.ID = li.ID
			WHERE l.LogTime < @LogTime;

			DELETE lb FROM [log].[LogTarget] AS  lb
			INNER JOIN [log].[Log] AS l ON l.ID = lb.ID
			WHERE l.CompanyId = @CompanyId
			AND  l.LogTime < @LogTime;

			DELETE la FROM [log].[LogApi] AS  la
			INNER JOIN [log].[Log] AS l ON l.ID = la.ID
			WHERE l.CompanyId = @CompanyId
			AND  l.LogTime < @LogTime;

			DELETE lw FROM [log].[LogWeb] AS  lw
			INNER JOIN [log].[Log] AS l ON l.ID = lw.ID
			WHERE l.CompanyId = @CompanyId
			AND  l.LogTime < @LogTime;

			DELETE l FROM [log].[Log] AS  l
			WHERE l.CompanyId = @CompanyId
			AND  l.LogTime < @LogTime;

		END

	END
GO

GRANT EXEC ON [log].[sp_ClearLogs] TO PUBLIC
GO