﻿<#@ import namespace="System.Collections.Generic" #>
<#+
	const string connectionString = "Server=xl7.corp.shinsoft.net,9210;Database=XL_DDI; uid=xldev;pwd=**********;";

	const string database = "XL_DDI";

	const string entityNamespace = "Shinsoft.DDI.Entities";
	const string entityPrefix = "";

	const string dalNamespace = "Shinsoft.DDI.Dal";
	const string dbContextName = "BizDbContext";

	const string companyIdTypeName = "Guid";
	const string companyTypeName = "Company";

    const bool useI18n = false;

	string[] entitySchemas = new string[]
	{
		"dbo",
	};

	string[] entityTables = new string[]
	{
	};

	string[] entityViews = new string[]
	{
	};

	string[] entityViewPrefixes = new string[]
	{
		"Vw"
	};

	string[] enumNamespaces = new string[]
	{
		"Shinsoft.Core.Mail",
	};
#>
