﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public static partial class UserExtender
    {
        public static void CopyFrom(this User user, Employee employee)
        {
            if (user != null && employee != null)
            {
                user.DisplayName = employee.DisplayName;
                user.Mobile = employee.Mobile;
                user.Email = employee.Email;
            }
        }
    }
}