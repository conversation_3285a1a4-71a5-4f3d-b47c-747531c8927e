﻿namespace Shinsoft.DDI.Api.Models
{
    [MapFromType(typeof(Auth))]
    public partial class RoleAuthMeta
    {
        [MapFromIgnore(typeof(Auth))]
        public override Guid ID { get; set; }

        [MapFromProperty(typeof(Auth), Auth.Columns.ID)]
        public override Guid AuthId { get; set; }

        [MapFromProperty(typeof(Auth), Auth.Columns.Code)]
        [MapFromProperty(typeof(RoleAuth), RoleAuth.Foreigns.Auth, Auth.Columns.Code)]
        public virtual string? AuthCode { get; set; }

        [MapFromProperty(typeof(Auth), Auth.Columns.Name)]
        [MapFromProperty(typeof(RoleAuth), RoleAuth.Foreigns.Auth, Auth.Columns.Name)]
        public virtual string? AuthName { get; set; }

        [MapFromProperty(typeof(Auth), Auth.Columns.EnumAuthTagType)]
        [MapFromProperty(typeof(RoleAuth), RoleAuth.Foreigns.Auth, Auth.Columns.EnumAuthTagType)]
        public virtual AuthTagType EnumAuthTagType { get; set; }

        [MapFromProperty(typeof(Auth), Auth.Columns.EnumProgramFlags)]
        [MapFromProperty(typeof(RoleAuth), RoleAuth.Foreigns.Auth, Auth.Columns.EnumProgramFlags)]
        public virtual ProgramFlag EnumProgramFlags { get; set; }
    }
}
