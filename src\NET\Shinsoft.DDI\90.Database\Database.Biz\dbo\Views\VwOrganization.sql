--组织架构视图
CREATE VIEW [dbo].[VwOrganization] AS

SELECT 
	CONCAT(sc.ID, '') AS [ID],
	sc.CompanyId,
	CONVERT(NVARCHAR, NULL) AS [ParentId],
	CASE sc.EnumType
		WHEN 1 THEN 1
		ELSE 2
	END AS [EnumType],
    sc.Name AS [Name],
	sc.Valid,
    1 AS [Level],
    NULL AS [Grade],
	sc.ID AS [SubCompanyId],
	NULL AS [DepartmentId],
	NULL AS [StationId],
	NULL AS [EmployeeId]
FROM dbo.SubCompany AS sc 
WHERE sc.Deleted = 0
UNION ALL
SELECT
	CONCAT(d.SubCompanyId, '|', d.ID) AS [ID],
	d.CompanyId,
	CASE 
		WHEN d.ParentId IS NULL THEN CONVERT(NVARCHAR(111), d.SubCompanyId)
		ELSE CONCAT(d.SubCompanyId,'|',d.ParentId)
	END AS [ParentId],
	100 AS [EnumType],
    d.Name AS [Name],
	d.Valid,
    2 AS [Level],
    NULL AS [Grade],
	d.SubCompanyId AS [SubCompanyId],
    d.ID AS [DepartmentId],
	NULL AS [StationId],
	NULL AS [EmployeeId]
FROM dbo.Department AS d
INNER JOIN dbo.SubCompany AS sc ON sc.ID = d.SubCompanyId
WHERE d.Deleted = 0
AND sc.Deleted = 0
UNION ALL
SELECT
	CONCAT(d.SubCompanyId, '|', s.DepartmentId, '|',s.ID) AS [ID],
	s.CompanyId,
	CONCAT(d.SubCompanyId, '|', s.DepartmentId) AS [ParentId],
	200 AS [EnumType],
    s.Name AS [Name],
	s.Valid,
    3 AS [Level],
    p.Grade AS [Grade],
	d.SubCompanyId AS [SubCompanyId],
	s.DepartmentId,
    s.ID AS [StationId],
	NULL AS [EmployeeId]
FROM dbo.Station AS s
INNER JOIN dbo.Department AS d ON d.ID = s.DepartmentId
INNER JOIN dbo.SubCompany AS sc ON sc.ID = d.SubCompanyId
INNER JOIN dbo.Position AS p ON p.ID = s.PositionId
WHERE s.Deleted = 0
AND d.Deleted = 0
AND sc.Deleted = 0
UNION ALL
SELECT
	CONCAT(d.SubCompanyId, '|', s.DepartmentId, '|', es.StationId, '|', es.ID) AS [ID],
	s.CompanyId,
	CONCAT(d.SubCompanyId, '|', s.DepartmentId, '|', es.StationId) AS [ParentId],
	300 AS [EnumType],
    e.DisplayName AS [Name],
    CONVERT(BIT,1) AS [Valid],
    4 AS [Level],
    p.Grade AS [Grade],
	d.SubCompanyId AS [SubCompanyId],
	s.DepartmentId,
    es.StationId AS [StationId],
	es.EmployeeId AS [EmployeeId]
FROM dbo.EmployeeStation AS es
INNER JOIN dbo.Employee AS e ON e.ID = es.EmployeeId
INNER JOIN dbo.Station AS s ON s.ID = es.StationId
INNER JOIN dbo.Position AS p ON p.ID = s.PositionId
INNER JOIN dbo.Department AS d ON d.ID = s.DepartmentId
INNER JOIN dbo.SubCompany AS sc ON sc.ID = d.SubCompanyId
WHERE e.Deleted = 0
AND s.Deleted = 0
AND d.Deleted = 0
AND sc.Deleted = 0
