﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public partial class SubCompany : IOrder
    {
        #region IOrder

        IComparable IOrder.Order => this.Sort;

        #endregion IOrder

        [NotMapped, XmlIgnore, JsonIgnore]
        public string? Text => this.ShortName.IsEmpty() ? this.Name : $"[{this.ShortName}] {this.Name}";
    }
}