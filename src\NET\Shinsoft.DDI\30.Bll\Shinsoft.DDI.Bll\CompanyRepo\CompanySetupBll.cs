﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class CompanySetupBll : BaseCompanyBll
    {
        #region Constructs

        public CompanySetupBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public CompanySetupBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public CompanySetupBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public CompanySetupBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region CompanySetting

        /// <summary>
        /// 编辑扩展配置
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<CompanySetting> UpdateCompanySetting(CompanySetting entity)
        {
            var result = new BizResult<CompanySetting>();

            var id = entity.ID;
            var dbEntity = this.Get<CompanySetting>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, CompanySetting.I18ns._Entity);
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.Value.IsEmpty())
                    {
                        result.Error(I18ns.Rule.CompanySetting.Value_Required);
                    }

                    if (entity.Remark.IsEmpty())
                    {
                        result.Error(I18ns.Rule.CompanySetting.Remark_Required);
                    }
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                }
            }

            return result;
        }

        #endregion CompanySetting

        public void InitVueRoute()
        {
            var cc = this.CompanyCache;

            var paths = new List<string>
            {
                VueRoute.Inverses.Meta
            };
            var routes = this.GetEntities<VueRoute>(VueRoute.Inverses.Meta, p => p.IsSys, true);

            // to do

            this.SaveChanges();

            cc.RemoveCache<VueRoute>();
        }
    }
}