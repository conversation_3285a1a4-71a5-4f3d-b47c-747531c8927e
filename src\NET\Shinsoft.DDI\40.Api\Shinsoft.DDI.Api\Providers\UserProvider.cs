﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Shinsoft.Core.Mvc.Caching.Memory;

namespace Shinsoft.DDI.Api.Providers
{
    public class UserProvider : BaseUserProvider<User, IdentityUser, Identity>
    {
        protected SysBll Repo => MvcHttpContext.GetRequiredService<SysBll>();

        protected SysCache SysCache => MvcHttpContext.GetRequiredService<SysCache>();

        protected CompanyCache GetCompanyCache(Guid? companyId = null)
        {
            var pool = MvcHttpContext.GetRequiredService<CompanyCachePool>();

            return pool.GetCompanyCache(companyId ?? Config.DefaultCompanyId);
        }

        protected override IdentityUser ToIdentityUser(User user, params object?[] loginArgs)
        {
            var autoLogin = false;

            if (loginArgs.Length >= 2)
            {
                autoLogin = Convert.ToBoolean(loginArgs[1]);
            }

            var identityUser = user.Map<IdentityUser>();

            var identity = identityUser.Map<Identity>();

            identityUser.AuthToken = $"{JwtBearerDefaults.AuthenticationScheme} {identity.GetJwt()}";

            if (Config.UserAuthorize.RefreshToken.Allow)
            {
                var expireMins = Config.UserAuthorize.RefreshToken.ExpireMinutes;

                if (expireMins >= 0)
                {
                    //设置刷新令牌

                    //过期时间不可以用SysDateTime
                    var expiry = DateTime.Now.AddMinutes(expireMins);

                    var identityExpiry = new IdentityExpiry(identity.IdentityKey, expiry);

                    var json = identityExpiry.ToJson();

                    identityUser.RefreshToken = AesHelper.Encrypt(json, Config.UserAuthorize.RefreshToken.AesKey);
                    identityUser.RefreshExpiry = expiry;
                }
            }

            if (Config.UserAuthorize.AutoLogin.Allow && autoLogin)
            {
                var expireDays = Config.UserAuthorize.AutoLogin.ExpireDays;

                if (expireDays >= 0)
                {
                    //设置自动登录token

                    //过期时间不可以用SysDateTime
                    var expiry = DateTime.Now.AddDays(expireDays);

                    var identityExpiry = new IdentityExpiry(identity.IdentityKey, expiry);

                    var json = identityExpiry.ToJson();

                    identityUser.LoginToken = AesHelper.Encrypt(json, Config.UserAuthorize.AutoLogin.AesKey);
                    identityUser.LoginExpiry = expiry;
                }
            }

            var sc = this.SysCache;

            var currEmployeeId = user.IsAgent ? user.Agent?.ID : user.Employee?.ID;

            if (user.MyIdentities?.Count > 0)
            {
                foreach (var keyValue in user.MyIdentities!)
                {
                    var company = keyValue.Key;
                    var identities = keyValue.Value;

                    var model = company.Map<IdentityCompany>();

                    model.Identities = identities.Maps<IdentityEmployee>();

                    var currEmployee = model.Identities.FirstOrDefault(p => p.ID == currEmployeeId);

                    if (currEmployee != null)
                    {
                        currEmployee.IsCurrent = !user.IsAgent;
                        currEmployee.IsAgent = user.IsAgent;

                        var delegateKey = user.MyDelegates?.Keys.FirstOrDefault(p => p.ID == currEmployee.ID);

                        if (delegateKey != null && user.MyDelegates!.TryGetValue(delegateKey, out List<Employee>? delegates))
                        {
                            model.Delegates = delegates.Maps<IdentityEmployee>();

                            if (user.IsAgent)
                            {
                                var current = model.Delegates.FirstOrDefault(p => p.ID == user.Employee?.ID);

                                if (current != null)
                                {
                                    current.IsCurrent = true;
                                }
                            }
                        }
                    }

                    identityUser.MyCompanies.TryAdd(model.ID, model);
                }
            }

            return identityUser;
        }

        public override BizResult<IdentityUser> UserLogin(string loginName, string password, params object?[] args)
        {
            var result = new BizResult<IdentityUser>();

            loginName = loginName.Trim();

            User? user;

            if (args.Length < 1 || args[0] == null)
            {
                result.Error("缺少登录终端类型");
            }
            else
            {
                var program = args[0]!.ToEnum<ProgramFlag>();

                if (password.IsEmpty())
                {
                    result.Error("请输入密码");
                }

                switch (program)
                {
                    case ProgramFlag.PC:
                    case ProgramFlag.Mobile:
                        break;

                    default:
                        program = ProgramFlag.PC;
                        //result.Error("请指定需要登录的终端类型");
                        break;
                }

                if (result.Success)
                {
                    // 验证用户是否可以登录

                    user = this.Repo.GetUserByLoginName(loginName, program);

                    if (user == null)
                    {
                        result.Error("用户或者密码不正确");
                    }
                    else if (user.EnumStatus != UserStatus.Valid)
                    {
                        result.Error("用户已被禁用");
                    }
                    else
                    {
                        //用户存在

                        if (Config.Debug)
                        {
                            if (!Config.DebugPwd.IsEmpty() && password != Config.DebugPwd)
                            {
                                result.Error("测试密码不正确");
                            }
                        }
                        else
                        {
                            var pwd = password.Trim();

                            switch (user.EnumPwdType)
                            {
                                case PwdType.MD5:
                                    pwd = pwd.ToMD5();
                                    break;

                                case PwdType.SHA1:
                                    pwd = pwd.ToSHA1();
                                    break;

                                case PwdType.SHA256:
                                    pwd = pwd.ToSHA256();
                                    break;
                            }

                            if (user.EnumPwdType == PwdType.None)
                            {
                                if (pwd != user.PwdText)
                                {
                                    result.Error("用户或者密码不正确!");
                                }
                            }
                            else
                            {
                                if (!pwd.Equals(user.PwdText, StringComparison.InvariantCultureIgnoreCase))
                                {
                                    result.Error("用户或者密码不正确!");
                                }
                            }
                        }
                    }

                    if (result.Success)
                    {
                        //验证员工

                        if (user?.EnumType == UserType.Employee)
                        {
                            if (user.Employee == null)
                            {
                                result.Error("员工不存在");
                            }
                            else if (!Config.AllowLoginEmployeeStatus.Contains(user.Employee.EnumStatus))
                            {
                                result.Error("员工状态不允许登录");
                            }
                        }
                    }

                    if (result.Success)
                    {
                        user = user.Value();

                        // 用户登录成功

                        var identityUser = this.ToIdentityUser(user, args);

                        this.SetUser(user);

                        result.Data = identityUser;
                    }
                }
            }

            return result;
        }

        protected override User? GetUserByIdentityKey(IIdentityKey identityKey)
        {
            User? user = null;

            if (identityKey is ApiIdentityKey apiIdentityKey)
            {
                user = this.Repo.GetUserByIdentityKey(apiIdentityKey);
            }

            return user;
        }

        public BizResult<IdentityUser> SwitchCulture(string culture)
        {
            var sc = this.SysCache;

            var currentUser = MvcHttpContext.GetOperatorUser<User>()
                ?? throw new InvalidOperationException(sc.Translate(I18ns.Message.User.CurrentUserNotExist));

            var result = new BizResult<IdentityUser>();

            if (currentUser.Culture != culture)
            {
                if (currentUser.OperatorCompanyId.HasValue)
                {
                    var cc = CompanyCache.GetCompanyCache(currentUser.OperatorCompanyId.Value);

                    if (!cc.CompanyCultures.Any(p => p.Culture == culture))
                    {
                        result.Error(I18ns.Message.Culture.Format.NotExist, culture);
                    }
                }
            }

            if (result.Success)
            {
                var setResult = this.Repo.SetUserCulture(currentUser.ID, culture);

                result.Merge(setResult);
            }

            if (result.Success)
            {
                currentUser.Culture = culture;

                this.SetUser(currentUser);

                // 切换语言成功
                result.Data = this.ToIdentityUser(currentUser);
            }

            return result;
        }

        public BizResult<IdentityUser> SwitchMyIdentity(Guid newIdentityId)
        {
            var currentUser = MvcHttpContext.GetOperatorUser<User>() ?? throw new InvalidOperationException($"无法获取当前用户信息");

            var program = currentUser.Program;

            var result = new BizResult<IdentityUser>();

            var employee = this.Repo.Get<Employee>(newIdentityId);

            if (employee == null)
            {
                result.Error("切换员工不存在");
            }
            else if (employee.UserId != currentUser.ID)
            {
                result.Error("当前用户无权切换至此员工");
            }
            else
            {
                var identityKey = new ApiIdentityKey
                {
                    Program = program,
                    UserId = currentUser.UserId,
                    EmployeeId = newIdentityId.ToString(),
                    AgentId = string.Empty
                };

                var user = this.Repo.GetUserByIdentityKey(identityKey);

                if (user == null)
                {
                    result.Error("切换员工出错");
                }
                else if (user.EnumStatus != UserStatus.Valid)
                {
                    result.Error("当前用户已被禁用");
                }
                else if (user.Employee != null && !Config.AllowLoginEmployeeStatus.Contains(user.Employee.EnumStatus))
                {
                    result.Error("员工被禁止登录");
                }
                else
                {
                    this.SetUser(user);

                    // 用户登录成功
                    result.Data = this.ToIdentityUser(user);
                }
            }

            return result;
        }

        public BizResult<IdentityUser> SwitchToAgent(Guid newIdentityId)
        {
            var currentUser = MvcHttpContext.GetOperatorUser<User>() ?? throw new InvalidOperationException($"无法获取当前用户信息");

            var program = currentUser.Program;

            var result = new BizResult<IdentityUser>();

            var myEmployeeId = currentUser.IsAgent ? currentUser.Agent?.ID : currentUser.Employee?.ID;

            if (myEmployeeId == null)
            {
                result.Error("当前身份无权使用代理功能");
            }
            else
            {
                var repo = this.Repo;

                var employee = repo.Get<Employee>(newIdentityId);

                if (employee == null)
                {
                    result.Error("授权员工不存在");
                }
                else
                {
                    var companyId = currentUser.CurrentCompanyId;

                    var today = DateTime.Today;

                    var employeeDelegate = repo.GetEntity<EmployeeDelegate>(p => p.CompanyId == companyId
                        && p.Valid
                        && p.EmployeeId == newIdentityId
                        && p.AgentId == myEmployeeId
                        && (!p.StartDate.HasValue || p.StartDate <= today)
                        && (!p.EndDate.HasValue || p.EndDate >= today));

                    if (employeeDelegate == null)
                    {
                        result.Error("当前用户无权切换至此员工");
                    }
                    else
                    {
                        var identityKey = new ApiIdentityKey
                        {
                            Program = program,
                            UserId = currentUser.UserId,
                            EmployeeId = newIdentityId.ToString(),
                            AgentId = myEmployeeId.AsString(),
                        };

                        var user = this.Repo.GetUserByIdentityKey(identityKey);

                        if (user == null)
                        {
                            result.Error("切换员工出错");
                        }
                        else if (user.EnumStatus != UserStatus.Valid)
                        {
                            result.Error("当前用户已被禁用");
                        }
                        else if (user.Employee != null && !Config.AllowLoginEmployeeStatus.Contains(user.Employee.EnumStatus))
                        {
                            result.Error("代理员工被禁止登录");
                        }
                        else
                        {
                            this.SetUser(user);

                            // 用户登录成功
                            result.Data = this.ToIdentityUser(user);
                        }
                    }
                }
            }
            return result;
        }

        public BizResult<IdentityUser> UserLogin(string loginName, ProgramFlag program)
        {
            var result = new BizResult<IdentityUser>();

            loginName = loginName.Trim();

            var user = this.Repo.GetUserByLoginName(loginName, program);

            if (user == null)
            {
                //用户不存在

                result.Error("Token错误");
            }
            else if (user.EnumStatus != UserStatus.Valid)
            {
                result.Error("用户被禁止登录");
            }
            else if (user.EnumType == UserType.Employee && user.Employee == null)
            {
                result.Error("员工不存在");
            }
            else if (user.Employee != null && !Config.AllowLoginEmployeeStatus.Contains(user.Employee.EnumStatus))
            {
                result.Error("员工被禁止登录");
            }
            else
            {
                this.SetUser(user);

                // 用户登录成功
                result.Data = this.ToIdentityUser(user);
            }

            return result;
        }

        public BizResult<IdentityUser> UserLogin(IIdentityKey identityKey)
        {
            var result = new BizResult<IdentityUser>();

            var user = this.GetUserByIdentityKey(identityKey);

            if (user == null)
            {
                //用户不存在

                result.Error("用户令牌信息错误");
            }
            else if (user.EnumStatus != UserStatus.Valid)
            {
                result.Error("用户被禁止登录");
            }
            else if (user.EnumType == UserType.Employee && user.Employee == null)
            {
                result.Error("员工不存在");
            }
            else if (user.Employee != null && !Config.AllowLoginEmployeeStatus.Contains(user.Employee.EnumStatus))
            {
                result.Error("员工被禁止登录");
            }
            else
            {
                this.SetUser(user);

                // 用户登录成功
                result.Data = this.ToIdentityUser(user);
            }

            return result;
        }
    }
}
