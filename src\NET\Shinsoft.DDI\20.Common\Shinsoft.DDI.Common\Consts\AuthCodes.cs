﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Common
{
    public static class AuthCodes
    {
        /// <summary>
        /// 平台管理
        /// </summary>
        public static class Platform
        {
            /// <summary>
            /// 平台系统设置
            /// </summary>
            public static class Setup
            {
                /// <summary>
                /// 权限定义
                /// </summary>
                public const string Auth_Define = "Platform:Auth:Define";

                /// <summary>
                /// 系统扩展配置管理
                /// </summary>
                public const string SysSetting_Manage = "Platform:SysSetting:Manage";

                /// <summary>
                /// 系统多语言管理
                /// </summary>
                public const string SysCulture_Manage = "Platform:SysCulture:Manage";

                /// <summary>
                /// 系统字典管理【暂不使用】
                /// </summary>
                public const string SysDict_Config = "Platform:SysDict:Config";

                /// <summary>
                /// 多语言文本管理
                /// </summary>
                public const string I18n_Manage = "Platform:I18n:Manage";

                /// <summary>
                /// 系统缓存操作
                /// </summary>
                public const string SysCache_Operate = "Platform:SysCache:Operate";
            }

            /// <summary>
            /// 平台公司管理
            /// </summary>
            public static class Company
            {
                /// <summary>
                /// 公司扩展配置【暂不使用】
                /// </summary>
                public const string Company_Config = "Platform:Company:Config";

                /// <summary>
                /// 平台公司配置管理【暂不使用】
                /// </summary>
                public const string CompanySetting_Config = "Platform:CompanySetting:Config";
            }

            /// <summary>
            /// 平台用户管理
            /// </summary>
            public static class User
            {
                /// <summary>
                /// 平台用户查询
                /// </summary>
                public const string User_Query = "Platform:User:Query";

                /// <summary>
                /// 平台用户管理
                /// </summary>
                public const string User_Manage = "Platform:User:Manage";

                /// <summary>
                /// 平台用户管理:新增
                /// </summary>
                public const string User_Manage_Add = "Platform:User:Manage:Add";

                /// <summary>
                /// 平台用户管理:编辑
                /// </summary>
                public const string User_Manage_Edit = "Platform:User:Manage:Edit";

                /// <summary>
                /// 平台用户管理:删除
                /// </summary>
                public const string User_Manage_Delete = "Platform:User:Manage:Delete";
            }
        }

        /// <summary>
        /// 系统管理
        /// </summary>
        public static class System
        {
            /// <summary>
            /// 系统设置
            /// </summary>
            public static class Setup
            {
                /// <summary>
                /// 公司配置维护
                /// </summary>
                public const string CompanySetting_Maintain = "CompanySetting:Maintain";

                /// <summary>
                /// 公司缓存操作
                /// </summary>
                public const string CompanyCache_Operate = "CompanyCache:Operate";
            }

            /// <summary>
            /// 公告
            /// </summary>
            public static class Announcement
            {
                /// <summary>
                /// 公告查询
                /// </summary>
                public const string Announcement_Query = "Announcement:Query";

                /// <summary>
                /// 公告管理
                /// </summary>
                public const string Announcement_Manage = "Announcement:Manage";
            }

            /// <summary>
            /// 邮件
            /// </summary>
            public static class Mail
            {
                /// <summary>
                /// 邮件查询
                /// </summary>
                public const string Mail_Query = "Mail:Query";

                /// <summary>
                /// 邮件管理
                /// </summary>
                public const string Mail_Manage = "Mail:Manage";
            }

            /// <summary>
            /// 日志
            /// </summary>
            public static class Log
            {
                /// <summary>
                /// 日志查询
                /// </summary>
                public const string Log_Query = "Log:Query";
            }
        }

        /// <summary>
        /// 授权管理
        /// </summary>
        public static class Authorize
        {
            /// <summary>
            /// 员工
            /// </summary>
            public static class Employee
            {
                /// <summary>
                /// 员工查询
                /// </summary>
                public const string Employee_Query = "Employee:Query";

                /// <summary>
                /// 员工管理
                /// </summary>
                public const string Employee_Manage = "Employee:Manage";

                /// <summary>
                /// 员工管理:新增
                /// </summary>
                public const string Employee_Manage_Add = "Employee:Manage:Add";

                /// <summary>
                /// 员工管理:编辑
                /// </summary>
                public const string Employee_Manage_Edit = "Employee:Manage:Edit";

                /// <summary>
                /// 员工管理:删除
                /// </summary>
                public const string Employee_Manage_Delete = "Employee:Manage:Delete";

                /// <summary>
                /// 员工管理:重置密码
                /// </summary>
                public const string Employee_Manage_ResetPwd = "Employee:Manage:ResetPwd";
            }

            /// <summary>
            /// 角色
            /// </summary>
            public static class Role
            {
                /// <summary>
                /// 角色查询
                /// </summary>
                public const string Role_Query = "Role:Query";

                /// <summary>
                /// 角色管理
                /// </summary>
                public const string Role_Manage = "Role:Manage";

                /// <summary>
                /// 角色管理:新增
                /// </summary>
                public const string Role_Manage_Add = "Role:Manage:Add";

                /// <summary>
                /// 角色管理:编辑
                /// </summary>
                public const string Role_Manage_Edit = "Role:Manage:Edit";

                /// <summary>
                /// 角色管理:删除
                /// </summary>
                public const string Role_Manage_Delete = "Role:Manage:Delete";

                /// <summary>
                /// 角色管理:授权
                /// </summary>
                public const string Role_Manage_Auth = "Role:Manage:Auth";

                /// <summary>
                /// 角色管理:成员维护
                /// </summary>
                public const string Role_Manage_Member = "Role:Manage:Member";
            }
        }

        /// <summary>
        /// 主数据
        /// </summary>
        public static class MasterData
        {
            /// <summary>
            /// 基础主数据
            /// </summary>
            public static class Basic
            {
                #region Organization 组织架构

                /// <summary>
                /// 组织架构查询
                /// </summary>
                public const string Organization_Query = "Organization:Query";

                /// <summary>
                /// 组织架构管理
                /// 包含部门管理、岗位管理等全部管理功能
                /// </summary>
                public const string Organization_Manage = "Organization:Manage";

                #region Department 部门管理

                /// <summary>
                /// 组织架构管理:部门管理【暂不使用】
                /// 包含新增、编辑、删除、移动、关联成本中心等全部管理功能
                /// </summary>
                public const string Organization_Manage_Department = "Organization:Manage:Department";

                /// 组织架构管理:部门管理:新增【暂不使用】
                /// </summary>
                public const string Organization_Manage_Department_Add = "Organization:Manage:Department:Add";

                /// <summary>
                ///  组织架构管理:部门管理:编辑【暂不使用】
                /// </summary>
                public const string Organization_Manage_Department_Edit = "Organization:Manage:Department:Edit";

                /// <summary>
                ///  组织架构管理:部门管理:删除【暂不使用】
                /// </summary>
                public const string Organization_Manage_Department_Delete = "Organization:Manage:Department:Delete";

                /// <summary>
                ///  组织架构管理:部门管理:移动【暂不使用】
                /// </summary>
                public const string Organization_Manage_Department_Move = "Organization:Manage:Department:Move";

                /// <summary>
                ///  组织架构管理:部门管理:关联成本中心【暂不使用】
                /// </summary>
                public const string Organization_Manage_Department_CostCenter = "Organization:Manage:Department:CostCenter";

                /// <summary>
                ///  组织架构管理:部门管理:激活/停用【暂不使用】
                /// </summary>
                public const string Organization_Manage_Department_Valid = "Organization:Manage:Department:Valid";

                #endregion Department 部门管理

                #region Station 岗位管理

                /// <summary>
                /// 组织架构管理:岗位管理【暂不使用】
                /// 包含新增,编辑，删除，人岗维护等全部管理功能
                /// </summary>
                public const string Organization_Manage_Station = "Organization:Manage:Station";

                /// <summary>
                ///组织架构管理:岗位管理:新增【暂不使用】
                /// </summary>
                public const string Organization_Manage_Station_Add = "Organization:Manage:Station:Add";

                /// <summary>
                /// 组织架构管理:岗位管理:编辑【暂不使用】
                /// </summary>
                public const string Organization_Manage_Station_Edit = "Organization:Manage:Station:Edit";

                /// <summary>
                /// 组织架构管理:岗位管理:删除【暂不使用】
                /// </summary>
                public const string Organization_Manage_Station_Delete = "Organization:Manage:Station:Delete";

                /// <summary>
                /// 组织架构管理:岗位管理:人岗维护【暂不使用】
                /// </summary>
                public const string Organization_Manage_Station_Employee = "Organization:Manage:Station:Employee";

                /// <summary>
                /// 组织架构管理:岗位管理:激活/停用【暂不使用】
                /// </summary>
                public const string Organization_Manage_Station_Valid = "Organization:Manage:Station:Valid";

                #endregion Station 岗位管理

                #endregion Organization 组织架构

                #region SubCompany 分公司

                /// <summary>
                /// 分公司查询
                /// </summary>
                public const string SubCompany_Query = "SubCompany:Query";

                /// <summary>
                /// 分公司管理
                /// 包含新增、编辑、删除等全部管理功能
                /// </summary>
                public const string SubCompany_Manage = "SubCompany:Manage";

                /// <summary>
                /// 分公司管理:新增【暂不使用】
                /// </summary>
                public const string SubCompany_Manage_Add = "SubCompany:Manage:Add";

                /// <summary>
                /// 分公司管理:编辑【暂不使用】
                /// </summary>
                public const string SubCompany_Manage_Edit = "SubCompany:Manage:Edit";

                /// <summary>
                /// 分公司管理:删除【暂不使用】
                /// </summary>
                public const string SubCompany_Manage_Delete = "SubCompany:Manage:Delete";

                /// <summary>
                /// 分公司管理:激活/停用【暂不使用】
                /// </summary>
                public const string SubCompany_Manage_Valid = "SubCompany:Manage:Valid";

                #endregion SubCompany 分公司

                #region Department 部门

                /// <summary>
                /// 部门查询
                /// </summary>
                public const string Department_Query = "Department:Query";

                /// <summary>
                /// 部门管理
                /// 包含新增、编辑、删除等全部管理功能
                /// </summary>
                public const string Department_Manage = "Department:Manage";

                /// 部门管理:新增【暂不使用】
                /// </summary>
                public const string Department_Manage_Add = "Department:Manage:Add";

                /// <summary>
                /// 部门管理:编辑【暂不使用】
                /// </summary>
                public const string Department_Manage_Edit = "Department:Manage:Edit";

                /// <summary>
                /// 部门管理:删除【暂不使用】
                /// </summary>
                public const string Department_Manage_Delete = "Department:Manage:Delete";

                /// <summary>
                /// 部门管理:删除【暂不使用】
                /// </summary>
                public const string Department_Manage_Move = "Department:Manage:Move";

                /// <summary>
                /// 部门管理:关联成本中心【暂不使用】
                /// </summary>
                public const string Department_Manage_CostCenter = "Department:Manage:CostCenter";

                /// <summary>
                /// 部门管理:激活/停用【暂不使用】
                /// </summary>
                public const string Department_Manage_Valid = "Department:Manage:Valid";

                #endregion Department 部门

                #region CostCenter 成本中心

                /// <summary>
                /// 成本中心查询
                /// </summary>
                public const string CostCenter_Query = "CostCenter:Query";

                /// <summary>
                /// 成本中心管理
                /// 包含新增、编辑、删除等全部管理功能
                /// </summary>
                public const string CostCenter_Manage = "CostCenter:Manage";

                /// <summary>
                /// 成本中心管理:新增【暂不使用】
                /// </summary>
                public const string CostCenter_Manage_Add = "CostCenter:Manage:Add";

                /// <summary>
                /// 成本中心管理:编辑【暂不使用】
                /// </summary>
                public const string CostCenter_Manage_Edit = "CostCenter:Manage:Edit";

                /// <summary>
                /// 成本中心管理:删除【暂不使用】
                /// </summary>
                public const string CostCenter_Manage_Delete = "CostCenter:Manage:Delete";

                /// <summary>
                /// 成本中心管理:激活/停用【暂不使用】
                /// </summary>
                public const string CostCenter_Manage_Valid = "CostCenter:Manage:Valid";

                #endregion CostCenter 成本中心

                #region Position 职位

                /// <summary>
                /// 职位查询
                /// </summary>
                public const string Position_Query = "Position:Query";

                /// <summary>
                /// 职位管理
                /// 包含新增、编辑、删除等全部管理功能
                /// </summary>
                public const string Position_Manage = "Position:Manage";

                /// <summary>
                ///职位管理:新增【暂不使用】
                /// </summary>
                public const string Position_Manage_Add = "Position:Manage:Add";

                /// <summary>
                /// 职位管理:编辑【暂不使用】
                /// </summary>
                public const string Position_Manage_Edit = "Position:Manage:Edit";

                /// <summary>
                /// 职位管理:删除【暂不使用】
                /// </summary>
                public const string Position_Manage_Delete = "Position:Manage:Delete";

                #endregion Position 职位

                #region Station 岗位

                /// <summary>
                /// 岗位查询
                /// </summary>
                public const string Station_Query = "Station:Query";

                /// <summary>
                /// 岗位管理
                /// 包含新增、编辑、删除、人岗维护等全部管理功能
                /// </summary>
                public const string Station_Manage = "Station:Manage";

                /// <summary>
                ///岗位管理:新增【暂不使用】
                /// </summary>
                public const string Station_Manage_Add = "Station:Manage:Add";

                /// <summary>
                /// 岗位管理:编辑【暂不使用】
                /// </summary>
                public const string Station_Manage_Edit = "Station:Manage:Edit";

                /// <summary>
                /// 岗位管理:删除【暂不使用】
                /// </summary>
                public const string Station_Manage_Delete = "Station:Manage:Delete";

                /// <summary>
                /// 岗位管理:人岗维护【暂不使用】
                /// </summary>
                public const string Station_Manage_Employee = "Station:Manage:Employee";

                /// <summary>
                /// 岗位管理:人岗维护【暂不使用】
                /// </summary>
                public const string Station_Manage_Valid = "Station:Manage:Valid";

                #endregion Station 岗位
            }

            /// <summary>
            /// 业务主数据
            /// </summary>
            public static class Business
            {
                #region BizDict 业务字典

                /// <summary>
                /// 业务字典查询
                /// </summary>
                public const string BizDict_Query = "BizDict:Query";

                /// <summary>
                /// 业务字典管理
                /// </summary>
                public const string BizDict_Manage = "BizDict:Manage";

                /// <summary>
                /// 业务字典管理:新增【暂不使用】
                /// </summary>
                public const string BizDict_Manage_Add = "BizDict:Manage:Add";

                /// <summary>
                /// 业务字典管理:编辑【暂不使用】
                /// </summary>
                public const string BizDict_Manage_Edit = "BizDict:Manage:Edit";

                /// <summary>
                /// 业务字典管理:删除【暂不使用】
                /// </summary>
                public const string BizDict_Manage_Delete = "BizDict:Manage:Delete";

                #endregion BizDict 业务字典

                #region Shipper 货主

                /// <summary>
                /// 货主查询
                /// </summary>
                public const string Shipper_Query = "Shipper:Query";
 
                /// <summary>
                /// 货主管理:新增
                /// </summary>
                public const string Shipper_Manage_Add = "Shipper:Manage:Add";

                /// <summary>
                /// 货主管理:编辑
                /// </summary>
                public const string Shipper_Manage_Edit = "Shipper:Manage:Edit";

                /// <summary>
                /// 货主管理:删除
                /// </summary>
                public const string Shipper_Manage_Delete = "Shipper:Manage:Delete";

                /// <summary>
                /// 货主管理:导出
                /// </summary>
                public const string Shipper_Manage_Export = "Shipper:Manage:Export";

                #endregion Shipper 货主
            }
        }

        /// <summary>
        /// 报表
        /// </summary>
        public static class Report
        {
            /// <summary>
            /// 桌面端
            /// </summary>
            public static class PC
            {
                /// <summary>
                /// 【桌面端】报表查看【AuthTagType: App】
                /// </summary>
                public const string View = "[PC]Report:View";
            }

            /// <summary>
            /// 移动端【暂不使用】
            /// </summary>
            public static class Mobile
            {
                /// <summary>
                /// 【移动端】报表查看【AuthTagType: App】
                /// </summary>
                public const string View = "[Mobile]Report:View";
            }
        }

        /// <summary>
        /// 业务
        /// </summary>
        public static class Business
        {
        }
    }
}