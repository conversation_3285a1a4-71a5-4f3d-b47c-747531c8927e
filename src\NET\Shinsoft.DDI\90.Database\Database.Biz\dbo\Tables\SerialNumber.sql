﻿CREATE TABLE [dbo].[SerialNumber]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_SerialNumber_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [Prefix]                    NVARCHAR(10)                NOT NULL,
    [DateFormat]                NVARCHAR(20)                NOT NULL,
    [SeedLength]                INT                         NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_SerialNumber] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_SerialNumber_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
)
GO

CREATE UNIQUE INDEX [IX_SerialNumber] ON [dbo].[SerialNumber]
(
	[CompanyId] ASC,
	[Prefix] ASC
	--[DateFormat] ASC,
	--[SeedLength] ASC
);
GO