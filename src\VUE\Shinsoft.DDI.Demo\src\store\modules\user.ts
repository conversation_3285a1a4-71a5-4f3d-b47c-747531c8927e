/**
 * 框架改造
 * 重写了整个用户信息的存储、读写逻辑
 * todo: refreshToken 和 autoLogin
 */
import { defineStore } from "pinia";
import { store, router, resetRouter, routerArrays } from "../utils";
import { useMultiTagsStoreHook } from "./multiTags";
import {
  setAuthToken,
  removeAuthToken,
  getAuthToken,
  isAuth,
  isAnyAuth,
  isAllAuth
} from "@/utils/auth";
import type { userInfo } from "../types/user.d";
import { sysApi } from "@/api/sys";
import { getAsyncI18n, setCulture } from "@/plugins/i18n";
import { initAsyncRoutes } from "@/router/utils";

const { VITE_PROGRAM } = import.meta.env;

// const init: userInfo = null;

export const useUserStore = defineStore({
  id: "pure-user",
  state: () => ({
    info: null
  }),
  getters: {
    currentUser: state => state.info,
    isAuthenticated: state => !!state.info,
    avatar: state => state.info?.avatar || "",
    displayName: state => state.info?.displayName || "",
    isAgent: state => state.info?.isAgent || false,
    currentCompanyId: state => state.info?.currentCompanyId || "",
    currentCompanyName: state => state.info?.currentCompanyName || "",
    currentEmployeeId: state => state.info?.employeeId || "",
    myCompanies: state => state.info?.myCompanies || {},
    identities: state => state.info?.myCompanies[state.info.currentCompanyId].identities || [],
    delegates: state => state.info?.myCompanies[state.info.currentCompanyId].delegates || []
  },
  actions: {
    /** 存储用户信息 */
    SET_USER_INFO(userInfo: userInfo | Record<string, any>) {
      this.info = userInfo;
    },
    REMOVE_USER_INFO() {
      this.info = null;
    },
    hasAuth(
      auth:
        | string // auth = "权限"
        | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
    ): boolean {
      return isAuth(this.info?.auths, auth);
    },
    hasAnyAuth(
      auths:
        | string // auths = "权限"
        | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
        | Array<
            // auths = [auth]
            | string // auth = "权限"
            | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
          >
    ): boolean {
      return isAnyAuth(this.info?.auths, auths);
    },
    hasAllAuth(
      auths:
        | string // auths = "权限"
        | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
        | Array<
            // auths = [auth]
            | string // auth = "权限"
            | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
          >
    ): boolean {
      return isAllAuth(this.info?.auths, auths);
    },
    /** 登入 */
    async login(data) {
      return new Promise<string>((resolve, reject) => {
        data.program = VITE_PROGRAM;

        sysApi
          .UserLogin(data)
          .then(res => {
            if (!res.success) {
              const message = res.messages.toString().replace(/,/g, "<br/>");
              reject({ message });
            } else {
              const { data } = res;
              const { authToken, redirectUri } = data;
              delete data.authToken;
              delete data.redirectUri;
              setAuthToken(authToken);
              this.SET_USER_INFO(data);
              getAsyncI18n(data.culture).finally(() => {
                setCulture(data.culture);
                resolve(redirectUri);
              });
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    /** 前端登出（不调用接口） */
    logout() {
      this.SET_USER_INFO(null);
      removeAuthToken();
      useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
      resetRouter();
      router.push("/login");
    },
    async getInfo() {
      return new Promise<userInfo>(resolve => {
        if (this.info) {
          resolve(this.info);
        } else {
          let authToken = getAuthToken();
          if (authToken) {
            sysApi
              .GetCurrentUser()
              .then(res => {
                if (res.success) {
                  const { data } = res;
                  this.SET_USER_INFO(data);

                  getAsyncI18n(data.culture).finally(() => {
                    setCulture(data.culture);
                    resolve(data);
                  });
                } else {
                  resolve(null);
                }
              })
              .catch(error => {
                if (error.response?.status === 401) {
                  removeAuthToken();
                }
                resolve(null);
              });
          } else {
            resolve(null);
          }
        }
      });
    },
    async switchMyIdentity(newIdentityId) {
      return new Promise<userInfo>(resolve => {
        const data = { newIdentityId };

        sysApi
          .SwitchMyIdentity(data)
          .then(res => {
            if (res.success) {
              const { data } = res;
              const { authToken } = data;
              delete data.authToken;

              if (useUserStore().currentCompanyId != data.currentCompanyId) {
                initAsyncRoutes();
              }

              setAuthToken(authToken);
              this.SET_USER_INFO(data);
              resolve(data);
            } else {
              resolve(null);
            }
          })
          .catch(error => {
            if (error.response?.status === 401) {
              removeAuthToken();
            }
            resolve(null);
          });
      });
    },
    async switchToAgent(newIdentityId) {
      return new Promise<userInfo>(resolve => {
        const data = { newIdentityId };

        sysApi
          .SwitchToAgent(data)
          .then(res => {
            if (res.success) {
              const { data } = res;
              const { authToken } = data;
              delete data.authToken;
              setAuthToken(authToken);
              this.SET_USER_INFO(data);
              resolve(data);
            } else {
              resolve(null);
            }
          })
          .catch(error => {
            if (error.response?.status === 401) {
              removeAuthToken();
            }
            resolve(null);
          });
      });
    },
    async switchCulture(culture: string) {
      const switchI18n = async () => {
        return new Promise<void>(resolve => {
          sysApi
            .SwitchCulture(culture)
            .then(res => {
              if (res.success) {
                const { data } = res;
                const { authToken } = data;
                delete data.authToken;
                setAuthToken(authToken);
                this.SET_USER_INFO(data);

                getAsyncI18n(data.culture).finally(() => {
                  resolve(data);
                });
              } else {
                resolve(null);
              }
            })
            .catch(error => {
              if (error.response?.status === 401) {
                removeAuthToken();
              }
              resolve(null);
            });
        });
      };

      const all = [switchI18n(), getAsyncI18n(culture)];

      return new Promise<void>(resolve => {
        Promise.all(all).then(() => {
          resolve();
        });
      });

      // return new Promise<userInfo>(resolve => {
      //   sysApi
      //     .SwitchCulture(culture)
      //     .then(res => {
      //       if (res.success) {
      //         const { data } = res;
      //         const { authToken } = data;
      //         delete data.authToken;
      //         setAuthToken(authToken);
      //         this.SET_USER_INFO(data);

      //         getAsyncI18n(data.culture).finally(() => {
      //           resolve(data);
      //         });
      //       } else {
      //         resolve(null);
      //       }
      //     })
      //     .catch(error => {
      //       if (error.response?.status === 401) {
      //         removeAuthToken();
      //       }
      //       resolve(null);
      //     });
      // });
    }

    /** 刷新`token` */
    // async handRefreshToken(data) {
    //   return new Promise<RefreshTokenResult>((resolve, reject) => {
    //     refreshTokenApi(data)
    //       .then(data => {
    //         if (data) {
    //           setToken(data.data);
    //           resolve(data);
    //         }
    //       })
    //       .catch(error => {
    //         reject(error);
    //       });
    //   });
    // }
  }
});

export function useUserStoreHook() {
  return useUserStore(store);
}
