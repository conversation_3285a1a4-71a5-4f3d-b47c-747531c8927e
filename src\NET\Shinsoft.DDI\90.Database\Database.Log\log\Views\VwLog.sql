﻿
CREATE VIEW [log].[VwLog]
AS
	SELECT 
		l.[ID],
		l.[CompanyId],
		l.[CompanyCode],
		l.[CompanyName],
		l.[Category],
		l.[Level],
		l.[LogTime],
		l.[Logger],
		p.[Platform],
		pr.[Program],
		o.[Operate],
		j.[Job],
		l.[Message],
		l.[Duration],
		l.[Remark],
        l.Culture,
		l.[UserId],
		l.[UserUniqueName],
		l.[UserDisplayName],
		l.[EmployeeId],
		l.[EmployeeName],
		l.[AgentId],
		l.[AgentName],
		ltt.[TargetType],
		lt.[TargetName],
		lt.[TargetId],
		lw.[Controller],
		lw.[Action],
		lw.[Method],
		lw.[Headers],
		lw.[Url],
		lw.[IsAuthenticated],
		lw.[QueryString],
		lw.[UserAgent],
		lw.[Identity],
		lw.[Host],
		lw.[IP],
		la.[ApiType],
		la.[Success],
		la.[Input],
		la.[OutHeaders],
		la.[Output],
		ll.[Site] AS [InterfaceSite],
		ll.[Name] AS [InterfaceName],
		ll.[Address] AS [InterfaceAddress],
		ll.[Method] AS [InterfaceMethod],
		ll.[Header] AS [InterfaceHeader],
		ll.[Request] AS [InterfaceRequest],
		ll.[Response] AS [InterfaceResponse],
		le.[Exception],
		le.[StackTrace]
	FROM [log].[Log] AS l 
	LEFT JOIN [log].[LogPlatform] AS p ON p.[ID] = l.[PlatformId]
	LEFT JOIN [log].[LogProgram] AS pr ON pr.[ID] = l.[ProgramId]
	LEFT JOIN [log].[LogOperate] AS o ON o.[ID] = l.[OperateId]
	LEFT JOIN [log].[LogTarget] AS lt ON lt.[ID] = l.[ID]
	LEFT JOIN [log].[LogTargetType] AS ltt ON ltt.[ID] = lt.[LogTargetTypeId]
	LEFT JOIN [log].[LogJob] AS j ON j.[ID] = l.[JobId]
	LEFT JOIN [log].[LogWeb] AS lw ON lw.[ID] = l.[ID]
	LEFT JOIN [log].[LogApi] AS la ON la.[ID] = l.[ID]
	LEFT JOIN [log].[LogInterface] AS ll ON ll.[ID] = l.[ID]
	LEFT JOIN [log].[LogException] AS le ON le.[ID] = l.[ID]
;
