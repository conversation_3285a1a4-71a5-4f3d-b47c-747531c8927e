﻿CREATE TABLE [log].[LogInterface] (
    [ID]                        UNIQUEIDENTIFIER        NOT NULL,
    [CompanyId]                 NVARCHAR (50)           NULL,
    [Site]                      NVARCHAR (200)          NULL,
    [Name]                      NVARCHAR (200)          NULL,
    [Address]                   NVARCHAR (500)          NULL,
    [Method]                    NVARCHAR (50)           NULL,
    [Header]                    NVARCHAR (2000)         NULL,
    [Request]                   NVARCHAR (MAX)          NULL,
    [Response]                  NVARCHAR (MAX)          NULL,
    CONSTRAINT [PK_LogInterface] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_LogInterface_Log] FOREIGN KEY ([ID]) REFERENCES [log].[Log] ([ID])
);
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'接口日志',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogInterface';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'接口地址',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogInterface',
    @level2type = N'COLUMN',
    @level2name = N'Address';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'接口方法',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogInterface',
    @level2type = N'COLUMN',
    @level2name = N'Method';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'接口输入',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogInterface',
    @level2type = N'COLUMN',
    @level2name = N'Request';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'接口输出',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogInterface',
    @level2type = N'COLUMN',
    @level2name = N'Response';

