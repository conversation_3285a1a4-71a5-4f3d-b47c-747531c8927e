﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class CompanyFilter
    {
        /// <summary>
        /// 关键词（编码,名称）
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(Company), Company.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Company), Company.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }

        /// <summary>
        /// 编码
        /// </summary>
        [Description("编码")]
        [DynamicQueryColumn(typeof(Company), Company.Columns.Code, Operation = Operation.StringIntelligence)]
        public string? Code { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        [DynamicQueryColumn(typeof(Company), Company.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Name { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>
        [DynamicQueryColumn(typeof(Company), Company.Columns.Valid, Operation = Operation.Equal)]
        public bool? Valid { get; set; }

        [DynamicQueryColumn(typeof(Company), Company.Columns.Valid, Operation = Operation.In)]
        public List<bool>? Valids { get; set; }
    }
}
