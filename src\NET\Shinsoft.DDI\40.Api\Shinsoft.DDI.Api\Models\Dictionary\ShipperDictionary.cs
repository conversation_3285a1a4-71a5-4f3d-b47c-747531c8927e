﻿using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public class ShipperDictionary
    {
        public static Dictionary<string, string> ExportShipperColumns
        {
            get
            {
                return new Dictionary<string, string>
                                                        {
                                                            {"货主编码", "Code"},
                                                            {"货主名称", "Name"},
                                                            {"货主简称", "ShortName"},
                                                            {"地址", "Address"},
                                                            {"电话", "Telephone"},
                                                            {"邮件", "EMail"},
                                                            {"联系人", "ContactPerson"},
                                                            {"状态", "EnumStatusDesc"},
                                                        };
            }
        }
    }
}
