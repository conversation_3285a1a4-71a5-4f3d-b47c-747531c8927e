﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码是从模板生成的.
//
//     手动更改此文件可能会导致应用程序出现意外行为。
//     如果重新生成代码，将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.ComponentModel;
using System.Text.Json.Serialization;
using Shinsoft.Core;
using Shinsoft.Core.AutoMapper;
using Shinsoft.Core.DynamicQuery;
using Shinsoft.Core.NLog;
using Shinsoft.DDI.Entities;

#pragma warning disable CS8669

namespace Shinsoft.DDI.Api.Models
{

	#region FileContent

    /// <summary>
    /// 文件内容
    /// </summary>
	[Description("文件内容")]
    [MapFromType(typeof(FileContent), Reverse = true)]
	public abstract class FileContentRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(FileContent);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 文件内容
        /// </summary>
		[Description("文件内容")]
        public virtual byte[] Content { get; set; }

    }

    /// <summary>
    /// 文件内容
    /// </summary>
	[Description("文件内容")]
	public abstract partial class FileContentMeta : FileContentRaw
	{
	}

    /// <summary>
    /// 文件内容
    /// </summary>
	[Description("文件内容")]
	public partial class FileContentModel : FileContentMeta
	{
	}

    /// <summary>
    /// 文件内容
    /// </summary>
	[Description("文件内容")]
	public partial class FileContentQuery : FileContentMeta
	{
	}

    /// <summary>
    /// 文件内容
    /// </summary>
	[Description("文件内容")]
	public partial class FileContentSelector : FileContentMeta
	{
	}

    /// <summary>
    /// 文件内容查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(FileContent))]
	public partial class FileContentFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 文件内容选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(FileContent))]
	public partial class FileContentSelectorFilter : PagingFilterModel
	{
	}

	#endregion FileContent


	#region FileIndex

    /// <summary>
    /// 文件检索
    /// </summary>
	[Description("文件检索")]
    [MapFromType(typeof(FileIndex), Reverse = true)]
	public abstract class FileIndexRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(FileIndex);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 文件大小
        /// </summary>
		[Description("文件大小")]
        public virtual long FileSize { get; set; }

        public virtual string MD5 { get; set; } = string.Empty;

        public virtual string SHA1 { get; set; } = string.Empty;

		/// <summary>
        /// 默认文件类型
        /// </summary>
		[Description("默认文件类型")]
        public virtual string ContentType { get; set; } = string.Empty;

		/// <summary>
        /// 默认文件名
        /// </summary>
		[Description("默认文件名")]
        public virtual string FileName { get; set; } = string.Empty;

		/// <summary>
        /// 默认文件扩展名
        /// </summary>
		[Description("默认文件扩展名")]
        public virtual string FileExt { get; set; } = string.Empty;

		/// <summary>
        /// 基本文件夹
        /// </summary>
		[Description("基本文件夹")]
        public virtual string BaseFolder { get; set; } = string.Empty;

		/// <summary>
        /// 相对子路径
        /// </summary>
		[Description("相对子路径")]
        public virtual string SubPath { get; set; } = string.Empty;

    }

    /// <summary>
    /// 文件检索
    /// </summary>
	[Description("文件检索")]
	public abstract partial class FileIndexMeta : FileIndexRaw
	{
	}

    /// <summary>
    /// 文件检索
    /// </summary>
	[Description("文件检索")]
	public partial class FileIndexModel : FileIndexMeta
	{
	}

    /// <summary>
    /// 文件检索
    /// </summary>
	[Description("文件检索")]
	public partial class FileIndexQuery : FileIndexMeta
	{
	}

    /// <summary>
    /// 文件检索
    /// </summary>
	[Description("文件检索")]
	public partial class FileIndexSelector : FileIndexMeta
	{
	}

    /// <summary>
    /// 文件检索查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(FileIndex))]
	public partial class FileIndexFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 文件检索选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(FileIndex))]
	public partial class FileIndexSelectorFilter : PagingFilterModel
	{
	}

	#endregion FileIndex

}

#pragma warning restore CS8669
