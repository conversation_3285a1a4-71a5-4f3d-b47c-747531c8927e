<script setup lang="ts">
import { useUserStoreHook } from "@/store/modules/user";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { bizMasterDataApi } from "@/api/bizMasterData";
import { getColumnOrder, getDefaultOrder } from "@/utils/table";
import Edit from "./dialogs/edit.vue";
defineOptions({
  name: "bizDict:query"
});

/**
 * 基本配置定义
 */
const userStore = useUserStoreHook();
const cfg = reactive({
  filter: {
    gutter: 3,
    span: 4
  },
  list: {
    height: 500,
    defaultSort: { prop: "name", order: "ascending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.edit ? 45 : 0))
    }
  },
  btn: {
    edit: computed(() => {
      return userStore.hasAnyAuth(["BizDict:Manage", "BizDict:Manage:Edit"]);
    })
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  }
});

/**
 * 当前组件ref
 */
const editRef = ref();
const filterRef = ref();
const listRef = ref();
const listContainerRef = ref();

/**
 * 初始化组件(created时调用)
 */
const init = () => {
  initFilter();
  query();
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  //cfg.loading.filter = true;
};
/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  bizMasterDataApi
    .QueryBizDict(filter)
    .then(res => {
      state.datas = res.datas;
      state.total = res.total;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  keywords: ""
});

/**
 * 编辑：点击列表中【编辑】按钮事件
 */
const editRow = (row: any) => {
  editRef.value?.open(row.id, row.name);
};

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

/**
 * 序号
 */
const indexMethod = index => {
  return (filter.pageIndex - 1) * filter.pageSize + index + 1;
};

/**
 * 执行init,created时调用
 */
init();
</script>

<template>
  <div>
    <div class="filter-container">
      <el-form
        ref="filterRef"
        v-loading="cfg.loading.filter"
        class="filter-form"
        :rules="rules.filter"
        :model="filter"
      >
        <el-row :gutter="cfg.filter.gutter" type="flex">
          <el-col :span="cfg.filter.span">
            <el-input
              v-model="filter.keywords"
              clearable
              placeholder="关键字(编码、名称)"
              class="filter-item"
              @keyup.enter="query"
            />
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-button
              class="query"
              :loading="cfg.loading.list"
              :icon="useRenderIcon('ep:search')"
              @click="query"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div ref="listContainerRef" class="list-container">
      <el-table
        ref="listRef"
        v-loading="cfg.loading.list"
        :data="state.datas"
        row-key="id"
        stripe
        border
        class-name="list"
        style="width: 100%"
        :height="cfg.list.height"
        :default-sort="cfg.list.defaultSort"
        @sort-change="sortChange"
      >
        <template #empty>
          <NoDatas />
        </template>
        <el-table-column
          fixed
          label="序号"
          :index="indexMethod"
          type="index"
          width="70"
          align="center"
        />
        <el-table-column fixed sortable="custom" prop="name" label="名称" width="710" />
        <el-table-column fixed sortable="custom" prop="remark" label="备注" width="710" />
        <el-table-column fixed="right" label="操作" class-name="operate" align="center">
          <template #default="{ row }">
            <el-button
              v-if="cfg.btn.edit"
              class="edit"
              size="small"
              :circle="true"
              title="维护"
              :icon="useRenderIcon('ep:edit')"
              @click="editRow(row)"
            />
          </template>
        </el-table-column>
      </el-table>
      <Pagination v-model:filter="filter" :total="state.total" @change="getList" />
    </div>
    <Edit ref="editRef" @refresh="getList" />
  </div>
</template>
