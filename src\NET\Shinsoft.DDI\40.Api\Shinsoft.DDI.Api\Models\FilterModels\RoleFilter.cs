﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class RoleFilter
    {
        /// <summary>
        /// 关键字
        /// </summary>
        [Description("关键字")]
        [DynamicQueryColumn(typeof(Role), Role.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Role), Role.Columns.Name, Operation = Operation.StringIntelligence)]
        public virtual string? Keywords { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Description("用户ID")]
        public virtual Guid? UserId { get; set; }

        /// <summary>
        /// 员工ID
        /// </summary>
        [Description("员工ID")]
        public virtual Guid? EmployeeId { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        [Description("标签")]
        [DynamicQueryColumn(typeof(Role), Role.Columns.EnumFlags, Operation = Operation.BitAny)]
        public virtual RoleFlag Flags { get; set; }
    }
}
