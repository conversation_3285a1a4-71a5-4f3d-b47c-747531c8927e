<script setup lang="ts">
defineOptions({
  name: "Pagination"
});

const emit = defineEmits(["change"]);

const props = defineProps({
  total: {
    required: true,
    type: Number
  },
  pageSizes: {
    type: Array<number>,
    default: [5, 10, 20, 50, 100, 500, 1000]
  },
  layout: {
    type: String,
    default: "->, total, sizes, prev, pager, next, jumper"
  },
  background: {
    type: <PERSON><PERSON><PERSON>,
    default: true
  },
  disabled: {
    type: <PERSON><PERSON><PERSON>,
    defalut: false
  },
  hidden: {
    type: <PERSON><PERSON>an,
    default: false
  },
  autoHide: {
    type: <PERSON><PERSON><PERSON>,
    default: true
  },
  autoScroll: {
    type: <PERSON><PERSON><PERSON>,
    default: true
  }
});

const cfg = reactive({
  visible: computed(() => {
    return !props.hidden && filter.value.pageSize > 0 && (!props.autoHide || props.total > 0);
  }),
  default: {
    filter: {
      pageIndex: 1,
      pageSize: 20
    }
  }
});

const filter = defineModel<PagingFilter>("filter");

const pageIndex = defineModel<number>("pageIndex");

const pageSize = defineModel<number>("pageSize");

const init = () => {
  if (filter.value == null) {
    filter.value = { ...cfg.default.filter };

    if (pageIndex.value == null) {
      pageIndex.value = filter.value.pageIndex;
    } else {
      filter.value.pageIndex = pageIndex.value;
    }

    if (pageSize.value == null) {
      pageSize.value = filter.value.pageSize;
    } else {
      filter.value.pageSize = pageSize.value;
    }
  } else {
    if (filter.value.pageIndex == null) {
      if (pageIndex.value == null) {
        filter.value.pageIndex = cfg.default.filter.pageIndex;
        pageIndex.value = cfg.default.filter.pageIndex;
      } else {
        filter.value.pageIndex = pageIndex.value;
      }
    } else {
      pageIndex.value = filter.value.pageIndex;
    }

    if (filter.value.pageSize == null) {
      if (pageSize.value == null) {
        filter.value.pageSize = cfg.default.filter.pageSize;
        pageSize.value = cfg.default.filter.pageSize;
      } else {
        filter.value.pageSize = pageSize.value;
      }
    } else {
      pageSize.value = filter.value.pageSize;
    }
  }
};

const handleSizeChange = (val: number) => {
  // currentPage.value = 1;
  pageIndex.value = 1;
  filter.value.pageIndex = 1;
  filter.value.pageSize = val;
  emit("change");
};

const handleCurrentChange = (val: number) => {
  // currentPage.value = val;
  pageIndex.value = val;
  filter.value.pageIndex = val;

  Reflect.set(filter.value, "pageIndex", val);
  emit("change");
};

init();
</script>

<template>
  <div v-show="cfg.visible" :class="{ hidden: hidden }" class="pagination-container">
    <el-pagination
      v-model:current-page="filter.pageIndex"
      v-model:page-size="filter.pageSize"
      :page-sizes="pageSizes"
      size="small"
      :disabled="disabled"
      :background="background"
      :layout="layout"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
