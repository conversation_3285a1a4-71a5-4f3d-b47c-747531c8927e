﻿CREATE TABLE [mail].[SmtpServer] (
    [ID]						UNIQUEIDENTIFIER			NOT NULL,
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [Host]						NVARCHAR(50)			    NOT NULL,
    [Port]						INT					        NOT NULL,
    [EnumSmtpSecure]			INT					        NOT NULL,
    [Username]					NVARCHAR(50)			    NULL,
    [Password]					NVARCHAR(200)			    NULL,
    [PwdEncrypted]              BIT                         NOT NULL,
    CONSTRAINT [PK_SmtpServer] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_SmtpServer_MailServer] FOREIGN KEY ([ID]) REFERENCES [mail].[MailServer] ([ID])
) 

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'''SMTP服务器',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'SmtpServer',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'地址',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'SmtpServer',
    @level2type = N'COLUMN',
    @level2name = N'Host'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'端口',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'SmtpServer',
    @level2type = N'COLUMN',
    @level2name = N'Port'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'1:Ssl; 10:Tls; 11:Tls1; 12:Tls2; 13:Tls3',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'SmtpServer',
    @level2type = N'COLUMN',
    @level2name = N'EnumSmtpSecure'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'用户名',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'SmtpServer',
    @level2type = N'COLUMN',
    @level2name = N'Username'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'密码',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'SmtpServer',
    @level2type = N'COLUMN',
    @level2name = N'Password'