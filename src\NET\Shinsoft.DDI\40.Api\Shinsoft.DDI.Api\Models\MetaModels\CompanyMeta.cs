﻿namespace Shinsoft.DDI.Api.Models
{
    [MapFromType(typeof(CompanyCfg), Reverse = true)]
    public partial class CompanyMeta
    {
        [MapFromProperty(typeof(Company), Company.Inverses.Cfg, CompanyCfg.Columns.Culture)]
        [MapFromProperty(typeof(CompanyCfg), CompanyCfg.Columns.Culture)]
        public virtual string? Culture { get; set; }

        [MapFromProperty(typeof(Company), Company.Inverses.Cfg, CompanyCfg.Columns.Redis)]
        [MapFromProperty(typeof(CompanyCfg), CompanyCfg.Columns.Redis)]
        public virtual string? Redis { get; set; }

        [MapFromProperty(typeof(Company), Company.Inverses.Cfg, CompanyCfg.Columns.MailSendType)]
        [MapFromProperty(typeof(CompanyCfg), CompanyCfg.Columns.MailSendType)]
        public virtual string? MailSendType { get; set; }

        [MapFromProperty(typeof(Company), Company.Inverses.Cfg, CompanyCfg.Columns.EnumLoginNameType)]
        [MapFromProperty(typeof(CompanyCfg), CompanyCfg.Columns.EnumLoginNameType)]
        public virtual LoginNameType EnumLoginNameType { get; set; }

        public virtual string EnumLoginNameTypeDesc => this.EnumLoginNameType.GetDesc();
    }
}
