﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    [ApiExplorerSettings(GroupName = "缓存")]
    public class SysCacheController : BaseApiController<SysBll>
    {
        [HttpGet]
        [LogApi(ApiType.Cache, Operate = "获取系统缓存所有键")]
        [Auth(AuthCodes.Platform.Setup.SysCache_Operate)]
        public List<string> GetAllKeys()
        {
            return this.SysCache.GetAllKeys();
        }

        [HttpGet]
        [LogApi(ApiType.Cache, Operate = "销毁系统缓存")]
        [Auth(AuthCodes.Platform.Setup.SysCache_Operate)]
        public void FlushCache([Required] string key)
        {
            this.SysCache.RemoveKey(key);
        }

        [HttpGet]
        [LogApi(ApiType.Cache, Operate = "获取在线用户")]
        [Auth(AuthCodes.Platform.Setup.SysCache_Operate)]
        public List<IdentityUser> GetOnlineUsers()
        {
            var users = this.UserProvider.OnlineUsers;

            var models = users.Maps<IdentityUser>();

            return models;
        }

        /// <summary>
        /// 收货方类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public List<SysCache.CascaderModel>? QueryReceiverTypeCascader()
        {
            return SysCache.ReceiverTypeCascader;
        }

        /// <summary>
        /// 省份城市下拉框
        /// </summary>
        [HttpGet]
        public List<SysCache.CascaderModel>? QueryProvinceCityCascader()
        {
            return SysCache.ProvinceCityCascader;
        }

        /// <summary>
        /// 省市区县下拉框
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public List<SysCache.CascaderModel>? QueryProvinceCityCountyCascader()
        {
            return SysCache.ProvinceCityCountyCascader;
        }
    }
}
