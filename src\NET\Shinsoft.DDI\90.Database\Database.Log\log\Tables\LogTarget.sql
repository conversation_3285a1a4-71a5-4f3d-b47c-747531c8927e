﻿CREATE TABLE [log].[LogTarget] (
    [ID]                        UNIQUEIDENTIFIER        NOT NULL,
    [CompanyId]                 NVARCHAR (50)           NULL,
    [LogTargetTypeId]           INT                     NULL,
    [TargetName]                NVARCHAR (200)          NULL,
    [TargetId]                  NVARCHAR (50)           NULL,
    CONSTRAINT [PK_LogBiz] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_LogBiz_Log] FOREIGN KEY ([ID]) REFERENCES [log].[Log] ([ID]),
    CONSTRAINT [FK_LogBiz_LogTargetType] FOREIGN KEY ([LogTargetTypeId]) REFERENCES [log].[LogTargetType] ([ID])
);
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'业务日志',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogTarget';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'业务类型ID',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogTarget',
    @level2type = N'COLUMN',
    @level2name = N'LogTargetTypeId';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'目标',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogTarget',
    @level2type = N'COLUMN',
    @level2name = N'TargetName';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'目标ID',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogTarget',
    @level2type = N'COLUMN',
    @level2name = N'TargetId';

