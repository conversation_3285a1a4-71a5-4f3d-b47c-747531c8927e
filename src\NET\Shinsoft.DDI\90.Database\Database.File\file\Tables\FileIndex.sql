﻿CREATE TABLE [file].[FileIndex] (
    [ID]                        UNIQUEIDENTIFIER        NOT NULL    CONSTRAINT [DF_FileIndex_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER        NOT NULL,
    [FileSize]                  BIGINT                  NOT NULL,
    [MD5]                       NVARCHAR (50)           NOT NULL,
    [SHA1]                      NVARCHAR (50)           NOT NULL,
    [ContentType]               NVARCHAR (200)          NOT NULL,
    [FileName]                  NVARCHAR (500)          NOT NULL,
    [FileExt]                   NVARCHAR (50)           NOT NULL,
	[BaseFolder]				NVARCHAR(500)		    NOT NULL,
    [SubPath]                   NVARCHAR (500)          NOT NULL,
    [Creator]				    NVARCHAR(50)		    NULL, 
    [CreateTime]			    DATETIME			    NULL, 
    [LastEditor]			    NVARCHAR(50)		    NULL, 
    [LastEditTime]			    DATETIME			    NULL, 
    CONSTRAINT [PK_FileIndex] PRIMARY KEY CLUSTERED ([ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_File]
    ON [file].[FileIndex]([CompanyId] ASC, [FileSize] ASC, [MD5] ASC, [SHA1] ASC);


GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'文件检索',
    @level0type = N'SCHEMA',
    @level0name = N'file',
    @level1type = N'TABLE',
    @level1name = N'FileIndex',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'文件大小',
    @level0type = N'SCHEMA',
    @level0name = N'file',
    @level1type = N'TABLE',
    @level1name = N'FileIndex',
    @level2type = N'COLUMN',
    @level2name = N'FileSize'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'默认文件类型',
    @level0type = N'SCHEMA',
    @level0name = N'file',
    @level1type = N'TABLE',
    @level1name = N'FileIndex',
    @level2type = N'COLUMN',
    @level2name = 'ContentType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'默认文件名',
    @level0type = N'SCHEMA',
    @level0name = N'file',
    @level1type = N'TABLE',
    @level1name = N'FileIndex',
    @level2type = N'COLUMN',
    @level2name = 'FileName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'默认文件扩展名',
    @level0type = N'SCHEMA',
    @level0name = N'file',
    @level1type = N'TABLE',
    @level1name = N'FileIndex',
    @level2type = N'COLUMN',
    @level2name = 'FileExt'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'相对子路径',
    @level0type = N'SCHEMA',
    @level0name = N'file',
    @level1type = N'TABLE',
    @level1name = N'FileIndex',
    @level2type = N'COLUMN',
    @level2name = 'SubPath'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'基本文件夹',
    @level0type = N'SCHEMA',
    @level0name = N'file',
    @level1type = N'TABLE',
    @level1name = N'FileIndex',
    @level2type = N'COLUMN',
    @level2name = N'BaseFolder'