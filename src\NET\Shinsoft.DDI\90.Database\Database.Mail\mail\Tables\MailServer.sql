﻿CREATE TABLE [mail].[MailServer] (
    [ID]					    UNIQUEIDENTIFIER			NOT NULL    CONSTRAINT [DF_MailServer_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [EnumType]				    INT					        NOT NULL,
    [Code]					    NVARCHAR(50)			    NOT NULL,
    [From]                      NVARCHAR(500)			    NOT NULL,
    [DebugTo]                   NVARCHAR(500)			    NOT NULL,
    [SendInterval]			    INT					        NOT NULL,
    [RetryInterval]			    INT					        NOT NULL,
    [MaxRetry]				    INT					        NOT NULL,
	[MaxResend]				    INT					        NOT NULL,
    [Valid]					    BIT					        NOT NULL,
    [Description]			    NVARCHAR(500)			    NULL,
    [Deleted]				    BIT					        NOT NULL, 
    [Creator]				    NVARCHAR(50)			    NULL, 
    [CreateTime]				DATETIME				    NULL, 
    [LastEditor]				NVARCHAR(50)			    NULL, 
    [LastEditTime]			    DATETIME				    NULL, 
    CONSTRAINT [PK_MailServer] PRIMARY KEY CLUSTERED ([ID] ASC),
);

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'邮件服务器',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'MailServer',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'邮件服务器类型',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'MailServer',
    @level2type = N'COLUMN',
    @level2name = N'EnumType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'邮件服务器编码',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'MailServer',
    @level2type = N'COLUMN',
    @level2name = N'Code'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'邮件发件人地址',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'MailServer',
    @level2type = N'COLUMN',
    @level2name = N'From'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'Debug收件人地址',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'MailServer',
    @level2type = N'COLUMN',
    @level2name = N'DebugTo'