CREATE TABLE [dbo].[VueRoute] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_VueRoute_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [ParentId]                  UNIQUEIDENTIFIER            NULL,
    [Name]                      NVARCHAR(50)		        NOT NULL,
    [Path]                      NVARCHAR(200)		        NOT NULL,
    [Redirect]                  NVARCHAR(200)		        NOT NULL,
    [Valid]                     BIT		                    NOT NULL,
    [IsSys]                     BIT		                    NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_VueRoute] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_VueRoute_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_VueRoute_VueRoute_00_Parent_Children] FOREIGN KEY ([ParentId]) REFERENCES [dbo].[VueRoute] ([ID]),
)
