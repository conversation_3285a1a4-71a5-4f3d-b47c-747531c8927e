﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Shinsoft.DDI.Entities;
using Shinsoft.Core.Mvc;

namespace Shinsoft.DDI.Bll
{
    public class SysBll : BaseBll
    {
        #region Constructs

        public SysBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        public SysBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        public SysBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        public SysBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs

        #region Login

        public User? GetUserByLoginName(string loginName, ProgramFlag program)
        {
            loginName = loginName.Trim();

            var user = this.GetEntity<User>(p => p.EnumType != UserType.Service && p.LoginName == loginName);

            if (user != null)
            {
                user = this.InitUser(program, user);
            }

            return user;
        }

        public User? GetUserByIdentityKey(ApiIdentityKey identityKey)
        {
            var id = identityKey.UserId.As<Guid>();

            var user = !id.IsEmpty() ? this.Get<User>(id) : null;

            if (user != null)
            {
                Employee? employee = null;
                if (!identityKey.EmployeeId.IsEmpty())
                {
                    var employeeId = identityKey.EmployeeId!.As<Guid>();
                    employee = !employeeId.IsEmpty() ? this.Get<Employee>(employeeId) : null;
                }

                Employee? agent = null;
                if (!identityKey.AgentId.IsEmpty())
                {
                    var agentId = identityKey.AgentId!.As<Guid>();
                    agent = !agentId.IsEmpty() ? this.Get<Employee>(agentId) : null;
                }

                user = this.InitUser(identityKey.Program, user, employee, agent);
            }

            return user;
        }

        /// <summary>
        /// 初始化用户身份
        /// </summary>
        protected User InitUser(ProgramFlag program, User user, Employee? employee = null, Employee? agent = null)
        {
            User clone = user.Clone();

            clone.Program = program;

            if (employee == null)
            {
                if (user.DefaultEmployeeId.HasValue)
                {
                    employee = this.Get<Employee>(user.DefaultEmployeeId);
                }
                else
                {
                    var identities = this.GetEntities<Employee>(p => p.UserId == user.ID && Config.AllowLoginEmployeeStatus.Contains(p.EnumStatus));

                    var query = identities.AsQueryable();

                    if (user.DefaultCompanyId.HasValue)
                    {
                        query = query
                            .OrderBy(p => p.CompanyId == user.DefaultCompanyId ? 0 : 1)
                            .ThenBy(p => p.CompanyId == Config.DefaultCompanyId ? 0 : 1);
                    }
                    else
                    {
                        query = query.OrderBy(p => p.CompanyId == Config.DefaultCompanyId ? 0 : 1);
                    }

                    employee = query.FirstOrDefault();
                }
            }

            if (employee?.LineManagerId != null)
            {
                var lm = this.Get<Employee>(employee.LineManagerId);

                clone.LineManager = lm?.Clone();
            }

            var companyId = employee?.CompanyId ?? user.DefaultCompanyId ?? Config.DefaultCompanyId;

            var cc = this.GetCompanyCache(companyId);

            clone.Employee = employee?.Clone();
            clone.Agent = agent?.Clone();
            clone.OperatorCompany = cc.Company;

            clone = this.InitUserAuths(clone);

            clone = this.InitUserDelegates(clone);

            if (clone.Culture.IsEmpty() || !cc.CompanyCultures.Any(p => p.Culture == clone.Culture))
            {
                clone.Culture = cc.Company.Cfg!.Culture;
            }

            return clone;
        }

        /// <summary>
        /// 初始化用户权限
        /// </summary>
        protected User InitUserAuths(User clone)
        {
            var companyId = clone.OperatorCompanyId ?? Config.DefaultCompanyId;
            var cc = this.GetCompanyCache(companyId);

            var program = clone.Program;

            var employee = clone.Employee;
            var agent = clone.Agent;

            var today = SysDateTime.Today;

            var exps = this.NewExps<RoleMember>(p => p.CompanyId == companyId);

            var exp = clone.IsAgent
                ? this.NewExp<RoleMember>(p => false)
                : this.NewExp<RoleMember>(p => p.EnumType == RoleMemberType.User && p.MemberId == clone.ID);

            if (employee != null)
            {
                exp = exp.Or(p => p.EnumType == RoleMemberType.Employee && p.MemberId == employee.ID);

                if (employee.MajorDepartmentId.HasValue)
                {
                    // 对于不适用岗位相关概念的情况，员工只属于一个部门，存储在Employee的MajorDepartmentId中

                    var dept = this.Get<Department>(employee.MajorDepartmentId);

                    if (dept?.Valid == true)
                    {
                        exp = exp.Or(p => p.EnumType == RoleMemberType.Department && p.MemberId == employee.MajorDepartmentId);
                    }
                }

                //岗位相关角色
                var stations = this.GetEntities<Station>(p =>
                    p.Valid // 岗位有效
                    && !p.Department.Deleted
                    && p.Department.Valid // 部门有效
                    && !p.Position.Deleted // 职位有效
                    && (!p.StartDate.HasValue || p.StartDate <= today) // 岗位时间有效
                    && (!p.EndDate.HasValue || p.EndDate >= today)
                    && p.EmployeeStation.Any(p1 =>
                        p1.EmployeeId == employee.ID // 入岗时间有效
                        && (!p1.StartDate.HasValue || p1.StartDate <= today)
                        && (!p1.EndDate.HasValue || p1.EndDate >= today)
                    )
                );

                if (stations.Count != 0)
                {
                    var departmentIds = stations.Select(p => p.DepartmentId);
                    var stationsIds = stations.Select(p => p.ID);
                    var positionIds = stations.Select(p => p.PositionId).Distinct();

                    exp = exp.Or(p => p.EnumType == RoleMemberType.Department && departmentIds.Contains(p.MemberId));
                    exp = exp.Or(p => p.EnumType == RoleMemberType.Station && stationsIds.Contains(p.MemberId));
                    exp = exp.Or(p => p.EnumType == RoleMemberType.Position && positionIds.Contains(p.MemberId));
                }

                clone.Stations = stations.Clone();
            }

            exps.Add(exp);

            var roleMembers = this.GetEntities(exps);

            var roleIds = roleMembers.Select(p => p.RoleId).Distinct();

            var isLM = false;

            if (employee != null)
            {
                var lineManager = this.GetEntity<VwStationLeader>(p =>
                    p.CompanyId == employee.CompanyId
                    && p.LineManagerId == employee.ID // 当前身份为直属上级
                    && (!p.LineManagerStationStartDate.HasValue || p.LineManagerStationStartDate <= today) // 直属上级岗位时间有效
                    && (!p.LineManagerStationEndDate.HasValue || p.LineManagerEndDate >= today)
                    && (!p.LineManagerStartDate.HasValue || p.LineManagerStartDate <= today) // 直属上级人岗时间有效
                    && (!p.LineManagerEndDate.HasValue || p.LineManagerEndDate >= today)
                );

                isLM = lineManager != null;
            }

            var roles = isLM
                ? cc.Roles
                    .Where(p => p.EnumFlags.HasFlag(RoleFlag.LoginUser) || p.EnumFlags.HasFlag(RoleFlag.LineManager) || roleIds.Contains(p.ID))
                    .ToList()
                : cc.Roles
                    .Where(p => p.EnumFlags.HasFlag(RoleFlag.LoginUser) || roleIds.Contains(p.ID))
                    .ToList();

            clone.Auths = [];

            List<EmployeeDelegateAuth>? delegateAuths = null;
            List<EmployeeDelegateAuthTag>? delegateAuthTags = null;

            if (clone.IsAgent)
            {
                var employeeDelegate = this.GetEntity<EmployeeDelegate>(p => p.Valid
                    && p.EmployeeId == employee!.ID
                    && p.AgentId == agent!.ID
                    && (!p.StartDate.HasValue || p.StartDate <= today)
                    && (!p.EndDate.HasValue || p.EndDate >= today));

                if (employeeDelegate != null)
                {
                    delegateAuths = this.GetEntities<EmployeeDelegateAuth>(p => p.EmployeeDelegateId == employeeDelegate.ID);
                    delegateAuthTags = this.GetEntities<EmployeeDelegateAuthTag>(p => p.EmployeeDelegateId == employeeDelegate.ID);
                }
            }

            foreach (var role in roles)
            {
                foreach (var roleAuth in role.Auths)
                {
                    var authCode = roleAuth.AuthCode;

                    var auth = cc.GetAuth(authCode);

                    if (auth != null && (auth.EnumProgramFlags == ProgramFlag.None || auth.EnumProgramFlags.HasFlag(program)))
                    {
                        if (employee == null && auth.EnumFlags.HasFlag(AuthFlag.EmployeeOnly))
                        {
                            //该权限只针对员工有效
                        }
                        else if (clone.IsAgent)
                        {
                            //代理授权
                            if (auth.EnumFlags.HasFlag(AuthFlag.Agentable))
                            {
                                //该权限可以被代理

                                var delegateAuth = delegateAuths?.FirstOrDefault(p => p.AuthId == auth.ID);

                                if (delegateAuth != null)
                                {
                                    //该权限被授权可代理

                                    var userAuth = clone.Auths.FirstOrDefault(p => p.AuthCode == authCode);

                                    if (userAuth == null)
                                    {
                                        userAuth = UserAuth.Create(authCode);
                                        clone.Auths.Add(userAuth);
                                    }

                                    if (auth.EnumAuthTagType == AuthTagType.None)
                                    {
                                        userAuth.AllowAllTags = null;
                                        userAuth.AuthTagCodes = null;
                                    }
                                    else if (userAuth.AllowAllTags == true)
                                    {
                                        //已有全部标签的权限，无需继续授权
                                    }
                                    else if (roleAuth.AllowAllTags == true && delegateAuth.AllowAllTags == true)
                                    {
                                        //原用户有 所有标签权限，且授权代理用户所有标签
                                        //则代理用户拥有所有标签权限
                                        userAuth.AllowAllTags = true;
                                    }
                                    else
                                    {
                                        //按标签授予代理用户

                                        userAuth.AllowAllTags = false;

                                        var authTagCodes = roleAuth.AllowAllTags == true
                                            ? cc.AuthTags.Where(p => p.EnumType == auth.EnumAuthTagType).Select(p => p.Code).ToList()
                                            : roleAuth.AuthTagCodes ?? [];

                                        if (delegateAuth.AllowAllTags != true)
                                        {
                                            // 原用户未授权所有标签

                                            var delegateAuthTagIds = delegateAuthTags?
                                                 .Where(p => p.AuthId == auth.ID)
                                                 .Select(p => p.AuthTagId)
                                                 .ToList();

                                            var delegateTagCodes = delegateAuthTagIds?.Count > 0
                                                ? cc.GetAuthTags(delegateAuthTagIds)
                                                : [];

                                            authTagCodes = authTagCodes.Where(p => delegateTagCodes.Any(p1 => p1.Code == p)).ToList();
                                        }

                                        if (userAuth.AuthTagCodes == null)
                                        {
                                            userAuth.AuthTagCodes = authTagCodes;
                                        }
                                        else
                                        {
                                            userAuth.AuthTagCodes = userAuth.AuthTagCodes.Union(authTagCodes).Distinct().ToList();
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            // 用户授权

                            //var authTagCodes = role.Auths[authCode];

                            var userAuth = clone.Auths.FirstOrDefault(p => p.AuthCode == authCode);

                            if (userAuth == null)
                            {
                                userAuth = UserAuth.Create(authCode);
                                clone.Auths.Add(userAuth);
                            }

                            if (auth.EnumAuthTagType == AuthTagType.None)
                            {
                                userAuth.AllowAllTags = null;
                                userAuth.AuthTagCodes = null;
                            }
                            else if (userAuth.AllowAllTags == true)
                            {
                                //已有全部标签的权限，无需继续授权
                            }
                            else if (roleAuth.AllowAllTags == true)
                            {
                                userAuth.AllowAllTags = true;
                            }
                            else
                            {
                                userAuth.AllowAllTags = false;

                                if (userAuth.AuthTagCodes == null)
                                {
                                    userAuth.AuthTagCodes = roleAuth.AuthTagCodes;
                                }
                                else
                                {
                                    userAuth.AuthTagCodes = userAuth.AuthTagCodes.Union(roleAuth.AuthTagCodes ?? []).Distinct().ToList();
                                }
                            }
                        }
                    }
                }
            }

            return clone;
        }

        protected User InitUserDelegates(User clone)
        {
            var sc = this.SysCache;
            var userId = clone.ID;

            var myEmployees = this.GetEntities<Employee>(p => p.UserId == userId && Config.AllowLoginEmployeeStatus.Contains(p.EnumStatus));

            if (myEmployees.Count != 0)
            {
                clone.MyIdentities = [];

                var companies = sc.GetValidCompanies();

                foreach (var company in companies)
                {
                    var employees = myEmployees.Where(p => p.CompanyId == company.ID).ToList();

                    if (employees.Count != 0)
                    {
                        clone.MyIdentities.TryAdd(company.Clone(), employees.Clone());
                    }
                }
            }

            var myEmployee = clone.IsAgent ? clone.Agent : clone.Employee;

            if (myEmployee != null)
            {
                clone.MyDelegates = [];

                var myEmployeeId = myEmployee.ID;

                var companyId = myEmployee.CompanyId;
                var today = SysDateTime.Today;

                var myEmployeeDelegates = this.GetEntities<EmployeeDelegate>(
                    EmployeeDelegate.Foreigns.Employee,
                    p =>
                        p.CompanyId == companyId
                        && p.Valid
                        && p.AgentId == myEmployeeId
                        && (!p.StartDate.HasValue || p.StartDate <= today)
                        && (!p.EndDate.HasValue || p.EndDate >= today)
                        && Config.AllowLoginEmployeeStatus.Contains(p.Employee.EnumStatus)
                );

                if (myEmployeeDelegates.Count != 0)
                {
                    var myDelegates = myEmployeeDelegates.Select(p => p.Employee).Distinct().OrderBy(p => p.DisplayName).ToList();

                    clone.MyDelegates.TryAdd(myEmployee, myDelegates.Clone());
                }
            }

            return clone;
        }

        #endregion Login

        public BizResult<User> SetUserCulture(Guid id, string culture)
        {
            var result = new BizResult<User>();

            var dbUser = this.Get<User>(id);

            if (dbUser == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, User.I18ns._Entity);
            }
            else if (!this.SysCache.SysCultures.Any(p => p.Culture == culture))
            {
                result.Error(I18ns.Message.Culture.Format.NotExist, culture);
            }
            else
            {
                dbUser.Culture = culture;
                this.Update(dbUser);
            }

            return result;
        }
    }
}
