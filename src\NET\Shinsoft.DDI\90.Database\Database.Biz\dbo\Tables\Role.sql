--角色
CREATE TABLE [dbo].[Role]
(
    [ID]                            UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_Role_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                     UNIQUEIDENTIFIER            NOT NULL,
    [EnumFlags]                     INT                         NOT NULL,
    [EnumEditFlags]                 INT                         NOT NULL,
    [Code]                          NVARCHAR(50)                NOT NULL,
    [Name]                          NVARCHAR(50)                NOT NULL,
    [Remark]                        NVARCHAR(500)               NOT NULL,
    [Deleted]                       BIT                         NOT NULL,
    [Creator]				        NVARCHAR(50)		        NULL, 
    [CreateTime]			        DATETIME			        NULL, 
    [LastEditor]			        NVARCHAR(50)		        NULL, 
    [LastEditTime]			        DATETIME			        NULL, 
    CONSTRAINT [PK_Role] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Role_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
)

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'角色',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Role',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'编辑标志',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Role',
    @level2type = N'COLUMN',
    @level2name = N'EnumEditFlags'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'编码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Role',
    @level2type = N'COLUMN',
    @level2name = N'Code'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Role',
    @level2type = N'COLUMN',
    @level2name = N'Name'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Role',
    @level2type = N'COLUMN',
    @level2name = N'Remark'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'标签',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Role',
    @level2type = N'COLUMN',
    @level2name = N'EnumFlags'
