﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Common
{
    public class XmlValidationResult
    {
        public bool IsValid { get; }
        public string ErrorMessage { get; }

        public XmlValidationResult(bool isValid, string errorMessage)
        {
            IsValid = isValid;
            ErrorMessage = errorMessage;
        }
    }
}
