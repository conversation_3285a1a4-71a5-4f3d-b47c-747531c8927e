﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public partial class CompanyCulture : IHashCache
    {
        #region IHashCache

        [NotMapped, XmlIgnore, JsonIgnore]
        public string Field => this.Culture;

        #endregion IHashCache

        [NotMapped, XmlIgnore, JsonIgnore]
        public Dictionary<string, object?>? I18nDict { get; set; }
    }
}