﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    public class SelectorController : BaseApiController<CompanyBll>
    {
        /// <summary>
        /// 获取枚举选项
        /// </summary>
        [JwtIgnore]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取枚举")]
        public BizResult<List<EnumInfo>> GetEnumInfos([FromQuery] EnumFilter filter)
        {
            return this.BizResult(filter.GetEnumInfos());
        }

        /// <summary>
        /// 获取语种
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取语种")]
        public BizResult<List<CompanyCultureSelector>> GetCultures()
        {
            var models = this.CompanyCache.CompanyCultures.Maps<CompanyCultureSelector>();

            return new BizResult<List<CompanyCultureSelector>>(models);
        }

        /// <summary>
        /// 获取字典选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取字典")]
        public BizResult<List<DictSelector>> GetDicts([FromQuery, Required] string parentCode)
        {
            var dicts = this.CompanyCache.Dicts.Where(p => p.Parent != null && p.Parent.Code == parentCode).ToList();

            return new BizResult<List<DictSelector>>(dicts.Maps<DictSelector>());
        }

        /// <summary>
        /// 获取公司币种选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取公司币种选项")]
        public BizResult<List<CompanyCurrencySelector>> GetCompanyCurrencySelectors()
        {
            var entities = this.CompanyCache.CompanyCurrencies;

            var models = entities.Maps<CompanyCurrencySelector>();

            return this.BizResult(models);
        }

        /// <summary>
        /// 获取分公司选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取分公司选项")]
        public BizResult<List<SubCompanySelector>> GetSubCompanySelectors([FromQuery] SubCompanySelectorFilter filter)
        {
            var exps = this.NewExps<SubCompany>();

            exps = filter.GetDynamicQueryExps(exps);

            var query = this.CompanyCache.SubCompanies.AsQueryable();

            foreach (var exp in exps)
            {
                query = query.Where(exp);
            }

            if (!string.IsNullOrEmpty(filter.Order))
            {
                query = query.OrderBy(filter.Order);
            }
            var entities = query.ToList();

            var models = entities.Maps<SubCompanySelector>();

            return this.BizResult(models);
        }

        /// <summary>
        /// 获取部门选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取部门选项")]
        public BizResult<List<DepartmentSelector>> GetDepartmentSelectors([FromQuery] DepartmentSelectorFilter filter)
        {
            var exps = this.NewExps<Department>();

            exps = filter.GetDynamicQueryExps(exps);

            var query = this.CompanyCache.Departments.AsQueryable();

            foreach (var exp in exps)
            {
                query = query.Where(exp);
            }

            if (!string.IsNullOrEmpty(filter.Order))
            {
                query = query.OrderBy(filter.Order);
            }
            var entities = query.ToList();

            var models = entities.Maps<DepartmentSelector>();

            return this.BizResult(models);
        }

        /// <summary>
        /// 获取成本中心选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取成本中心选项")]
        public BizResult<List<CostCenterSelector>> GetCostCenterSelectors([FromQuery] CostCenterSelectorFilter filter)
        {
            var exps = this.NewExps<CostCenter>();

            exps = filter.GetDynamicQueryExps(exps);

            var query = this.CompanyCache.CostCenters.AsQueryable();

            foreach (var exp in exps)
            {
                query = query.Where(exp);
            }

            if (!string.IsNullOrEmpty(filter.Order))
            {
                query = query.OrderBy(filter.Order);
            }
            var entities = query.ToList();

            var models = entities.Maps<CostCenterSelector>();

            return this.BizResult(models);
        }

        /// <summary>
        /// 获取职位选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取职位选项")]
        public BizResult<List<PositionSelector>> GetPositionSelectors()
        {
            var entites = this.Repo.GetEntities<Position>();

            var models = entites.Maps<PositionSelector>();

            return this.BizResult(models);
        }

        /// <summary>
        /// 查询员工选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询员工选项")]
        public QueryResult<EmployeeSelector> QueryEmployeeSelector([FromQuery] EmployeeSelectorFilter filter)
        {
            var exps = this.NewExps<Employee>();

            return this.Repo.GetDynamicQuery<Employee, EmployeeSelector>(filter, exps);
        }

        /// <summary>
        /// 查询岗位选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询岗位选项")]
        public QueryResult<StationSelector> QueryStationSelector([FromQuery] StationSelectorFilter filter)
        {
            var exps = this.NewExps<Station>();

            return this.Repo.GetDynamicQuery<Station, StationSelector>(filter, exps);
        }

        /// <summary>
        /// 查询职位选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询职位选项")]
        public QueryResult<PositionSelector> QueryPositionSelector([FromQuery] PositionSelectorFilter filter)
        {
            var exps = this.NewExps<Position>();

            return this.Repo.GetDynamicQuery<Position, PositionSelector>(filter, exps);
        }

        /// <summary>
        /// 查询部门选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询部门选项")]
        public QueryResult<DepartmentSelector> QueryDepartmentSelector([FromQuery] DepartmentSelectorFilter filter)
        {
            var exps = this.NewExps<Department>();

            return this.Repo.GetDynamicQuery<Department, DepartmentSelector>(filter, exps);
        }

        /// <summary>
        /// 查询用户选择器
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询用户选择器")]
        public QueryResult<UserSelector> QueryUserSelector([FromQuery] UserSelectorFilter filter)
        {
            var exps = this.NewExps<User>();

            return this.Repo.GetDynamicQuery<User, UserSelector>(filter, exps);
        }

        /// <summary>
        /// 获取岗位选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取岗位选项")]
        public BizResult<List<StationSelector>> GetStationSelectors()
        {
            var entites = this.CompanyCache.Stations;

            var models = entites.Maps<StationSelector>();

            return this.BizResult(models);
        }

        /// <summary>
        /// 查询成本中心选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询成本中心选项")]
        public QueryResult<CostCenterSelector> QueryCostCenterSelectors([FromQuery] CostCenterSelectorFilter filter)
        {
            var exps = this.NewExps<CostCenter>();

            if (!filter.ExcludeUsed.IsEmpty() && filter.ExcludeUsed == true)
            {
                var usedCosCenterIds = this.Repo.GetEntities<DepartmentCostCenter>().Select(a => a.CostCenterId);

                exps.And(a => !usedCosCenterIds.Contains(a.ID));
            }

            exps = filter.GetDynamicQueryExps(exps);

            return this.Repo.GetDynamicQuery<CostCenter, CostCenterSelector>(filter, exps);
        }

        /// <summary>
        /// 查询经销商选项
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询经销商选项")]
        public QueryResult<ReceiverSelector> QueryReceiverSelectors([FromQuery] ReceiverSelectorFilter filter)
        {
            var exps = this.NewExps<Receiver>();

            exps.And(a => a.ReceiverType.Code.StartsWith(ConstDefinition.ReceiverType.DistributorCode));

            exps = filter.GetDynamicQueryExps(exps);

            return this.Repo.GetDynamicQuery<Receiver, ReceiverSelector>(filter, exps);
        }
    }
}