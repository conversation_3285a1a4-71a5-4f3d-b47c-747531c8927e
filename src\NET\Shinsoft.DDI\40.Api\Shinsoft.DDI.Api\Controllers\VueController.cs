﻿using Shinsoft.Core.I18n;

namespace Shinsoft.DDI.Api
{
    public class VueController : BaseApiController<CompanySetupBll>
    {
        /// <summary>
        /// 初始化路由
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        public void InitVueRoute()
        {
            var sc = this.SysCache;
            var companies = sc.GetValidCompanies();

            foreach (var company in companies)
            {
                var bll = this.GetRepo<CompanySetupBll>(company.ID);

                bll.InitVueRoute();
            }
        }

        /// <summary>
        /// 获取动态路由
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取动态路由")]
        public List<VueRouteTree> GetAsyncRoutes()
        {
            var roots = this.CompanyCache.VueRoutes.Where(p => !p.ParentId.HasValue).ToList();

            var models = new List<VueRouteTree>();

            foreach (var root in roots)
            {
                var model = this.ToVueRouteTree(root);

                models.Add(model);
            }

            return models;
        }

        private VueRouteTree ToVueRouteTree(VueRoute route)
        {
            var model = route.Map<VueRouteTree>();

            if (route.Children?.Count > 0)
            {
                model.Children = [];

                foreach (var child in route.Children)
                {
                    var childModel = this.ToVueRouteTree(child);

                    model.Children.Add(childModel);
                }
            }
            else
            {
                model.Children = null;
            }

            return model;
        }
    }
}
