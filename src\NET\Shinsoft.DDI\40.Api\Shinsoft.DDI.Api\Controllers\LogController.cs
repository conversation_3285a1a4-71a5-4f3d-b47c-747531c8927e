﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    [ApiExplorerSettings(GroupName = "日志")]
    public class LogController : BaseApiController<LogBll>
    {
        #region Log

        /// <summary>
        /// 查询日志
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.System.Log.Log_Query)]
        [LogApi(ApiType.Query, Operate = "查询日志")]
        public QueryResult<VwLogQuery> QueryLog([FromQuery] VwLogFilter filter)
        {
            var exps = this.NewExps<VwLog>();
            if (filter.LogTime != null && filter.LogTime.Count() == 2)
            {
                var startTime = filter.LogTime[0];
                var endTime = filter.LogTime[1]?.Date.AddDays(1);
                exps.Add(x => x.LogTime >= startTime && x.LogTime <= endTime);
            }
            var result = this.Repo.GetDynamicQuery<VwLog, VwLogQuery>(filter, exps);

            return result;
        }

        /// <summary>
        /// 获取日志
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.System.Log.Log_Query)]
        [LogApi(ApiType.Query, Operate = "获取日志")]
        public BizResult<VwLogModel> GetLog([FromQuery, Required] Guid id)
        {
            var result = new BizResult<VwLogModel>();
            var entity = this.Repo.Get<VwLog>(id);

            if (entity == null)
            {
                result.Error("日志不存在");
            }
            else
            {
                var model = entity.Map<VwLogModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 获取日志操作
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取所有日志操作选择器")]
        public QueryResult<LogOperateSelector> GetlogsSelector([FromQuery] LogOperateSelectorFilter filter)
        {
            var exps = this.NewExps<LogOperate>();

            var result = this.Repo.GetDynamicQuery<LogOperate, LogOperateSelector>(filter, exps);

            return result;
        }

        #endregion Log

    }
}
