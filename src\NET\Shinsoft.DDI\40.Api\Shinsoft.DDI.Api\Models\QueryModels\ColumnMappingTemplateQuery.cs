﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class ColumnMappingTemplateQuery
    {
        /// <summary>
        /// 公司名称
        /// </summary>
        [MapFromProperty(typeof(DepartmentCostCenter), DepartmentCostCenter.Foreigns.Company, Company.Columns.Name)]
        public string? CompanyName { get; set; }

        /// <summary>
        /// Excel字段
        /// </summary>
        public string? ExDbFiled { get; set; }

        /// <summary>
        /// 日期类型
        /// </summary>
        public string? DateFormatType { get; set; }
    }
}
