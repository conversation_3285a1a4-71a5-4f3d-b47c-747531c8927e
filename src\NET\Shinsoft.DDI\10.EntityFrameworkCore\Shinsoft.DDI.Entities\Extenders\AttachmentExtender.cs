﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public static class AttachmentExtender
    {
        public static MailAttachment ToMailAttachment(this Attachment attachment)
        {
            return new MailAttachment()
            {
                FileIndexId = attachment.FileIndexId,
                ContentType = attachment.ContentType,
                FileName = attachment.FileName,
                FileExt = attachment.FileExt,
                FileSize = attachment.FileSize,
            };
        }
    }
}