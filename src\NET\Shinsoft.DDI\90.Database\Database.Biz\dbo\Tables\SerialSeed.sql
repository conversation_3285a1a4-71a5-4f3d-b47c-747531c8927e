﻿CREATE TABLE [dbo].[SerialSeed]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_SerialSeed_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [SerialNumberId]            UNIQUEIDENTIFIER            NOT NULL,
    [Year]                      INT                         NULL,
    [Month]                     INT                         NULL,
    [Day]                       INT                         NULL,
    [Hour]                      INT                         NULL,
    [Min]                       INT                         NULL,
    [Sec]                       INT                         NULL,
    [Seed]                      INT                         NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_SerialSeed] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_SerialSeed_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_SerialSeed_SerialNumber_00_SerialNumber] FOREIGN KEY ([SerialNumberId]) REFERENCES [dbo].[SerialNumber] ([ID]),

)
