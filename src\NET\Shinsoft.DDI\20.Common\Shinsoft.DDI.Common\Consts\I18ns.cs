﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Common
{
    public static partial class I18ns
    {
        /// <summary>
        /// 验证规则
        /// </summary>
        [Description("验证规则")]
        public static partial class Rule
        {
            /// <summary>
            /// 登录
            /// </summary>
            [Description("登录")]
            public static partial class Login
            {
                /// <summary>
                /// 请输入登录名
                /// </summary>
                [Description("请输入登录名")]
                public const string LoginName_Required = "Rule.Login.LoginName:Required";

                /// <summary>
                /// 请输入密码
                /// </summary>
                [Description("请输入密码")]
                public const string Password_Required = "Rule.Login.Password:Required";
            }

            /// <summary>
            /// 角色
            /// </summary>
            [Description("角色")]
            public static partial class Role
            {
                /// <summary>
                /// 编码长度不可以超过50个字符
                /// </summary>
                [Description("编码长度不可以超过50个字符")]
                public const string Code_Length = "Rule.Role.Code:Length";

                /// <summary>
                /// 请输入名称
                /// </summary>
                [Description("请输入名称")]
                public const string Name_Required = "Rule.Role.Name:Required";

                /// <summary>
                /// 姓名长度不可以超过50个字符
                /// </summary>
                [Description("姓名长度不可以超过50个字符")]
                public const string Name_Length = "Rule.Role.Name:Length";

                /// <summary>
                /// 备注长度不可以超过500个字符
                /// </summary>
                [Description("备注长度不可以超过500个字符")]
                public const string Remark_Length = "Rule.Role.Remark:Length";

                /// <summary>
                /// 角色不存在
                /// </summary>
                [Description("角色不存在")]
                public const string NotExist = "Rule.Role.NotExist";

                /// <summary>
                /// 请选择角色
                /// </summary>
                [Description("请选择角色")]
                public const string Choose_Role = "Rule.Role.Choose:Role";

                /// <summary>
                /// 请选择角色成员
                /// </summary>
                [Description("请选择角色成员")]
                public const string Choose_Employee = "Rule.Role.Choose:Employee";

                /// <summary>
                /// 请选择角色成员类型
                /// </summary>
                [Description("请选择角色成员类型")]
                public const string Choose_EmployeeType = "Rule.Role.Choose:EmployeeType";

                /// <summary>
                /// 请选择有效的角色成员
                /// </summary>
                [Description("请选择有效的角色成员")]
                public const string Employee_InVaild = "Rule.Role.Employee:InVaild";

                /// <summary>
                /// 不可以对角色授予无效的权限
                /// </summary>
                [Description("不可以对角色授予无效的权限")]
                public const string Auth_InVaild = "Rule.Role.Auth:InVaild";

                /// <summary>
                /// 不可以对角色授予无效的内容权限
                /// </summary>
                [Description("不可以对角色授予无效的内容权限")]
                public const string AuthContent_InVaild = "Rule.Role.AuthContent:InVaild";

                /// <summary>
                /// 已存在相同编码的角色
                /// </summary>
                [Description("已存在相同编码的角色")]
                public const string Code_Repeat = "Rule.Role.Code:Repeat";

                /// <summary>
                /// 已存在相同名称的角色
                /// </summary>
                [Description("已存在相同名称的角色")]
                public const string Name_Repeat = "Rule.Role.Name:Repeat";

                /// <summary>
                /// 格式化字符串
                /// </summary>
                [Description("格式化字符串")]
                public static class Format
                { /// <summary>
                  /// 不允许删除角色【{0}】
                  /// </summary>
                    [Description("不允许删除角色【{0}】")]
                    public const string CanNot_Remove = "Message.Entity.Format.CanNot:Remove";
                }
            }

            /// <summary>
            /// 角色成员
            /// </summary>
            [Description("角色成员")]
            public static partial class RoleMember
            {
                /// <summary>
                /// 请选择角色
                /// </summary>
                [Description("请选择角色")]
                public const string RoleId_Length = "Rule.RoleMember.RoleId:Required";

                /// <summary>
                /// 请选择成员类型
                /// </summary>
                [Description("请选择成员类型")]
                public const string MemberType_Required = "Rule.RoleMember.MemberType:Required";

                /// <summary>
                /// 请选择成员
                /// </summary>
                [Description("请选择成员")]
                public const string Member_Required = "Rule.RoleMember.Member:Required";
            }

            /// <summary>
            /// 职位
            /// </summary>
            [Description("职位")]
            public static partial class Position
            {
                /// <summary>
                /// 请输入编码
                /// </summary>
                [Description("请输入编码")]
                public const string Code_Required = "Rule.Position.Code:Required";

                /// <summary>
                /// 编码长度不可以超过50个字符
                /// </summary>
                [Description("编码长度不可以超过50个字符")]
                public const string Code_Length = "Rule.Position.Code:Length";

                /// <summary>
                /// 请输入名称
                /// </summary>
                [Description("请输入名称")]
                public const string Name_Required = "Rule.Position.Name:Required";

                /// <summary>
                /// 姓名长度不可以超过50个字符
                /// </summary>
                [Description("姓名长度不可以超过50个字符")]
                public const string Name_Length = "Rule.Position.Name:Length";

                /// <summary>
                /// 请输入职级
                /// </summary>
                [Description("请输入职级")]
                public const string Grade_Required = "Rule.Position.Grade:Required";

                /// <summary>
                /// 职级只能是整数
                /// </summary>
                [Description("职级只能是整数")]
                public const string Grade_Pattern = "Rule.Position.Grade:Length";

                /// <summary>
                /// 备注长度不可以超过500个字符
                /// </summary>
                [Description("备注长度不可以超过500个字符")]
                public const string Remark_Length = "Rule.Position.Remark:Length";
            }

            /// <summary>
            /// 扩展配置
            /// </summary>
            [Description("扩展配置")]
            public static partial class CompanySetting
            {
                /// <summary>
                /// 请输入Value值
                /// </summary>
                [Description("请输入Value值")]
                public const string Value_Required = "Rule.CompanySetting.Value:Required";

                /// <summary>
                /// 请输入备注
                /// </summary>
                [Description("请输入备注")]
                public const string Remark_Required = "Rule.CompanySetting.Remark:Required";

                /// <summary>
                /// 备注长度不可以超过500个字符
                /// </summary>
                [Description("备注长度不可以超过500个字符")]
                public const string Remark_Length = "Rule.CompanySetting.Remark:Length";
            }

            /// <summary>
            /// 成本中心
            /// </summary>
            [Description("成本中心")]
            public static partial class CostCenter
            {
                [Description("请输入编码")]
                public const string Code_Required = "Rule.CostCenter.Code:Required";

                [Description("请输入名称")]
                public const string Name_Required = "Rule.CostCenter.Name:Required";

                [Description("编码长度不可以超过50个字符")]
                public const string Code_Length = "Rule.CostCenter.Code:Length";
            }

            /// <summary>
            /// 分公司
            /// </summary>
            [Description("分公司")]
            public static partial class SubCompany
            {
                [Description("请输入编码")]
                public const string Code_Required = "Rule.SubCompany.Code:Required";

                [Description("请输入名称")]
                public const string Name_Required = "Rule.SubCompany.Name:Required";

                [Description("请输入简称")]
                public const string ShortName_Required = "Rule.SubCompany.ShortName:Required";

                [Description("编码长度不可以超过50个字符")]
                public const string Code_Length = "Rule.SubCompany.Code:Length";
            }

            /// <summary>
            /// 部门
            /// </summary>
            [Description("部门")]
            public static partial class Department
            {
                /// <summary>
                /// 请输入编码
                /// </summary>
                [Description("请输入编码")]
                public const string Code_Required = "Rule.Department.Code:Required";

                /// <summary>
                /// 编码长度不可以超过50个字符
                /// </summary>
                [Description("编码长度不可以超过50个字符")]
                public const string Code_Length = "Rule.Department.Code:Length";

                /// <summary>
                /// 请输入名称
                /// </summary>
                [Description("请输入名称")]
                public const string Name_Required = "Rule.Department.Name:Required";

                /// <summary>
                /// 姓名长度不可以超过50个字符
                /// </summary>
                [Description("名称长度不可以超过50个字符")]
                public const string Name_Length = "Rule.Department.Name:Length";

                /// <summary>
                /// 请输入简称
                /// </summary>
                [Description("请输入简称")]
                public const string ShortName_Required = "Rule.Department.ShortName:Required";

                /// <summary>
                /// 简称长度不可以超过50个字符
                /// </summary>
                [Description("简称长度不可以超过50个字符")]
                public const string ShortName_Length = "Rule.Department.ShortName:Length";

                /// <summary>
                /// 备注长度不可以超过500个字符
                /// </summary>
                [Description("备注长度不可以超过500个字符")]
                public const string Remark_Length = "Rule.Position.Remark:Length";
            }

            /// <summary>
            /// 员工岗位
            /// </summary>
            [Description("员工岗位")]
            public static partial class EmployeeStation
            {
                /// <summary>
                /// 岗位不存在
                /// </summary>
                [Description("岗位不存在")]
                public const string NotExist = "Rule.EmployeeStation.NotExist";

                /// <summary>
                /// 离岗日期不能小于挂岗日期
                /// </summary>
                [Description("离岗日期不能小于挂岗日期")]
                public const string EndDate_LessThenStartDate = "Rule.EmployeeStation.EndDate:LessThenStartDate";
            }

            /// <summary>
            /// 员工
            /// </summary>
            [Description("员工")]
            public static partial class Employee
            {
                /// <summary>
                /// 请输入姓名
                /// </summary>
                [Description("请输入姓名")]
                public const string DisplayName_Empty = "Rule.Employee.DisplayName:Empty";

                /// <summary>
                /// 请输入工号
                /// </summary>
                [Description("请输入工号")]
                public const string JobNo_Empty = "Rule.Employee.JobNo:Empty";

                /// <summary>
                /// 请输入邮箱
                /// </summary>
                [Description("请输入邮箱")]
                public const string Email_Empty = "Rule.Employee.Email:Empty";

                /// <summary>
                /// 邮箱格式不正确
                /// </summary>
                [Description("邮箱格式不正确")]
                public const string Email_FormateError = "Rule.Employee.Email:FormateError";

                /// <summary>
                /// 邮箱格式不正确
                /// </summary>
                [Description("邮箱格式不正确")]
                public const string Mobile_FormateError = "Rule.Employee.Mobile:FormateError";

                /// <summary>
                /// 格式化字符串
                /// </summary>
                [Description("格式化字符串")]
                public static class Format
                { /// <summary>
                  /// 只允许使用公司指定的邮箱:{0}
                  /// </summary>
                    [Description("只允许使用公司指定的邮箱:{0}")]
                    public const string CompanyEmail = "Message.Entity.Format.CompanyEmail";
                }

                /// <summary>
                /// 请选择员工状态
                /// </summary>
                [Description("请选择员工状态")]
                public const string Choose_EmployeeState = "Rule.Employee.Choose:EmployeeState";

                /// <summary>
                /// 已存在相同工号的员工
                /// </summary>
                [Description("已存在相同工号的员工")]
                public const string JobNo_Repeat = "Rule.Employee.JobNo:Repeat";

                /// <summary>
                /// 已存在相同邮箱的员工
                /// </summary>
                [Description("已存在相同邮箱的员工")]
                public const string Email_Repeat = "Rule.Employee.Email:Repeat";

                /// <summary>
                /// 所选用户不存在
                /// </summary>
                [Description("所选用户不存在")]
                public const string ChooseEmployee_NotExist = "Rule.Employee.ChooseEmployee:NotExist";

                /// <summary>
                /// 不可以选择非当前集团公司的用户
                /// </summary>
                [Description("不可以选择非当前集团公司的用户")]
                public const string CanNotChoose_OtherCompanyEmployee = "Rule.Employee.CanNotChoose:OtherCompanyEmployee";

                /// <summary>
                /// 不允许删除您自己
                /// </summary>
                [Description("不允许删除您自己")]
                public const string CanNot_RemoveSelf = "Rule.Employee.CanNot:RemoveSelf";

                /// <summary>
                /// 员工不存在
                /// </summary>
                [Description("员工不存在")]
                public const string NotExist = "Rule.Employee.NotExist";

                /// <summary>
                /// 员工状态不可用
                /// </summary>
                [Description("员工状态不可用")]
                public const string EmployeeState_Error = "Rule.Employee.EmployeeState：Error";

                /// <summary>
                /// 员工未初始化用户
                /// </summary>
                [Description("员工未初始化用户")]
                public const string Employee_NoInit = "Rule.Employee.Employee:NoInit";

                /// <summary>
                /// 当前用户无法设置代理
                /// </summary>
                [Description("当前用户无法设置代理")]
                public const string CanNotSetProxy = "Rule.Employee.CanNotSetProxy";

                /// <summary>
                /// 代理用户无权设置代理
                /// </summary>
                [Description("代理用户无权设置代理")]
                public const string NoReightSetProxy = "Rule.Employee.NoReightSetProxy";

                /// <summary>
                /// 代理设置不存在
                /// </summary>
                [Description("代理设置不存在")]
                public const string ProxyNotExist = "Rule.Employee.ProxyNotExist";

                /// <summary>
                /// 请选择代理人
                /// </summary>
                [Description("请选择代理人")]
                public const string Select_Agent = "Rule.Employee.Select:Agent";

                /// <summary>
                /// 用户无权查看他人的代理设置
                /// </summary>
                [Description("用户无权查看他人的代理设置")]
                public const string NoRightViewProxy = "Rule.Employee.NoRightViewProxy";

                /// <summary>
                /// 无权修改他人的代理设置
                /// </summary>
                [Description("无权修改他人的代理设置")]
                public const string NoRightUpdateProxy = "Rule.Employee.NoRightUpdateProxy";

                /// <summary>
                /// 请登录系统
                /// </summary>
                [Description("请登录系统")]
                public const string Please_Login = "Rule.Employee.Please:Login";

                /// <summary>
                /// 代理用户不可以修改密码
                /// </summary>
                [Description("代理用户不可以修改密码")]
                public const string ProxyUserCanNotChangePassword = "Rule.Employee.ProxyUserCanNotChangePassword";

                /// <summary>
                /// 用户不存在
                /// </summary>
                [Description("用户不存在")]
                public const string Employee_NotExist = "Rule.Employee.Employee:NotExist";

                /// <summary>
                /// 无效用户
                /// </summary>
                [Description("无效用户")]
                public const string Employee_Invaild = "Rule.Employee.Employee:Invaild";

                /// <summary>
                /// 新密码不可为空
                /// </summary>
                [Description("新密码不可为空")]
                public const string NewPasword_Empty = "Rule.Employee.NewPasword:Empty";

                /// <summary>
                /// 密码不符合系统复杂性要求
                /// </summary>
                [Description("密码不符合系统复杂性要求")]
                public const string Pasword_SimpleForSystem = "Rule.Employee.Pasword:SimpleForSystem";

                /// <summary>
                /// 密码不符合公司复杂性要求
                /// </summary>
                [Description("密码不符合公司复杂性要求")]
                public const string Pasword_SimpleForCompany = "Rule.Employee.Pasword:SimpleForCompany";

                /// <summary>
                /// 原密码不正确
                /// </summary>
                [Description("原密码不正确")]
                public const string OriginalPassword_error = "Rule.Employee.OriginalPassword:error";
            }

            /// <summary>
            /// 岗位
            /// </summary>
            [Description("岗位")]
            public static partial class Station
            {
                /// <summary>
                /// 请输入名称
                /// </summary>
                [Description("请输入名称")]
                public const string Name_Required = "Rule.Station.Name:Required";

                /// <summary>
                /// 姓名长度不可以超过50个字符
                /// </summary>
                [Description("名称长度不可以超过50个字符")]
                public const string Name_Length = "Rule.Station.Name:Length";

                /// <summary>
                /// 请选择职位
                /// </summary>
                [Description("请选择职位")]
                public const string PositionId_Required = "Rule.Station.PositionId:Required";

                /// <summary>
                /// 备注长度不可以超过500个字符
                /// </summary>
                [Description("备注长度不可以超过500个字符")]
                public const string Remark_Length = "Rule.Station.Remark:Length";
            }

            /// <summary>
            /// 公告
            /// </summary>
            [Description("公告")]
            public static partial class Announcement
            {
                /// <summary>
                /// 结束时间不能早于开始时间
                /// </summary>
                [Description("结束时间不能早于开始时间")]
                public const string Not_Exist = "Rule.Announcement.Not:Exist";

                /// <summary>
                /// 标题不能为空
                /// </summary>
                [Description("标题不能为空")]
                public const string Subject_Required = "Rule.Announcement.Subject:Required";

                /// <summary>
                /// 标题不能为空
                /// </summary>
                [Description("标题长度不能超过50个字符")]
                public const string Subject_Length = "Rule.Announcement.Subject:Length";

                /// <summary>
                /// 请选择重要性
                /// </summary>
                [Description("请选择重要性")]
                public const string EnumImportant_Required = "Rule.Announcement.EnumImportant:Required";

                /// <summary>
                /// 公告内容不能为空
                /// </summary>
                [Description("公告内容不能为空")]
                public const string AnnouncementContent_Required = "Rule.Announcement.AnnouncementContent:Required";

                /// <summary>
                /// 结束时间不能早于开始时间
                /// </summary>
                [Description("开始时间不能晚于结束时间")]
                public const string EndTimeEarlierStart = "Rule.Announcement.EndTimeEarlierStartTime";
            }
        }

        /// <summary>
        /// 消息
        /// </summary>
        [Description("消息")]
        public static partial class Message
        {
            /// <summary>
            /// 数据实体
            /// </summary>
            [Description("数据实体")]
            public static partial class Entity
            {
                /// <summary>
                /// 格式化字符串
                /// </summary>
                [Description("格式化字符串")]
                public static class Format
                {
                    /// <summary>
                    /// {0}不存在
                    /// </summary>
                    [Description("{0}不存在")]
                    public const string NotExist = "Message.Entity.Format.NotExist";

                    /// <summary>
                    /// 已创建用户，默认密码：{0}
                    /// </summary>
                    [Description("已创建用户，默认密码：{0}")]
                    public const string CreatedEmployee_DefaultPwd = "Message.Entity.Format.CreatedEmployee:DefaultPwd";

                    /// <summary>
                    /// 密码已重置为：{0}
                    /// </summary>
                    [Description("密码已重置为：{0}")]
                    public const string Pwd_Reset = "Message.Entity.Format.Pwd:Reset";
                }
            }

            /// <summary>
            /// 用户
            /// </summary>
            [Description("用户")]
            public static partial class User
            {
                /// <summary>
                /// 当前用户信息不存在
                /// </summary>
                [Description("当前用户信息不存在")]
                public const string CurrentUserNotExist = "Message.User.CurrentUserNotExist";
            }

            /// <summary>
            /// 多语言
            /// </summary>
            [Description("多语言")]
            public static partial class Culture
            {
                /// <summary>
                /// 格式化字符串
                /// </summary>
                [Description("格式化字符串")]
                public static partial class Format
                {
                    /// <summary>
                    /// {0}语言不存在
                    /// </summary>
                    [Description("{0}语言不存在")]
                    public const string NotExist = "Message.Culture.Format.NotExist";
                }
            }

            /// <summary>
            /// 职位
            /// </summary>
            [Description("职位")]
            public static partial class Position
            {
                /// <summary>
                /// 已存在相同编码的职位
                /// </summary>
                [Description("已存在相同编码的职位")]
                public const string HasSamePosition = "Message.Position.HasSamePosition";

                /// <summary>
                /// 存在下级职位不可以直接删除
                /// </summary>
                [Description("存在下级职位不可以直接删除")]
                public const string HasChildPosition = "Message.Position.HasChildPosition";
            }

            /// <summary>
            /// 成本中心
            /// </summary>
            [Description("成本中心")]
            public static partial class CostCenter
            {
                [Description("已存在相同编码的成本中心")]
                public const string HasSameCodeCostCenter = "Message.CostCenter.HasSameCodeCostCenter";

                [Description("已存在相同名称的成本中心")]
                public const string HasSameNameCostCenter = "Message.CostCenter.HasSameNameCostCenter";

                [Description("父成本中心不存在")]
                public const string HasParentCostCenterNoExist = "Message.CostCenter.HasParentCostCenterNoExist";

                [Description("不可以将成本中心移动至当前下级成本中心")]
                public const string HasMoveCostCenterToChildCostCenter = "Message.CostCenter.HasMoveCostCenterToChildCostCenter";

                [Description("存在下级成本中心不能删除")]
                public const string HasChildCostCenterNoDelete = "Message.CostCenter.HasChildCostCenterNoDelete";
            }

            /// <summary>
            /// 分公司
            /// </summary>
            [Description("分公司")]
            public static partial class SubCompany
            {
                [Description("已存在相同编码的公司")]
                public const string HasSameCodeSubCompany = "Message.SubCompany.HasSameCodeSubCompany";

                [Description("已存在相同名称的公司")]
                public const string HasSameNameSubCompany = "Message.SubCompany.HasSameNameSubCompany";
            }
        }

        /// <summary>
        /// UI
        /// </summary>
        [Description("UI")]
        public static partial class UI
        {
        }

        /// <summary>
        /// 枚举
        /// </summary>
        [Description("枚举")]
        public static partial class Enums
        {
        }
    }
}