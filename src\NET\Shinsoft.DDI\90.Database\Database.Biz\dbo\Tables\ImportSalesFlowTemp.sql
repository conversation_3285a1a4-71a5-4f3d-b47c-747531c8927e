CREATE TABLE [dbo].[ImportSalesFlowTemp] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL CONSTRAINT [Default_ImportSalesFlowTemp_ID] DEFAULT (NEWSEQUENTIALID()),
    [ImportDataLogTempId]       UNIQUEIDENTIFIER          NOT NULL,
    [DistributorProvinceName]   NVARCHAR(50)                NULL,
    [DistributorCityName]       NVARCHAR(50)                NULL,
    [DistributorName]           NVARCHAR(200)               NULL,
    [ReceiverProvinceName]      NVARCHAR(50)                NULL,
    [ReceiverCityName]          NVARCHAR(50)                NULL,
    [ReceiverName]              NVARCHAR(200)               NULL,
    [RelateReceiverName]        NVARCHAR(200)               NULL,
    [ManufacturerName]          NVARCHAR(300)               NULL,
    [CommonName]                NVARCHAR(100)               NULL,
    [ProductName]               NVARCHAR(50)                NULL,
    [SpecificationName]         NVARCHAR(50)                NULL,
    [SaleDate]                  NVARCHAR(50)                NULL,
    [BatchNumber]               NVARCHAR(50)                NULL,
    [ExpireDate]                NVARCHAR(50)                NULL,
    [Quantity]                  NVARCHAR(20)                NULL,
    [TempDistributorId]         UNIQUEIDENTIFIER            NULL,
    [TempReceiverId]            UNIQUEIDENTIFIER            NULL,
    [TempProductId]             UNIQUEIDENTIFIER            NULL,
    [TempProductSpecId]         UNIQUEIDENTIFIER            NULL,
    [TempProductAliasId]        UNIQUEIDENTIFIER            NULL,
    [TempReceiverAliasId]       UNIQUEIDENTIFIER            NULL,
    [TempDistributorProvinceId] UNIQUEIDENTIFIER            NULL,
    [TempDistributorCityId]     UNIQUEIDENTIFIER            NULL,
    [TempManufacturerId]        UNIQUEIDENTIFIER            NULL,
    [EnumImportErrorType]       INT                         NOT NULL,
    [EnumErrorSalesFlowType]    INT                         NOT NULL,
    [EnumWarningSalesFlowType]  INT                         NOT NULL,
    [WarningProvinceId]         UNIQUEIDENTIFIER            NULL,
    [WarningCityId]             UNIQUEIDENTIFIER            NULL,
    [Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL, 
    [CreateTime]                DATETIME                    NULL, 
    [LastEditor]                NVARCHAR(50)                NULL, 
    [LastEditTime]              DATETIME                    NULL, 
    CONSTRAINT [PK_ImportSalesFlowTemp] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ImportSalesFlowTemp_ImportDataLog] FOREIGN KEY ([ImportDataLogTempId]) REFERENCES [dbo].[ImportDataLog] ([ID])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'销售流向导入临时表',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'销售流向导入主表临时表ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'ImportDataLogTempId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'经销商省份名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'DistributorProvinceName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'经销商城市名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'DistributorCityName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'经销商名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'DistributorName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'收货方省份名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'ReceiverProvinceName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'收货方城市名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'ReceiverCityName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'收货方名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'ReceiverName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'关联收货方名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'RelateReceiverName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'药企名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'ManufacturerName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'通用名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'CommonName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'产品名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'ProductName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'规格名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'SpecificationName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'销售日期',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'SaleDate'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'批号',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'BatchNumber'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'有效期',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'ExpireDate'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'数量',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'Quantity'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'临时经销商ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'TempDistributorId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'临时收货方ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'TempReceiverId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'临时产品ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'TempProductId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'临时产品规格ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'TempProductSpecId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'临时产品别名ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'TempProductAliasId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'临时收货方别名ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'TempReceiverAliasId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'临时经销商省份ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'TempDistributorProvinceId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'临时经销商城市ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'TempDistributorCityId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'临时药企ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'TempManufacturerId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'导入错误类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'EnumImportErrorType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'流向错误类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'EnumErrorSalesFlowType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'流向警告类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'EnumWarningSalesFlowType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'警告省份ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'WarningProvinceId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'警告城市ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'WarningCityId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ImportSalesFlowTemp',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'
GO
