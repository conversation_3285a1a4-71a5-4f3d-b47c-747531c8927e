button.info {
  color: #ffffff;
  background-color: #8c8c8c;
}
button.info:hover {
  color: #ffffff;
  background-color: #bebebe;
}
button.info:active {
  color: #ffffff;
  background-color: #7c7c7c;
}
button.info:disabled, button.info:disabled:hover{
  color: #ffffff;
  background-color: #c8c9cc;
}
button.info.is-link {
  color: #8c8c8c;
  background-color: transparent;
}
button.info.is-link:hover {
  color: #bebebe;
  background-color: transparent;
}
button.info.is-link:active {
  color: #7c7c7c;
  background-color: transparent;
}
button.info.is-link:disabled, button.info.is-link:disabled:hover{
  color: #c8c9cc;
  background-color:  transparent;
}

button.query {
  color: #ffffff;
  background-color: #163c6d;
}
button.query:hover {
  color: #ffffff;
  background-color: #215daa;
}
button.query:active {
  color: #ffffff;
  background-color: #1d5193;
}
button.query:disabled, button.query:disabled:hover{
  color: #ffffff;
  background-color: #6a7a91;
}
button.query.is-link {
  color: #163c6d;
  background-color: transparent;
}
button.query.is-link:hover {
  color: #215daa;
  background-color: transparent;
}
button.query.is-link:active {
  color: #1d5193;
  background-color: transparent;
}
button.query.is-link:disabled, button.query.is-link:disabled:hover{
  color: #6a7a91;
  background-color:  transparent;
}

button.view {
  color: #ffffff;
  background-color: #163c6d;
}
button.view:hover {
  color: #ffffff;
  background-color: #215daa;
}
button.view:active {
  color: #ffffff;
  background-color: #1d5193;
}
button.view:disabled, button.view:disabled:hover{
  color: #ffffff;
  background-color: #6a7a91;
}
button.view.is-link {
  color: #163c6d;
  background-color: transparent;
}
button.view.is-link:hover {
  color: #215daa;
  background-color: transparent;
}
button.view.is-link:active {
  color: #1d5193;
  background-color: transparent;
}
button.view.is-link:disabled, button.view.is-link:disabled:hover{
  color: #6a7a91;
  background-color:  transparent;
}


button.new {
  color: #ffffff;
  background-color: #163c6d;
}
button.new:hover {
  color: #ffffff;
  background-color: #215daa;
}
button.new:active {
  color: #ffffff;
  background-color: #1d5193;
}
button.new:disabled, button.new:disabled:hover{
  color: #ffffff;
  background-color: #6a7a91;
}
button.new.is-link {
  color: #163c6d;
  background-color: transparent;
}
button.new.is-link:hover {
  color: #215daa;
  background-color: transparent;
}
button.new.is-link:active {
  color: #1d5193;
  background-color: transparent;
}
button.new.is-link:disabled, button.new.is-link:disabled:hover{
  color: #6a7a91;
  background-color: transparent;
}

button.edit {
  color: #ffffff;
  background-color: #163c6d;
}
button.edit:hover {
  color: #ffffff;
  background-color: #215daa;
}
button.edit:active {
  color: #ffffff;
  background-color: #1d5193;
}
button.edit:disabled, button.edit:disabled:hover{
  color: #ffffff;
  background-color: #6a7a91;
}
button.edit.is-link {
  color: #163c6d;
  background-color: transparent;
}
button.edit.is-link:hover {
  color: #215daa;
  background-color: transparent;
}
button.edit.is-link:active {
  color: #1d5193;
  background-color: transparent;
}
button.edit.is-link:disabled, button.edit.is-link:disabled:hover{
  color: #6a7a91;
  background-color: transparent;
}


button.delete {
  color: #ffffff;
  background-color: #ff3317;
}
button.delete:hover {
  color: #ffffff;
  background-color: #ff5e48;
}
button.delete:active {
  color: #ffffff;
  background-color: #ff4c34;
}
button.delete:disabled, button.delete:disabled:hover{
  color: #ffffff;
  background-color: #fab6b6;
}
button.delete.is-link {
  color: #ff3317;
  background-color: transparent;
}
button.delete.is-link:hover {
  color: #ff5e48;
  background-color: transparent;
}
button.delete.is-link:active {
  color: #ff4c34;
  background-color: transparent;
}
button.delete.is-link:disabled, button.delete.is-link:disabled:hover{
  color: #fab6b6;
  background-color: transparent;
}


button.save {
  color: #ffffff;
  background-color: #163c6d;
}
button.save:hover {
  color: #ffffff;
  background-color: #215daa;
}
button.save:active {
  color: #ffffff;
  background-color: #1d5193;
}
button.save:disabled, button.save:disabled:hover{
  color: #ffffff;
  background-color: #6a7a91;
}
button.save.is-link {
  color: #163c6d;
  background-color: transparent;
}
button.save.is-link:hover {
  color: #215daa;
  background-color: transparent;
}
button.save.is-link:active {
  color: #1d5193;
  background-color: transparent;
}
button.save.is-link:disabled, button.save.is-link:disabled:hover{
  color: #6a7a91;
  background-color:  transparent;
}


button.close {
  color: #ffffff;
  background-color: #8c8c8c;
}
button.close:hover {
  color: #ffffff;
  background-color: #bebebe;
}
button.close:active {
  color: #ffffff;
  background-color: #7c7c7c;
}
button.close:disabled, button.close:disabled:hover{
  color: #ffffff;
  background-color: #c8c9cc;
}
button.close.is-link {
  color: #8c8c8c;
  background-color: transparent;
}
button.close.is-link:hover {
  color: #bebebe;
  background-color: transparent;
}
button.close.is-link:active {
  color: #7c7c7c;
  background-color: transparent;
}
button.close.is-link:disabled, button.close.is-link:disabled:hover{
  color: #c8c9cc;
  background-color:  transparent;
}