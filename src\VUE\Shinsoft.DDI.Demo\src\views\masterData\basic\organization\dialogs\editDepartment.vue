<script setup lang="ts">
import { basicMasterDataApi } from "@/api/basicMasterData";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "organization:department:edit"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.model?.id
    ? `${t("operate.edit")} ${tt("Entity.Department._Entity")} - ${state.model.name}`
    : `${t("operate.add")} ${tt("Entity.Department._Entity")}`;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      subCompanyId: "",
      parentId: "",
      valid: 1
    }
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  },
  // 按钮权限
  btn: {
    save: computed(() => {
      return state.model.id
        ? userStore.hasAnyAuth([
            "Organization:Manage",
            "Department:Manage",
            "Department:Manage:Edit"
          ])
        : userStore.hasAnyAuth([
            "Organization:Manage",
            "Department:Manage",
            "Department:Manage:Add"
          ]);
    })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  model: cfg.default.model as Record<string, any>
});

/**
 * 验证规则
 */
const rules = {
  form: {
    code: [
      { required: true, message: "Rule.Department.Code:Required", trigger: "blur" },
      { max: 50, message: "Rule.Department.Code:Length", trigger: "blur" }
    ],
    name: [
      { required: true, message: "Rule.Department.Name:Required", trigger: "blur" },
      { max: 50, message: "Rule.Department.Name:Length", trigger: "blur" }
    ],
    shortName: [
      { required: true, message: "Rule.Department.ShortName:Required", trigger: "blur" },
      { max: 50, message: "Rule.Department.ShortName:Length", trigger: "blur" }
    ],
    remark: [{ max: 500, message: "Rule.Position.Remark:Length", trigger: "blur" }]
  }
};

/**
 * 存储
 */
const userStore = useUserStoreHook();

/**
 * 当前组件ref
 */
const formRef = ref<FormInstance>();

/**
 * 初始化组件（异步）
 */
const init = () => {
  // 防止之前打开的数据残留，因此初始化时赋予model默认值
  initState();

  cfg.loading.form = true;

  const allInits = [];

  return new Promise<void>(() => {
    Promise.all(allInits).then(() => {
      cfg.loading.form = false;
      get();
    });
  });
};

/**
 * 设置model数据
 */
const setModel = (data: any) => {
  state.model = data;
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 初始化state数据
 */
const initState = () => {
  state.model = cfg.default.model;
};

/**
 * 清空mstate数据
 */
const clearState = () => {
  initState();
  formRef.value.clearValidate();
  formRef.value.resetFields();
};

/**
 * 获取model数据
 */
const get = () => {
  if (state.id) {
    cfg.loading.form = true;
    basicMasterDataApi
      .GetDepartment(state.id)
      .then(res => {
        if (res.success) {
          setModel(res.data);
        } else {
          close();
        }
      })
      .finally(() => {
        cfg.loading.form = false;
      });
  }
};

/**
 * 新增数据
 */
const add = () => {
  cfg.loading.btn = true;

  const data = getModel();
  basicMasterDataApi
    .AddDepartment(data, { messageBoxResult: true })
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh(t("operate.message.success"), "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 更新数据
 */
const update = () => {
  cfg.loading.btn = true;

  const data = getModel();

  basicMasterDataApi
    .UpdateDepartment(data, { messageBoxResult: true })
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh(t("operate.message.success"), "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 按钮事件：【保存】
 */
const save = async () => {
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      if (state.model.id) {
        update();
      } else {
        add();
      }
    }
  });
};

/**
 * dialog:显示(父组件调用)
 */
const open = (id?: string, subCompanyId?: string, parentId?: string) => {
  cfg.dialog.visible = true;
  cfg.default.model.subCompanyId = subCompanyId;
  cfg.default.model.parentId = parentId;
  state.id = id;

  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        v-loading="cfg.loading.form"
        :rules="rules.form"
        :model="state.model"
        label-position="right"
        :label-width="formLabelWidth"
        class="el-dialog-form"
      >
        <el-row :gutter="formGutter">
          <el-col :span="formColSpan">
            <el-form-item prop="code" :label="tt('Entity.Department.Code')">
              <el-input
                v-model="state.model.code"
                :placeholder="tt('Entity.Department.Code')"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="name" :label="tt('Entity.Department.Name')">
              <el-input
                v-model="state.model.name"
                :placeholder="tt('Entity.Department.Name')"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="shortName" :label="tt('Entity.Department.ShortName')">
              <el-input
                v-model="state.model.shortName"
                :placeholder="tt('Entity.Department.ShortName')"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="formGutter">
          <el-col :span="24">
            <el-form-item prop="remark" :label="tt('Entity.Department.Remark')">
              <el-input
                v-model="state.model.remark"
                :placeholder="tt('Entity.Department.Remark')"
                maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.save"
            class="save"
            :round="btnRound"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnSaveIcon)"
            @click="save()"
          >
            {{ t("operate.save") }}
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
