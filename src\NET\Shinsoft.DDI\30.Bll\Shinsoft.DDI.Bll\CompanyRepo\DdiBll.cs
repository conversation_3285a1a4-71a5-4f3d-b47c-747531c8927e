﻿using Shinsoft.Core.NLog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using static Shinsoft.DDI.Common.ConstDefinition;

namespace Shinsoft.DDI.Bll
{
    public class DdiBll : BaseCompanyBll
    {
        #region Constructs

        public DdiBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        public DdiBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        public DdiBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        public DdiBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs


        /// <summary>
        /// 保存结果
        /// </summary>
        /// <param name="salesFlowDataType"></param>
        /// <param name="dataXml"></param>
        public void SaveClientData(SalesFlowDataType salesFlowDataType, string dataXml, string receiverCode)
        {
            // 将XML转换为CSV并保存到DDIFile文件夹
            string? csvFilePath = this.ConvertXmlToCsvAndSave(salesFlowDataType, dataXml, receiverCode);

            //TODO 获取文件目录将来存储到队列csvFilePath

        }

        /// <summary>
        /// 保存客户端日志
        /// </summary>
        /// <param name="logInfo">日志内容</param>
        /// <param name="logTime">日志时间</param>
        public void SaveClientLog(ReceiverClientLog receiverClientLog)
        {
            this.Add(receiverClientLog);
        }

        /// <summary>
        /// 更新客户端当前版本信息
        /// </summary>
        /// <param name="receiverClientId"></param>
        /// <param name="version"></param>
        public void UpdateReceiverClientVersion(Guid receiverClientId, string version)
        {
            var client = this.Get<ReceiverClient>(receiverClientId);
            if (client != null && client.Version != version)
            {
                client.Version = version;

                this.Update(client);
            }
        }

        /// <summary>
        /// 将XML数据转换为CSV格式并保存到DDIFile文件夹
        /// </summary>
        /// <param name="salesFlowDataType">数据类型</param>
        /// <param name="dataXml">XML数据</param>
        /// <returns>生成的CSV文件路径，失败时返回null</returns>
        public string? ConvertXmlToCsvAndSave(SalesFlowDataType salesFlowDataType, string dataXml, string receiverCode)
        {
            try
            {
                if (string.IsNullOrEmpty(dataXml))
                {
                    return null;
                }

                // 根据数据类型创建不同的文件夹
                string dataTypeName = GetDataTypeName(salesFlowDataType);
                string targetFolder = Config.File.DDIInterfaceFilePath;

                if (!Directory.Exists(targetFolder))
                {
                    Directory.CreateDirectory(targetFolder);
                }

                // 解析XML数据
                var xmlDoc = new XmlDocument();
                xmlDoc.LoadXml(dataXml);

                var rootNode = xmlDoc.SelectSingleNode(ClientXMLNode.RootNode);
                if (rootNode == null)
                {
                    return null;
                }

                // 获取头信息
                var headerNode = rootNode.SelectSingleNode(ClientXMLNode.HeaderNode);
                if (headerNode == null)
                {
                    return null;
                }

                var headers = new List<string>();
                foreach (XmlNode child in headerNode.ChildNodes)
                {
                    if (!string.IsNullOrEmpty(child.InnerText))
                    {
                        headers.Add(child.InnerText);
                    }
                }

                if (headers.Count == 0)
                {
                    return null;
                }

                // 生成CSV文件
                string fileName = $"{dataTypeName}_{receiverCode}_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                string filePath = Path.Combine(targetFolder, fileName);

                // 写入CSV文件
                using (var writer = new StreamWriter(filePath, false, Encoding.UTF8))
                {
                    // 写入表头
                    writer.WriteLine(string.Join(",", headers.Select(h => EscapeCsvField(h))));

                    // 写入数据行
                    var dataNodes = rootNode.SelectNodes(ClientXMLNode.DataNode);
                    if (dataNodes != null)
                    {
                        foreach (XmlNode dataNode in dataNodes)
                        {
                            var values = new List<string>();

                            // 按照头信息的顺序获取对应的字段值
                            for (int i = 0; i < headers.Count; i++)
                            {
                                string fieldName = $"{ClientXMLNode.ItemNode}{i + 1}";
                                var fieldNode = dataNode.SelectSingleNode(fieldName);
                                string fieldValue = fieldNode?.InnerText ?? "";
                                values.Add(EscapeCsvField(fieldValue));
                            }

                            writer.WriteLine(string.Join(",", values));
                        }
                    }
                }

                // 返回生成的文件路径
                return filePath;
            }
            catch (Exception ex)
            {
                // 记录错误日志，但不影响主流程
                NLogHelper.Error(ex, "XML转CSV失败", ex.Message);
                return null;
            }
        }

        /// <summary>
        /// 获取数据类型名称
        /// </summary>
        /// <param name="salesFlowDataType">数据类型</param>
        /// <returns>数据类型名称</returns>
        private string GetDataTypeName(SalesFlowDataType salesFlowDataType)
        {
            return salesFlowDataType switch
            {
                SalesFlowDataType.B => "Purchase", // 购进
                SalesFlowDataType.S => "Sales",    // 销售
                SalesFlowDataType.I => "Inventory", // 库存
                _ => "Unknown"
            };
        }

        /// <summary>
        /// 转义CSV字段值
        /// </summary>
        /// <param name="field">字段值</param>
        /// <returns>转义后的字段值</returns>
        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
            {
                return "";
            }

            // 如果字段包含逗号、双引号或换行符，需要用双引号包围
            if (field.Contains(",") || field.Contains("\"") || field.Contains("\n") || field.Contains("\r"))
            {
                // 将字段中的双引号转义为两个双引号
                field = field.Replace("\"", "\"\"");
                return $"\"{field}\"";
            }

            return field;
        }

    }
}