﻿CREATE TABLE [dbo].[DistributorPurchaseMonthly]
(
	[ID]  UNIQUEIDENTIFIER            NOT NULL  CONSTRAINT [Default_DistributorPurchaseMonthly_ID] DEFAULT (NEWSEQUENTIALID()), 
	[DistributorId] UNIQUEIDENTIFIER NOT NULL,
    [ManufacturerId] UNIQUEIDENTIFIER NOT NULL,
	[ProductId] UNIQUEIDENTIFIER NOT NULL,
	[ProductSpecId] UNIQUEIDENTIFIER NULL,
	[PurchaseDate] [datetime] NOT NULL,
	[Quantity] [decimal](18, 2) NOT NULL,
	[UpstreamDistributorId] UNIQUEIDENTIFIER NOT NULL,
	[Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL, 
    [CreateTime]                DATETIME                    NULL, 
    [LastEditor]                NVARCHAR(50)                NULL, 
    [LastEditTime]              DATETIME                    NULL, 
	CONSTRAINT [PK_DistributorPurchaseMonthly] PRIMARY KEY CLUSTERED ([ID]),
	CONSTRAINT [FK_DistributorPurchaseMonthly_Receiver_00_UpstreamDistributor] FOREIGN KEY ([UpstreamDistributorId]) REFERENCES [dbo].[Receiver] ([ID]),
    CONSTRAINT [FK_DistributorPurchaseMonthly_Receiver_01_Distributor] FOREIGN KEY ([DistributorId]) REFERENCES [dbo].[Receiver] ([ID]),
    CONSTRAINT [FK_DistributorPurchaseMonthly_Manufacturer] FOREIGN KEY ([ManufacturerId]) REFERENCES [dbo].[Manufacturer] ([ID]),
	CONSTRAINT [FK_DistributorPurchaseMonthly_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ID]),
	CONSTRAINT [FK_DistributorPurchaseMonthly_ProductSpec] FOREIGN KEY ([ProductSpecId]) REFERENCES [dbo].[ProductSpec] ([ID]),
)
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'月采购',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorPurchaseMonthly',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorPurchaseMonthly',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'商业Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorPurchaseMonthly',
    @level2type = N'COLUMN',
    @level2name = 'DistributorId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'产品Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorPurchaseMonthly',
    @level2type = N'COLUMN',
    @level2name = N'ProductId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'规格Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorPurchaseMonthly',
    @level2type = N'COLUMN',
    @level2name = N'ProductSpecId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'采购日期',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorPurchaseMonthly',
    @level2type = N'COLUMN',
    @level2name = N'PurchaseDate'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'数量',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorPurchaseMonthly',
    @level2type = N'COLUMN',
    @level2name = N'Quantity'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'上游商业',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorPurchaseMonthly',
    @level2type = N'COLUMN',
    @level2name = N'UpstreamDistributorId'
GO