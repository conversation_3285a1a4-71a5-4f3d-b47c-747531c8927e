﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public partial class Company : IUid<Company>, IHashCache
    {
        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent


        #region IHashCache

        [NotMapped, XmlIgnore, JsonIgnore]
        public string Field => this.ID.ToString();

        #endregion IHashCache
    }
}
