<script setup lang="ts">
import { authorizeApi } from "@/api/authorize";
import { Sort } from "element-plus";

defineOptions({
  name: "employee:view:roles"
});

const employeeId = defineModel<string>("employeeId");

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    datas: []
  },
  // loading：控制变量
  loading: {
    list: false
  },
  // 列表相关配置
  list: {
    // 高度
    height: 250,
    // 默认排序
    defaultSort: reactive<Sort>({ prop: "name", order: "ascending" })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  datas: cfg.default.datas
});

/**
 * 初始化组件
 */
const init = () => {
  // 防止之前打开的数据残留，因此初始化时赋予state默认值
  initState();
  getList();
};

/**
 * 获取列表数据
 */
const getList = () => {
  if (employeeId.value) {
    cfg.loading.list = true;
    authorizeApi
      .GetEmployeeRoles(employeeId.value)
      .then(res => {
        if (res.success) {
          state.datas = res.data;
        }
      })
      .finally(() => {
        cfg.loading.list = false;
      });
  }
};

/**
 * 初始化state
 */
const initState = () => {
  state.datas = cfg.default.datas;
};

/**
 * 清空
 */
const clear = () => {
  initState();
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  init,
  clear
});
</script>

<template>
  <div class="list-container">
    <el-table
      v-loading="cfg.loading.list"
      :data="state.datas"
      :height="cfg.list.height"
      :default-sort="cfg.list.defaultSort"
      row-key="id"
      stripe
      border
      class-name="list"
      style="width: 100%"
    >
      <template #empty>
        <NoDatas />
      </template>
      <el-table-column fixed type="index" label="序号" show-overflow-tooltip width="60" />
      <el-table-column fixed sortable prop="name" label="名称" width="200" />
      <el-table-column sortable sort-by="enumFlags" prop="enumFlagsDesc" label="标签" width="200" />
      <el-table-column sortable prop="remark" label="备注" min-width="200" />
    </el-table>
  </div>
</template>
