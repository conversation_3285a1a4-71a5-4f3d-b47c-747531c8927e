﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    /// <summary>
    /// 业务主数据维护
    /// </summary>
    [ApiExplorerSettings(GroupName = "数据维护")]
    public class BizMasterDataController : BaseApiController<BizMasterDataBll>
    {
        #region 业务字典

        /// <summary>
        /// 查询业务字典
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Business.BizDict_Query)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage_Add)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage_Edit)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage_Delete)]
        [LogApi(ApiType.Query, Operate = "查询业务字典")]
        public QueryResult<DictQuery> QueryBizDict([FromQuery] DictFilter filter)
        {
            var exps = this.NewExps<Dict>(p => p.EnumFlags.HasFlag(DictFlag.Biz));

            if (filter.ParentId.HasValue)
            {
                exps.And(p => p.ParentId == filter.ParentId.Value);
            }
            else
            {
                exps.And(p => !p.ParentId.HasValue);
            }

            if (filter.Order.IsEmpty())
            {
                // 如果查询子字典项，按Ordinal排序；否则按创建时间倒序
                if (filter.ParentId.HasValue)
                {
                    filter.Order = $"{Dict.Columns.Ordinal} ASC";
                }
                else
                {
                    filter.Order = $"{Dict.Columns.CreateTime} DESC";
                }
            }

            return this.Repo.GetDynamicQuery<Dict, DictQuery>(filter, exps);
        }

        /// <summary>
        /// 获取业务字典
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Business.BizDict_Query)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage_Add)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage_Edit)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage_Delete)]
        [LogApi(ApiType.Query, Operate = "获取业务字典")]
        public BizResult<DictModel> GetBizDict([FromQuery] Guid id)
        {
            var result = new BizResult<DictModel>();

            var entity = this.Repo.Get<Dict>(id);

            if (entity == null)
            {
                result.Error("业务字典不存在");
            }
            else if (!entity.EnumFlags.HasFlag(DictFlag.Biz))
            {
                result.Error("该字典项并非业务字典");
            }
            else
            {
                var model = entity.Map<DictModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增业务字典
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增业务字典")]
        public BizResult<DictModel> AddBizDict(DictModel model)
        {
            var entity = model.Map<Dict>();

            var result = this.Repo.AddBizDict(entity);

            return result.Map<DictModel>();
        }

        /// <summary>
        /// 编辑业务字典
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑业务字典")]
        public BizResult<DictModel> UpdateBizDict(DictModel model)
        {
            var entity = model.Map<Dict>();

            var result = this.Repo.UpdateBizDict(entity);

            return result.Map<DictModel>();
        }

        /// <summary>
        /// 删除业务字典
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除业务字典")]
        public BizResult DeleteBizDict(DictModel model)
        {
            return this.Repo.DeleteBizDict(model.ID);
        }

        /// <summary>
        /// 新增业务字典及其子项
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增业务字典及其子项")]
        public BizResult<DictModel> AddDictWithChildren(Models.ParentDictWithChildrenModel model)
        {
            var dto = new Bll.ParentDictWithChildrenModel
            {
                ParentDict = model.ParentDict.Map<Dict>(),
                Children = model.Children.Select(c => c.Map<Dict>()).ToList()
            };

            var result = this.Repo.AddDictWithChildren(dto);

            // 将结果转换为API模型
            var apiResult = new BizResult<DictModel>();

            if (result.Success && result.Data != null)
            {
                apiResult.Data = result.Data.Map<DictModel>();
            }
            else if (!result.Success)
            {
                // 复制错误消息
                foreach (var message in result.Messages)
                {
                    apiResult.Error(message);
                }
            }

            return apiResult;
        }

        /// <summary>
        /// 编辑业务字典及其子项
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑业务字典及其子项")]
        public BizResult<DictModel> UpdateDictWithChildren(Models.ParentDictWithChildrenModel model)
        {
            var dto = new Bll.ParentDictWithChildrenModel
            {
                ParentDict = model.ParentDict.Map<Dict>(),
                Children = model.Children.Select(c => c.Map<Dict>()).ToList()
            };

            var result = this.Repo.UpdateDictWithChildren(dto);

            // 将结果转换为API模型
            var apiResult = new BizResult<DictModel>();

            if (result.Success && result.Data != null)
            {
                apiResult.Data = result.Data.Map<DictModel>();
            }
            else if (!result.Success)
            {
                // 复制错误消息
                foreach (var message in result.Messages)
                {
                    apiResult.Error(message);
                }
            }

            return apiResult;
        }

        /// <summary>
        /// 删除字典项并重新排序
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage)]
        [Auth(AuthCodes.MasterData.Business.BizDict_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除字典项并重新排序")]
        public BizResult DeleteDictItem([FromQuery] Guid id)
        {
            return this.Repo.DeleteDictItem(id);
        }

        #endregion 业务字典
    }
}
