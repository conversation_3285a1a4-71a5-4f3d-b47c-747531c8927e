--员工代理权限
CREATE TABLE [dbo].[EmployeeDelegateAuth] (
    [ID]                            UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_EmployeeDelegateAuth_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                     UNIQUEIDENTIFIER            NOT NULL,
    [EmployeeDelegateId]            UNIQUEIDENTIFIER            NOT NULL,
    [AuthId]                        UNIQUEIDENTIFIER            NOT NULL,
    [AllowAllTags]                  BIT                         NULL,
    CONSTRAINT [PK_EmployeeDelegateAuth] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_EmployeeDelegateAuth_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_EmployeeDelegateAuth_EmployeeDelegate_00_EmployeeDelegate] FOREIGN KEY ([EmployeeDelegateId]) REFERENCES [dbo].[EmployeeDelegate] ([ID]),
    CONSTRAINT [FK_EmployeeDelegateAuth_Auth_00_Auth] FOREIGN KEY ([AuthId]) REFERENCES [dbo].[Auth] ([ID]),
)

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'员工代理权限',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'EmployeeDelegateAuth',
    @level2type = NULL,
    @level2name = NULL
