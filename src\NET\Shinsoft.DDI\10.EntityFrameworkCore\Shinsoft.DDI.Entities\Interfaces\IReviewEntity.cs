﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public interface IReviewEntity : IPrimaryKey<Guid>
    {
        int ReviewIndex { get; set; }

        ReviewType ReviewType { get; set; }

        string ReviewOperation { get; set; }

        Guid? ReviewEmployeeId { set; }

        DateTime? ReviewTime { set; }

        string? ReviewRemark { set; }

        ReviewState EnumReviewState { get; set; }

        List<string> IgnoreChangedColumn { get; }

        ReviewExtInfo GetReviewExtInfo(IRepository repo);
    }
}