﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class EmployeeDelegateFilter
    {
        /// <summary>
        /// 被代理员工ID
        /// </summary>
        [DynamicQueryColumn(typeof(EmployeeDelegate), EmployeeDelegate.Columns.EmployeeId, Operation = Operation.Equal)]
        public Guid? EmployeeId { get; set; }

        /// <summary>
        /// 被代理员工姓名
        /// </summary>
        [DynamicQueryColumn(typeof(EmployeeDelegate), EmployeeDelegate.Foreigns.Employee, Employee.Columns.DisplayName, Operation = Operation.StringIntelligence)]
        public string? EmployeeName { get; set; }

        /// <summary>
        /// 代理员工ID
        /// </summary>
        [DynamicQueryColumn(typeof(EmployeeDelegate), EmployeeDelegate.Columns.AgentId, Operation = Operation.Equal)]
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 代理人关键字
        /// </summary>
        [DynamicQueryColumn(typeof(EmployeeDelegate), EmployeeDelegate.Foreigns.Agent, Employee.Columns.LoginName, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(EmployeeDelegate), EmployeeDelegate.Foreigns.Agent, Employee.Columns.DisplayName, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(EmployeeDelegate), EmployeeDelegate.Foreigns.Agent, Employee.Columns.JobNo, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(EmployeeDelegate), EmployeeDelegate.Foreigns.Agent, Employee.Columns.Email, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(EmployeeDelegate), EmployeeDelegate.Foreigns.Agent, Employee.Columns.Title, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(EmployeeDelegate), EmployeeDelegate.Foreigns.Agent, Employee.Columns.Position, Operation = Operation.StringIntelligence)]
        public string? AgentKeywords { get; set; }

        /// <summary>
        /// 代理人姓名
        /// </summary>
        [DynamicQueryColumn(typeof(EmployeeDelegate), EmployeeDelegate.Foreigns.Agent, Employee.Columns.DisplayName, Operation = Operation.StringIntelligence)]
        public string? AgentName { get; set; }

        /// <summary>
        /// 有效性
        /// </summary>
        [DynamicQueryColumn(typeof(EmployeeDelegate), EmployeeDelegate.Columns.Valid, Operation = Operation.In)]
        public List<bool>? Valids { get; set; }

        /// <summary>
        /// 有效日期范围
        /// </summary>
        public List<DateTime?>? Dates { get; set; }
    }
}
