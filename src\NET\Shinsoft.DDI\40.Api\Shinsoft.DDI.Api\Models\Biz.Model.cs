﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码是从模板生成的.
//
//     手动更改此文件可能会导致应用程序出现意外行为。
//     如果重新生成代码，将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.ComponentModel;
using System.Text.Json.Serialization;
using Shinsoft.Core;
using Shinsoft.Core.AutoMapper;
using Shinsoft.Core.DynamicQuery;
using Shinsoft.Core.NLog;
using Shinsoft.DDI.Entities;
using Shinsoft.Core.Mail;

#pragma warning disable CS8669

namespace Shinsoft.DDI.Api.Models
{

	#region Announcement

    /// <summary>
    /// 公告
    /// </summary>
	[Description("公告")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Announcement), Reverse = true)]
	public abstract class AnnouncementRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Announcement);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 标签
        /// </summary>
		[Description("标签")]
        public virtual AnnouncementFlag EnumFlags { get; set; }

		/// <summary>
        /// 标签
        /// </summary>
		[Description("标签")]
		public virtual string EnumFlagsDesc => this.EnumFlags.GetDesc();

		/// <summary>
        /// 重要性
        /// </summary>
		[Description("重要性")]
        public virtual Important EnumImportant { get; set; }

		/// <summary>
        /// 重要性
        /// </summary>
		[Description("重要性")]
		public virtual string EnumImportantDesc => this.EnumImportant.GetDesc();

		/// <summary>
        /// 标题
        /// </summary>
		[Description("标题")]
        public virtual string Subject { get; set; } = string.Empty;

		/// <summary>
        /// 是否显示
        /// </summary>
		[Description("是否显示")]
        public virtual bool IsShow { get; set; }

		/// <summary>
        /// 开始时间
        /// </summary>
		[Description("开始时间")]
        public virtual DateTime? StartTime { get; set; }

		/// <summary>
        /// 结束时间
        /// </summary>
		[Description("结束时间")]
        public virtual DateTime? EndTime { get; set; }

		/// <summary>
        /// 发布时间
        /// </summary>
		[Description("发布时间")]
        public virtual DateTime? PublishTime { get; set; }

    }

    /// <summary>
    /// 公告
    /// </summary>
	[Description("公告")]
	public abstract partial class AnnouncementMeta : AnnouncementRaw
	{
	}

    /// <summary>
    /// 公告
    /// </summary>
	[Description("公告")]
	public partial class AnnouncementModel : AnnouncementMeta
	{
	}

    /// <summary>
    /// 公告
    /// </summary>
	[Description("公告")]
	public partial class AnnouncementQuery : AnnouncementMeta
	{
	}

    /// <summary>
    /// 公告
    /// </summary>
	[Description("公告")]
	public partial class AnnouncementSelector : AnnouncementMeta
	{
	}

    /// <summary>
    /// 公告查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Announcement))]
	public partial class AnnouncementFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 公告选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Announcement))]
	public partial class AnnouncementSelectorFilter : PagingFilterModel
	{
	}

	#endregion Announcement


	#region AnnouncementContent

    /// <summary>
    /// 公告内容
    /// </summary>
	[Description("公告内容")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.AnnouncementContent), Reverse = true)]
	public abstract class AnnouncementContentRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(AnnouncementContent);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 内容
        /// </summary>
		[Description("内容")]
        public virtual string Content { get; set; } = string.Empty;

    }

    /// <summary>
    /// 公告内容
    /// </summary>
	[Description("公告内容")]
	public abstract partial class AnnouncementContentMeta : AnnouncementContentRaw
	{
	}

    /// <summary>
    /// 公告内容
    /// </summary>
	[Description("公告内容")]
	public partial class AnnouncementContentModel : AnnouncementContentMeta
	{
	}

    /// <summary>
    /// 公告内容
    /// </summary>
	[Description("公告内容")]
	public partial class AnnouncementContentQuery : AnnouncementContentMeta
	{
	}

    /// <summary>
    /// 公告内容
    /// </summary>
	[Description("公告内容")]
	public partial class AnnouncementContentSelector : AnnouncementContentMeta
	{
	}

    /// <summary>
    /// 公告内容查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.AnnouncementContent))]
	public partial class AnnouncementContentFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 公告内容选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.AnnouncementContent))]
	public partial class AnnouncementContentSelectorFilter : PagingFilterModel
	{
	}

	#endregion AnnouncementContent


	#region Attachment

    /// <summary>
    /// 附件
    /// </summary>
	[Description("附件")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Attachment), Reverse = true)]
	public abstract class AttachmentRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Attachment);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 文件索引ID
        /// </summary>
		[Description("文件索引ID")]
        public virtual Guid FileIndexId { get; set; }

        public virtual Guid? SrcAttachmentId { get; set; }

        public virtual AttachmentType EnumType { get; set; }

		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

        public virtual AttachmentFlag EnumFlags { get; set; }

		public virtual string EnumFlagsDesc => this.EnumFlags.GetDesc();

		/// <summary>
        /// 所属对象类型
        /// </summary>
		[Description("所属对象类型")]
        public virtual string ObjectType { get; set; } = string.Empty;

		/// <summary>
        /// 所属对象ID
        /// </summary>
		[Description("所属对象ID")]
        public virtual string ObjectId { get; set; } = string.Empty;

        public virtual string GroupType { get; set; } = string.Empty;

		/// <summary>
        /// 所属对象组ID
        /// </summary>
		[Description("所属对象组ID")]
        public virtual string GroupId { get; set; } = string.Empty;

        public virtual string ItemType { get; set; } = string.Empty;

		/// <summary>
        /// 所属对象条目ID
        /// </summary>
		[Description("所属对象条目ID")]
        public virtual string ItemId { get; set; } = string.Empty;

		/// <summary>
        /// 额外分组
        /// </summary>
		[Description("额外分组")]
        public virtual string ExtGroup { get; set; } = string.Empty;

		/// <summary>
        /// 文件大小
        /// </summary>
		[Description("文件大小")]
        public virtual long FileSize { get; set; }

		/// <summary>
        /// 文件类型
        /// </summary>
		[Description("文件类型")]
        public virtual string ContentType { get; set; } = string.Empty;

		/// <summary>
        /// 文件名
        /// </summary>
		[Description("文件名")]
        public virtual string FileName { get; set; } = string.Empty;

		/// <summary>
        /// 文件扩展名
        /// </summary>
		[Description("文件扩展名")]
        public virtual string FileExt { get; set; } = string.Empty;

    }

    /// <summary>
    /// 附件
    /// </summary>
	[Description("附件")]
	public abstract partial class AttachmentMeta : AttachmentRaw
	{
	}

    /// <summary>
    /// 附件
    /// </summary>
	[Description("附件")]
	public partial class AttachmentModel : AttachmentMeta
	{
	}

    /// <summary>
    /// 附件
    /// </summary>
	[Description("附件")]
	public partial class AttachmentQuery : AttachmentMeta
	{
	}

    /// <summary>
    /// 附件
    /// </summary>
	[Description("附件")]
	public partial class AttachmentSelector : AttachmentMeta
	{
	}

    /// <summary>
    /// 附件查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Attachment))]
	public partial class AttachmentFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 附件选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Attachment))]
	public partial class AttachmentSelectorFilter : PagingFilterModel
	{
	}

	#endregion Attachment


	#region Auth

    /// <summary>
    /// 权限
    /// </summary>
	[Description("权限")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Auth), Reverse = true)]
	public abstract class AuthRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Auth);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid RootId { get; set; }

        public virtual Guid? ParentId { get; set; }

        [MapFromProperty(typeof(Shinsoft.DDI.Entities.Auth), Reverse = false)]
        public virtual int Uid { get; set; }

        [MapFromProperty(typeof(Shinsoft.DDI.Entities.Auth), Reverse = false)]
        public virtual string UidPath { get; set; } = string.Empty;

        public virtual ProgramFlag EnumProgramFlags { get; set; }

		public virtual string EnumProgramFlagsDesc => this.EnumProgramFlags.GetDesc();

        public virtual AuthType EnumType { get; set; }

		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

        public virtual AuthFlag EnumFlags { get; set; }

		public virtual string EnumFlagsDesc => this.EnumFlags.GetDesc();

        public virtual AuthTagType EnumAuthTagType { get; set; }

		public virtual string EnumAuthTagTypeDesc => this.EnumAuthTagType.GetDesc();

        public virtual string Code { get; set; } = string.Empty;

        public virtual string Name { get; set; } = string.Empty;

        public virtual int Rank { get; set; }

        public virtual bool Valid { get; set; }

        public virtual int Ordinal { get; set; }

        public virtual string Remark { get; set; } = string.Empty;

    }

    /// <summary>
    /// 权限
    /// </summary>
	[Description("权限")]
	public abstract partial class AuthMeta : AuthRaw
	{
	}

    /// <summary>
    /// 权限
    /// </summary>
	[Description("权限")]
	public partial class AuthModel : AuthMeta
	{
	}

    /// <summary>
    /// 权限
    /// </summary>
	[Description("权限")]
	public partial class AuthQuery : AuthMeta
	{
	}

    /// <summary>
    /// 权限
    /// </summary>
	[Description("权限")]
	public partial class AuthSelector : AuthMeta
	{
	}

    /// <summary>
    /// 权限查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Auth))]
	public partial class AuthFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 权限选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Auth))]
	public partial class AuthSelectorFilter : PagingFilterModel
	{
	}

	#endregion Auth


	#region AuthTag

    [MapFromType(typeof(Shinsoft.DDI.Entities.AuthTag), Reverse = true)]
	public abstract class AuthTagRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(AuthTag);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual AuthTagType EnumType { get; set; }

		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

        public virtual ProgramFlag EnumProgramFlags { get; set; }

		public virtual string EnumProgramFlagsDesc => this.EnumProgramFlags.GetDesc();

        public virtual string Code { get; set; } = string.Empty;

        public virtual string Name { get; set; } = string.Empty;

        public virtual int Ordinal { get; set; }

        public virtual string Remark { get; set; } = string.Empty;

    }

	public abstract partial class AuthTagMeta : AuthTagRaw
	{
	}

	public partial class AuthTagModel : AuthTagMeta
	{
	}

	public partial class AuthTagQuery : AuthTagMeta
	{
	}

	public partial class AuthTagSelector : AuthTagMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.AuthTag))]
	public partial class AuthTagFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.AuthTag))]
	public partial class AuthTagSelectorFilter : PagingFilterModel
	{
	}

	#endregion AuthTag


	#region BizGroup

    [MapFromType(typeof(Shinsoft.DDI.Entities.BizGroup), Reverse = true)]
	public abstract class BizGroupRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(BizGroup);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual BizGroupType EnumType { get; set; }

		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

        public virtual string Code { get; set; } = string.Empty;

        public virtual string Name { get; set; } = string.Empty;

        public virtual string Remark { get; set; } = string.Empty;

    }

	public abstract partial class BizGroupMeta : BizGroupRaw
	{
	}

	public partial class BizGroupModel : BizGroupMeta
	{
	}

	public partial class BizGroupQuery : BizGroupMeta
	{
	}

	public partial class BizGroupSelector : BizGroupMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.BizGroup))]
	public partial class BizGroupFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.BizGroup))]
	public partial class BizGroupSelectorFilter : PagingFilterModel
	{
	}

	#endregion BizGroup


	#region BizGroupMember

    [MapFromType(typeof(Shinsoft.DDI.Entities.BizGroupMember), Reverse = true)]
	public abstract class BizGroupMemberRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(BizGroupMember);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid BizGroupId { get; set; }

        public virtual Guid? BizGroupInstId { get; set; }

        public virtual BizGroupMemberType EnumType { get; set; }

		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

        public virtual Guid MemberId { get; set; }

    }

	public abstract partial class BizGroupMemberMeta : BizGroupMemberRaw
	{
	}

	public partial class BizGroupMemberModel : BizGroupMemberMeta
	{
	}

	public partial class BizGroupMemberQuery : BizGroupMemberMeta
	{
	}

	public partial class BizGroupMemberSelector : BizGroupMemberMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.BizGroupMember))]
	public partial class BizGroupMemberFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.BizGroupMember))]
	public partial class BizGroupMemberSelectorFilter : PagingFilterModel
	{
	}

	#endregion BizGroupMember


	#region BizMail

    [MapFromType(typeof(Shinsoft.DDI.Entities.BizMail), Reverse = true)]
	public abstract class BizMailRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(BizMail);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual MailTrigger EnumMailTrigger { get; set; }

		public virtual string EnumMailTriggerDesc => this.EnumMailTrigger.GetDesc();

        public virtual BizType EnumBizType { get; set; }

		public virtual string EnumBizTypeDesc => this.EnumBizType.GetDesc();

        public virtual Guid? BizId { get; set; }

        public virtual DateTime? LastGeneratedTime { get; set; }

    }

	public abstract partial class BizMailMeta : BizMailRaw
	{
	}

	public partial class BizMailModel : BizMailMeta
	{
	}

	public partial class BizMailQuery : BizMailMeta
	{
	}

	public partial class BizMailSelector : BizMailMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.BizMail))]
	public partial class BizMailFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.BizMail))]
	public partial class BizMailSelectorFilter : PagingFilterModel
	{
	}

	#endregion BizMail


	#region Calendar

    /// <summary>
    /// 日历
    /// </summary>
	[Description("日历")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Calendar), Reverse = true)]
	public abstract class CalendarRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Calendar);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 日期
        /// </summary>
		[Description("日期")]
        public virtual DateTime Date { get; set; }

		/// <summary>
        /// 类型
        /// </summary>
		[Description("类型")]
        public virtual CalendarType EnumType { get; set; }

		/// <summary>
        /// 类型
        /// </summary>
		[Description("类型")]
		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

		/// <summary>
        /// 年
        /// </summary>
		[Description("年")]
        public virtual int Year { get; set; }

		/// <summary>
        /// 月
        /// </summary>
		[Description("月")]
        public virtual int Month { get; set; }

		/// <summary>
        /// 日
        /// </summary>
		[Description("日")]
        public virtual int Day { get; set; }

        public virtual int DayOfWeek { get; set; }

        public virtual int DayOfYear { get; set; }

        public virtual int WeekOfMonth { get; set; }

        public virtual int WeekOfYear { get; set; }

        public virtual string Remark { get; set; } = string.Empty;

    }

    /// <summary>
    /// 日历
    /// </summary>
	[Description("日历")]
	public abstract partial class CalendarMeta : CalendarRaw
	{
	}

    /// <summary>
    /// 日历
    /// </summary>
	[Description("日历")]
	public partial class CalendarModel : CalendarMeta
	{
	}

    /// <summary>
    /// 日历
    /// </summary>
	[Description("日历")]
	public partial class CalendarQuery : CalendarMeta
	{
	}

    /// <summary>
    /// 日历
    /// </summary>
	[Description("日历")]
	public partial class CalendarSelector : CalendarMeta
	{
	}

    /// <summary>
    /// 日历查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Calendar))]
	public partial class CalendarFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 日历选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Calendar))]
	public partial class CalendarSelectorFilter : PagingFilterModel
	{
	}

	#endregion Calendar


	#region City

    /// <summary>
    /// 城市
    /// </summary>
	[Description("城市")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.City), Reverse = true)]
	public abstract class CityRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(City);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 城市编码
        /// </summary>
		[Description("城市编码")]
        public virtual string? Code { get; set; }

		/// <summary>
        /// 城市名称
        /// </summary>
		[Description("城市名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 城市简称
        /// </summary>
		[Description("城市简称")]
        public virtual string? ShortName { get; set; }

		/// <summary>
        /// 省份ID
        /// </summary>
		[Description("省份ID")]
        public virtual Guid? ProvinceId { get; set; }

    }

    /// <summary>
    /// 城市
    /// </summary>
	[Description("城市")]
	public abstract partial class CityMeta : CityRaw
	{
	}

    /// <summary>
    /// 城市
    /// </summary>
	[Description("城市")]
	public partial class CityModel : CityMeta
	{
	}

    /// <summary>
    /// 城市
    /// </summary>
	[Description("城市")]
	public partial class CityQuery : CityMeta
	{
	}

    /// <summary>
    /// 城市
    /// </summary>
	[Description("城市")]
	public partial class CitySelector : CityMeta
	{
	}

    /// <summary>
    /// 城市查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.City))]
	public partial class CityFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 城市选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.City))]
	public partial class CitySelectorFilter : PagingFilterModel
	{
	}

	#endregion City


	#region ColumnMapping

    /// <summary>
    /// 列映射
    /// </summary>
	[Description("列映射")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ColumnMapping), Reverse = true)]
	public abstract class ColumnMappingRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ColumnMapping);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 收货方Id
        /// </summary>
		[Description("收货方Id")]
        public virtual Guid ReceiverId { get; set; }

		/// <summary>
        /// 字段名称
        /// </summary>
		[Description("字段名称")]
        public virtual string DbFiled { get; set; } = string.Empty;

		/// <summary>
        /// 模版Id
        /// </summary>
		[Description("模版Id")]
        public virtual Guid? ColumnMappingTemplateId { get; set; }

		/// <summary>
        /// 日期格式化类型
        /// </summary>
		[Description("日期格式化类型")]
        public virtual string? DateFormatType { get; set; }

    }

    /// <summary>
    /// 列映射
    /// </summary>
	[Description("列映射")]
	public abstract partial class ColumnMappingMeta : ColumnMappingRaw
	{
	}

    /// <summary>
    /// 列映射
    /// </summary>
	[Description("列映射")]
	public partial class ColumnMappingModel : ColumnMappingMeta
	{
	}

    /// <summary>
    /// 列映射
    /// </summary>
	[Description("列映射")]
	public partial class ColumnMappingQuery : ColumnMappingMeta
	{
	}

    /// <summary>
    /// 列映射
    /// </summary>
	[Description("列映射")]
	public partial class ColumnMappingSelector : ColumnMappingMeta
	{
	}

    /// <summary>
    /// 列映射查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ColumnMapping))]
	public partial class ColumnMappingFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 列映射选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ColumnMapping))]
	public partial class ColumnMappingSelectorFilter : PagingFilterModel
	{
	}

	#endregion ColumnMapping


	#region ColumnMappingTemplate

    /// <summary>
    /// 列映射模版
    /// </summary>
	[Description("列映射模版")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ColumnMappingTemplate), Reverse = true)]
	public abstract class ColumnMappingTemplateRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ColumnMappingTemplate);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 字段名称
        /// </summary>
		[Description("字段名称")]
        public virtual string FieldName { get; set; } = string.Empty;

		/// <summary>
        /// 数据库字段名称
        /// </summary>
		[Description("数据库字段名称")]
        public virtual string DbFiled { get; set; } = string.Empty;

		/// <summary>
        /// 格式
        /// </summary>
		[Description("格式")]
        public virtual FieldType EnumFieldType { get; set; }

		/// <summary>
        /// 格式
        /// </summary>
		[Description("格式")]
		public virtual string EnumFieldTypeDesc => this.EnumFieldType.GetDesc();

		/// <summary>
        /// 是否必填
        /// </summary>
		[Description("是否必填")]
        public virtual bool IsRequired { get; set; }

		/// <summary>
        /// DDI类型
        /// </summary>
		[Description("DDI类型")]
        public virtual DDIType EnumDDIType { get; set; }

		/// <summary>
        /// DDI类型
        /// </summary>
		[Description("DDI类型")]
		public virtual string EnumDDITypeDesc => this.EnumDDIType.GetDesc();

    }

    /// <summary>
    /// 列映射模版
    /// </summary>
	[Description("列映射模版")]
	public abstract partial class ColumnMappingTemplateMeta : ColumnMappingTemplateRaw
	{
	}

    /// <summary>
    /// 列映射模版
    /// </summary>
	[Description("列映射模版")]
	public partial class ColumnMappingTemplateModel : ColumnMappingTemplateMeta
	{
	}

    /// <summary>
    /// 列映射模版
    /// </summary>
	[Description("列映射模版")]
	public partial class ColumnMappingTemplateQuery : ColumnMappingTemplateMeta
	{
	}

    /// <summary>
    /// 列映射模版
    /// </summary>
	[Description("列映射模版")]
	public partial class ColumnMappingTemplateSelector : ColumnMappingTemplateMeta
	{
	}

    /// <summary>
    /// 列映射模版查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ColumnMappingTemplate))]
	public partial class ColumnMappingTemplateFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 列映射模版选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ColumnMappingTemplate))]
	public partial class ColumnMappingTemplateSelectorFilter : PagingFilterModel
	{
	}

	#endregion ColumnMappingTemplate


	#region Company

    /// <summary>
    /// 公司
    /// </summary>
	[Description("公司")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Company), Reverse = true)]
	public abstract class CompanyRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Company);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 集团公司ID
        /// </summary>
		[Description("集团公司ID")]
        public virtual Guid GroupId { get; set; }

		/// <summary>
        /// 母公司ID
        /// </summary>
		[Description("母公司ID")]
        public virtual Guid? ParentId { get; set; }

		/// <summary>
        /// 唯一码
        /// </summary>
		[Description("唯一码")]
        [MapFromProperty(typeof(Shinsoft.DDI.Entities.Company), Reverse = false)]
        public virtual int Uid { get; set; }

		/// <summary>
        /// 唯一码路径
        /// </summary>
		[Description("唯一码路径")]
        [MapFromProperty(typeof(Shinsoft.DDI.Entities.Company), Reverse = false)]
        public virtual string UidPath { get; set; } = string.Empty;

		/// <summary>
        /// 等级
        /// </summary>
		[Description("等级")]
        public virtual int Rank { get; set; }

		/// <summary>
        /// 类型
        /// </summary>
		[Description("类型")]
        public virtual CompanyType EnumType { get; set; }

		/// <summary>
        /// 类型
        /// </summary>
		[Description("类型")]
		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

		/// <summary>
        /// 编码
        /// </summary>
		[Description("编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 名称
        /// </summary>
		[Description("名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 简称
        /// </summary>
		[Description("简称")]
        public virtual string ShortName { get; set; } = string.Empty;

		/// <summary>
        /// 本位币
        /// </summary>
		[Description("本位币")]
        public virtual string StdCurrency { get; set; } = string.Empty;

        public virtual bool Valid { get; set; }

    }

    /// <summary>
    /// 公司
    /// </summary>
	[Description("公司")]
	public abstract partial class CompanyMeta : CompanyRaw
	{
	}

    /// <summary>
    /// 公司
    /// </summary>
	[Description("公司")]
	public partial class CompanyModel : CompanyMeta
	{
	}

    /// <summary>
    /// 公司
    /// </summary>
	[Description("公司")]
	public partial class CompanyQuery : CompanyMeta
	{
	}

    /// <summary>
    /// 公司
    /// </summary>
	[Description("公司")]
	public partial class CompanySelector : CompanyMeta
	{
	}

    /// <summary>
    /// 公司查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Company))]
	public partial class CompanyFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 公司选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Company))]
	public partial class CompanySelectorFilter : PagingFilterModel
	{
	}

	#endregion Company


	#region CompanyAuth

    [MapFromType(typeof(Shinsoft.DDI.Entities.CompanyAuth), Reverse = true)]
	public abstract class CompanyAuthRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(CompanyAuth);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid AuthId { get; set; }

        public virtual bool Valid { get; set; }

    }

	public abstract partial class CompanyAuthMeta : CompanyAuthRaw
	{
	}

	public partial class CompanyAuthModel : CompanyAuthMeta
	{
	}

	public partial class CompanyAuthQuery : CompanyAuthMeta
	{
	}

	public partial class CompanyAuthSelector : CompanyAuthMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.CompanyAuth))]
	public partial class CompanyAuthFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.CompanyAuth))]
	public partial class CompanyAuthSelectorFilter : PagingFilterModel
	{
	}

	#endregion CompanyAuth


	#region CompanyCfg

    [MapFromType(typeof(Shinsoft.DDI.Entities.CompanyCfg), Reverse = true)]
	public abstract class CompanyCfgRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(CompanyCfg);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string Culture { get; set; } = string.Empty;

        public virtual string Redis { get; set; } = string.Empty;

        public virtual string MailSendType { get; set; } = string.Empty;

        public virtual LoginNameType EnumLoginNameType { get; set; }

		public virtual string EnumLoginNameTypeDesc => this.EnumLoginNameType.GetDesc();

        public virtual string EmailSuffix { get; set; } = string.Empty;

        public virtual string DefaultPwdFormat { get; set; } = string.Empty;

    }

	public abstract partial class CompanyCfgMeta : CompanyCfgRaw
	{
	}

	public partial class CompanyCfgModel : CompanyCfgMeta
	{
	}

	public partial class CompanyCfgQuery : CompanyCfgMeta
	{
	}

	public partial class CompanyCfgSelector : CompanyCfgMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.CompanyCfg))]
	public partial class CompanyCfgFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.CompanyCfg))]
	public partial class CompanyCfgSelectorFilter : PagingFilterModel
	{
	}

	#endregion CompanyCfg


	#region CompanyCulture

    [MapFromType(typeof(Shinsoft.DDI.Entities.CompanyCulture), Reverse = true)]
	public abstract class CompanyCultureRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(CompanyCulture);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string Culture { get; set; } = string.Empty;

        public virtual string Name { get; set; } = string.Empty;

    }

	public abstract partial class CompanyCultureMeta : CompanyCultureRaw
	{
	}

	public partial class CompanyCultureModel : CompanyCultureMeta
	{
	}

	public partial class CompanyCultureQuery : CompanyCultureMeta
	{
	}

	public partial class CompanyCultureSelector : CompanyCultureMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.CompanyCulture))]
	public partial class CompanyCultureFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.CompanyCulture))]
	public partial class CompanyCultureSelectorFilter : PagingFilterModel
	{
	}

	#endregion CompanyCulture


	#region CompanyCurrency

    [MapFromType(typeof(Shinsoft.DDI.Entities.CompanyCurrency), Reverse = true)]
	public abstract class CompanyCurrencyRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(CompanyCurrency);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string Currency { get; set; } = string.Empty;

        public virtual string Name { get; set; } = string.Empty;

        public virtual string? Symbol { get; set; }

        public virtual bool Valid { get; set; }

        public virtual bool IsStandard { get; set; }

    }

	public abstract partial class CompanyCurrencyMeta : CompanyCurrencyRaw
	{
	}

	public partial class CompanyCurrencyModel : CompanyCurrencyMeta
	{
	}

	public partial class CompanyCurrencyQuery : CompanyCurrencyMeta
	{
	}

	public partial class CompanyCurrencySelector : CompanyCurrencyMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.CompanyCurrency))]
	public partial class CompanyCurrencyFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.CompanyCurrency))]
	public partial class CompanyCurrencySelectorFilter : PagingFilterModel
	{
	}

	#endregion CompanyCurrency


	#region CompanySetting

    /// <summary>
    /// 扩展配置
    /// </summary>
	[Description("扩展配置")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.CompanySetting), Reverse = true)]
	public abstract class CompanySettingRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(CompanySetting);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 唯一标识
        /// </summary>
		[Description("唯一标识")]
        public virtual string Key { get; set; } = string.Empty;

		/// <summary>
        /// 名称
        /// </summary>
		[Description("名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 值
        /// </summary>
		[Description("值")]
        public virtual string Value { get; set; } = string.Empty;

		/// <summary>
        /// 备注
        /// </summary>
		[Description("备注")]
        public virtual string Remark { get; set; } = string.Empty;

    }

    /// <summary>
    /// 扩展配置
    /// </summary>
	[Description("扩展配置")]
	public abstract partial class CompanySettingMeta : CompanySettingRaw
	{
	}

    /// <summary>
    /// 扩展配置
    /// </summary>
	[Description("扩展配置")]
	public partial class CompanySettingModel : CompanySettingMeta
	{
	}

    /// <summary>
    /// 扩展配置
    /// </summary>
	[Description("扩展配置")]
	public partial class CompanySettingQuery : CompanySettingMeta
	{
	}

    /// <summary>
    /// 扩展配置
    /// </summary>
	[Description("扩展配置")]
	public partial class CompanySettingSelector : CompanySettingMeta
	{
	}

    /// <summary>
    /// 扩展配置查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.CompanySetting))]
	public partial class CompanySettingFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 扩展配置选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.CompanySetting))]
	public partial class CompanySettingSelectorFilter : PagingFilterModel
	{
	}

	#endregion CompanySetting


	#region CostCenter

    /// <summary>
    /// 成本中心
    /// </summary>
	[Description("成本中心")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.CostCenter), Reverse = true)]
	public abstract class CostCenterRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(CostCenter);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 上级成本中心ID
        /// </summary>
		[Description("上级成本中心ID")]
        public virtual Guid? ParentId { get; set; }

		/// <summary>
        /// 唯一码
        /// </summary>
		[Description("唯一码")]
        [MapFromProperty(typeof(Shinsoft.DDI.Entities.CostCenter), Reverse = false)]
        public virtual int Uid { get; set; }

		/// <summary>
        /// 唯一码路径
        /// </summary>
		[Description("唯一码路径")]
        [MapFromProperty(typeof(Shinsoft.DDI.Entities.CostCenter), Reverse = false)]
        public virtual string UidPath { get; set; } = string.Empty;

		/// <summary>
        /// 分公司ID
        /// </summary>
		[Description("分公司ID")]
        public virtual Guid? SubCompanyId { get; set; }

		/// <summary>
        /// 等级
        /// </summary>
		[Description("等级")]
        public virtual int Rank { get; set; }

		/// <summary>
        /// 编码
        /// </summary>
		[Description("编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 名称
        /// </summary>
		[Description("名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 有效性
        /// </summary>
		[Description("有效性")]
        public virtual bool Valid { get; set; }

    }

    /// <summary>
    /// 成本中心
    /// </summary>
	[Description("成本中心")]
	public abstract partial class CostCenterMeta : CostCenterRaw
	{
	}

    /// <summary>
    /// 成本中心
    /// </summary>
	[Description("成本中心")]
	public partial class CostCenterModel : CostCenterMeta
	{
	}

    /// <summary>
    /// 成本中心
    /// </summary>
	[Description("成本中心")]
	public partial class CostCenterQuery : CostCenterMeta
	{
	}

    /// <summary>
    /// 成本中心
    /// </summary>
	[Description("成本中心")]
	public partial class CostCenterSelector : CostCenterMeta
	{
	}

    /// <summary>
    /// 成本中心查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.CostCenter))]
	public partial class CostCenterFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 成本中心选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.CostCenter))]
	public partial class CostCenterSelectorFilter : PagingFilterModel
	{
	}

	#endregion CostCenter


	#region County

    /// <summary>
    /// 区县
    /// </summary>
	[Description("区县")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.County), Reverse = true)]
	public abstract class CountyRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(County);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 区县编码
        /// </summary>
		[Description("区县编码")]
        public virtual string? Code { get; set; }

		/// <summary>
        /// 区县名称
        /// </summary>
		[Description("区县名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 区县简称
        /// </summary>
		[Description("区县简称")]
        public virtual string? ShortName { get; set; }

		/// <summary>
        /// 城市ID
        /// </summary>
		[Description("城市ID")]
        public virtual Guid? CityId { get; set; }

    }

    /// <summary>
    /// 区县
    /// </summary>
	[Description("区县")]
	public abstract partial class CountyMeta : CountyRaw
	{
	}

    /// <summary>
    /// 区县
    /// </summary>
	[Description("区县")]
	public partial class CountyModel : CountyMeta
	{
	}

    /// <summary>
    /// 区县
    /// </summary>
	[Description("区县")]
	public partial class CountyQuery : CountyMeta
	{
	}

    /// <summary>
    /// 区县
    /// </summary>
	[Description("区县")]
	public partial class CountySelector : CountyMeta
	{
	}

    /// <summary>
    /// 区县查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.County))]
	public partial class CountyFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 区县选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.County))]
	public partial class CountySelectorFilter : PagingFilterModel
	{
	}

	#endregion County


	#region Department

    /// <summary>
    /// 部门
    /// </summary>
	[Description("部门")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Department), Reverse = true)]
	public abstract class DepartmentRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Department);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 上级部门ID
        /// </summary>
		[Description("上级部门ID")]
        public virtual Guid? ParentId { get; set; }

		/// <summary>
        /// 分公司ID
        /// </summary>
		[Description("分公司ID")]
        public virtual Guid SubCompanyId { get; set; }

		/// <summary>
        /// 唯一码
        /// </summary>
		[Description("唯一码")]
        [MapFromProperty(typeof(Shinsoft.DDI.Entities.Department), Reverse = false)]
        public virtual int Uid { get; set; }

		/// <summary>
        /// 唯一码路径
        /// </summary>
		[Description("唯一码路径")]
        [MapFromProperty(typeof(Shinsoft.DDI.Entities.Department), Reverse = false)]
        public virtual string UidPath { get; set; } = string.Empty;

		/// <summary>
        /// 默认成本中心ID
        /// </summary>
		[Description("默认成本中心ID")]
        public virtual Guid? DefaultCostCenterId { get; set; }

		/// <summary>
        /// 标签
        /// </summary>
		[Description("标签")]
        public virtual DepartmentFlag EnumFlags { get; set; }

		/// <summary>
        /// 标签
        /// </summary>
		[Description("标签")]
		public virtual string EnumFlagsDesc => this.EnumFlags.GetDesc();

		/// <summary>
        /// 级别
        /// </summary>
		[Description("级别")]
        public virtual int Rank { get; set; }

		/// <summary>
        /// 编码
        /// </summary>
		[Description("编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 名称
        /// </summary>
		[Description("名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 简称
        /// </summary>
		[Description("简称")]
        public virtual string ShortName { get; set; } = string.Empty;

		/// <summary>
        /// 备注
        /// </summary>
		[Description("备注")]
        public virtual string Remark { get; set; } = string.Empty;

		/// <summary>
        /// 有效性
        /// </summary>
		[Description("有效性")]
        public virtual bool Valid { get; set; }

    }

    /// <summary>
    /// 部门
    /// </summary>
	[Description("部门")]
	public abstract partial class DepartmentMeta : DepartmentRaw
	{
	}

    /// <summary>
    /// 部门
    /// </summary>
	[Description("部门")]
	public partial class DepartmentModel : DepartmentMeta
	{
	}

    /// <summary>
    /// 部门
    /// </summary>
	[Description("部门")]
	public partial class DepartmentQuery : DepartmentMeta
	{
	}

    /// <summary>
    /// 部门
    /// </summary>
	[Description("部门")]
	public partial class DepartmentSelector : DepartmentMeta
	{
	}

    /// <summary>
    /// 部门查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Department))]
	public partial class DepartmentFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 部门选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Department))]
	public partial class DepartmentSelectorFilter : PagingFilterModel
	{
	}

	#endregion Department


	#region DepartmentCostCenter

    [MapFromType(typeof(Shinsoft.DDI.Entities.DepartmentCostCenter), Reverse = true)]
	public abstract class DepartmentCostCenterRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(DepartmentCostCenter);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid DepartmentId { get; set; }

        public virtual Guid CostCenterId { get; set; }

        public virtual bool IsDefault { get; set; }

    }

	public abstract partial class DepartmentCostCenterMeta : DepartmentCostCenterRaw
	{
	}

	public partial class DepartmentCostCenterModel : DepartmentCostCenterMeta
	{
	}

	public partial class DepartmentCostCenterQuery : DepartmentCostCenterMeta
	{
	}

	public partial class DepartmentCostCenterSelector : DepartmentCostCenterMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.DepartmentCostCenter))]
	public partial class DepartmentCostCenterFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.DepartmentCostCenter))]
	public partial class DepartmentCostCenterSelectorFilter : PagingFilterModel
	{
	}

	#endregion DepartmentCostCenter


	#region Dict

    /// <summary>
    /// 字典
    /// </summary>
	[Description("字典")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Dict), Reverse = true)]
	public abstract class DictRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Dict);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 上级字典ID
        /// </summary>
		[Description("上级字典ID")]
        public virtual Guid? ParentId { get; set; }

        public virtual DictFlag EnumFlags { get; set; }

		public virtual string EnumFlagsDesc => this.EnumFlags.GetDesc();

		/// <summary>
        /// 编辑标志
        /// </summary>
		[Description("编辑标志")]
        public virtual EditFlag EnumEditFlags { get; set; }

		/// <summary>
        /// 编辑标志
        /// </summary>
		[Description("编辑标志")]
		public virtual string EnumEditFlagsDesc => this.EnumEditFlags.GetDesc();

		/// <summary>
        /// 子字典项编辑标志
        /// </summary>
		[Description("子字典项编辑标志")]
        public virtual EditFlag EnumEditFlags_Child { get; set; }

		/// <summary>
        /// 子字典项编辑标志
        /// </summary>
		[Description("子字典项编辑标志")]
		public virtual string EnumEditFlags_ChildDesc => this.EnumEditFlags_Child.GetDesc();

		/// <summary>
        /// 编码
        /// </summary>
		[Description("编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 名称
        /// </summary>
		[Description("名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 简称
        /// </summary>
		[Description("简称")]
        public virtual string ShortName { get; set; } = string.Empty;

		/// <summary>
        /// 排序字段
        /// </summary>
		[Description("排序字段")]
        public virtual int Ordinal { get; set; }

		/// <summary>
        /// 备注
        /// </summary>
		[Description("备注")]
        public virtual string Remark { get; set; } = string.Empty;

    }

    /// <summary>
    /// 字典
    /// </summary>
	[Description("字典")]
	public abstract partial class DictMeta : DictRaw
	{
	}

    /// <summary>
    /// 字典
    /// </summary>
	[Description("字典")]
	public partial class DictModel : DictMeta
	{
	}

    /// <summary>
    /// 字典
    /// </summary>
	[Description("字典")]
	public partial class DictQuery : DictMeta
	{
	}

    /// <summary>
    /// 字典
    /// </summary>
	[Description("字典")]
	public partial class DictSelector : DictMeta
	{
	}

    /// <summary>
    /// 字典查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Dict))]
	public partial class DictFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 字典选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Dict))]
	public partial class DictSelectorFilter : PagingFilterModel
	{
	}

	#endregion Dict


	#region DistributorInventoryDaily

    /// <summary>
    /// 月库存
    /// </summary>
	[Description("月库存")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.DistributorInventoryDaily), Reverse = true)]
	public abstract class DistributorInventoryDailyRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(DistributorInventoryDaily);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 商业ID
        /// </summary>
		[Description("商业ID")]
        public virtual Guid DistributorId { get; set; }

        public virtual Guid ManufacturerId { get; set; }

		/// <summary>
        /// 产品ID
        /// </summary>
		[Description("产品ID")]
        public virtual Guid ProductId { get; set; }

		/// <summary>
        /// 规格Id
        /// </summary>
		[Description("规格Id")]
        public virtual Guid? ProductSpecId { get; set; }

		/// <summary>
        /// 数量
        /// </summary>
		[Description("数量")]
        public virtual decimal Quantity { get; set; }

		/// <summary>
        /// 日期
        /// </summary>
		[Description("日期")]
        public virtual DateTime? InventoryDate { get; set; }

    }

    /// <summary>
    /// 月库存
    /// </summary>
	[Description("月库存")]
	public abstract partial class DistributorInventoryDailyMeta : DistributorInventoryDailyRaw
	{
	}

    /// <summary>
    /// 月库存
    /// </summary>
	[Description("月库存")]
	public partial class DistributorInventoryDailyModel : DistributorInventoryDailyMeta
	{
	}

    /// <summary>
    /// 月库存
    /// </summary>
	[Description("月库存")]
	public partial class DistributorInventoryDailyQuery : DistributorInventoryDailyMeta
	{
	}

    /// <summary>
    /// 月库存
    /// </summary>
	[Description("月库存")]
	public partial class DistributorInventoryDailySelector : DistributorInventoryDailyMeta
	{
	}

    /// <summary>
    /// 月库存查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.DistributorInventoryDaily))]
	public partial class DistributorInventoryDailyFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 月库存选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.DistributorInventoryDaily))]
	public partial class DistributorInventoryDailySelectorFilter : PagingFilterModel
	{
	}

	#endregion DistributorInventoryDaily


	#region DistributorPurchaseDaily

    /// <summary>
    /// 日库存
    /// </summary>
	[Description("日库存")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.DistributorPurchaseDaily), Reverse = true)]
	public abstract class DistributorPurchaseDailyRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(DistributorPurchaseDaily);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 商业Id
        /// </summary>
		[Description("商业Id")]
        public virtual Guid DistributorId { get; set; }

        public virtual Guid ManufacturerId { get; set; }

		/// <summary>
        /// 产品Id
        /// </summary>
		[Description("产品Id")]
        public virtual Guid ProductId { get; set; }

		/// <summary>
        /// 规格Id
        /// </summary>
		[Description("规格Id")]
        public virtual Guid? ProductSpecId { get; set; }

		/// <summary>
        /// 采购日期
        /// </summary>
		[Description("采购日期")]
        public virtual DateTime PurchaseDate { get; set; }

		/// <summary>
        /// 数量
        /// </summary>
		[Description("数量")]
        public virtual decimal Quantity { get; set; }

		/// <summary>
        /// 上游商业
        /// </summary>
		[Description("上游商业")]
        public virtual Guid UpstreamDistributorId { get; set; }

    }

    /// <summary>
    /// 日库存
    /// </summary>
	[Description("日库存")]
	public abstract partial class DistributorPurchaseDailyMeta : DistributorPurchaseDailyRaw
	{
	}

    /// <summary>
    /// 日库存
    /// </summary>
	[Description("日库存")]
	public partial class DistributorPurchaseDailyModel : DistributorPurchaseDailyMeta
	{
	}

    /// <summary>
    /// 日库存
    /// </summary>
	[Description("日库存")]
	public partial class DistributorPurchaseDailyQuery : DistributorPurchaseDailyMeta
	{
	}

    /// <summary>
    /// 日库存
    /// </summary>
	[Description("日库存")]
	public partial class DistributorPurchaseDailySelector : DistributorPurchaseDailyMeta
	{
	}

    /// <summary>
    /// 日库存查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.DistributorPurchaseDaily))]
	public partial class DistributorPurchaseDailyFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 日库存选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.DistributorPurchaseDaily))]
	public partial class DistributorPurchaseDailySelectorFilter : PagingFilterModel
	{
	}

	#endregion DistributorPurchaseDaily


	#region DistributorPurchaseMonthly

    /// <summary>
    /// 月采购
    /// </summary>
	[Description("月采购")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.DistributorPurchaseMonthly), Reverse = true)]
	public abstract class DistributorPurchaseMonthlyRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(DistributorPurchaseMonthly);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 商业Id
        /// </summary>
		[Description("商业Id")]
        public virtual Guid DistributorId { get; set; }

        public virtual Guid ManufacturerId { get; set; }

		/// <summary>
        /// 产品Id
        /// </summary>
		[Description("产品Id")]
        public virtual Guid ProductId { get; set; }

		/// <summary>
        /// 规格Id
        /// </summary>
		[Description("规格Id")]
        public virtual Guid? ProductSpecId { get; set; }

		/// <summary>
        /// 采购日期
        /// </summary>
		[Description("采购日期")]
        public virtual DateTime PurchaseDate { get; set; }

		/// <summary>
        /// 数量
        /// </summary>
		[Description("数量")]
        public virtual decimal Quantity { get; set; }

		/// <summary>
        /// 上游商业
        /// </summary>
		[Description("上游商业")]
        public virtual Guid UpstreamDistributorId { get; set; }

    }

    /// <summary>
    /// 月采购
    /// </summary>
	[Description("月采购")]
	public abstract partial class DistributorPurchaseMonthlyMeta : DistributorPurchaseMonthlyRaw
	{
	}

    /// <summary>
    /// 月采购
    /// </summary>
	[Description("月采购")]
	public partial class DistributorPurchaseMonthlyModel : DistributorPurchaseMonthlyMeta
	{
	}

    /// <summary>
    /// 月采购
    /// </summary>
	[Description("月采购")]
	public partial class DistributorPurchaseMonthlyQuery : DistributorPurchaseMonthlyMeta
	{
	}

    /// <summary>
    /// 月采购
    /// </summary>
	[Description("月采购")]
	public partial class DistributorPurchaseMonthlySelector : DistributorPurchaseMonthlyMeta
	{
	}

    /// <summary>
    /// 月采购查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.DistributorPurchaseMonthly))]
	public partial class DistributorPurchaseMonthlyFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 月采购选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.DistributorPurchaseMonthly))]
	public partial class DistributorPurchaseMonthlySelectorFilter : PagingFilterModel
	{
	}

	#endregion DistributorPurchaseMonthly


	#region DistributorSalesFlowDaily

    /// <summary>
    /// 日库存
    /// </summary>
	[Description("日库存")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.DistributorSalesFlowDaily), Reverse = true)]
	public abstract class DistributorSalesFlowDailyRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(DistributorSalesFlowDaily);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

        public virtual string ShipperIds { get; set; } = string.Empty;

		/// <summary>
        /// 发货方Id
        /// </summary>
		[Description("发货方Id")]
        public virtual Guid DistributorId { get; set; }

		/// <summary>
        /// 收货方Id
        /// </summary>
		[Description("收货方Id")]
        public virtual Guid ReceiverId { get; set; }

		/// <summary>
        /// 厂商Id
        /// </summary>
		[Description("厂商Id")]
        public virtual Guid ManufacturerId { get; set; }

		/// <summary>
        /// 产品Id
        /// </summary>
		[Description("产品Id")]
        public virtual Guid ProductId { get; set; }

		/// <summary>
        /// 规格Id
        /// </summary>
		[Description("规格Id")]
        public virtual Guid? ProductSpecId { get; set; }

		/// <summary>
        /// 销售
        /// </summary>
		[Description("销售")]
        public virtual DateTime SaleDate { get; set; }

        public virtual DateTime? ExpireDate { get; set; }

        public virtual Guid ReceiverAliasId { get; set; }

		/// <summary>
        /// 数量
        /// </summary>
		[Description("数量")]
        public virtual decimal Quantity { get; set; }

        public virtual string? OrderNumber { get; set; }

    }

    /// <summary>
    /// 日库存
    /// </summary>
	[Description("日库存")]
	public abstract partial class DistributorSalesFlowDailyMeta : DistributorSalesFlowDailyRaw
	{
	}

    /// <summary>
    /// 日库存
    /// </summary>
	[Description("日库存")]
	public partial class DistributorSalesFlowDailyModel : DistributorSalesFlowDailyMeta
	{
	}

    /// <summary>
    /// 日库存
    /// </summary>
	[Description("日库存")]
	public partial class DistributorSalesFlowDailyQuery : DistributorSalesFlowDailyMeta
	{
	}

    /// <summary>
    /// 日库存
    /// </summary>
	[Description("日库存")]
	public partial class DistributorSalesFlowDailySelector : DistributorSalesFlowDailyMeta
	{
	}

    /// <summary>
    /// 日库存查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.DistributorSalesFlowDaily))]
	public partial class DistributorSalesFlowDailyFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 日库存选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.DistributorSalesFlowDaily))]
	public partial class DistributorSalesFlowDailySelectorFilter : PagingFilterModel
	{
	}

	#endregion DistributorSalesFlowDaily


	#region DistributorSalesFlowMonthly

    /// <summary>
    /// 月库存
    /// </summary>
	[Description("月库存")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.DistributorSalesFlowMonthly), Reverse = true)]
	public abstract class DistributorSalesFlowMonthlyRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(DistributorSalesFlowMonthly);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

        public virtual string ShipperIds { get; set; } = string.Empty;

		/// <summary>
        /// 发货方Id
        /// </summary>
		[Description("发货方Id")]
        public virtual Guid DistributorId { get; set; }

		/// <summary>
        /// 收货方Id
        /// </summary>
		[Description("收货方Id")]
        public virtual Guid ReceiverId { get; set; }

		/// <summary>
        /// 厂商Id
        /// </summary>
		[Description("厂商Id")]
        public virtual Guid ManufacturerId { get; set; }

		/// <summary>
        /// 产品Id
        /// </summary>
		[Description("产品Id")]
        public virtual Guid ProductId { get; set; }

		/// <summary>
        /// 规格Id
        /// </summary>
		[Description("规格Id")]
        public virtual Guid? ProductSpecId { get; set; }

		/// <summary>
        /// 销售
        /// </summary>
		[Description("销售")]
        public virtual DateTime SaleDate { get; set; }

        public virtual DateTime? ExpireDate { get; set; }

        public virtual Guid ReceiverAliasId { get; set; }

		/// <summary>
        /// 数量
        /// </summary>
		[Description("数量")]
        public virtual decimal Quantity { get; set; }

        public virtual string? OrderNumber { get; set; }

    }

    /// <summary>
    /// 月库存
    /// </summary>
	[Description("月库存")]
	public abstract partial class DistributorSalesFlowMonthlyMeta : DistributorSalesFlowMonthlyRaw
	{
	}

    /// <summary>
    /// 月库存
    /// </summary>
	[Description("月库存")]
	public partial class DistributorSalesFlowMonthlyModel : DistributorSalesFlowMonthlyMeta
	{
	}

    /// <summary>
    /// 月库存
    /// </summary>
	[Description("月库存")]
	public partial class DistributorSalesFlowMonthlyQuery : DistributorSalesFlowMonthlyMeta
	{
	}

    /// <summary>
    /// 月库存
    /// </summary>
	[Description("月库存")]
	public partial class DistributorSalesFlowMonthlySelector : DistributorSalesFlowMonthlyMeta
	{
	}

    /// <summary>
    /// 月库存查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.DistributorSalesFlowMonthly))]
	public partial class DistributorSalesFlowMonthlyFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 月库存选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.DistributorSalesFlowMonthly))]
	public partial class DistributorSalesFlowMonthlySelectorFilter : PagingFilterModel
	{
	}

	#endregion DistributorSalesFlowMonthly


	#region Employee

    /// <summary>
    /// 员工
    /// </summary>
	[Description("员工")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Employee), Reverse = true)]
	public abstract class EmployeeRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.DisplayName;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Employee);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 用户ID
        /// </summary>
		[Description("用户ID")]
        public virtual Guid? UserId { get; set; }

		/// <summary>
        /// 唯一码
        /// </summary>
		[Description("唯一码")]
        [MapFromProperty(typeof(Shinsoft.DDI.Entities.Employee), Reverse = false)]
        public virtual int Uid { get; set; }

        public virtual EmployeeFlag EnumFlags { get; set; }

		public virtual string EnumFlagsDesc => this.EnumFlags.GetDesc();

		/// <summary>
        /// 登录名
        /// </summary>
		[Description("登录名")]
        public virtual string LoginName { get; set; } = string.Empty;

		/// <summary>
        /// 工号
        /// </summary>
		[Description("工号")]
        public virtual string JobNo { get; set; } = string.Empty;

		/// <summary>
        /// 姓名
        /// </summary>
		[Description("姓名")]
        public virtual string DisplayName { get; set; } = string.Empty;

		/// <summary>
        /// 职称
        /// </summary>
		[Description("职称")]
        public virtual string Title { get; set; } = string.Empty;

		/// <summary>
        /// 职务
        /// </summary>
		[Description("职务")]
        public virtual string Position { get; set; } = string.Empty;

		/// <summary>
        /// 性别
        /// </summary>
		[Description("性别")]
        public virtual Gender EnumGender { get; set; }

		/// <summary>
        /// 性别
        /// </summary>
		[Description("性别")]
		public virtual string EnumGenderDesc => this.EnumGender.GetDesc();

		/// <summary>
        /// 邮箱
        /// </summary>
		[Description("邮箱")]
        public virtual string Email { get; set; } = string.Empty;

        public virtual string Tel { get; set; } = string.Empty;

		/// <summary>
        /// 手机
        /// </summary>
		[Description("手机")]
        public virtual string Mobile { get; set; } = string.Empty;

        public virtual string Remark { get; set; } = string.Empty;

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
        public virtual EmployeeStatus EnumStatus { get; set; }

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
		public virtual string EnumStatusDesc => this.EnumStatus.GetDesc();

        public virtual Guid? LineManagerId { get; set; }

        public virtual Guid? MajorSubCompanyId { get; set; }

		/// <summary>
        /// 主部门ID
        /// </summary>
		[Description("主部门ID")]
        public virtual Guid? MajorDepartmentId { get; set; }

        public virtual Guid? MajorCostCenterId { get; set; }

		/// <summary>
        /// 主岗ID
        /// </summary>
		[Description("主岗ID")]
        public virtual Guid? MajorStationId { get; set; }

		/// <summary>
        /// 入职日期
        /// </summary>
		[Description("入职日期")]
        public virtual DateTime? HireDate { get; set; }

		/// <summary>
        /// 离职日期
        /// </summary>
		[Description("离职日期")]
        public virtual DateTime? LeaveDate { get; set; }

		/// <summary>
        /// 同步时间
        /// </summary>
		[Description("同步时间")]
        public virtual DateTime? SyncTime { get; set; }

    }

    /// <summary>
    /// 员工
    /// </summary>
	[Description("员工")]
	public abstract partial class EmployeeMeta : EmployeeRaw
	{
	}

    /// <summary>
    /// 员工
    /// </summary>
	[Description("员工")]
	public partial class EmployeeModel : EmployeeMeta
	{
	}

    /// <summary>
    /// 员工
    /// </summary>
	[Description("员工")]
	public partial class EmployeeQuery : EmployeeMeta
	{
	}

    /// <summary>
    /// 员工
    /// </summary>
	[Description("员工")]
	public partial class EmployeeSelector : EmployeeMeta
	{
	}

    /// <summary>
    /// 员工查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Employee))]
	public partial class EmployeeFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 员工选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Employee))]
	public partial class EmployeeSelectorFilter : PagingFilterModel
	{
	}

	#endregion Employee


	#region EmployeeDelegate

    [MapFromType(typeof(Shinsoft.DDI.Entities.EmployeeDelegate), Reverse = true)]
	public abstract class EmployeeDelegateRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(EmployeeDelegate);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid EmployeeId { get; set; }

        public virtual Guid AgentId { get; set; }

        public virtual DateTime? StartDate { get; set; }

        public virtual DateTime? EndDate { get; set; }

        public virtual bool Valid { get; set; }

    }

	public abstract partial class EmployeeDelegateMeta : EmployeeDelegateRaw
	{
	}

	public partial class EmployeeDelegateModel : EmployeeDelegateMeta
	{
	}

	public partial class EmployeeDelegateQuery : EmployeeDelegateMeta
	{
	}

	public partial class EmployeeDelegateSelector : EmployeeDelegateMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.EmployeeDelegate))]
	public partial class EmployeeDelegateFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.EmployeeDelegate))]
	public partial class EmployeeDelegateSelectorFilter : PagingFilterModel
	{
	}

	#endregion EmployeeDelegate


	#region EmployeeDelegateAuth

    /// <summary>
    /// 员工代理权限
    /// </summary>
	[Description("员工代理权限")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.EmployeeDelegateAuth), Reverse = true)]
	public abstract class EmployeeDelegateAuthRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(EmployeeDelegateAuth);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid EmployeeDelegateId { get; set; }

        public virtual Guid AuthId { get; set; }

        public virtual bool? AllowAllTags { get; set; }

    }

    /// <summary>
    /// 员工代理权限
    /// </summary>
	[Description("员工代理权限")]
	public abstract partial class EmployeeDelegateAuthMeta : EmployeeDelegateAuthRaw
	{
	}

    /// <summary>
    /// 员工代理权限
    /// </summary>
	[Description("员工代理权限")]
	public partial class EmployeeDelegateAuthModel : EmployeeDelegateAuthMeta
	{
	}

    /// <summary>
    /// 员工代理权限
    /// </summary>
	[Description("员工代理权限")]
	public partial class EmployeeDelegateAuthQuery : EmployeeDelegateAuthMeta
	{
	}

    /// <summary>
    /// 员工代理权限
    /// </summary>
	[Description("员工代理权限")]
	public partial class EmployeeDelegateAuthSelector : EmployeeDelegateAuthMeta
	{
	}

    /// <summary>
    /// 员工代理权限查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.EmployeeDelegateAuth))]
	public partial class EmployeeDelegateAuthFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 员工代理权限选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.EmployeeDelegateAuth))]
	public partial class EmployeeDelegateAuthSelectorFilter : PagingFilterModel
	{
	}

	#endregion EmployeeDelegateAuth


	#region EmployeeDelegateAuthTag

    /// <summary>
    /// 员工代理权限标签
    /// </summary>
	[Description("员工代理权限标签")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.EmployeeDelegateAuthTag), Reverse = true)]
	public abstract class EmployeeDelegateAuthTagRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(EmployeeDelegateAuthTag);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid EmployeeDelegateId { get; set; }

        public virtual Guid AuthId { get; set; }

        public virtual Guid AuthTagId { get; set; }

    }

    /// <summary>
    /// 员工代理权限标签
    /// </summary>
	[Description("员工代理权限标签")]
	public abstract partial class EmployeeDelegateAuthTagMeta : EmployeeDelegateAuthTagRaw
	{
	}

    /// <summary>
    /// 员工代理权限标签
    /// </summary>
	[Description("员工代理权限标签")]
	public partial class EmployeeDelegateAuthTagModel : EmployeeDelegateAuthTagMeta
	{
	}

    /// <summary>
    /// 员工代理权限标签
    /// </summary>
	[Description("员工代理权限标签")]
	public partial class EmployeeDelegateAuthTagQuery : EmployeeDelegateAuthTagMeta
	{
	}

    /// <summary>
    /// 员工代理权限标签
    /// </summary>
	[Description("员工代理权限标签")]
	public partial class EmployeeDelegateAuthTagSelector : EmployeeDelegateAuthTagMeta
	{
	}

    /// <summary>
    /// 员工代理权限标签查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.EmployeeDelegateAuthTag))]
	public partial class EmployeeDelegateAuthTagFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 员工代理权限标签选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.EmployeeDelegateAuthTag))]
	public partial class EmployeeDelegateAuthTagSelectorFilter : PagingFilterModel
	{
	}

	#endregion EmployeeDelegateAuthTag


	#region EmployeeStation

    /// <summary>
    /// 员工岗位
    /// </summary>
	[Description("员工岗位")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.EmployeeStation), Reverse = true)]
	public abstract class EmployeeStationRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(EmployeeStation);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 员工ID
        /// </summary>
		[Description("员工ID")]
        public virtual Guid EmployeeId { get; set; }

		/// <summary>
        /// 岗位ID
        /// </summary>
		[Description("岗位ID")]
        public virtual Guid StationId { get; set; }

		/// <summary>
        /// 起始日期
        /// </summary>
		[Description("起始日期")]
        public virtual DateTime? StartDate { get; set; }

		/// <summary>
        /// 截止日期
        /// </summary>
		[Description("截止日期")]
        public virtual DateTime? EndDate { get; set; }

    }

    /// <summary>
    /// 员工岗位
    /// </summary>
	[Description("员工岗位")]
	public abstract partial class EmployeeStationMeta : EmployeeStationRaw
	{
	}

    /// <summary>
    /// 员工岗位
    /// </summary>
	[Description("员工岗位")]
	public partial class EmployeeStationModel : EmployeeStationMeta
	{
	}

    /// <summary>
    /// 员工岗位
    /// </summary>
	[Description("员工岗位")]
	public partial class EmployeeStationQuery : EmployeeStationMeta
	{
	}

    /// <summary>
    /// 员工岗位
    /// </summary>
	[Description("员工岗位")]
	public partial class EmployeeStationSelector : EmployeeStationMeta
	{
	}

    /// <summary>
    /// 员工岗位查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.EmployeeStation))]
	public partial class EmployeeStationFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 员工岗位选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.EmployeeStation))]
	public partial class EmployeeStationSelectorFilter : PagingFilterModel
	{
	}

	#endregion EmployeeStation


	#region I18n

    [MapFromType(typeof(Shinsoft.DDI.Entities.I18n), Reverse = true)]
	public abstract class I18nRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(I18n);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid? CompanyId { get; set; }

        public virtual Guid? ParentId { get; set; }

        public virtual I18nCategory EnumCategory { get; set; }

		public virtual string EnumCategoryDesc => this.EnumCategory.GetDesc();

        public virtual I18nType EnumType { get; set; }

		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

        public virtual I18nFlag EnumFlags { get; set; }

		public virtual string EnumFlagsDesc => this.EnumFlags.GetDesc();

        public virtual int Rank { get; set; }

        public virtual string Group { get; set; } = string.Empty;

        public virtual string Key { get; set; } = string.Empty;

        public virtual string Name { get; set; } = string.Empty;

        public virtual string Text { get; set; } = string.Empty;

        public virtual string Remark { get; set; } = string.Empty;

        public virtual bool Valid { get; set; }

        public virtual bool IsSys { get; set; }

    }

	public abstract partial class I18nMeta : I18nRaw
	{
	}

	public partial class I18nModel : I18nMeta
	{
	}

	public partial class I18nQuery : I18nMeta
	{
	}

	public partial class I18nSelector : I18nMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.I18n))]
	public partial class I18nFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.I18n))]
	public partial class I18nSelectorFilter : PagingFilterModel
	{
	}

	#endregion I18n


	#region I18nCulture

    [MapFromType(typeof(Shinsoft.DDI.Entities.I18nCulture), Reverse = true)]
	public abstract class I18nCultureRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(I18nCulture);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid? CompanyId { get; set; }

        public virtual Guid I18nId { get; set; }

        public virtual string Culture { get; set; } = string.Empty;

        public virtual string Text { get; set; } = string.Empty;

    }

	public abstract partial class I18nCultureMeta : I18nCultureRaw
	{
	}

	public partial class I18nCultureModel : I18nCultureMeta
	{
	}

	public partial class I18nCultureQuery : I18nCultureMeta
	{
	}

	public partial class I18nCultureSelector : I18nCultureMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.I18nCulture))]
	public partial class I18nCultureFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.I18nCulture))]
	public partial class I18nCultureSelectorFilter : PagingFilterModel
	{
	}

	#endregion I18nCulture


	#region ImportDataLog

    /// <summary>
    /// 导入日志
    /// </summary>
	[Description("导入日志")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ImportDataLog), Reverse = true)]
	public abstract class ImportDataLogRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ImportDataLog);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// ID
        /// </summary>
		[Description("ID")]
        public virtual Guid ID { get; set; }

        public virtual Guid ReceiverId { get; set; }

        public virtual Guid AttachmentId { get; set; }

		/// <summary>
        /// 原始文件名称
        /// </summary>
		[Description("原始文件名称")]
        public virtual string FileName { get; set; } = string.Empty;

		/// <summary>
        /// 原始文件路径
        /// </summary>
		[Description("原始文件路径")]
        public virtual string Path { get; set; } = string.Empty;

		/// <summary>
        /// 数据模板类型
        /// </summary>
		[Description("数据模板类型")]
        public virtual DataTemplateType EnumDataTemplateType { get; set; }

		/// <summary>
        /// 数据模板类型
        /// </summary>
		[Description("数据模板类型")]
		public virtual string EnumDataTemplateTypeDesc => this.EnumDataTemplateType.GetDesc();

		/// <summary>
        /// 导入类型
        /// </summary>
		[Description("导入类型")]
        public virtual ImportMode EnumImportMode { get; set; }

		/// <summary>
        /// 导入类型
        /// </summary>
		[Description("导入类型")]
		public virtual string EnumImportModeDesc => this.EnumImportMode.GetDesc();

		/// <summary>
        /// 正确数
        /// </summary>
		[Description("正确数")]
        public virtual int RightCount { get; set; }

        public virtual int WranningCount { get; set; }

        public virtual int ErrorCount { get; set; }

		/// <summary>
        /// 总数
        /// </summary>
		[Description("总数")]
        public virtual int TotalCount { get; set; }

        public virtual int HandledCount { get; set; }

		/// <summary>
        /// 导入时间
        /// </summary>
		[Description("导入时间")]
        public virtual DateTime ImportTime { get; set; }

		/// <summary>
        /// 异常文件名称
        /// </summary>
		[Description("异常文件名称")]
        public virtual string? ErrorFileName { get; set; }

		/// <summary>
        /// 异常文件路径
        /// </summary>
		[Description("异常文件路径")]
        public virtual string? ErrorFilePath { get; set; }

        public virtual DistributorType EnumDistributorType { get; set; }

		public virtual string EnumDistributorTypeDesc => this.EnumDistributorType.GetDesc();

    }

    /// <summary>
    /// 导入日志
    /// </summary>
	[Description("导入日志")]
	public abstract partial class ImportDataLogMeta : ImportDataLogRaw
	{
	}

    /// <summary>
    /// 导入日志
    /// </summary>
	[Description("导入日志")]
	public partial class ImportDataLogModel : ImportDataLogMeta
	{
	}

    /// <summary>
    /// 导入日志
    /// </summary>
	[Description("导入日志")]
	public partial class ImportDataLogQuery : ImportDataLogMeta
	{
	}

    /// <summary>
    /// 导入日志
    /// </summary>
	[Description("导入日志")]
	public partial class ImportDataLogSelector : ImportDataLogMeta
	{
	}

    /// <summary>
    /// 导入日志查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ImportDataLog))]
	public partial class ImportDataLogFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 导入日志选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ImportDataLog))]
	public partial class ImportDataLogSelectorFilter : PagingFilterModel
	{
	}

	#endregion ImportDataLog


	#region ImportInventoryTemp

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ImportInventoryTemp), Reverse = true)]
	public abstract class ImportInventoryTempRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ImportInventoryTemp);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 销售流向导入主表临时表ID
        /// </summary>
		[Description("销售流向导入主表临时表ID")]
        public virtual Guid ImportDataLogTempId { get; set; }

		/// <summary>
        /// 经销商省份名称
        /// </summary>
		[Description("经销商省份名称")]
        public virtual string? DistributorProvinceName { get; set; }

		/// <summary>
        /// 经销商城市名称
        /// </summary>
		[Description("经销商城市名称")]
        public virtual string? DistributorCityName { get; set; }

		/// <summary>
        /// 经销商名称
        /// </summary>
		[Description("经销商名称")]
        public virtual string? DistributorName { get; set; }

		/// <summary>
        /// 药企名称
        /// </summary>
		[Description("药企名称")]
        public virtual string? ManufacturerName { get; set; }

		/// <summary>
        /// 通用名称
        /// </summary>
		[Description("通用名称")]
        public virtual string? CommonName { get; set; }

		/// <summary>
        /// 产品名称
        /// </summary>
		[Description("产品名称")]
        public virtual string? ProductName { get; set; }

		/// <summary>
        /// 规格名称
        /// </summary>
		[Description("规格名称")]
        public virtual string? SpecificationName { get; set; }

		/// <summary>
        /// 批号
        /// </summary>
		[Description("批号")]
        public virtual string? BatchNumber { get; set; }

		/// <summary>
        /// 有效期
        /// </summary>
		[Description("有效期")]
        public virtual string? ExpireDate { get; set; }

		/// <summary>
        /// 数量
        /// </summary>
		[Description("数量")]
        public virtual string? Quantity { get; set; }

		/// <summary>
        /// 临时经销商ID
        /// </summary>
		[Description("临时经销商ID")]
        public virtual Guid? TempDistributorId { get; set; }

		/// <summary>
        /// 临时产品ID
        /// </summary>
		[Description("临时产品ID")]
        public virtual Guid? TempProductId { get; set; }

		/// <summary>
        /// 临时产品规格ID
        /// </summary>
		[Description("临时产品规格ID")]
        public virtual Guid? TempProductSpecId { get; set; }

		/// <summary>
        /// 临时产品别名ID
        /// </summary>
		[Description("临时产品别名ID")]
        public virtual Guid? TempProductAliasId { get; set; }

		/// <summary>
        /// 临时经销商省份ID
        /// </summary>
		[Description("临时经销商省份ID")]
        public virtual Guid? TempDistributorProvinceId { get; set; }

		/// <summary>
        /// 临时经销商城市ID
        /// </summary>
		[Description("临时经销商城市ID")]
        public virtual Guid? TempDistributorCityId { get; set; }

		/// <summary>
        /// 临时药企ID
        /// </summary>
		[Description("临时药企ID")]
        public virtual Guid? TempManufacturerId { get; set; }

		/// <summary>
        /// 导入错误类型
        /// </summary>
		[Description("导入错误类型")]
        public virtual ImportErrorType EnumImportErrorType { get; set; }

		/// <summary>
        /// 导入错误类型
        /// </summary>
		[Description("导入错误类型")]
		public virtual string EnumImportErrorTypeDesc => this.EnumImportErrorType.GetDesc();

		/// <summary>
        /// 流向错误类型
        /// </summary>
		[Description("流向错误类型")]
        public virtual ErrorSalesFlowType EnumErrorSalesFlowType { get; set; }

		/// <summary>
        /// 流向错误类型
        /// </summary>
		[Description("流向错误类型")]
		public virtual string EnumErrorSalesFlowTypeDesc => this.EnumErrorSalesFlowType.GetDesc();

		/// <summary>
        /// 流向警告类型
        /// </summary>
		[Description("流向警告类型")]
        public virtual WarningSalesFlowType EnumWarningSalesFlowType { get; set; }

		/// <summary>
        /// 流向警告类型
        /// </summary>
		[Description("流向警告类型")]
		public virtual string EnumWarningSalesFlowTypeDesc => this.EnumWarningSalesFlowType.GetDesc();

		/// <summary>
        /// 警告省份ID
        /// </summary>
		[Description("警告省份ID")]
        public virtual Guid? WarningProvinceId { get; set; }

		/// <summary>
        /// 警告城市ID
        /// </summary>
		[Description("警告城市ID")]
        public virtual Guid? WarningCityId { get; set; }

    }

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
	public abstract partial class ImportInventoryTempMeta : ImportInventoryTempRaw
	{
	}

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
	public partial class ImportInventoryTempModel : ImportInventoryTempMeta
	{
	}

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
	public partial class ImportInventoryTempQuery : ImportInventoryTempMeta
	{
	}

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
	public partial class ImportInventoryTempSelector : ImportInventoryTempMeta
	{
	}

    /// <summary>
    /// 销售流向导入临时表查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ImportInventoryTemp))]
	public partial class ImportInventoryTempFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 销售流向导入临时表选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ImportInventoryTemp))]
	public partial class ImportInventoryTempSelectorFilter : PagingFilterModel
	{
	}

	#endregion ImportInventoryTemp


	#region ImportPurchaseTemp

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ImportPurchaseTemp), Reverse = true)]
	public abstract class ImportPurchaseTempRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ImportPurchaseTemp);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 销售流向导入主表临时表ID
        /// </summary>
		[Description("销售流向导入主表临时表ID")]
        public virtual Guid ImportDataLogTempId { get; set; }

		/// <summary>
        /// 经销商省份名称
        /// </summary>
		[Description("经销商省份名称")]
        public virtual string? DistributorProvinceName { get; set; }

		/// <summary>
        /// 经销商城市名称
        /// </summary>
		[Description("经销商城市名称")]
        public virtual string? DistributorCityName { get; set; }

		/// <summary>
        /// 经销商名称
        /// </summary>
		[Description("经销商名称")]
        public virtual string? DistributorName { get; set; }

		/// <summary>
        /// 药企名称
        /// </summary>
		[Description("药企名称")]
        public virtual string? ManufacturerName { get; set; }

		/// <summary>
        /// 通用名称
        /// </summary>
		[Description("通用名称")]
        public virtual string? CommonName { get; set; }

		/// <summary>
        /// 产品名称
        /// </summary>
		[Description("产品名称")]
        public virtual string? ProductName { get; set; }

		/// <summary>
        /// 规格名称
        /// </summary>
		[Description("规格名称")]
        public virtual string? SpecificationName { get; set; }

		/// <summary>
        /// 批号
        /// </summary>
		[Description("批号")]
        public virtual string? BatchNumber { get; set; }

		/// <summary>
        /// 有效期
        /// </summary>
		[Description("有效期")]
        public virtual string? ExpireDate { get; set; }

		/// <summary>
        /// 数量
        /// </summary>
		[Description("数量")]
        public virtual string? Quantity { get; set; }

		/// <summary>
        /// 临时经销商ID
        /// </summary>
		[Description("临时经销商ID")]
        public virtual Guid? TempDistributorId { get; set; }

		/// <summary>
        /// 临时产品ID
        /// </summary>
		[Description("临时产品ID")]
        public virtual Guid? TempProductId { get; set; }

		/// <summary>
        /// 临时产品规格ID
        /// </summary>
		[Description("临时产品规格ID")]
        public virtual Guid? TempProductSpecId { get; set; }

		/// <summary>
        /// 临时产品别名ID
        /// </summary>
		[Description("临时产品别名ID")]
        public virtual Guid? TempProductAliasId { get; set; }

		/// <summary>
        /// 临时经销商省份ID
        /// </summary>
		[Description("临时经销商省份ID")]
        public virtual Guid? TempDistributorProvinceId { get; set; }

		/// <summary>
        /// 临时经销商城市ID
        /// </summary>
		[Description("临时经销商城市ID")]
        public virtual Guid? TempDistributorCityId { get; set; }

		/// <summary>
        /// 临时药企ID
        /// </summary>
		[Description("临时药企ID")]
        public virtual Guid? TempManufacturerId { get; set; }

		/// <summary>
        /// 导入错误类型
        /// </summary>
		[Description("导入错误类型")]
        public virtual ImportErrorType EnumImportErrorType { get; set; }

		/// <summary>
        /// 导入错误类型
        /// </summary>
		[Description("导入错误类型")]
		public virtual string EnumImportErrorTypeDesc => this.EnumImportErrorType.GetDesc();

		/// <summary>
        /// 流向错误类型
        /// </summary>
		[Description("流向错误类型")]
        public virtual ErrorSalesFlowType EnumErrorSalesFlowType { get; set; }

		/// <summary>
        /// 流向错误类型
        /// </summary>
		[Description("流向错误类型")]
		public virtual string EnumErrorSalesFlowTypeDesc => this.EnumErrorSalesFlowType.GetDesc();

		/// <summary>
        /// 流向警告类型
        /// </summary>
		[Description("流向警告类型")]
        public virtual WarningSalesFlowType EnumWarningSalesFlowType { get; set; }

		/// <summary>
        /// 流向警告类型
        /// </summary>
		[Description("流向警告类型")]
		public virtual string EnumWarningSalesFlowTypeDesc => this.EnumWarningSalesFlowType.GetDesc();

		/// <summary>
        /// 警告省份ID
        /// </summary>
		[Description("警告省份ID")]
        public virtual Guid? WarningProvinceId { get; set; }

		/// <summary>
        /// 警告城市ID
        /// </summary>
		[Description("警告城市ID")]
        public virtual Guid? WarningCityId { get; set; }

    }

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
	public abstract partial class ImportPurchaseTempMeta : ImportPurchaseTempRaw
	{
	}

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
	public partial class ImportPurchaseTempModel : ImportPurchaseTempMeta
	{
	}

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
	public partial class ImportPurchaseTempQuery : ImportPurchaseTempMeta
	{
	}

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
	public partial class ImportPurchaseTempSelector : ImportPurchaseTempMeta
	{
	}

    /// <summary>
    /// 销售流向导入临时表查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ImportPurchaseTemp))]
	public partial class ImportPurchaseTempFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 销售流向导入临时表选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ImportPurchaseTemp))]
	public partial class ImportPurchaseTempSelectorFilter : PagingFilterModel
	{
	}

	#endregion ImportPurchaseTemp


	#region ImportSalesFlowTemp

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ImportSalesFlowTemp), Reverse = true)]
	public abstract class ImportSalesFlowTempRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ImportSalesFlowTemp);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 销售流向导入主表临时表ID
        /// </summary>
		[Description("销售流向导入主表临时表ID")]
        public virtual Guid ImportDataLogTempId { get; set; }

		/// <summary>
        /// 经销商省份名称
        /// </summary>
		[Description("经销商省份名称")]
        public virtual string? DistributorProvinceName { get; set; }

		/// <summary>
        /// 经销商城市名称
        /// </summary>
		[Description("经销商城市名称")]
        public virtual string? DistributorCityName { get; set; }

		/// <summary>
        /// 经销商名称
        /// </summary>
		[Description("经销商名称")]
        public virtual string? DistributorName { get; set; }

		/// <summary>
        /// 收货方省份名称
        /// </summary>
		[Description("收货方省份名称")]
        public virtual string? ReceiverProvinceName { get; set; }

		/// <summary>
        /// 收货方城市名称
        /// </summary>
		[Description("收货方城市名称")]
        public virtual string? ReceiverCityName { get; set; }

		/// <summary>
        /// 收货方名称
        /// </summary>
		[Description("收货方名称")]
        public virtual string? ReceiverName { get; set; }

		/// <summary>
        /// 关联收货方名称
        /// </summary>
		[Description("关联收货方名称")]
        public virtual string? RelateReceiverName { get; set; }

		/// <summary>
        /// 药企名称
        /// </summary>
		[Description("药企名称")]
        public virtual string? ManufacturerName { get; set; }

		/// <summary>
        /// 通用名称
        /// </summary>
		[Description("通用名称")]
        public virtual string? CommonName { get; set; }

		/// <summary>
        /// 产品名称
        /// </summary>
		[Description("产品名称")]
        public virtual string? ProductName { get; set; }

		/// <summary>
        /// 规格名称
        /// </summary>
		[Description("规格名称")]
        public virtual string? SpecificationName { get; set; }

		/// <summary>
        /// 销售日期
        /// </summary>
		[Description("销售日期")]
        public virtual string? SaleDate { get; set; }

		/// <summary>
        /// 批号
        /// </summary>
		[Description("批号")]
        public virtual string? BatchNumber { get; set; }

		/// <summary>
        /// 有效期
        /// </summary>
		[Description("有效期")]
        public virtual string? ExpireDate { get; set; }

		/// <summary>
        /// 数量
        /// </summary>
		[Description("数量")]
        public virtual string? Quantity { get; set; }

		/// <summary>
        /// 临时经销商ID
        /// </summary>
		[Description("临时经销商ID")]
        public virtual Guid? TempDistributorId { get; set; }

		/// <summary>
        /// 临时收货方ID
        /// </summary>
		[Description("临时收货方ID")]
        public virtual Guid? TempReceiverId { get; set; }

		/// <summary>
        /// 临时产品ID
        /// </summary>
		[Description("临时产品ID")]
        public virtual Guid? TempProductId { get; set; }

		/// <summary>
        /// 临时产品规格ID
        /// </summary>
		[Description("临时产品规格ID")]
        public virtual Guid? TempProductSpecId { get; set; }

		/// <summary>
        /// 临时产品别名ID
        /// </summary>
		[Description("临时产品别名ID")]
        public virtual Guid? TempProductAliasId { get; set; }

		/// <summary>
        /// 临时收货方别名ID
        /// </summary>
		[Description("临时收货方别名ID")]
        public virtual Guid? TempReceiverAliasId { get; set; }

		/// <summary>
        /// 临时经销商省份ID
        /// </summary>
		[Description("临时经销商省份ID")]
        public virtual Guid? TempDistributorProvinceId { get; set; }

		/// <summary>
        /// 临时经销商城市ID
        /// </summary>
		[Description("临时经销商城市ID")]
        public virtual Guid? TempDistributorCityId { get; set; }

		/// <summary>
        /// 临时药企ID
        /// </summary>
		[Description("临时药企ID")]
        public virtual Guid? TempManufacturerId { get; set; }

		/// <summary>
        /// 导入错误类型
        /// </summary>
		[Description("导入错误类型")]
        public virtual ImportErrorType EnumImportErrorType { get; set; }

		/// <summary>
        /// 导入错误类型
        /// </summary>
		[Description("导入错误类型")]
		public virtual string EnumImportErrorTypeDesc => this.EnumImportErrorType.GetDesc();

		/// <summary>
        /// 流向错误类型
        /// </summary>
		[Description("流向错误类型")]
        public virtual ErrorSalesFlowType EnumErrorSalesFlowType { get; set; }

		/// <summary>
        /// 流向错误类型
        /// </summary>
		[Description("流向错误类型")]
		public virtual string EnumErrorSalesFlowTypeDesc => this.EnumErrorSalesFlowType.GetDesc();

		/// <summary>
        /// 流向警告类型
        /// </summary>
		[Description("流向警告类型")]
        public virtual WarningSalesFlowType EnumWarningSalesFlowType { get; set; }

		/// <summary>
        /// 流向警告类型
        /// </summary>
		[Description("流向警告类型")]
		public virtual string EnumWarningSalesFlowTypeDesc => this.EnumWarningSalesFlowType.GetDesc();

		/// <summary>
        /// 警告省份ID
        /// </summary>
		[Description("警告省份ID")]
        public virtual Guid? WarningProvinceId { get; set; }

		/// <summary>
        /// 警告城市ID
        /// </summary>
		[Description("警告城市ID")]
        public virtual Guid? WarningCityId { get; set; }

    }

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
	public abstract partial class ImportSalesFlowTempMeta : ImportSalesFlowTempRaw
	{
	}

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
	public partial class ImportSalesFlowTempModel : ImportSalesFlowTempMeta
	{
	}

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
	public partial class ImportSalesFlowTempQuery : ImportSalesFlowTempMeta
	{
	}

    /// <summary>
    /// 销售流向导入临时表
    /// </summary>
	[Description("销售流向导入临时表")]
	public partial class ImportSalesFlowTempSelector : ImportSalesFlowTempMeta
	{
	}

    /// <summary>
    /// 销售流向导入临时表查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ImportSalesFlowTemp))]
	public partial class ImportSalesFlowTempFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 销售流向导入临时表选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ImportSalesFlowTemp))]
	public partial class ImportSalesFlowTempSelectorFilter : PagingFilterModel
	{
	}

	#endregion ImportSalesFlowTemp


	#region Manufacturer

    /// <summary>
    /// 药企
    /// </summary>
	[Description("药企")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Manufacturer), Reverse = true)]
	public abstract class ManufacturerRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Manufacturer);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 厂家编码
        /// </summary>
		[Description("厂家编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 厂家名称
        /// </summary>
		[Description("厂家名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 厂家简称
        /// </summary>
		[Description("厂家简称")]
        public virtual string? ShortName { get; set; }

		/// <summary>
        /// 国家
        /// </summary>
		[Description("国家")]
        public virtual string? Country { get; set; }

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
        public virtual ManufacturerStatus EnumStatus { get; set; }

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
		public virtual string EnumStatusDesc => this.EnumStatus.GetDesc();

		/// <summary>
        /// 备注
        /// </summary>
		[Description("备注")]
        public virtual string? Remark { get; set; }

    }

    /// <summary>
    /// 药企
    /// </summary>
	[Description("药企")]
	public abstract partial class ManufacturerMeta : ManufacturerRaw
	{
	}

    /// <summary>
    /// 药企
    /// </summary>
	[Description("药企")]
	public partial class ManufacturerModel : ManufacturerMeta
	{
	}

    /// <summary>
    /// 药企
    /// </summary>
	[Description("药企")]
	public partial class ManufacturerQuery : ManufacturerMeta
	{
	}

    /// <summary>
    /// 药企
    /// </summary>
	[Description("药企")]
	public partial class ManufacturerSelector : ManufacturerMeta
	{
	}

    /// <summary>
    /// 药企查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Manufacturer))]
	public partial class ManufacturerFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 药企选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Manufacturer))]
	public partial class ManufacturerSelectorFilter : PagingFilterModel
	{
	}

	#endregion Manufacturer


	#region Position

    /// <summary>
    /// 职位
    /// </summary>
	[Description("职位")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Position), Reverse = true)]
	public abstract class PositionRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Position);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 上级职位ID
        /// </summary>
		[Description("上级职位ID")]
        public virtual Guid? ParentId { get; set; }

		/// <summary>
        /// 唯一码
        /// </summary>
		[Description("唯一码")]
        [MapFromProperty(typeof(Shinsoft.DDI.Entities.Position), Reverse = false)]
        public virtual int Uid { get; set; }

		/// <summary>
        /// 唯一码路径
        /// </summary>
		[Description("唯一码路径")]
        [MapFromProperty(typeof(Shinsoft.DDI.Entities.Position), Reverse = false)]
        public virtual string UidPath { get; set; } = string.Empty;

		/// <summary>
        /// 标签
        /// </summary>
		[Description("标签")]
        public virtual PositionFlag EnumFlags { get; set; }

		/// <summary>
        /// 标签
        /// </summary>
		[Description("标签")]
		public virtual string EnumFlagsDesc => this.EnumFlags.GetDesc();

		/// <summary>
        /// 职级
        /// </summary>
		[Description("职级")]
        public virtual int Grade { get; set; }

		/// <summary>
        /// 编码
        /// </summary>
		[Description("编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 名称
        /// </summary>
		[Description("名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 备注
        /// </summary>
		[Description("备注")]
        public virtual string? Remark { get; set; }

    }

    /// <summary>
    /// 职位
    /// </summary>
	[Description("职位")]
	public abstract partial class PositionMeta : PositionRaw
	{
	}

    /// <summary>
    /// 职位
    /// </summary>
	[Description("职位")]
	public partial class PositionModel : PositionMeta
	{
	}

    /// <summary>
    /// 职位
    /// </summary>
	[Description("职位")]
	public partial class PositionQuery : PositionMeta
	{
	}

    /// <summary>
    /// 职位
    /// </summary>
	[Description("职位")]
	public partial class PositionSelector : PositionMeta
	{
	}

    /// <summary>
    /// 职位查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Position))]
	public partial class PositionFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 职位选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Position))]
	public partial class PositionSelectorFilter : PagingFilterModel
	{
	}

	#endregion Position


	#region Product

    /// <summary>
    /// 产品
    /// </summary>
	[Description("产品")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Product), Reverse = true)]
	public abstract class ProductRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.NameCn;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Product);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 药企ID
        /// </summary>
		[Description("药企ID")]
        public virtual Guid ManufacturerId { get; set; }

		/// <summary>
        /// 编码
        /// </summary>
		[Description("编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 产品中文名称
        /// </summary>
		[Description("产品中文名称")]
        public virtual string NameCn { get; set; } = string.Empty;

		/// <summary>
        /// 产品英文名称
        /// </summary>
		[Description("产品英文名称")]
        public virtual string? NameEn { get; set; }

		/// <summary>
        /// 通用名称
        /// </summary>
		[Description("通用名称")]
        public virtual string CommonName { get; set; } = string.Empty;

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
        public virtual ProductStatus EnumStatus { get; set; }

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
		public virtual string EnumStatusDesc => this.EnumStatus.GetDesc();

    }

    /// <summary>
    /// 产品
    /// </summary>
	[Description("产品")]
	public abstract partial class ProductMeta : ProductRaw
	{
	}

    /// <summary>
    /// 产品
    /// </summary>
	[Description("产品")]
	public partial class ProductModel : ProductMeta
	{
	}

    /// <summary>
    /// 产品
    /// </summary>
	[Description("产品")]
	public partial class ProductQuery : ProductMeta
	{
	}

    /// <summary>
    /// 产品
    /// </summary>
	[Description("产品")]
	public partial class ProductSelector : ProductMeta
	{
	}

    /// <summary>
    /// 产品查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Product))]
	public partial class ProductFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 产品选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Product))]
	public partial class ProductSelectorFilter : PagingFilterModel
	{
	}

	#endregion Product


	#region ProductAlias

    /// <summary>
    /// 产品别名
    /// </summary>
	[Description("产品别名")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ProductAlias), Reverse = true)]
	public abstract class ProductAliasRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ProductAlias);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 产品规格ID
        /// </summary>
		[Description("产品规格ID")]
        public virtual Guid ProductSpecId { get; set; }

		/// <summary>
        /// 收货方ID
        /// </summary>
		[Description("收货方ID")]
        public virtual Guid ReceiverId { get; set; }

		/// <summary>
        /// 产品别名名称
        /// </summary>
		[Description("产品别名名称")]
        public virtual string ProductAliasName { get; set; } = string.Empty;

		/// <summary>
        /// 产品规格别名
        /// </summary>
		[Description("产品规格别名")]
        public virtual string ProductSpecAlias { get; set; } = string.Empty;

    }

    /// <summary>
    /// 产品别名
    /// </summary>
	[Description("产品别名")]
	public abstract partial class ProductAliasMeta : ProductAliasRaw
	{
	}

    /// <summary>
    /// 产品别名
    /// </summary>
	[Description("产品别名")]
	public partial class ProductAliasModel : ProductAliasMeta
	{
	}

    /// <summary>
    /// 产品别名
    /// </summary>
	[Description("产品别名")]
	public partial class ProductAliasQuery : ProductAliasMeta
	{
	}

    /// <summary>
    /// 产品别名
    /// </summary>
	[Description("产品别名")]
	public partial class ProductAliasSelector : ProductAliasMeta
	{
	}

    /// <summary>
    /// 产品别名查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ProductAlias))]
	public partial class ProductAliasFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 产品别名选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ProductAlias))]
	public partial class ProductAliasSelectorFilter : PagingFilterModel
	{
	}

	#endregion ProductAlias


	#region ProductSpec

    /// <summary>
    /// 产品规格
    /// </summary>
	[Description("产品规格")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ProductSpec), Reverse = true)]
	public abstract class ProductSpecRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Code;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ProductSpec);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 产品ID
        /// </summary>
		[Description("产品ID")]
        public virtual Guid ProductId { get; set; }

		/// <summary>
        /// 规格编码
        /// </summary>
		[Description("规格编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 规格
        /// </summary>
		[Description("规格")]
        public virtual string Spec { get; set; } = string.Empty;

		/// <summary>
        /// 单位
        /// </summary>
		[Description("单位")]
        public virtual string? Unit { get; set; }

		/// <summary>
        /// 剂型ID
        /// </summary>
		[Description("剂型ID")]
        public virtual Guid? DosageFormId { get; set; }

		/// <summary>
        /// 生产厂家
        /// </summary>
		[Description("生产厂家")]
        public virtual string PharmaceuticalFactory { get; set; } = string.Empty;

		/// <summary>
        /// 分型
        /// </summary>
		[Description("分型")]
        public virtual string? MaterialGroup { get; set; }

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
        public virtual ProductSpecStatus EnumStatus { get; set; }

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
		public virtual string EnumStatusDesc => this.EnumStatus.GetDesc();

    }

    /// <summary>
    /// 产品规格
    /// </summary>
	[Description("产品规格")]
	public abstract partial class ProductSpecMeta : ProductSpecRaw
	{
	}

    /// <summary>
    /// 产品规格
    /// </summary>
	[Description("产品规格")]
	public partial class ProductSpecModel : ProductSpecMeta
	{
	}

    /// <summary>
    /// 产品规格
    /// </summary>
	[Description("产品规格")]
	public partial class ProductSpecQuery : ProductSpecMeta
	{
	}

    /// <summary>
    /// 产品规格
    /// </summary>
	[Description("产品规格")]
	public partial class ProductSpecSelector : ProductSpecMeta
	{
	}

    /// <summary>
    /// 产品规格查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ProductSpec))]
	public partial class ProductSpecFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 产品规格选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ProductSpec))]
	public partial class ProductSpecSelectorFilter : PagingFilterModel
	{
	}

	#endregion ProductSpec


	#region Province

    /// <summary>
    /// 省份
    /// </summary>
	[Description("省份")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Province), Reverse = true)]
	public abstract class ProvinceRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Province);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 省份编码
        /// </summary>
		[Description("省份编码")]
        public virtual string? Code { get; set; }

		/// <summary>
        /// 省份名称
        /// </summary>
		[Description("省份名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 省份简称
        /// </summary>
		[Description("省份简称")]
        public virtual string? ShortName { get; set; }

    }

    /// <summary>
    /// 省份
    /// </summary>
	[Description("省份")]
	public abstract partial class ProvinceMeta : ProvinceRaw
	{
	}

    /// <summary>
    /// 省份
    /// </summary>
	[Description("省份")]
	public partial class ProvinceModel : ProvinceMeta
	{
	}

    /// <summary>
    /// 省份
    /// </summary>
	[Description("省份")]
	public partial class ProvinceQuery : ProvinceMeta
	{
	}

    /// <summary>
    /// 省份
    /// </summary>
	[Description("省份")]
	public partial class ProvinceSelector : ProvinceMeta
	{
	}

    /// <summary>
    /// 省份查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Province))]
	public partial class ProvinceFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 省份选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Province))]
	public partial class ProvinceSelectorFilter : PagingFilterModel
	{
	}

	#endregion Province


	#region Receiver

    /// <summary>
    /// 经销商/收货方
    /// </summary>
	[Description("经销商/收货方")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Receiver), Reverse = true)]
	public abstract class ReceiverRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Receiver);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 授权码
        /// </summary>
		[Description("授权码")]
        public virtual string SdrCode { get; set; } = string.Empty;

		/// <summary>
        /// 编码
        /// </summary>
		[Description("编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 名称
        /// </summary>
		[Description("名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 所属医药集团
        /// </summary>
		[Description("所属医药集团")]
        public virtual Guid? MedicineGroupId { get; set; }

		/// <summary>
        /// 省份ID
        /// </summary>
		[Description("省份ID")]
        public virtual Guid ProvinceId { get; set; }

		/// <summary>
        /// 城市ID
        /// </summary>
		[Description("城市ID")]
        public virtual Guid? CityId { get; set; }

		/// <summary>
        /// 区县ID
        /// </summary>
		[Description("区县ID")]
        public virtual Guid? CountyId { get; set; }

		/// <summary>
        /// 收货方类型ID
        /// </summary>
		[Description("收货方类型ID")]
        public virtual Guid ReceiverTypeId { get; set; }

        public virtual string? UnifiedSocialCreditCode { get; set; }

		/// <summary>
        /// 地址
        /// </summary>
		[Description("地址")]
        public virtual string? Address { get; set; }

        public virtual string? Telephone { get; set; }

        public virtual string? EMail { get; set; }

        public virtual string? PostalCode { get; set; }

        public virtual string? NetAddress { get; set; }

        public virtual DateTime? StopTime { get; set; }

        public virtual Guid? TargetTerminalId { get; set; }

        public virtual Guid? HospitalGradeId { get; set; }

        public virtual Guid? HospitalLevelId { get; set; }

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
        public virtual ReceiverStatus EnumStatus { get; set; }

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
		public virtual string EnumStatusDesc => this.EnumStatus.GetDesc();

		/// <summary>
        /// 备注
        /// </summary>
		[Description("备注")]
        public virtual string? Remark { get; set; }

    }

    /// <summary>
    /// 经销商/收货方
    /// </summary>
	[Description("经销商/收货方")]
	public abstract partial class ReceiverMeta : ReceiverRaw
	{
	}

    /// <summary>
    /// 经销商/收货方
    /// </summary>
	[Description("经销商/收货方")]
	public partial class ReceiverModel : ReceiverMeta
	{
	}

    /// <summary>
    /// 经销商/收货方
    /// </summary>
	[Description("经销商/收货方")]
	public partial class ReceiverQuery : ReceiverMeta
	{
	}

    /// <summary>
    /// 经销商/收货方
    /// </summary>
	[Description("经销商/收货方")]
	public partial class ReceiverSelector : ReceiverMeta
	{
	}

    /// <summary>
    /// 经销商/收货方查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Receiver))]
	public partial class ReceiverFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 经销商/收货方选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Receiver))]
	public partial class ReceiverSelectorFilter : PagingFilterModel
	{
	}

	#endregion Receiver


	#region ReceiverAlias

    /// <summary>
    /// 收货方别名
    /// </summary>
	[Description("收货方别名")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ReceiverAlias), Reverse = true)]
	public abstract class ReceiverAliasRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ReceiverAlias);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 收货方ID
        /// </summary>
		[Description("收货方ID")]
        public virtual Guid ReceiverId { get; set; }

		/// <summary>
        /// 经销商ID
        /// </summary>
		[Description("经销商ID")]
        public virtual Guid DistributorId { get; set; }

		/// <summary>
        /// 收货方别名名称
        /// </summary>
		[Description("收货方别名名称")]
        public virtual string ReceiverAliasName { get; set; } = string.Empty;

    }

    /// <summary>
    /// 收货方别名
    /// </summary>
	[Description("收货方别名")]
	public abstract partial class ReceiverAliasMeta : ReceiverAliasRaw
	{
	}

    /// <summary>
    /// 收货方别名
    /// </summary>
	[Description("收货方别名")]
	public partial class ReceiverAliasModel : ReceiverAliasMeta
	{
	}

    /// <summary>
    /// 收货方别名
    /// </summary>
	[Description("收货方别名")]
	public partial class ReceiverAliasQuery : ReceiverAliasMeta
	{
	}

    /// <summary>
    /// 收货方别名
    /// </summary>
	[Description("收货方别名")]
	public partial class ReceiverAliasSelector : ReceiverAliasMeta
	{
	}

    /// <summary>
    /// 收货方别名查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReceiverAlias))]
	public partial class ReceiverAliasFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 收货方别名选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReceiverAlias))]
	public partial class ReceiverAliasSelectorFilter : PagingFilterModel
	{
	}

	#endregion ReceiverAlias


	#region ReceiverClient

    /// <summary>
    /// 收货方配置
    /// </summary>
	[Description("收货方配置")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ReceiverClient), Reverse = true)]
	public abstract class ReceiverClientRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ReceiverClient);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid? ShareReceiverClientId { get; set; }

        public virtual int VerifyFrequency { get; set; }

        public virtual bool AutoUpdate { get; set; }

		/// <summary>
        /// 当前版本
        /// </summary>
		[Description("当前版本")]
        public virtual string? Version { get; set; }

        public virtual string UpdateToVersion { get; set; } = string.Empty;

        public virtual string UpdateUrl { get; set; } = string.Empty;

        public virtual string ScheduleType { get; set; } = string.Empty;

        public virtual string ScheduleDay { get; set; } = string.Empty;

        public virtual string ScheduleTime { get; set; } = string.Empty;

        public virtual string SourceType { get; set; } = string.Empty;

        public virtual string TargetType { get; set; } = string.Empty;

        public virtual string LogType { get; set; } = string.Empty;

        public virtual string DbConnectType { get; set; } = string.Empty;

        public virtual string DBConnect { get; set; } = string.Empty;

        public virtual string DBSqlB { get; set; } = string.Empty;

        public virtual string DBSqlS { get; set; } = string.Empty;

        public virtual string DBSqlI { get; set; } = string.Empty;

        public virtual string FixPathB { get; set; } = string.Empty;

        public virtual string FixPathS { get; set; } = string.Empty;

        public virtual string FixPathI { get; set; } = string.Empty;

        public virtual string FixFileB { get; set; } = string.Empty;

        public virtual string FixFileS { get; set; } = string.Empty;

        public virtual string FixFileI { get; set; } = string.Empty;

        public virtual string? WSUrl { get; set; }

        public virtual string? WSUsername { get; set; }

        public virtual string? WSPassword { get; set; }

        public virtual string? HTTPUrl { get; set; }

        public virtual string? HTTPUsername { get; set; }

        public virtual string? HTTPPassword { get; set; }

        public virtual string? FTPServer { get; set; }

        public virtual string? FTPPort { get; set; }

        public virtual string? FtpType { get; set; }

        public virtual string? FTPUsername { get; set; }

        public virtual string? FTPPassword { get; set; }

        public virtual string? FTPPath { get; set; }

        public virtual string? SavePath { get; set; }

        public virtual string? LogHttpUrl { get; set; }

        public virtual string? LogHttpUsername { get; set; }

        public virtual string? LogHttpPassword { get; set; }

        public virtual string? HTTPProxy { get; set; }

        public virtual string? HTTPProxyUsername { get; set; }

        public virtual string? HTTPProxyPassword { get; set; }

        public virtual string? FTPProxy { get; set; }

        public virtual string? FTPProxyUsername { get; set; }

        public virtual string? FTPProxyPassword { get; set; }

        public virtual string? DeleteClientData { get; set; }

        public virtual string? FileEncoding { get; set; }

        public virtual string? EnvironmentVariable { get; set; }

        public virtual string? Mode { get; set; }

        public virtual string? RestartTime { get; set; }

        public virtual string OtherXml { get; set; } = string.Empty;

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
        public virtual string? Status { get; set; }

		/// <summary>
        /// 服务检测频率
        /// </summary>
		[Description("服务检测频率")]
        public virtual int? Frequency { get; set; }

    }

    /// <summary>
    /// 收货方配置
    /// </summary>
	[Description("收货方配置")]
	public abstract partial class ReceiverClientMeta : ReceiverClientRaw
	{
	}

    /// <summary>
    /// 收货方配置
    /// </summary>
	[Description("收货方配置")]
	public partial class ReceiverClientModel : ReceiverClientMeta
	{
	}

    /// <summary>
    /// 收货方配置
    /// </summary>
	[Description("收货方配置")]
	public partial class ReceiverClientQuery : ReceiverClientMeta
	{
	}

    /// <summary>
    /// 收货方配置
    /// </summary>
	[Description("收货方配置")]
	public partial class ReceiverClientSelector : ReceiverClientMeta
	{
	}

    /// <summary>
    /// 收货方配置查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReceiverClient))]
	public partial class ReceiverClientFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 收货方配置选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReceiverClient))]
	public partial class ReceiverClientSelectorFilter : PagingFilterModel
	{
	}

	#endregion ReceiverClient


	#region ReceiverClientLog

    /// <summary>
    /// 客户端日志
    /// </summary>
	[Description("客户端日志")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ReceiverClientLog), Reverse = true)]
	public abstract class ReceiverClientLogRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ReceiverClientLog);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 客户编码
        /// </summary>
		[Description("客户编码")]
        public virtual string ReceiverCode { get; set; } = string.Empty;

		/// <summary>
        /// 客户名称
        /// </summary>
		[Description("客户名称")]
        public virtual string ReceiverName { get; set; } = string.Empty;

		/// <summary>
        /// 日志时间
        /// </summary>
		[Description("日志时间")]
        public virtual DateTime LogTime { get; set; }

		/// <summary>
        /// 日志内容
        /// </summary>
		[Description("日志内容")]
        public virtual string? Message { get; set; }

    }

    /// <summary>
    /// 客户端日志
    /// </summary>
	[Description("客户端日志")]
	public abstract partial class ReceiverClientLogMeta : ReceiverClientLogRaw
	{
	}

    /// <summary>
    /// 客户端日志
    /// </summary>
	[Description("客户端日志")]
	public partial class ReceiverClientLogModel : ReceiverClientLogMeta
	{
	}

    /// <summary>
    /// 客户端日志
    /// </summary>
	[Description("客户端日志")]
	public partial class ReceiverClientLogQuery : ReceiverClientLogMeta
	{
	}

    /// <summary>
    /// 客户端日志
    /// </summary>
	[Description("客户端日志")]
	public partial class ReceiverClientLogSelector : ReceiverClientLogMeta
	{
	}

    /// <summary>
    /// 客户端日志查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReceiverClientLog))]
	public partial class ReceiverClientLogFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 客户端日志选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReceiverClientLog))]
	public partial class ReceiverClientLogSelectorFilter : PagingFilterModel
	{
	}

	#endregion ReceiverClientLog


	#region ReceiverType

    /// <summary>
    /// 收货方类型
    /// </summary>
	[Description("收货方类型")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ReceiverType), Reverse = true)]
	public abstract class ReceiverTypeRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ReceiverType);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 父级收货方类型ID
        /// </summary>
		[Description("父级收货方类型ID")]
        public virtual Guid? ParentReceiverTypeId { get; set; }

		/// <summary>
        /// 类型编码
        /// </summary>
		[Description("类型编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 类型名称
        /// </summary>
		[Description("类型名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 类型简称
        /// </summary>
		[Description("类型简称")]
        public virtual string ShortName { get; set; } = string.Empty;

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
        public virtual ReceiverTypeStatus EnumStatus { get; set; }

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
		public virtual string EnumStatusDesc => this.EnumStatus.GetDesc();

		/// <summary>
        /// 备注
        /// </summary>
		[Description("备注")]
        public virtual string? Remark { get; set; }

    }

    /// <summary>
    /// 收货方类型
    /// </summary>
	[Description("收货方类型")]
	public abstract partial class ReceiverTypeMeta : ReceiverTypeRaw
	{
	}

    /// <summary>
    /// 收货方类型
    /// </summary>
	[Description("收货方类型")]
	public partial class ReceiverTypeModel : ReceiverTypeMeta
	{
	}

    /// <summary>
    /// 收货方类型
    /// </summary>
	[Description("收货方类型")]
	public partial class ReceiverTypeQuery : ReceiverTypeMeta
	{
	}

    /// <summary>
    /// 收货方类型
    /// </summary>
	[Description("收货方类型")]
	public partial class ReceiverTypeSelector : ReceiverTypeMeta
	{
	}

    /// <summary>
    /// 收货方类型查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReceiverType))]
	public partial class ReceiverTypeFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 收货方类型选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReceiverType))]
	public partial class ReceiverTypeSelectorFilter : PagingFilterModel
	{
	}

	#endregion ReceiverType


	#region ReviewAuditor

    [MapFromType(typeof(Shinsoft.DDI.Entities.ReviewAuditor), Reverse = true)]
	public abstract class ReviewAuditorRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ReviewAuditor);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid ReviewIndexId { get; set; }

        public virtual Guid ReviewTaskId { get; set; }

        public virtual Guid EmployeeId { get; set; }

        public virtual Guid? ReviewEmployeeId { get; set; }

        public virtual DateTime? ReviewTime { get; set; }

        public virtual string ReviewRemark { get; set; } = string.Empty;

        public virtual ReviewStatus EnumReviewStatus { get; set; }

		public virtual string EnumReviewStatusDesc => this.EnumReviewStatus.GetDesc();

    }

	public abstract partial class ReviewAuditorMeta : ReviewAuditorRaw
	{
	}

	public partial class ReviewAuditorModel : ReviewAuditorMeta
	{
	}

	public partial class ReviewAuditorQuery : ReviewAuditorMeta
	{
	}

	public partial class ReviewAuditorSelector : ReviewAuditorMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReviewAuditor))]
	public partial class ReviewAuditorFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReviewAuditor))]
	public partial class ReviewAuditorSelectorFilter : PagingFilterModel
	{
	}

	#endregion ReviewAuditor


	#region ReviewData

    /// <summary>
    /// 数据审核内容
    /// </summary>
	[Description("数据审核内容")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ReviewData), Reverse = true)]
	public abstract class ReviewDataRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ReviewData);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 原数据
        /// </summary>
		[Description("原数据")]
        public virtual string OriData { get; set; } = string.Empty;

		/// <summary>
        /// 待审核数据
        /// </summary>
		[Description("待审核数据")]
        public virtual string NewData { get; set; } = string.Empty;

		/// <summary>
        /// 已审核数据
        /// </summary>
		[Description("已审核数据")]
        public virtual string ApprovedData { get; set; } = string.Empty;

    }

    /// <summary>
    /// 数据审核内容
    /// </summary>
	[Description("数据审核内容")]
	public abstract partial class ReviewDataMeta : ReviewDataRaw
	{
	}

    /// <summary>
    /// 数据审核内容
    /// </summary>
	[Description("数据审核内容")]
	public partial class ReviewDataModel : ReviewDataMeta
	{
	}

    /// <summary>
    /// 数据审核内容
    /// </summary>
	[Description("数据审核内容")]
	public partial class ReviewDataQuery : ReviewDataMeta
	{
	}

    /// <summary>
    /// 数据审核内容
    /// </summary>
	[Description("数据审核内容")]
	public partial class ReviewDataSelector : ReviewDataMeta
	{
	}

    /// <summary>
    /// 数据审核内容查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReviewData))]
	public partial class ReviewDataFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 数据审核内容选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReviewData))]
	public partial class ReviewDataSelectorFilter : PagingFilterModel
	{
	}

	#endregion ReviewData


	#region ReviewExtInfo

    /// <summary>
    /// 数据审核扩展信息
    /// </summary>
	[Description("数据审核扩展信息")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ReviewExtInfo), Reverse = true)]
	public abstract class ReviewExtInfoRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ReviewExtInfo);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string Code { get; set; } = string.Empty;

        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 标题
        /// </summary>
		[Description("标题")]
        public virtual string Title { get; set; } = string.Empty;

    }

    /// <summary>
    /// 数据审核扩展信息
    /// </summary>
	[Description("数据审核扩展信息")]
	public abstract partial class ReviewExtInfoMeta : ReviewExtInfoRaw
	{
	}

    /// <summary>
    /// 数据审核扩展信息
    /// </summary>
	[Description("数据审核扩展信息")]
	public partial class ReviewExtInfoModel : ReviewExtInfoMeta
	{
	}

    /// <summary>
    /// 数据审核扩展信息
    /// </summary>
	[Description("数据审核扩展信息")]
	public partial class ReviewExtInfoQuery : ReviewExtInfoMeta
	{
	}

    /// <summary>
    /// 数据审核扩展信息
    /// </summary>
	[Description("数据审核扩展信息")]
	public partial class ReviewExtInfoSelector : ReviewExtInfoMeta
	{
	}

    /// <summary>
    /// 数据审核扩展信息查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReviewExtInfo))]
	public partial class ReviewExtInfoFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 数据审核扩展信息选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReviewExtInfo))]
	public partial class ReviewExtInfoSelectorFilter : PagingFilterModel
	{
	}

	#endregion ReviewExtInfo


	#region ReviewIndex

    /// <summary>
    /// 数据审核目录
    /// </summary>
	[Description("数据审核目录")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ReviewIndex), Reverse = true)]
	public abstract class ReviewIndexRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ReviewIndex);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual ReviewType EnumReviewType { get; set; }

		public virtual string EnumReviewTypeDesc => this.EnumReviewType.GetDesc();

        public virtual string ReviewOperation { get; set; } = string.Empty;

		/// <summary>
        /// 审核数据类型（表名）
        /// </summary>
		[Description("审核数据类型（表名）")]
        public virtual string ReviewDataType { get; set; } = string.Empty;

		/// <summary>
        /// 审核数据ID（主键）
        /// </summary>
		[Description("审核数据ID（主键）")]
        public virtual Guid? ReviewDataId { get; set; }

		/// <summary>
        /// 是否集合
        /// </summary>
		[Description("是否集合")]
        public virtual bool IsCollection { get; set; }

		/// <summary>
        /// 集合条数
        /// </summary>
		[Description("集合条数")]
        public virtual int Count { get; set; }

		/// <summary>
        /// 审核通过条数
        /// </summary>
		[Description("审核通过条数")]
        public virtual int ApprovedCount { get; set; }

		/// <summary>
        /// 提交员工ID
        /// </summary>
		[Description("提交员工ID")]
        public virtual Guid? SubmitEmployeeId { get; set; }

        public virtual string AuditorNames { get; set; } = string.Empty;

		/// <summary>
        /// 审核员工ID
        /// </summary>
		[Description("审核员工ID")]
        public virtual Guid? ReviewEmployeeId { get; set; }

		/// <summary>
        /// 审核时间
        /// </summary>
		[Description("审核时间")]
        public virtual DateTime? ReviewTime { get; set; }

		/// <summary>
        /// 审核备注
        /// </summary>
		[Description("审核备注")]
        public virtual string ReviewRemark { get; set; } = string.Empty;

		/// <summary>
        /// 审核状态
        /// </summary>
		[Description("审核状态")]
        public virtual ReviewStatus EnumReviewStatus { get; set; }

		/// <summary>
        /// 审核状态
        /// </summary>
		[Description("审核状态")]
		public virtual string EnumReviewStatusDesc => this.EnumReviewStatus.GetDesc();

    }

    /// <summary>
    /// 数据审核目录
    /// </summary>
	[Description("数据审核目录")]
	public abstract partial class ReviewIndexMeta : ReviewIndexRaw
	{
	}

    /// <summary>
    /// 数据审核目录
    /// </summary>
	[Description("数据审核目录")]
	public partial class ReviewIndexModel : ReviewIndexMeta
	{
	}

    /// <summary>
    /// 数据审核目录
    /// </summary>
	[Description("数据审核目录")]
	public partial class ReviewIndexQuery : ReviewIndexMeta
	{
	}

    /// <summary>
    /// 数据审核目录
    /// </summary>
	[Description("数据审核目录")]
	public partial class ReviewIndexSelector : ReviewIndexMeta
	{
	}

    /// <summary>
    /// 数据审核目录查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReviewIndex))]
	public partial class ReviewIndexFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 数据审核目录选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReviewIndex))]
	public partial class ReviewIndexSelectorFilter : PagingFilterModel
	{
	}

	#endregion ReviewIndex


	#region ReviewTask

    [MapFromType(typeof(Shinsoft.DDI.Entities.ReviewTask), Reverse = true)]
	public abstract class ReviewTaskRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ReviewTask);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid ReviewIndexId { get; set; }

        public virtual string Code { get; set; } = string.Empty;

        public virtual string Name { get; set; } = string.Empty;

        public virtual string AuditorNames { get; set; } = string.Empty;

        public virtual string Remark { get; set; } = string.Empty;

        public virtual double Sequence { get; set; }

        public virtual Guid? ReviewEmployeeId { get; set; }

        public virtual DateTime? ReviewTime { get; set; }

        public virtual string ReviewRemark { get; set; } = string.Empty;

        public virtual ReviewStatus EnumReviewStatus { get; set; }

		public virtual string EnumReviewStatusDesc => this.EnumReviewStatus.GetDesc();

    }

	public abstract partial class ReviewTaskMeta : ReviewTaskRaw
	{
	}

	public partial class ReviewTaskModel : ReviewTaskMeta
	{
	}

	public partial class ReviewTaskQuery : ReviewTaskMeta
	{
	}

	public partial class ReviewTaskSelector : ReviewTaskMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReviewTask))]
	public partial class ReviewTaskFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ReviewTask))]
	public partial class ReviewTaskSelectorFilter : PagingFilterModel
	{
	}

	#endregion ReviewTask


	#region Role

    /// <summary>
    /// 角色
    /// </summary>
	[Description("角色")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Role), Reverse = true)]
	public abstract class RoleRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Role);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 标签
        /// </summary>
		[Description("标签")]
        public virtual RoleFlag EnumFlags { get; set; }

		/// <summary>
        /// 标签
        /// </summary>
		[Description("标签")]
		public virtual string EnumFlagsDesc => this.EnumFlags.GetDesc();

		/// <summary>
        /// 编辑标志
        /// </summary>
		[Description("编辑标志")]
        public virtual EditFlag EnumEditFlags { get; set; }

		/// <summary>
        /// 编辑标志
        /// </summary>
		[Description("编辑标志")]
		public virtual string EnumEditFlagsDesc => this.EnumEditFlags.GetDesc();

		/// <summary>
        /// 编码
        /// </summary>
		[Description("编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 名称
        /// </summary>
		[Description("名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 备注
        /// </summary>
		[Description("备注")]
        public virtual string Remark { get; set; } = string.Empty;

    }

    /// <summary>
    /// 角色
    /// </summary>
	[Description("角色")]
	public abstract partial class RoleMeta : RoleRaw
	{
	}

    /// <summary>
    /// 角色
    /// </summary>
	[Description("角色")]
	public partial class RoleModel : RoleMeta
	{
	}

    /// <summary>
    /// 角色
    /// </summary>
	[Description("角色")]
	public partial class RoleQuery : RoleMeta
	{
	}

    /// <summary>
    /// 角色
    /// </summary>
	[Description("角色")]
	public partial class RoleSelector : RoleMeta
	{
	}

    /// <summary>
    /// 角色查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Role))]
	public partial class RoleFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 角色选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Role))]
	public partial class RoleSelectorFilter : PagingFilterModel
	{
	}

	#endregion Role


	#region RoleAuth

    /// <summary>
    /// 角色权限
    /// </summary>
	[Description("角色权限")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.RoleAuth), Reverse = true)]
	public abstract class RoleAuthRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(RoleAuth);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 角色ID
        /// </summary>
		[Description("角色ID")]
        public virtual Guid RoleId { get; set; }

		/// <summary>
        /// 权限ID
        /// </summary>
		[Description("权限ID")]
        public virtual Guid AuthId { get; set; }

		/// <summary>
        /// 允许全部权限标签
        /// </summary>
		[Description("允许全部权限标签")]
        public virtual bool? AllowAllTags { get; set; }

    }

    /// <summary>
    /// 角色权限
    /// </summary>
	[Description("角色权限")]
	public abstract partial class RoleAuthMeta : RoleAuthRaw
	{
	}

    /// <summary>
    /// 角色权限
    /// </summary>
	[Description("角色权限")]
	public partial class RoleAuthModel : RoleAuthMeta
	{
	}

    /// <summary>
    /// 角色权限
    /// </summary>
	[Description("角色权限")]
	public partial class RoleAuthQuery : RoleAuthMeta
	{
	}

    /// <summary>
    /// 角色权限
    /// </summary>
	[Description("角色权限")]
	public partial class RoleAuthSelector : RoleAuthMeta
	{
	}

    /// <summary>
    /// 角色权限查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.RoleAuth))]
	public partial class RoleAuthFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 角色权限选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.RoleAuth))]
	public partial class RoleAuthSelectorFilter : PagingFilterModel
	{
	}

	#endregion RoleAuth


	#region RoleAuthTag

    /// <summary>
    /// 角色权限标签
    /// </summary>
	[Description("角色权限标签")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.RoleAuthTag), Reverse = true)]
	public abstract class RoleAuthTagRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(RoleAuthTag);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 角色ID
        /// </summary>
		[Description("角色ID")]
        public virtual Guid RoleId { get; set; }

		/// <summary>
        /// 权限ID
        /// </summary>
		[Description("权限ID")]
        public virtual Guid AuthId { get; set; }

		/// <summary>
        /// 权限标签ID
        /// </summary>
		[Description("权限标签ID")]
        public virtual Guid AuthTagId { get; set; }

    }

    /// <summary>
    /// 角色权限标签
    /// </summary>
	[Description("角色权限标签")]
	public abstract partial class RoleAuthTagMeta : RoleAuthTagRaw
	{
	}

    /// <summary>
    /// 角色权限标签
    /// </summary>
	[Description("角色权限标签")]
	public partial class RoleAuthTagModel : RoleAuthTagMeta
	{
	}

    /// <summary>
    /// 角色权限标签
    /// </summary>
	[Description("角色权限标签")]
	public partial class RoleAuthTagQuery : RoleAuthTagMeta
	{
	}

    /// <summary>
    /// 角色权限标签
    /// </summary>
	[Description("角色权限标签")]
	public partial class RoleAuthTagSelector : RoleAuthTagMeta
	{
	}

    /// <summary>
    /// 角色权限标签查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.RoleAuthTag))]
	public partial class RoleAuthTagFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 角色权限标签选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.RoleAuthTag))]
	public partial class RoleAuthTagSelectorFilter : PagingFilterModel
	{
	}

	#endregion RoleAuthTag


	#region RoleMember

    /// <summary>
    /// 角色成员
    /// </summary>
	[Description("角色成员")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.RoleMember), Reverse = true)]
	public abstract class RoleMemberRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(RoleMember);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 角色ID
        /// </summary>
		[Description("角色ID")]
        public virtual Guid RoleId { get; set; }

		/// <summary>
        /// 成员类型
        /// </summary>
		[Description("成员类型")]
        public virtual RoleMemberType EnumType { get; set; }

		/// <summary>
        /// 成员类型
        /// </summary>
		[Description("成员类型")]
		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

		/// <summary>
        /// 成员ID
        /// </summary>
		[Description("成员ID")]
        public virtual Guid MemberId { get; set; }

    }

    /// <summary>
    /// 角色成员
    /// </summary>
	[Description("角色成员")]
	public abstract partial class RoleMemberMeta : RoleMemberRaw
	{
	}

    /// <summary>
    /// 角色成员
    /// </summary>
	[Description("角色成员")]
	public partial class RoleMemberModel : RoleMemberMeta
	{
	}

    /// <summary>
    /// 角色成员
    /// </summary>
	[Description("角色成员")]
	public partial class RoleMemberQuery : RoleMemberMeta
	{
	}

    /// <summary>
    /// 角色成员
    /// </summary>
	[Description("角色成员")]
	public partial class RoleMemberSelector : RoleMemberMeta
	{
	}

    /// <summary>
    /// 角色成员查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.RoleMember))]
	public partial class RoleMemberFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 角色成员选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.RoleMember))]
	public partial class RoleMemberSelectorFilter : PagingFilterModel
	{
	}

	#endregion RoleMember


	#region SerialNumber

    [MapFromType(typeof(Shinsoft.DDI.Entities.SerialNumber), Reverse = true)]
	public abstract class SerialNumberRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(SerialNumber);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string Prefix { get; set; } = string.Empty;

        public virtual string DateFormat { get; set; } = string.Empty;

        public virtual int SeedLength { get; set; }

    }

	public abstract partial class SerialNumberMeta : SerialNumberRaw
	{
	}

	public partial class SerialNumberModel : SerialNumberMeta
	{
	}

	public partial class SerialNumberQuery : SerialNumberMeta
	{
	}

	public partial class SerialNumberSelector : SerialNumberMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.SerialNumber))]
	public partial class SerialNumberFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.SerialNumber))]
	public partial class SerialNumberSelectorFilter : PagingFilterModel
	{
	}

	#endregion SerialNumber


	#region SerialSeed

    [MapFromType(typeof(Shinsoft.DDI.Entities.SerialSeed), Reverse = true)]
	public abstract class SerialSeedRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(SerialSeed);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid SerialNumberId { get; set; }

        public virtual int? Year { get; set; }

        public virtual int? Month { get; set; }

        public virtual int? Day { get; set; }

        public virtual int? Hour { get; set; }

        public virtual int? Min { get; set; }

        public virtual int? Sec { get; set; }

        public virtual int Seed { get; set; }

    }

	public abstract partial class SerialSeedMeta : SerialSeedRaw
	{
	}

	public partial class SerialSeedModel : SerialSeedMeta
	{
	}

	public partial class SerialSeedQuery : SerialSeedMeta
	{
	}

	public partial class SerialSeedSelector : SerialSeedMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.SerialSeed))]
	public partial class SerialSeedFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.SerialSeed))]
	public partial class SerialSeedSelectorFilter : PagingFilterModel
	{
	}

	#endregion SerialSeed


	#region Shipper

    /// <summary>
    /// 发货方
    /// </summary>
	[Description("发货方")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Shipper), Reverse = true)]
	public abstract class ShipperRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Shipper);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 发货方编码
        /// </summary>
		[Description("发货方编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 发货方名称
        /// </summary>
		[Description("发货方名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 发货方简称
        /// </summary>
		[Description("发货方简称")]
        public virtual string ShortName { get; set; } = string.Empty;

		/// <summary>
        /// 地址
        /// </summary>
		[Description("地址")]
        public virtual string? Address { get; set; }

        public virtual string? Telephone { get; set; }

        public virtual string? EMail { get; set; }

        public virtual string? ContactPerson { get; set; }

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
        public virtual ShipperStatus EnumStatus { get; set; }

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
		public virtual string EnumStatusDesc => this.EnumStatus.GetDesc();

		/// <summary>
        /// 是否默认货主
        /// </summary>
		[Description("是否默认货主")]
        public virtual bool IsDefault { get; set; }

    }

    /// <summary>
    /// 发货方
    /// </summary>
	[Description("发货方")]
	public abstract partial class ShipperMeta : ShipperRaw
	{
	}

    /// <summary>
    /// 发货方
    /// </summary>
	[Description("发货方")]
	public partial class ShipperModel : ShipperMeta
	{
	}

    /// <summary>
    /// 发货方
    /// </summary>
	[Description("发货方")]
	public partial class ShipperQuery : ShipperMeta
	{
	}

    /// <summary>
    /// 发货方
    /// </summary>
	[Description("发货方")]
	public partial class ShipperSelector : ShipperMeta
	{
	}

    /// <summary>
    /// 发货方查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Shipper))]
	public partial class ShipperFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 发货方选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Shipper))]
	public partial class ShipperSelectorFilter : PagingFilterModel
	{
	}

	#endregion Shipper


	#region ShipperProductSpec

    /// <summary>
    /// 货主产品配置表
    /// </summary>
	[Description("货主产品配置表")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ShipperProductSpec), Reverse = true)]
	public abstract class ShipperProductSpecRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ShipperProductSpec);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 货主Id
        /// </summary>
		[Description("货主Id")]
        public virtual Guid ShipperId { get; set; }

		/// <summary>
        /// 规格Id
        /// </summary>
		[Description("规格Id")]
        public virtual Guid ProductSpecId { get; set; }

    }

    /// <summary>
    /// 货主产品配置表
    /// </summary>
	[Description("货主产品配置表")]
	public abstract partial class ShipperProductSpecMeta : ShipperProductSpecRaw
	{
	}

    /// <summary>
    /// 货主产品配置表
    /// </summary>
	[Description("货主产品配置表")]
	public partial class ShipperProductSpecModel : ShipperProductSpecMeta
	{
	}

    /// <summary>
    /// 货主产品配置表
    /// </summary>
	[Description("货主产品配置表")]
	public partial class ShipperProductSpecQuery : ShipperProductSpecMeta
	{
	}

    /// <summary>
    /// 货主产品配置表
    /// </summary>
	[Description("货主产品配置表")]
	public partial class ShipperProductSpecSelector : ShipperProductSpecMeta
	{
	}

    /// <summary>
    /// 货主产品配置表查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ShipperProductSpec))]
	public partial class ShipperProductSpecFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 货主产品配置表选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ShipperProductSpec))]
	public partial class ShipperProductSpecSelectorFilter : PagingFilterModel
	{
	}

	#endregion ShipperProductSpec


	#region ShipperReceiver

    /// <summary>
    /// 货主经销商配置
    /// </summary>
	[Description("货主经销商配置")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.ShipperReceiver), Reverse = true)]
	public abstract class ShipperReceiverRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(ShipperReceiver);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

		/// <summary>
        /// 主键ID
        /// </summary>
		[Description("主键ID")]
        public virtual Guid ID { get; set; }

		/// <summary>
        /// 货主Id
        /// </summary>
		[Description("货主Id")]
        public virtual Guid ShipperId { get; set; }

		/// <summary>
        /// 经销商Id
        /// </summary>
		[Description("经销商Id")]
        public virtual Guid ReceiverId { get; set; }

    }

    /// <summary>
    /// 货主经销商配置
    /// </summary>
	[Description("货主经销商配置")]
	public abstract partial class ShipperReceiverMeta : ShipperReceiverRaw
	{
	}

    /// <summary>
    /// 货主经销商配置
    /// </summary>
	[Description("货主经销商配置")]
	public partial class ShipperReceiverModel : ShipperReceiverMeta
	{
	}

    /// <summary>
    /// 货主经销商配置
    /// </summary>
	[Description("货主经销商配置")]
	public partial class ShipperReceiverQuery : ShipperReceiverMeta
	{
	}

    /// <summary>
    /// 货主经销商配置
    /// </summary>
	[Description("货主经销商配置")]
	public partial class ShipperReceiverSelector : ShipperReceiverMeta
	{
	}

    /// <summary>
    /// 货主经销商配置查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ShipperReceiver))]
	public partial class ShipperReceiverFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 货主经销商配置选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.ShipperReceiver))]
	public partial class ShipperReceiverSelectorFilter : PagingFilterModel
	{
	}

	#endregion ShipperReceiver


	#region Station

    /// <summary>
    /// 岗位
    /// </summary>
	[Description("岗位")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.Station), Reverse = true)]
	public abstract class StationRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name.GetValueOrDefault();

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Station);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 上级岗位ID
        /// </summary>
		[Description("上级岗位ID")]
        public virtual Guid? ParentId { get; set; }

		/// <summary>
        /// 唯一码
        /// </summary>
		[Description("唯一码")]
        [MapFromProperty(typeof(Shinsoft.DDI.Entities.Station), Reverse = false)]
        public virtual int Uid { get; set; }

		/// <summary>
        /// Uid路径
        /// </summary>
		[Description("Uid路径")]
        [MapFromProperty(typeof(Shinsoft.DDI.Entities.Station), Reverse = false)]
        public virtual string UidPath { get; set; } = string.Empty;

		/// <summary>
        /// 部门ID
        /// </summary>
		[Description("部门ID")]
        public virtual Guid DepartmentId { get; set; }

		/// <summary>
        /// 职位ID
        /// </summary>
		[Description("职位ID")]
        public virtual Guid PositionId { get; set; }

		/// <summary>
        /// 岗位名称
        /// </summary>
		[Description("岗位名称")]
        public virtual string? Name { get; set; }

		/// <summary>
        /// 有效性
        /// </summary>
		[Description("有效性")]
        public virtual bool Valid { get; set; }

		/// <summary>
        /// 开始日期
        /// </summary>
		[Description("开始日期")]
        public virtual DateTime? StartDate { get; set; }

		/// <summary>
        /// 结束日期
        /// </summary>
		[Description("结束日期")]
        public virtual DateTime? EndDate { get; set; }

		/// <summary>
        /// 备注
        /// </summary>
		[Description("备注")]
        public virtual string Remark { get; set; } = string.Empty;

    }

    /// <summary>
    /// 岗位
    /// </summary>
	[Description("岗位")]
	public abstract partial class StationMeta : StationRaw
	{
	}

    /// <summary>
    /// 岗位
    /// </summary>
	[Description("岗位")]
	public partial class StationModel : StationMeta
	{
	}

    /// <summary>
    /// 岗位
    /// </summary>
	[Description("岗位")]
	public partial class StationQuery : StationMeta
	{
	}

    /// <summary>
    /// 岗位
    /// </summary>
	[Description("岗位")]
	public partial class StationSelector : StationMeta
	{
	}

    /// <summary>
    /// 岗位查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Station))]
	public partial class StationFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 岗位选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.Station))]
	public partial class StationSelectorFilter : PagingFilterModel
	{
	}

	#endregion Station


	#region SubCompany

    /// <summary>
    /// 分公司
    /// </summary>
	[Description("分公司")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.SubCompany), Reverse = true)]
	public abstract class SubCompanyRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(SubCompany);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 公司分类
        /// </summary>
		[Description("公司分类")]
        public virtual SubCompanyType EnumType { get; set; }

		/// <summary>
        /// 公司分类
        /// </summary>
		[Description("公司分类")]
		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
        public virtual SubCompanyFlag EnumFlags { get; set; }

		/// <summary>
        /// 状态
        /// </summary>
		[Description("状态")]
		public virtual string EnumFlagsDesc => this.EnumFlags.GetDesc();

		/// <summary>
        /// 编码
        /// </summary>
		[Description("编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 名称
        /// </summary>
		[Description("名称")]
        public virtual string Name { get; set; } = string.Empty;

		/// <summary>
        /// 简称
        /// </summary>
		[Description("简称")]
        public virtual string ShortName { get; set; } = string.Empty;

		/// <summary>
        /// 有效性
        /// </summary>
		[Description("有效性")]
        public virtual bool Valid { get; set; }

		/// <summary>
        /// 排序
        /// </summary>
		[Description("排序")]
        public virtual int Sort { get; set; }

    }

    /// <summary>
    /// 分公司
    /// </summary>
	[Description("分公司")]
	public abstract partial class SubCompanyMeta : SubCompanyRaw
	{
	}

    /// <summary>
    /// 分公司
    /// </summary>
	[Description("分公司")]
	public partial class SubCompanyModel : SubCompanyMeta
	{
	}

    /// <summary>
    /// 分公司
    /// </summary>
	[Description("分公司")]
	public partial class SubCompanyQuery : SubCompanyMeta
	{
	}

    /// <summary>
    /// 分公司
    /// </summary>
	[Description("分公司")]
	public partial class SubCompanySelector : SubCompanyMeta
	{
	}

    /// <summary>
    /// 分公司查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.SubCompany))]
	public partial class SubCompanyFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 分公司选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.SubCompany))]
	public partial class SubCompanySelectorFilter : PagingFilterModel
	{
	}

	#endregion SubCompany


	#region SyncTask

    [MapFromType(typeof(Shinsoft.DDI.Entities.SyncTask), Reverse = true)]
	public abstract class SyncTaskRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(SyncTask);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string Code { get; set; } = string.Empty;

        public virtual string Name { get; set; } = string.Empty;

        public virtual string Remark { get; set; } = string.Empty;

        public virtual DateTime? LastSyncTime { get; set; }

    }

	public abstract partial class SyncTaskMeta : SyncTaskRaw
	{
	}

	public partial class SyncTaskModel : SyncTaskMeta
	{
	}

	public partial class SyncTaskQuery : SyncTaskMeta
	{
	}

	public partial class SyncTaskSelector : SyncTaskMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.SyncTask))]
	public partial class SyncTaskFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.SyncTask))]
	public partial class SyncTaskSelectorFilter : PagingFilterModel
	{
	}

	#endregion SyncTask


	#region SysCulture

    [MapFromType(typeof(Shinsoft.DDI.Entities.SysCulture), Reverse = true)]
	public abstract class SysCultureRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(SysCulture);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string Culture { get; set; } = string.Empty;

        public virtual string Name { get; set; } = string.Empty;

        public virtual bool IsDefault { get; set; }

    }

	public abstract partial class SysCultureMeta : SysCultureRaw
	{
	}

	public partial class SysCultureModel : SysCultureMeta
	{
	}

	public partial class SysCultureQuery : SysCultureMeta
	{
	}

	public partial class SysCultureSelector : SysCultureMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.SysCulture))]
	public partial class SysCultureFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.SysCulture))]
	public partial class SysCultureSelectorFilter : PagingFilterModel
	{
	}

	#endregion SysCulture


	#region SysSetting

    /// <summary>
    /// 系统设置
    /// </summary>
	[Description("系统设置")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.SysSetting), Reverse = true)]
	public abstract class SysSettingRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(SysSetting);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual SysSettingFlag EnumFlags { get; set; }

		public virtual string EnumFlagsDesc => this.EnumFlags.GetDesc();

        public virtual string Name { get; set; } = string.Empty;

        public virtual string Key { get; set; } = string.Empty;

        public virtual string Value { get; set; } = string.Empty;

        public virtual string Remark { get; set; } = string.Empty;

    }

    /// <summary>
    /// 系统设置
    /// </summary>
	[Description("系统设置")]
	public abstract partial class SysSettingMeta : SysSettingRaw
	{
	}

    /// <summary>
    /// 系统设置
    /// </summary>
	[Description("系统设置")]
	public partial class SysSettingModel : SysSettingMeta
	{
	}

    /// <summary>
    /// 系统设置
    /// </summary>
	[Description("系统设置")]
	public partial class SysSettingQuery : SysSettingMeta
	{
	}

    /// <summary>
    /// 系统设置
    /// </summary>
	[Description("系统设置")]
	public partial class SysSettingSelector : SysSettingMeta
	{
	}

    /// <summary>
    /// 系统设置查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.SysSetting))]
	public partial class SysSettingFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 系统设置选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.SysSetting))]
	public partial class SysSettingSelectorFilter : PagingFilterModel
	{
	}

	#endregion SysSetting


	#region User

    /// <summary>
    /// 用户
    /// </summary>
	[Description("用户")]
    [MapFromType(typeof(Shinsoft.DDI.Entities.User), Reverse = true)]
	public abstract class UserRaw : OperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.DisplayName;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(User);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid GroupCompanyId { get; set; }

		/// <summary>
        /// 类型
        /// </summary>
		[Description("类型")]
        public virtual UserType EnumType { get; set; }

		/// <summary>
        /// 类型
        /// </summary>
		[Description("类型")]
		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

		/// <summary>
        /// 登录名
        /// </summary>
		[Description("登录名")]
        public virtual string LoginName { get; set; } = string.Empty;

		/// <summary>
        /// 显示名
        /// </summary>
		[Description("显示名")]
        public virtual string DisplayName { get; set; } = string.Empty;

		/// <summary>
        /// 邮箱
        /// </summary>
		[Description("邮箱")]
        public virtual string Email { get; set; } = string.Empty;

		/// <summary>
        /// 手机
        /// </summary>
		[Description("手机")]
        public virtual string Mobile { get; set; } = string.Empty;

		/// <summary>
        /// 密码加密类型
        /// </summary>
		[Description("密码加密类型")]
        public virtual PwdType EnumPwdType { get; set; }

		/// <summary>
        /// 密码加密类型
        /// </summary>
		[Description("密码加密类型")]
		public virtual string EnumPwdTypeDesc => this.EnumPwdType.GetDesc();

		/// <summary>
        /// 文本密码
        /// </summary>
		[Description("文本密码")]
        public virtual string PwdText { get; set; } = string.Empty;

		/// <summary>
        /// 密码过期时间
        /// </summary>
		[Description("密码过期时间")]
        public virtual DateTime? PwdExpireTime { get; set; }

		/// <summary>
        /// 身份验证种子，每次修改密码后改变
        /// </summary>
		[Description("身份验证种子，每次修改密码后改变")]
        public virtual int IdentitySeed { get; set; }

        public virtual string Culture { get; set; } = string.Empty;

		/// <summary>
        /// 用户状态
        /// </summary>
		[Description("用户状态")]
        public virtual UserStatus EnumStatus { get; set; }

		/// <summary>
        /// 用户状态
        /// </summary>
		[Description("用户状态")]
		public virtual string EnumStatusDesc => this.EnumStatus.GetDesc();

		/// <summary>
        /// 默认公司ID
        /// </summary>
		[Description("默认公司ID")]
        public virtual Guid? DefaultCompanyId { get; set; }

		/// <summary>
        /// 默认员工ID
        /// </summary>
		[Description("默认员工ID")]
        public virtual Guid? DefaultEmployeeId { get; set; }

        public virtual string WeChatId { get; set; } = string.Empty;

        public virtual string WeChatAvatar { get; set; } = string.Empty;

    }

    /// <summary>
    /// 用户
    /// </summary>
	[Description("用户")]
	public abstract partial class UserMeta : UserRaw
	{
	}

    /// <summary>
    /// 用户
    /// </summary>
	[Description("用户")]
	public partial class UserModel : UserMeta
	{
	}

    /// <summary>
    /// 用户
    /// </summary>
	[Description("用户")]
	public partial class UserQuery : UserMeta
	{
	}

    /// <summary>
    /// 用户
    /// </summary>
	[Description("用户")]
	public partial class UserSelector : UserMeta
	{
	}

    /// <summary>
    /// 用户查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.User))]
	public partial class UserFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 用户选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.User))]
	public partial class UserSelectorFilter : PagingFilterModel
	{
	}

	#endregion User


	#region VueRoute

    [MapFromType(typeof(Shinsoft.DDI.Entities.VueRoute), Reverse = true)]
	public abstract class VueRouteRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VueRoute);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid? ParentId { get; set; }

        public virtual string Name { get; set; } = string.Empty;

        public virtual string Path { get; set; } = string.Empty;

        public virtual string Redirect { get; set; } = string.Empty;

        public virtual bool Valid { get; set; }

        public virtual bool IsSys { get; set; }

    }

	public abstract partial class VueRouteMeta : VueRouteRaw
	{
	}

	public partial class VueRouteModel : VueRouteMeta
	{
	}

	public partial class VueRouteQuery : VueRouteMeta
	{
	}

	public partial class VueRouteSelector : VueRouteMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VueRoute))]
	public partial class VueRouteFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VueRoute))]
	public partial class VueRouteSelectorFilter : PagingFilterModel
	{
	}

	#endregion VueRoute


	#region VueRouteMeta

    [MapFromType(typeof(Shinsoft.DDI.Entities.VueRouteMeta), Reverse = true)]
	public abstract class VueRouteMetaRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Title;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VueRouteMeta);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string Title { get; set; } = string.Empty;

        public virtual string? Icon { get; set; }

        public virtual int? Rank { get; set; }

        public virtual bool? ShowLink { get; set; }

        public virtual bool? AlwaysShow { get; set; }

        public virtual bool? Anonymous { get; set; }

        public virtual bool? IsBreadcrumbLink { get; set; }

        public virtual string? Auths { get; set; }

    }

	public abstract partial class VueRouteMetaMeta : VueRouteMetaRaw
	{
	}

	public partial class VueRouteMetaModel : VueRouteMetaMeta
	{
	}

	public partial class VueRouteMetaQuery : VueRouteMetaMeta
	{
	}

	public partial class VueRouteMetaSelector : VueRouteMetaMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VueRouteMeta))]
	public partial class VueRouteMetaFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VueRouteMeta))]
	public partial class VueRouteMetaSelectorFilter : PagingFilterModel
	{
	}

	#endregion VueRouteMeta


	#region VwEmployeeStation

    [MapFromType(typeof(Shinsoft.DDI.Entities.VwEmployeeStation), Reverse = true)]
	public abstract class VwEmployeeStationRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VwEmployeeStation);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid EmployeeId { get; set; }

        public virtual Guid StationId { get; set; }

        public virtual Guid? ParentStationId { get; set; }

        public virtual string? StationName { get; set; }

        public virtual DateTime? StationStartDate { get; set; }

        public virtual DateTime? StationEndDate { get; set; }

        public virtual DateTime? StartDate { get; set; }

        public virtual DateTime? EndDate { get; set; }

    }

	public abstract partial class VwEmployeeStationMeta : VwEmployeeStationRaw
	{
	}

	public partial class VwEmployeeStationModel : VwEmployeeStationMeta
	{
	}

	public partial class VwEmployeeStationQuery : VwEmployeeStationMeta
	{
	}

	public partial class VwEmployeeStationSelector : VwEmployeeStationMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VwEmployeeStation))]
	public partial class VwEmployeeStationFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VwEmployeeStation))]
	public partial class VwEmployeeStationSelectorFilter : PagingFilterModel
	{
	}

	#endregion VwEmployeeStation


	#region VwOrganization

    [MapFromType(typeof(Shinsoft.DDI.Entities.VwOrganization), Reverse = true)]
	public abstract class VwOrganizationRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name.GetValueOrDefault();

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VwOrganization);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual string ID { get; set; } = string.Empty;

        public virtual string? ParentId { get; set; }

        public virtual OrganizationType EnumType { get; set; }

		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

        public virtual string? Name { get; set; }

        public virtual bool? Valid { get; set; }

        public virtual int Level { get; set; }

        public virtual int? Grade { get; set; }

        public virtual Guid SubCompanyId { get; set; }

        public virtual Guid? DepartmentId { get; set; }

        public virtual Guid? StationId { get; set; }

        public virtual Guid? EmployeeId { get; set; }

    }

	public abstract partial class VwOrganizationMeta : VwOrganizationRaw
	{
	}

	public partial class VwOrganizationModel : VwOrganizationMeta
	{
	}

	public partial class VwOrganizationQuery : VwOrganizationMeta
	{
	}

	public partial class VwOrganizationSelector : VwOrganizationMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VwOrganization))]
	public partial class VwOrganizationFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VwOrganization))]
	public partial class VwOrganizationSelectorFilter : PagingFilterModel
	{
	}

	#endregion VwOrganization


	#region VwRoleMember

    [MapFromType(typeof(Shinsoft.DDI.Entities.VwRoleMember), Reverse = true)]
	public abstract class VwRoleMemberRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VwRoleMember);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? MemberName { get; set; }

        public virtual Guid RoleId { get; set; }

        public virtual RoleMemberType EnumType { get; set; }

		public virtual string EnumTypeDesc => this.EnumType.GetDesc();

        public virtual Guid MemberId { get; set; }

        public virtual Guid? UserId { get; set; }

        public virtual Guid? EmployeeId { get; set; }

        public virtual Guid? StationId { get; set; }

        public virtual Guid? PositionId { get; set; }

        public virtual Guid? DepartmentID { get; set; }

    }

	public abstract partial class VwRoleMemberMeta : VwRoleMemberRaw
	{
	}

	public partial class VwRoleMemberModel : VwRoleMemberMeta
	{
	}

	public partial class VwRoleMemberQuery : VwRoleMemberMeta
	{
	}

	public partial class VwRoleMemberSelector : VwRoleMemberMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VwRoleMember))]
	public partial class VwRoleMemberFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VwRoleMember))]
	public partial class VwRoleMemberSelectorFilter : PagingFilterModel
	{
	}

	#endregion VwRoleMember


	#region VwStation

    [MapFromType(typeof(Shinsoft.DDI.Entities.VwStation), Reverse = true)]
	public abstract class VwStationRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name.GetValueOrDefault();

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VwStation);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid? ParentId { get; set; }

        public virtual Guid DepartmentId { get; set; }

        public virtual Guid PositionId { get; set; }

        public virtual string? Name { get; set; }

        public virtual bool Valid { get; set; }

        public virtual DateTime? StartDate { get; set; }

        public virtual DateTime? EndDate { get; set; }

        public virtual string Remark { get; set; } = string.Empty;

        public virtual int Uid { get; set; }

        public virtual string UidPath { get; set; } = string.Empty;

    }

	public abstract partial class VwStationMeta : VwStationRaw
	{
	}

	public partial class VwStationModel : VwStationMeta
	{
	}

	public partial class VwStationQuery : VwStationMeta
	{
	}

	public partial class VwStationSelector : VwStationMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VwStation))]
	public partial class VwStationFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VwStation))]
	public partial class VwStationSelectorFilter : PagingFilterModel
	{
	}

	#endregion VwStation


	#region VwStationLeader

    [MapFromType(typeof(Shinsoft.DDI.Entities.VwStationLeader), Reverse = true)]
	public abstract class VwStationLeaderRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VwStationLeader);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual string ID { get; set; } = string.Empty;

        public virtual Guid? EmployeeId { get; set; }

        public virtual Guid StationId { get; set; }

        public virtual DateTime? StationStartDate { get; set; }

        public virtual DateTime? StationEndDate { get; set; }

        public virtual Guid? LineManagerStationId { get; set; }

        public virtual DateTime? LineManagerStationStartDate { get; set; }

        public virtual DateTime? LineManagerStationEndDate { get; set; }

        public virtual Guid? LineManagerId { get; set; }

        public virtual DateTime? LineManagerStartDate { get; set; }

        public virtual DateTime? LineManagerEndDate { get; set; }

        public virtual Guid? LeaderStationId { get; set; }

        public virtual DateTime? LeaderStationStartDate { get; set; }

        public virtual DateTime? LeaderStationEndDate { get; set; }

        public virtual Guid? LeaderId { get; set; }

        public virtual DateTime? LeaderStartDate { get; set; }

        public virtual DateTime? LeaderEndDate { get; set; }

    }

	public abstract partial class VwStationLeaderMeta : VwStationLeaderRaw
	{
	}

	public partial class VwStationLeaderModel : VwStationLeaderMeta
	{
	}

	public partial class VwStationLeaderQuery : VwStationLeaderMeta
	{
	}

	public partial class VwStationLeaderSelector : VwStationLeaderMeta
	{
	}

	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VwStationLeader))]
	public partial class VwStationLeaderFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(Shinsoft.DDI.Entities.VwStationLeader))]
	public partial class VwStationLeaderSelectorFilter : PagingFilterModel
	{
	}

	#endregion VwStationLeader

}

#pragma warning restore CS8669
