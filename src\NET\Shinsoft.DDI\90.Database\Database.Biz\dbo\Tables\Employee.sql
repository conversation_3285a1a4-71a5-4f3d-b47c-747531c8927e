--员工
CREATE TABLE [dbo].[Employee] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL        CONSTRAINT [DF_Employee_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [UserId]                    UNIQUEIDENTIFIER            NULL,
    [Uid]                       INT                         NOT NULL        IDENTITY (1, 1) ,
    [EnumFlags]                 INT                         NOT NULL,
    [LoginName]				    NVARCHAR(50)		        NOT NULL,		    --登录名
    [JobNo]                     NVARCHAR(50)                NOT NULL,
    [DisplayName]               NVARCHAR(100)               NOT NULL,
    [Title]                     NVARCHAR(100)               NOT NULL,
    [Position]                  NVARCHAR(100)               NOT NULL,
    [EnumGender]                INT                         NOT NULL,
    [Email]                     NVARCHAR(100)               NOT NULL,
    [Tel]                       NVARCHAR(50)                NOT NULL,
    [Mobile]                    NVARCHAR(20)                NOT NULL,
    [Remark]                    NVARCHAR(500)               NOT NULL,
    [EnumStatus]	            INT			                NOT	NULL, 
    [LineManagerId]             UNIQUEIDENTIFIER            NULL,
    [MajorSubCompanyId]         UNIQUEIDENTIFIER            NULL,
    [MajorDepartmentId]         UNIQUEIDENTIFIER            NULL,
    [MajorCostCenterId]         UNIQUEIDENTIFIER            NULL,
    [MajorStationId]            UNIQUEIDENTIFIER            NULL,
    [HireDate]                  DATETIME                    NULL,
    [LeaveDate]                 DATETIME                    NULL,
    [SyncTime]                  DATETIME                    NULL,
    [Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL,
    [CreateTime]                DATETIME                    NULL,
    [LastEditor]                NVARCHAR(50)                NULL,
    [LastEditTime]              DATETIME                    NULL,
    CONSTRAINT [PK_Employee] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Employee_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_Employee_User_00_User_Employees] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User] ([ID]),
    CONSTRAINT [FK_Employee_Employee_00_LineManager] FOREIGN KEY ([LineManagerId]) REFERENCES [dbo].[Employee] ([ID]),
    CONSTRAINT [FK_Employee_SubCompany_00_MajorSubCompany] FOREIGN KEY ([MajorSubCompanyId]) REFERENCES [dbo].[SubCompany] ([ID]),
    CONSTRAINT [FK_Employee_Department_00_MajorDepartment] FOREIGN KEY ([MajorDepartmentId]) REFERENCES [dbo].[Department] ([ID]),
    CONSTRAINT [FK_Employee_CostCenter_00_MajorCostCenter] FOREIGN KEY ([MajorCostCenterId]) REFERENCES [dbo].[CostCenter] ([ID]),
    CONSTRAINT [FK_Employee_Station_00_MajorStation] FOREIGN KEY ([MajorStationId]) REFERENCES [dbo].[Station] ([ID]),
);
GO

CREATE INDEX [IX_Employee_Uid] ON [dbo].[Employee]
(
	[Uid] ASC
);

GO



EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'员工',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'用户ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = N'UserId'
GO
EXECUTE sp_addextendedproperty 
    @name = N'MS_Description',
    @value = N'邮箱', 
    @level0type = N'SCHEMA', 
    @level0name = N'dbo', 
    @level1type = N'TABLE', 
    @level1name = N'Employee', 
    @level2type = N'COLUMN', 
    @level2name = N'Email';
GO
EXECUTE sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'性别', 
    @level0type = N'SCHEMA', 
    @level0name = N'dbo', 
    @level1type = N'TABLE', 
    @level1name = N'Employee', 
    @level2type = N'COLUMN', 
    @level2name = N'EnumGender';
GO
EXECUTE sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'工号', 
    @level0type = N'SCHEMA', 
    @level0name = N'dbo', 
    @level1type = N'TABLE', 
    @level1name = N'Employee', 
    @level2type = N'COLUMN', 
    @level2name = 'JobNo';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'姓名',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = 'DisplayName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'手机',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = N'Mobile'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'职称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = N'Title'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'职务',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = N'Position'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'唯一码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = N'Uid'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主岗ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = N'MajorStationId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'状态',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = N'EnumStatus'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主部门ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = N'MajorDepartmentId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'入职日期',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = N'HireDate'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'离职日期',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = 'LeaveDate'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'同步时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = 'SyncTime'

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'登录名',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Employee',
    @level2type = N'COLUMN',
    @level2name = N'LoginName'
