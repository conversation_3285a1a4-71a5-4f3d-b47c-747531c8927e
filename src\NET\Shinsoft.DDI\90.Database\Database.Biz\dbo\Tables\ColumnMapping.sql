﻿CREATE TABLE [dbo].[ColumnMapping] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_ColumnMapping_ID]  DEFAULT (NEWSEQUENTIALID()),
    [ReceiverId]                UNIQUEIDENTIFIER            NOT NULL,
    [DbFiled]                   NVARCHAR(50)                NOT NULL,
    [ColumnMappingTemplateId]   UNIQUEIDENTIFIER                NULL,
    [DateFormatType]            NVARCHAR(50)                    NULL,
    [Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL, 
    [CreateTime]                DATETIME                    NULL, 
    [LastEditor]                NVARCHAR(50)                NULL, 
    [LastEditTime]              DATETIME                    NULL, 
    CONSTRAINT [PK_ColumnMapping] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_ColumnMapping_Receiver] FOREIGN KEY (ReceiverId) REFERENCES [dbo].[Receiver] ([ID]),
    CONSTRAINT [FK_ColumnMapping_ColumnMappingTemplate] FOREIGN KEY (ColumnMappingTemplateId) REFERENCES [dbo].[ColumnMappingTemplate] ([ID]),
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'列映射',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMapping',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMapping',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'收货方Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMapping',
    @level2type = N'COLUMN',
    @level2name = N'ReceiverId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'字段名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMapping',
    @level2type = N'COLUMN',
    @level2name = N'DbFiled'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'模版Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMapping',
    @level2type = N'COLUMN',
    @level2name = N'ColumnMappingTemplateId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'日期格式化类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMapping',
    @level2type = N'COLUMN',
    @level2name = N'DateFormatType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMapping',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMapping',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMapping',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMapping',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ColumnMapping',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'

