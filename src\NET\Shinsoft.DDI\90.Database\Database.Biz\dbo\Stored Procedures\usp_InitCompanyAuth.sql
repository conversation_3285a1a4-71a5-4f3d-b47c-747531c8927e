CREATE PROCEDURE [dbo].[usp_InitCompanyAuth]
    @CompanyCode NVARCHAR(50),
    @IsPlatform BIT = false
AS
	INSERT INTO dbo.CompanyAuth
	(
		CompanyId,
		AuthId,
		Valid
	)
	SELECT
		c.ID AS [CompanyId],
		a.ID AS [AuthId],
		1
	FROM dbo.vAuth AS a
	CROSS JOIN dbo.Company AS c 
	LEFT JOIN dbo.CompanyAuth AS ca ON ca.AuthId = a.ID AND ca.CompanyId = c.ID
	WHERE c.Deleted = 0
    --AND a.EnumAuthType = 1
    --AND a.Deleted = 0
    AND a.Valid = 1
    AND (
        (ISNULL(@IsPlatform,0) = 0 AND (a.EnumAuthFlags & 1) = 0)
        OR (ISNULL(@IsPlatform,0) = 1 AND (a.EnumAuthFlags & 1) <> 0)
    )
    AND c.Code = @CompanyCode
	AND ca.ID IS NULL
	ORDER BY a.Ordinal

	SELECT
		*
	FROM [dbo].[vCompanyAuth] AS ca
    WHERE ca.CompanyCode = @CompanyCode
	ORDER BY ca.CompanyCode,ca.Ordinal


