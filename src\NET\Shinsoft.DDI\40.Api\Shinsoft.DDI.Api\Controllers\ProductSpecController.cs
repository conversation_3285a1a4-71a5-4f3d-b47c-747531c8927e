using Microsoft.AspNetCore.Mvc;
using Shinsoft.Core.Mvc;
using Shinsoft.DDI.Api.Models;
using Shinsoft.DDI.Bll;
using Shinsoft.DDI.Common;
using Shinsoft.DDI.Entities;
using System;
using System.ComponentModel;

namespace Shinsoft.DDI.Api.Controllers
{
    /// <summary>
    /// 产品规格控制器
    /// 提供产品规格相关的API接口，包括增删改查等功能
    /// </summary>
    [ApiExplorerSettings(GroupName = "产品规格维护")]
    public class ProductSpecController : BaseApiController<ProductSpecBll>
    {
        /// <summary>
        /// 查询产品规格
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <returns>产品规格列表</returns>
        [HttpGet]
        // [Auth(AuthCodes.MasterData.Business.ProductSpec_Manage_Query)]
        [LogApi(ApiType.Query, Operate = "查询产品规格")]
        public QueryResult<ProductSpecQuery> QueryProductSpec([FromQuery] ProductSpecFilter filter)
        {          
            return this.Repo.GetDynamicQuery<ProductSpec, ProductSpecQuery>(filter);
        }

        /// <summary>
        /// 获取产品规格详情
        /// </summary>
        /// <param name="id">产品规格ID</param>
        /// <returns>产品规格详情</returns>
        [HttpGet]
        public BizResult<ProductSpecModel> Get(Guid id)
        {
            var result = new BizResult<ProductSpecModel>();

            var entity = this.Repo.Get<ProductSpec>(id);
            if (entity == null)
            {
                result.Error("产品规格不存在");
            }
            else
            {
                result.Data = this.Map<ProductSpecModel>(entity);
            }

            return result;
        }

        /// <summary>
        /// 新增产品规格
        /// </summary>
        /// <param name="model">产品规格模型</param>
        /// <returns>新增结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.ProductSpec_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增产品规格")]
        public BizResult<ProductSpecModel> Add([FromBody] ProductSpecModel model)
        {
            var entity = this.Map<ProductSpec>(model);
            var result = this.Repo.AddProductSpec(entity);
            return result.Map<ProductSpecModel>();
        }

        /// <summary>
        /// 编辑产品规格
        /// </summary>
        /// <param name="model">产品规格模型</param>
        /// <returns>编辑结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.ProductSpec_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑产品规格")]
        public BizResult<ProductSpecModel> Edit([FromBody] ProductSpecModel model)
        {
            var entity = this.Map<ProductSpec>(model);
            var result = this.Repo.UpdateProductSpec(entity);

            return result.Map<ProductSpecModel>();
        }

        /// <summary>
        /// 删除产品规格
        /// </summary>
        /// <param name="model">产品规格</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.ProductSpec_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除产品规格")]
        public BizResult Delete([FromBody] ProductSpec model)
        {
            return this.Repo.DeleteProductSpec(model.ID);
        }
    }
}