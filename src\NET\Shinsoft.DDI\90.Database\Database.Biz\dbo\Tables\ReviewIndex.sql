﻿--数据审核目录
CREATE TABLE [dbo].[ReviewIndex]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_ReviewIndex_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [EnumReviewType]            INT                         NOT NULL,
    [ReviewOperation]           NVARCHAR(50)                NOT NULL,
    [ReviewDataType]            NVARCHAR(200)               NOT NULL,
    [ReviewDataId]              UNIQUEIDENTIFIER            NULL,
    [IsCollection]              BIT                         NOT NULL,
    [Count]                     INT                         NOT NULL,
    [ApprovedCount]             INT                         NOT NULL,
    [SubmitEmployeeId]          UNIQUEIDENTIFIER            NULL,
    [AuditorNames]              NVARCHAR(500)               NOT NULL,
    [ReviewEmployeeId]          UNIQUEIDENTIFIER            NULL,
    [ReviewTime]                DATETIME                    NULL,
    [ReviewRemark]              NVARCHAR(200)               NOT NULL,
    [EnumReviewStatus]          INT                         NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_ReviewIndex] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_ReviewIndex_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_ReviewIndex_Employee_00_SubmitEmployee] FOREIGN KEY ([SubmitEmployeeId]) REFERENCES [dbo].[Employee] ([ID]),
    CONSTRAINT [FK_ReviewIndex_Employee_02_ReviewEmployee] FOREIGN KEY ([ReviewEmployeeId]) REFERENCES [dbo].[Employee] ([ID]),
)
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'数据审核目录',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewIndex',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'审核数据类型（表名）',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewIndex',
    @level2type = N'COLUMN',
    @level2name = N'ReviewDataType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'审核数据ID（主键）',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewIndex',
    @level2type = N'COLUMN',
    @level2name = N'ReviewDataId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否集合',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewIndex',
    @level2type = N'COLUMN',
    @level2name = N'IsCollection'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'集合条数',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewIndex',
    @level2type = N'COLUMN',
    @level2name = N'Count'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'审核通过条数',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewIndex',
    @level2type = N'COLUMN',
    @level2name = N'ApprovedCount'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'提交员工ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewIndex',
    @level2type = N'COLUMN',
    @level2name = N'SubmitEmployeeId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'审核员工ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewIndex',
    @level2type = N'COLUMN',
    @level2name = N'ReviewEmployeeId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'审核时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewIndex',
    @level2type = N'COLUMN',
    @level2name = N'ReviewTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'审核备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewIndex',
    @level2type = N'COLUMN',
    @level2name = N'ReviewRemark'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'审核状态',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewIndex',
    @level2type = N'COLUMN',
    @level2name = N'EnumReviewStatus'