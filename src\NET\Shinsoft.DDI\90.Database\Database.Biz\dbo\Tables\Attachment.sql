CREATE TABLE [dbo].[Attachment]
(
    [ID]					UNIQUEIDENTIFIER        	NOT NULL    CONSTRAINT [DF_Attachment_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]             UNIQUEIDENTIFIER        	NOT NULL,
	[FileIndexId]		    UNIQUEIDENTIFIER			NOT NULL,
    [SrcAttachmentId]       UNIQUEIDENTIFIER            NULL,
    [EnumType]              INT                         NOT NULL,
    [EnumFlags]             INT                         NOT NULL,
	[ObjectType]			NVARCHAR(200)		        NOT NULL,
	[ObjectId]			    NVARCHAR(50)		        NOT NULL,
	[GroupType]			    NVARCHAR(200)		        NOT NULL,
	[GroupId]		        NVARCHAR(50)		        NOT NULL,
	[ItemType]			    NVARCHAR(200)		        NOT NULL,
    [ItemId]		        NVARCHAR(50)		        NOT NULL,
    [ExtGroup]              NVARCHAR(50)		        NOT NULL,
	[FileSize]			    BIGINT			            NOT NULL,
	[ContentType]		    NVARCHAR(200)		        NOT NULL,
	[FileName]			    NVARCHAR(500)		        NOT NULL,
	[FileExt]			    NVARCHAR(50)		        NOT NULL,
    [Deleted]			    BIT					        NOT NULL , 
    [Creator]			    NVARCHAR(50)		        NULL, 
    [CreateTime]			DATETIME			        NULL, 
    [LastEditor]			NVARCHAR(50)		        NULL, 
    [LastEditTime]		    DATETIME			        NULL, 
    CONSTRAINT [PK_Attachment] PRIMARY KEY CLUSTERED ([ID]) , 
    CONSTRAINT [FK_Attachment_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_Attachment_Attachment_00_SrcAttachment] FOREIGN KEY ([SrcAttachmentId]) REFERENCES [dbo].[Attachment] ([ID]),
)

GO
CREATE NONCLUSTERED INDEX [IX_Attachment] ON [dbo].[Attachment]
(
    [CompanyId] ASC,
    [ObjectType] ASC,
    [ObjectId] ASC,
    [ItemId] ASC
);
GO

CREATE NONCLUSTERED INDEX [IX_Attachment_Group] ON [dbo].[Attachment]
(
    [CompanyId] ASC,
    [ObjectType] ASC,
    [GroupId] ASC
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'文件索引ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Attachment',
    @level2type = N'COLUMN',
    @level2name = N'FileIndexId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'所属对象类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Attachment',
    @level2type = N'COLUMN',
    @level2name = N'ObjectType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'所属对象ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Attachment',
    @level2type = N'COLUMN',
    @level2name = N'ObjectId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'所属对象条目ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Attachment',
    @level2type = N'COLUMN',
    @level2name = N'ItemId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'文件大小',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Attachment',
    @level2type = N'COLUMN',
    @level2name = N'FileSize'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'文件类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Attachment',
    @level2type = N'COLUMN',
    @level2name = N'ContentType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'文件名',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Attachment',
    @level2type = N'COLUMN',
    @level2name = N'FileName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'文件扩展名',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Attachment',
    @level2type = N'COLUMN',
    @level2name = N'FileExt'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'附件',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Attachment',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'所属对象组ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Attachment',
    @level2type = N'COLUMN',
    @level2name = N'GroupId'

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'额外分组',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Attachment',
    @level2type = N'COLUMN',
    @level2name = N'ExtGroup'
