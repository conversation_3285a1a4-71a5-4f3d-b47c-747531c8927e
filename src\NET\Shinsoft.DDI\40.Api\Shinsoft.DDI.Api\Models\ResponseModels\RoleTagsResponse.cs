﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    [MapFromType(typeof(Role), Reverse = false)]
    public class RoleTagsResponse : BaseModel
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        [Description("角色ID")]
        [Required]
        [MapFromProperty(typeof(Role), Role.Columns.ID)]
        public Guid RoleId { get; set; }

        /// <summary>
        /// 角色名称
        /// </summary>
        [Description("角色名称")]
        [MapFromProperty(typeof(Role), Role.Columns.Name)]
        public string? RoleName { get; set; }

        /// <summary>
        /// 标签类型
        /// </summary>
        [Description("标签类型")]
        [Required]
        public AuthTagType EnumTagType { get; set; }

        /// <summary>
        /// 是否允许全部
        /// </summary>
        [Description("是否允许全部")]
        public bool AllowAll { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        [Description("标签")]
        public List<AuthTagQuery>? Tags { get; set; }
    }
}