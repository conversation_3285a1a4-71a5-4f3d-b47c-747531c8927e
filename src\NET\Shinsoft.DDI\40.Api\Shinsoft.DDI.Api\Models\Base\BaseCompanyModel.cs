﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public class BaseCompanyModel : BaseModel
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        [Description("公司ID")]
        [MapFromProperty(Reverse = false)]
        [JsonIgnore]
        public virtual Guid CompanyId { get; set; }

        protected virtual CompanyCache CompanyCache => this.GetCompanyCache(this.CompanyId);

        protected virtual CompanyCache GetCompanyCache(Guid companyId)
        {
            var pool = HostContext.GetRequiredService<CompanyCachePool>();

            return pool.GetCompanyCache(companyId);
        }

        protected virtual CompanyCache GetCompanyCache(IRepository repo)
        {
            var companyId = this.CompanyId;

            if (companyId.IsEmpty())
            {
                if (repo is ICompanyRepo companyRepo)
                {
                    companyId = companyRepo.CurrentCompanyId;
                }
                else
                {
                    var user = MvcHttpContext.GetOperatorUser<User>();

                    if (user != null)
                    {
                        companyId = user.CurrentCompanyId;
                    }
                    else
                    {
                        companyId = Config.DefaultCompanyId;
                    }
                }
            }

            return this.GetCompanyCache(companyId);
        }
    }
}
