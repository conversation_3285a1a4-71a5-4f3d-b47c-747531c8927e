﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class EmployeeStationFilter
    {
        /// <summary>
        /// 分公司ID
        /// </summary>
        [Description("分公司ID")]
        [DynamicQueryColumn(typeof(EmployeeStation), EmployeeStation.Columns.CompanyId, Operation = Operation.Equal)]
        public Guid? SubCompanyId { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        [Description("部门ID")]
        public Guid? DepartmentId { get; set; }

        /// <summary>
        /// 岗位ID
        /// </summary>
        [Description("岗位ID")]
        [DynamicQueryColumn(typeof(EmployeeStation), EmployeeStation.Columns.StationId, Operation = Operation.Equal)]
        public Guid? StationId { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        [Description("工号")]
        [DynamicQueryColumn(typeof(EmployeeStation), EmployeeStation.Foreigns.Employee, Employee.Columns.JobNo, Operation = Operation.StringIntelligence)]
        public string? EmployeeJobNo { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [Description("姓名")]
        [DynamicQueryColumn(typeof(EmployeeStation), EmployeeStation.Foreigns.Employee, Employee.Columns.DisplayName, Operation = Operation.StringIntelligence)]
        public string? EmployeeName { get; set; }

        /// <summary>
        /// 关键词（工号,名称）
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(EmployeeStation), EmployeeStation.Foreigns.Employee, Employee.Columns.JobNo, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(EmployeeStation), EmployeeStation.Foreigns.Employee, Employee.Columns.DisplayName, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }
    }
}
