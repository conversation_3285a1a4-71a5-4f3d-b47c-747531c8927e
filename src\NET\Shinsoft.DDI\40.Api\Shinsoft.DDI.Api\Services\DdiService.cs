﻿using Microsoft.Extensions.Primitives;
using Microsoft.Net.Http.Headers;
using Shinsoft.DDI.Api.App_Start.Interfaces;
using System;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Xml;
using static Shinsoft.DDI.Common.ConstDefinition;

namespace Shinsoft.DDI.Api
{
    [AllowAnonymous, SdrAuthorize]
    [Route("ddi/[action]")]
    [ApiExplorerSettings(GroupName = "DDI")]
    public class DdiService : BaseApiController<DdiBll>, IReceiverController
    {
        #region IReceiverController

        string IReceiverController.SdrCode => this.SdrCode;

        Receiver? IReceiverController.RawReceiver
        {
            get => _receiver;
            set => _receiver = value;
        }

        #endregion IReceiverController

        #region Receiver

        protected virtual string SdrCode => this.Request.Headers[Config.SDR.CodeHeader].AsString();

        private Receiver? _receiver = null;

        protected virtual Receiver CurrentReceiver => _receiver ??= this.GetCurrentReceiver();

        protected virtual Receiver GetCurrentReceiver()
        {
            var sdrCode = this.SdrCode;

            if (sdrCode.IsEmpty())
            {
                throw new Exception("获取经销商授权码失败");
            }

            var repo = this.GetRepo<SysBll>(Config.DefaultCompanyId);

            var receiver = repo.GetEntity<Receiver>(p => p.SdrCode == this.SdrCode);

            if (receiver == null)
            {
                throw new Exception("获取经销商失败");
            }

            return receiver;
        }

        protected override Guid? OperatorCompanyId => null;

        #endregion Receiver

        #region 测试

        /// <summary>
        /// 加密SDR令牌
        /// </summary>
        [SdrAuthorize(false)]
        [HttpGet]
        public string EncryptSdrToken([FromQuery] string sdrCode, [FromQuery] DateTime? day = null)
        {
            return Config.Debug
                ? SdrAuthHelper.EncryptSdrToken(sdrCode, day)
                : "只允许测试环境调用";
        }

        /// <summary>
        /// 验证SDR令牌
        /// </summary>
        [SdrAuthorize(false)]
        [HttpGet]
        public bool ValdateSdrToken([FromQuery] string sdrCode, [FromQuery] string sdrToken, [FromQuery] DateTime? day = null)
        {
            return SdrAuthHelper.ValdateSdrToken(sdrCode, sdrToken, day);
        }

        /// <summary>
        /// 测试SDR令牌
        /// </summary>
        [HttpGet]
        public IActionResult TestSdrToken()
        {
            return this.Xml(this.CurrentReceiver.Map<ReceiverModel>());
        }

        #endregion 测试

        /// <summary>
        /// 校验经销商授权码
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Interface, Operate = "获取经销商配置")]
        public IActionResult VerifyCode([FromForm] IFormCollection form)
        {
            object? result = null;

            if (this.CurrentReceiver.Client == null)
            {
                ClientResult verifyException = new ClientResult()
                {
                    resHead = new ClientResultInfo()
                    {
                        resCode = this.CurrentReceiver.Code,
                        resMsg = "该经销商未配置"
                    }
                };
            }
            else
            {
                var version = form["version"].ToString();
                //更新客户端当前版本信息
                this.Repo.UpdateReceiverClientVersion(this.CurrentReceiver.Client.ID, version);

                result = this.CurrentReceiver.Client.Map<ReceiverCfg>();
            }

            return this.Xml(result);
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Interface, Operate = "保存数据", Mask = ApiMask.Input)]
        public IActionResult SaveData([FromForm] IFormCollection form)
        {
            //流向类型（B/S/I），B-买进、S-卖出、I-库存
            var dataType = form["dataType"].ToString();
            //流向数据，XML格式
            var dataXml = form["dataXml"].ToString();
            //分配给经销商的WS存数帐号
            var user = form["user"].ToString();
            //分配给经销商的WS存数密码
            var pwd = form["pwd"].ToString();

            //校验参数有效性
            var errors = new List<string>();

            if (dataType.IsEmpty())
            {
                errors.Add("流向类型不能空");
            }
            else
            {
                if (dataType != SalesFlowDataType.B.ToString() && dataType != SalesFlowDataType.S.ToString() && dataType != SalesFlowDataType.I.ToString())
                {
                    errors.Add("流向类型格式不正确");
                }
            }

            if (dataXml.IsEmpty())
            {
                errors.Add("流向数据不能空");
            }
            else
            {
                // 验证是否为符合条件格式的XML
                var xmlValidationResult = ValidateXmlFormat(dataXml);
                if (!xmlValidationResult.IsValid)
                {
                    errors.Add(xmlValidationResult.ErrorMessage);
                }
            }
            if (user.IsEmpty())
            {
                errors.Add("帐号不能为空");
            }

            if (pwd.IsEmpty())
            {
                errors.Add("密码不能为空");
            }

            if (!user.IsEmpty() && !pwd.IsEmpty())
            {
                if (this.CurrentReceiver.Client != null && (this.CurrentReceiver.Client.WSUsername != user || this.CurrentReceiver.Client.WSPassword != pwd))
                {
                    errors.Add("用户名密码校验不通过");
                }
            }

            if (errors.Any())
            {
                ClientResult verifyException = new ClientResult()
                {
                    resHead = new ClientResultInfo()
                    {
                        resCode = this.CurrentReceiver.Code,
                        resMsg = string.Join(";", errors)
                    }
                };

                return this.Xml(verifyException);
            }

            //调用业务方法，处理数据
            this.Repo.SaveClientData((SalesFlowDataType)Enum.Parse(typeof(SalesFlowDataType), dataType), dataXml, this.CurrentReceiver.Code);

            ClientResult result = new ClientResult()
            {
                resHead = new ClientResultInfo()
                {
                    resCode = "OK"
                }
            };

            return this.Xml(result);
        }

        /// <summary>
        /// 保存日志到服务端
        /// </summary>
        /// <param name="form"></param>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.Interface, Operate = "保存日志到服务端")]
        public IActionResult SaveLog([FromForm] IFormCollection form)
        {
            //日志内容
            var log = form["log"].ToString();
            //日志时间
            var logtime = form["logtime"].ToString();
            //分配给经销商的日志帐号
            var user = form["user"].ToString();
            //分配给经销商的日志密码
            var pwd = form["pwd"].ToString();

            //校验参数有效性
            var errors = new List<string>();

            if (log.IsEmpty())
            {
                errors.Add("日志内容不能空");
            }
            if (logtime.IsEmpty())
            {
                errors.Add("日志时间不能空");
            }
            if (user.IsEmpty())
            {
                errors.Add("帐号不能为空");
            }
            if (pwd.IsEmpty())
            {
                errors.Add("密码不能为空");
            }

            if (!user.IsEmpty() && !pwd.IsEmpty())
            {
                if (this.CurrentReceiver.Client != null && (this.CurrentReceiver.Client.WSUsername != user || this.CurrentReceiver.Client.WSPassword != pwd))
                {
                    errors.Add("用户名密码校验不通过");
                }
            }

            if (errors.Any())
            {
                ClientResult verifyException = new ClientResult()
                {
                    resHead = new ClientResultInfo()
                    {
                        resCode = this.CurrentReceiver.Code,
                        resMsg = string.Join(";", errors)
                    }
                };

                return this.Xml(verifyException);
            }

            var receiverClientLog = new ReceiverClientLogModel()
            {
                ID = Guid.NewGuid(),
                ReceiverCode = this.CurrentReceiver.Code,
                ReceiverName = this.CurrentReceiver.Name,
                LogTime = Convert.ToDateTime(logtime),
                Message = log
            }.Map<ReceiverClientLog>();

            //调用业务处理数据
            this.Repo.SaveClientLog(receiverClientLog);

            return this.Ok();
        }

        /// <summary>
        /// 验证XML格式是否符合要求
        /// </summary>
        /// <param name="xmlData">XML数据</param>
        /// <returns>验证结果</returns>
        private XmlValidationResult ValidateXmlFormat(string xmlData)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(xmlData))
                {
                    return new XmlValidationResult(false, "XML数据不能为空");
                }

                // 尝试解析XML
                var xmlDoc = new System.Xml.XmlDocument();
                xmlDoc.LoadXml(xmlData);

                // 验证根节点R是否存在
                var rootNode = xmlDoc.SelectSingleNode(ClientXMLNode.RootNode);
                if (rootNode == null)
                {
                    return new XmlValidationResult(false, $"XML格式错误：缺少根节点{ClientXMLNode.RootNode}");
                }

                // 验证头节点H是否存在
                var headerNode = rootNode.SelectSingleNode(ClientXMLNode.HeaderNode);
                if (headerNode == null)
                {
                    return new XmlValidationResult(false, $"XML格式错误：缺少头节点{ClientXMLNode.HeaderNode}");
                }

                // 验证头节点是否包含字段定义
                var headerChildren = headerNode.ChildNodes;
                if (headerChildren.Count == 0)
                {
                    return new XmlValidationResult(false, "XML格式错误：头节点{ClientXMLNode.RootNode}中没有字段定义");
                }

                // 验证头节点中的字段是否按I1, I2, I3...格式命名
                var expectedFieldCount = headerChildren.Count;
                for (int i = 1; i <= expectedFieldCount; i++)
                {
                    var expectedFieldName = $"{ClientXMLNode.ItemNode}{i}";
                    var fieldNode = headerNode.SelectSingleNode(expectedFieldName);
                    if (fieldNode == null)
                    {
                        return new XmlValidationResult(false, $"XML格式错误：头节点中缺少字段{expectedFieldName}");
                    }

                    if (string.IsNullOrWhiteSpace(fieldNode.InnerText))
                    {
                        return new XmlValidationResult(false, $"XML格式错误：字段{expectedFieldName}的值不能为空");
                    }
                }

                // 验证是否存在数据节点D
                var dataNodes = rootNode.SelectNodes(ClientXMLNode.DataNode);
                if (dataNodes == null || dataNodes.Count == 0)
                {
                    return new XmlValidationResult(false, $"XML格式错误：没有找到数据节点{ClientXMLNode.DataNode}");
                }

                // 验证每个数据节点D的结构
                foreach (System.Xml.XmlNode dataNode in dataNodes)
                {
                    // 验证数据节点中的字段数量是否与头节点一致
                    for (int i = 1; i <= expectedFieldCount; i++)
                    {
                        var expectedFieldName = $"{ClientXMLNode.ItemNode}{i}";
                        var fieldNode = dataNode.SelectSingleNode(expectedFieldName);
                        if (fieldNode == null)
                        {
                            return new XmlValidationResult(false, $"XML格式错误：数据节点中缺少字段{expectedFieldName}");
                        }
                    }
                }

                return new XmlValidationResult(true, "XML格式验证通过");
            }
            catch (System.Xml.XmlException ex)
            {
                return new XmlValidationResult(false, $"XML格式错误：{ex.Message}");
            }
            catch (Exception ex)
            {
                return new XmlValidationResult(false, $"XML验证失败：{ex.Message}");
            }
        }
    }
}