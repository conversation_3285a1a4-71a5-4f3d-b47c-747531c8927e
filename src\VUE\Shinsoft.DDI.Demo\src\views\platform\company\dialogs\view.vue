<script setup lang="ts">
import { sysSetupApi } from "@/api/sysSetup";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineOptions({
  name: "company:config:view"
});

const props = defineProps({
  title: {
    type: String,
    default: "查看公司"
  },
  draggable: {
    type: Boolean,
    default: true
  },
  // 宽度
  width: {
    type: String,
    default: "60%"
  },
  // 字段：间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // 字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // 字段内容：宽度
  formColSpan: {
    type: Number,
    default: 24
  },
  // 按钮：圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // 按钮：关闭按钮：图标
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  }
});

const formRef = ref();

const visible = ref(false);
const loading = ref(false);

const defaultModel: Record<string, any> = {};

const model: Record<string, any> = ref(defaultModel);

const open = (id: string) => {
  show();
  if (id) {
    get(id);
  } else {
    close();
  }
};

const show = () => {
  visible.value = true;
  model.value = defaultModel;
};

const get = (id: string) => {
  loading.value = true;
  sysSetupApi
    .GetCompany(id)
    .then(res => {
      if (res.success) {
        model.value = res.data;
      } else {
        close();
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const close = (msg?: string) => {
  visible.value = false;
};

defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        v-loading="loading"
        :model="model"
        label-position="right"
        :label-width="formLabelWidth"
        class="el-dialog-form"
      >
        <el-row :gutter="formGutter">
          <el-col :span="formColSpan">
            <el-form-item prop="code" label="编码">
              <span>{{ model.code }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="name" label="名称">
              <span>{{ model.name }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div>
          <el-button
            class="close"
            :round="btnRound"
            :disabled="loading"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
