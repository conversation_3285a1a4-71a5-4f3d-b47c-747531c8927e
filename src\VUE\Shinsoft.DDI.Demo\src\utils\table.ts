import type { Sort } from "element-plus";

export const resetFilter = (filter: Paging<PERSON>ilter, defaultFilter?: Record<string, any>) => {
  Object.keys(filter).forEach(prop => {
    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
    delete filter[prop];
  });

  if (defaultFilter) {
    Object.keys(defaultFilter).forEach(prop => {
      filter[prop] = defaultFilter[prop];
    });
  }
};

export const getDefaultOrder = (defaultOrder: Sort | undefined): string => {
  let order = "";

  if (defaultOrder) {
    const symbol = defaultOrder.order === "descending" ? "-" : "+";
    order = `${symbol}${defaultOrder.prop}`;
  }

  return order;
};

export const getColumnOrder = (column: any): string => {
  let order = "";

  if (column.order) {
    const symbol = column.order === "descending" ? "-" : "+";

    const sortBy = column.column.sortBy;

    if (!sortBy) {
      order = symbol + column.prop;
    } else if (typeof sortBy === "string") {
      order = symbol + sortBy;
    } else if (sortBy instanceof Array) {
      sortBy.every(prop => {
        order += symbol + prop + ",";
      });
    }

    return order;
  }
};

export const getWorkflowColumnOrder = (column: any): string => {
  let order = "";

  if (column.order) {
    const symbol = column.order === "descending" ? "Desc" : "Asc";

    const sortBy = column.column.sortBy;

    if (!sortBy) {
      order = column.prop + " " + symbol;
    } else if (typeof sortBy === "string") {
      order = sortBy + " " + symbol;
    } else if (sortBy instanceof Array) {
      sortBy.every(prop => {
        order += prop + " " + symbol + ",";
      });
    }

    return order;
  }
};
