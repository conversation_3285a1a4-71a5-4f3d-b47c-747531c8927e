﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Controllers
{
    [ApiExplorerSettings(GroupName = "文件")]
    public class FileController : BaseApiController<FileBll>
    {
        #region FileIndex

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.File, Operate = "上传文件")]
        public BizResult<FileIndexModel> UploadFile([FromForm] IFormCollection form)
        {
            var result = new BizResult<FileIndexModel>();

            var file = form.Files[0];

            if (file == null)
            {
                result.Error("未发现上传的文件");
            }
            else
            {
                MemoryStream ms = new();
                try
                {
                    file.CopyTo(ms);

                    var entity = new FileIndex
                    {
                        ContentType = file.ContentType,
                        FileName = file.FileName.GetFileName(),
                    };

                    var bizResult = this.Repo.SaveFile(entity, ms);

                    return bizResult.Map<FileIndexModel>();
                }
                finally
                {
                    ms.Dispose();
                }
            }

            return result;
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="id">文件ID</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("{id}")]
        [LogApi(ApiType.Query, Operate = "下载文件")]
        public IActionResult DownloadFile([FromRoute, Required] Guid id)
        {
            var (fileContent, fileIndex) = this.Repo.DownloadFile(id);

            if (fileContent == null || fileIndex == null)
            {
                return this.StatusCode((int)HttpStatusCode.NotFound);
            }
            else
            {
                return this.File(fileContent, fileIndex.ContentType, fileIndex.FileName);
            }
        }

        #endregion FileIndex

        protected BizResult<Attachment> SaveAttachment(IFormFile file, Attachment? entity = null)
        {
            var result = new BizResult<Attachment>();

            entity ??= new Attachment();

            entity.ContentType = file.ContentType;
            entity.FileName = file.FileName.GetFileName();

            var ms = new MemoryStream();

            try
            {
                file.CopyTo(ms);

                result = this.CompanyBll.SaveAttachment(entity, ms);
            }
            catch (Exception ex)
            {
                result.Error(ex.Message);
            }
            finally
            {
                ms.Dispose();
            }

            return result;
        }

        #region Image

        /// <summary>
        /// 下载图片
        /// </summary>
        /// <param name="id">文件ID</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("{id}")]
        [LogApi(ApiType.Query, Operate = "下载图片")]
        public IActionResult DownloadImage([FromRoute, Required] Guid id)
        {
            var attachment = this.CompanyBll.Get<Attachment>(id);

            if (attachment == null)
            {
                return this.StatusCode((int)HttpStatusCode.NotFound);
            }
            else if (attachment.EnumType != AttachmentType.Image)
            {
                return this.StatusCode((int)HttpStatusCode.BadRequest);
            }
            else
            {
                var (fileBytes, fileIndex) = this.Repo.DownloadFile(attachment.FileIndexId);

                if (fileBytes == null || fileIndex == null)
                {
                    return this.StatusCode((int)HttpStatusCode.NotFound);
                }
                else
                {
                    return this.File(fileBytes, attachment.ContentType, attachment.FileName);
                }
            }
        }

        [HttpPost]
        [LogApi(ApiType.File, Operate = "删除图片")]
        public BizResult DeleteImage(AttachmentModel model)
        {
            return this.CompanyBll.DeleteAttachment(model.ID);
        }

        /// <summary>
        /// 上传图片
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.File, Operate = "上传图片")]
        public BizResult<AttachmentModel> UploadImage([FromForm] IFormCollection form)
        {
            var result = new BizResult<AttachmentModel>();

            var file = form.Files[0];

            if (file == null)
            {
                result.Error("未发现上传的文件");
            }
            else if (!file.IsImage())
            {
                result.Error("上传文件并非媒体");
            }
            else
            {
                var attach = new Attachment
                {
                    EnumType = AttachmentType.Image,
                    EnumFlags = form[nameof(Attachment.EnumFlags)].AsString().As<AttachmentFlag>(),
                    ObjectType = form[nameof(Attachment.ObjectType)].AsString(),
                    ObjectId = form[nameof(Attachment.ObjectId)].AsString(),
                    GroupId = form[nameof(Attachment.GroupId)].AsString(),
                    ItemId = form[nameof(Attachment.ItemId)].AsString(),
                };

                var saveResult = this.SaveAttachment(file, attach);

                result = saveResult.Map<AttachmentModel>();
            }

            return result;
        }

        /// <summary>
        /// 批量上传媒体
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.File, Operate = "批量上传媒体")]
        public BizResult<List<AttachmentModel>> UploadImages([FromForm] IFormCollection form)
        {
            var result = new BizResult<List<AttachmentModel>>();

            if (!form.Files.Any())
            {
                result.Error("未发现上传的文件");
            }
            else if (form.Files.Any(p => !p.IsImage()))
            {
                result.Error("上传文件中存在非媒体");
            }
            else
            {
                var attach = new Attachment
                {
                    EnumType = AttachmentType.Image,
                    EnumFlags = form[nameof(Attachment.EnumFlags)].AsString().As<AttachmentFlag>(),
                    ObjectType = form[nameof(Attachment.ObjectType)].AsString(),
                    ObjectId = form[nameof(Attachment.ObjectId)].AsString(),
                    GroupId = form[nameof(Attachment.GroupId)].AsString(),
                    ItemId = form[nameof(Attachment.ItemId)].AsString(),
                };

                var attachs = new List<Attachment>();

                foreach (var file in form.Files)
                {
                    var clone = attach.Clone();

                    var saveResult = this.SaveAttachment(file, clone);

                    if (saveResult.Success)
                    {
                        attachs.Add(saveResult.Data!);
                    }
                    else
                    {
                        result.Merge(saveResult);
                        break;
                    }
                }

                if (result.Success)
                {
                    result.Data = attachs.Maps<AttachmentModel>();
                }
            }

            return result;
        }

        #endregion Image

        #region Attach

        /// <summary>
        /// 上传附件
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.File, Operate = "上传附件")]
        public BizResult<AttachmentModel> UploadAttach([FromForm] IFormCollection form)
        {
            var result = new BizResult<AttachmentModel>();

            var file = form.Files[0];

            if (file == null)
            {
                result.Error("未发现上传的文件");
            }
            else
            {
                var attach = new Attachment
                {
                    EnumType = AttachmentType.Attach,
                    EnumFlags = form[nameof(Attachment.EnumFlags)].AsString().As<AttachmentFlag>(),
                    ObjectType = form[nameof(Attachment.ObjectType)].AsString(),
                    ObjectId = form[nameof(Attachment.ObjectId)].AsString(),
                    GroupId = form[nameof(Attachment.GroupId)].AsString(),
                    ItemId = form[nameof(Attachment.ItemId)].AsString(),
                };

                var saveResult = this.SaveAttachment(file, attach);

                result = saveResult.Map<AttachmentModel>();
            }

            return result;
        }

        /// <summary>
        /// 批量上传附件
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [LogApi(ApiType.File, Operate = "批量上传附件")]
        public BizResult<List<AttachmentModel>> UploadAttachs([FromForm] IFormCollection form)
        {
            var result = new BizResult<List<AttachmentModel>>();

            if (!form.Files.Any())
            {
                result.Error("未发现上传的文件");
            }
            else
            {
                var attach = new Attachment
                {
                    EnumType = AttachmentType.Attach,
                    EnumFlags = form[nameof(Attachment.EnumFlags)].AsString().As<AttachmentFlag>(),
                    ObjectType = form[nameof(Attachment.ObjectType)].AsString(),
                    ObjectId = form[nameof(Attachment.ObjectId)].AsString(),
                    GroupId = form[nameof(Attachment.GroupId)].AsString(),
                    ItemId = form[nameof(Attachment.ItemId)].AsString(),
                };

                var attachs = new List<Attachment>();

                foreach (var file in form.Files)
                {
                    var clone = attach.Clone();

                    var saveResult = this.SaveAttachment(file, clone);

                    if (saveResult.Success)
                    {
                        attachs.Add(saveResult.Data!);
                    }
                    else
                    {
                        result.Merge(saveResult);
                        break;
                    }
                }

                if (result.Success)
                {
                    result.Data = attachs.Maps<AttachmentModel>();
                }
            }

            return result;
        }

        [HttpPost]
        [LogApi(ApiType.File, Operate = "删除附件")]
        public BizResult DeleteAttach(AttachmentModel model)
        {
            return this.CompanyBll.DeleteAttachment(model.ID);
        }

        /// <summary>
        /// 下载附件
        /// </summary>
        [AllowAnonymous]
        [HttpGet]
        [Route("{id}")]
        [LogApi(ApiType.Query, Operate = "下载附件")]
        public IActionResult DownloadAttach([FromRoute, Required] Guid id)
        {
            var attachment = this.CompanyBll.Get<Attachment>(id);

            if (attachment == null)
            {
                return this.StatusCode((int)HttpStatusCode.NotFound);
            }
            else
            {
                var (fileBytes, fileIndex) = this.Repo.DownloadFile(attachment.FileIndexId);

                if (fileBytes == null || fileIndex == null)
                {
                    return this.StatusCode((int)HttpStatusCode.NotFound);
                }
                else
                {
                    return this.File(fileBytes, attachment.ContentType, attachment.FileName);
                }
            }
        }

        #endregion Attach
    }
}