<script setup lang="ts">
import { basicMasterDataApi } from "@/api/basicMasterData";
import { selectorApi } from "@/api/selector";
import { useUserStoreHook } from "@/store/modules/user";
import { useEnumStoreHook } from "@/store/modules/enum";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;
defineOptions({
  name: "CostCenter:edit"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // 标题：新建
  newTitle: {
    type: String,
    default: "新建分公司"
  },
  // 标题：编辑
  editTitle: {
    type: String,
    default: "编辑分公司"
  },
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "70%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "210px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.model.id
    ? `${t("operate.edit")} ${tt("Entity.SubCompany._Entity")} - ${state.model.name}`
    : `${t("operate.add")} ${tt("Entity.SubCompany._Entity")}`;
});
/**
 * dialog：标题
 */
const defaltDate = computed(() => {
  return state.model.openingDate ? `${state.model.openingDate}` : `${new Date()}`;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      rank: 1,
      valid: true
    }
  },
  // 枚举定义
  enums: {
    enumTypes: []
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  },
  getValid: [
    { key: 1, value: true, name: "是" },
    { key: 0, value: false, name: "否" }
  ],
  // 按钮权限
  btn: {
    save: computed(() => {
      return state.model.id
        ? userStore.hasAnyAuth(["SubCompany:Manage", "SubCompany:Manage:Edit"])
        : userStore.hasAnyAuth(["SubCompany:Manage", "SubCompany:Manage:Add"]);
    })
  }
});

/**
 * 验证规则
 */
const rules = {
  form: {
    name: [{ required: true, message: tt("Rule.SubCompany.Name:Required"), trigger: "blur" }],
    code: [
      { required: true, message: tt("Rule.SubCompany.Code:Required"), trigger: "blur" },
      { max: 50, message: tt("Rule.SubCompany.Code:Length"), trigger: "blur" }
    ],
    shortName: [
      { required: true, message: tt("Rule.SubCompany.ShortName:Required"), trigger: "blur" }
    ]
  }
};

/**
 * 当前用户存储
 */
const userStore = useUserStoreHook();
const enumStore = useEnumStoreHook();

/**
 * 当前组件ref
 */
const formRef = ref<FormInstance>();

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  model: cfg.default.model as Record<string, any>
});
/**
 * 初始化组件（异步）
 */
const init = () => {
  // 防止之前打开的dialog数据残留，因此初始化时赋予model默认值
  state.model = cfg.default.model;

  cfg.loading.form = true;
  const initEnumType = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("SubCompanyType").then(enumInfos => {
        cfg.enums.enumTypes = enumInfos;
        resolve();
      });
    });
  };
  const allInits = [initEnumType()];
  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.form = false;
      resolve();
    });
  });
};

/**
 * 初始化state数据
 */
const initState = () => {
  state.model = cfg.default.model;
};

/**
 * 清空mstate数据
 */
const clearState = () => {
  initState();
  formRef.value.clearValidate();
  formRef.value.resetFields();
};
/**
 * 获取model数据
 */
const get = (id: string) => {
  cfg.loading.form = true;
  basicMasterDataApi
    .GetSubCompany(id)
    .then(res => {
      if (res.success) {
        setModel(res.data);
      } else {
        close();
      }
    })
    .finally(() => {
      cfg.loading.form = false;
    });
};

/**
 * 设置model数据
 */
const setModel = (data: any) => {
  state.model = data;
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 新增数据
 */
const add = () => {
  cfg.loading.btn = true;

  const data = getModel();

  basicMasterDataApi
    .AddSubCompany(data, { messageBoxResult: true })
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh(t("operate.message.success"), "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 更新数据
 */
const update = () => {
  cfg.loading.btn = true;

  const data = getModel();

  basicMasterDataApi
    .UpdateSubCompany(data, { messageBoxResult: true })
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh(t("operate.message.success"), "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 按钮事件：【保存】
 */
const save = async () => {
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      state.model.enumType = 2;
      if (state.model.id) {
        update();
      } else {
        add();
      }
    }
  });
};

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = (id?: string) => {
  cfg.dialog.visible = true;
  init().then(() => {
    if (id) {
      get(id);
    }
  });
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        v-loading="cfg.loading.form"
        :rules="rules.form"
        :model="state.model"
        label-position="right"
        :label-width="formLabelWidth"
        class="el-dialog-form"
      >
        <el-row :gutter="formGutter">
          <el-col :span="formColSpan">
            <el-form-item prop="name" :label="tt('Entity.SubCompany.Name')">
              <el-input
                v-model="state.model.name"
                :placeholder="tt('Entity.SubCompany.Name')"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="code" :label="tt('Entity.SubCompany.Code')">
              <el-input
                v-model="state.model.code"
                :placeholder="tt('Entity.SubCompany.Code')"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="formGutter">
          <el-col :span="formColSpan">
            <el-form-item prop="shortName" :label="tt('Entity.SubCompany.ShortName')">
              <el-input
                v-model="state.model.shortName"
                :placeholder="tt('Entity.SubCompany.ShortName')"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="valid" :label="tt('Entity.SubCompany.Valid')">
              <el-switch
                v-model="state.model.valid"
                class="ml-2"
                inline-prompt
                style="--el-switch-on-color: #13ce66"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.save"
            class="save"
            :round="btnRound"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnSaveIcon)"
            @click="save()"
          >
            {{ t("operate.save") }}
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="css">
.is-not-error > .el-input__wrapper {
  box-shadow: #dcdfe6 !important;
}
</style>
