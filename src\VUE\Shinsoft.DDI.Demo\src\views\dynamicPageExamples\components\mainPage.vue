<script setup lang="ts">
import { goToRoute } from "@/router/utils";
import { useI18n } from "vue-i18n";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Epclose from "@iconify-icons/ep/close-bold";
import Epselect from "@iconify-icons/ep/select";
import { ElMessage, ElMessageBox } from "element-plus";
import { el } from "element-plus/es/locales.mjs";
import { resolve } from "path";
import { rejects } from "assert";
import { isEqual, cloneDeep } from "lodash";
// import Epearch from "@iconify-icons/ep/search";
// import pagess from "./view/page1.vue";

const { t } = useI18n();
const tt = t;

//加载的组件
var componentRefs = ref([]);

var loading = ref(false);
var resModel = ref<Record<string, any>>({});
var oldResModel = ref({});
function getData() {
  loading.value = true;
  setTimeout(() => {
    resModel.value = {
      schema: {
        getApi: "",
        moudles: [
          {
            appMoudleFlag: 2,
            components: [
              {
                enumType: 1,
                code: "0001",
                name: "基本信息",
                component: "../../dynamicPageExamples/components/modify/page1.vue",
                ordinal: 1
              },
              {
                enumType: 1,
                code: "0002",
                name: "供应商信息",
                component: "../../dynamicPageExamples/components/modify/page2.vue",
                ordinal: 2
              },
              {
                enumType: 1,
                code: "0003",
                name: "订单信息",
                component: "../../dynamicPageExamples/components/modify/page3.vue",
                ordinal: 3
              }
            ]
          }
        ]
      },
      model: {
        employeeModel: {
          name: "张三",
          code: "18100000000",
          enumFlags: 2,
          remark:
            "这个是测试这个是测试这个是测试这个是测试这个是测试这个是测试这个是测试这个是测试这个是测试这个是测试这个是测试"
        },
        customerModel: {}
      }
    };
    // provide(resModel.value.model);
    //初始化页面组件
    initComponents();
    loading.value = false;
  }, 1000);
}

// 页面结构compenents
const appMoudles = ref<Record<string, any>>([]);
function initComponents() {
  //后台model定义的是数组  暂时先按取第一个处理
  appMoudles.value = resModel.value?.schema.moudles[0];
  appMoudles.value.components.map((item, index) => {
    let path: string = item.component;
    let component = defineAsyncComponent(() => import(path));
    item["component"] = markRaw(component);
    item["ref"] = "componentRef" + index;
    item.showComponent = true;
    item.moudle = item;
    // createRef("componentRef" + index);
    return item;
  });
  oldResModel.value = cloneDeep(resModel);
  // console.log("qqqqq", appMoudles.value);
}
getData();

//组件内表单验证结果
async function valid(ref) {
  if (typeof ref.validate === "function" && !(await ref.validate())) {
    return false;
  } else {
    return true;
  }
}

function hanleSubmit() {
  var validResult = [];
  componentRefs.value.forEach((ref, index) => {
    validResult.push(valid(ref));
  });

  Promise.all(validResult).then(vr => {
    if (
      vr.some(item => {
        return item === false;
      })
    ) {
      ElMessage.error("验证失败");
    } else {
      console.log(resModel.value);
      ElMessage.success("验证通过");
    }
  });
}
onUnmounted(() => {
  // if (!isEqual(oldResModel.value, resModel.value)) {
  //   ElMessageBox.confirm("页面还没有保存", "提示", {
  //     showClose: false,
  //     closeOnClickModal: false,
  //     draggable: true,
  //     dangerouslyUseHTMLString: true,
  //     confirmButtonText: t("operate.ok"),
  //     cancelButtonText: t("operate.cancel"),
  //     type: "warning"
  //   })
  //     .then(() => {})
  //     .catch(() => {
  //       close();
  //     });
  // }
  // ElMessageBox.confirm("页面还没有保存", "提示", {
  //   showClose: false,
  //   closeOnClickModal: false,
  //   draggable: true,
  //   dangerouslyUseHTMLString: true,
  //   confirmButtonText: t("operate.ok"),
  //   cancelButtonText: t("operate.cancel"),
  //   type: "warning"
  // })
  //   .then(() => {})
  //   .catch(() => {
  //     close();
  //   });
});
</script>

<template>
  <div>
    <div v-loading="loading" class="main-page-content">
      <div v-for="(item, index) in appMoudles.components" :key="index">
        <component
          :is="item['component']"
          ref="componentRefs"
          :key="index"
          v-model:dataModel="resModel.model"
          v-model:moudle="item.moudle"
          :name="item.code"
        />
      </div>
      <div class="footer">
        <div v-if="Object.keys(resModel).length > 0" class="btn-footer">
          <el-button title="拒绝" :icon="useRenderIcon(Epclose)" type="danger">拒绝</el-button>
          <el-button title="暂存" :icon="useRenderIcon(Epselect)" type="success">暂存</el-button>
          <el-button
            title="提交"
            :icon="useRenderIcon(Epselect)"
            type="primary"
            @click="hanleSubmit"
            >提交</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="css" scoped>
:deep(.member-type-button .el-radio-button__inner) {
  width: 120px;
}

:deep(.member-type-button.is-active .el-radio-button__inner) {
  background-color: #2d85dc;
  border-color: #2d85dc;
  box-shadow: -1px 0 0 0 #2d85dc;
}
</style>
