﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class ReceiverQuery
    {
        /// <summary>
        /// 收货方类型第三级
        /// </summary>
        [MapFromProperty(typeof(Receiver), Receiver.Foreigns.ReceiverType, ReceiverType.Columns.Name)]
        public string? ReceiverTypeLevelThreeName { get; set; }

        /// <summary>
        /// 收货方类型第二级
        /// </summary>
        [MapFromProperty(typeof(Receiver), "ReceiverType.ParentReceiverType.Name")]
        public string? ReceiverTypeLevelTwoName { get; set; }

        /// <summary>
        /// 收货方类型第一级
        /// </summary>
        [MapFromProperty(typeof(Receiver), "ReceiverType.ParentReceiverType.ParentReceiverType.Name")]
        public string? ReceiverTypeLevelOneName { get; set; }

        /// <summary>
        /// 上级单位
        /// </summary>
        [MapFromProperty(typeof(Receiver), Receiver.Foreigns.MedicineGroup,Receiver.Columns.Name)]
        public string? MedicineGroupName { get; set; }

        /// <summary>
        /// 目标终端
        /// </summary>
        [MapFromProperty(typeof(Receiver), Receiver.Foreigns.TargetTerminal, Receiver.Columns.Name)]
        public string? TargetTerminalName { get; set; }

        /// <summary>
        /// 省份
        /// </summary>
        [MapFromProperty(typeof(Receiver), Receiver.Foreigns.Province, Province.Columns.Name)]
        public string? ProvinceName { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        [MapFromProperty(typeof(Receiver), Receiver.Foreigns.City, City.Columns.Name)]
        public string? CityName { get; set; }

        /// <summary>
        /// 区县
        /// </summary>
        [MapFromProperty(typeof(Receiver), Receiver.Foreigns.County, County.Columns.Name)]
        public string? CountyName { get; set; }
    }
}