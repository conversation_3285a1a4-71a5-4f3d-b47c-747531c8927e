﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Shinsoft.DDI.Dal;

namespace Shinsoft.DDI.Bll
{
    public abstract class BaseCompanyBll : CompanyRepository<BizDbContext, Guid>, ICompanyRepo
    {
        #region Constructs

        protected BaseCompanyBll(IUser? operatorUser = null)
            : base(operatorUser)
        {
        }

        protected BaseCompanyBll(string operatorUniqueName)
            : base(operatorUniqueName)
        {
        }

        protected BaseCompanyBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        protected BaseCompanyBll(IRepo bll)
            : base(bll.BizRepo)
        {
        }

        #endregion Constructs

        #region IRepo

        protected virtual IRepo SysRepo => this.SysBll;
        protected virtual IRepo BizRepo => this.CompanyBll;
        protected virtual IRepo FileRepo => this.FileBll;
        protected virtual IRepo LogRepo => this.LogBll;
        protected virtual IRepo MailRepo => this.MailBll;

        protected virtual SysBll SysBll => this.GetRepo<SysBll>();
        protected virtual CompanyBll CompanyBll => this.GetRepo<CompanyBll>();
        protected virtual FileBll FileBll => this.GetRepo<FileBll>();
        protected virtual LogBll LogBll => this.GetRepo<LogBll>();
        protected virtual MailBll MailBll => this.GetRepo<MailBll>();

        IRepo IRepo.SysRepo => this.SysRepo;
        IRepo IRepo.BizRepo => this.BizRepo;
        IRepo IRepo.FileRepo => this.FileRepo;
        IRepo IRepo.LogRepo => this.LogRepo;
        IRepo IRepo.MailRepo => this.MailRepo;

        #endregion IRepo

        #region Current Company

        private Guid? _currentCompanyId;

        public override Guid CurrentCompanyId
        {
            get => _currentCompanyId ??= this.GetCurrentCompanyId();
            set
            {
                _currentCompanyId = value;
                _companyCache = null;
            }
        }

        protected Guid GetCurrentCompanyId()
        {
            var companyId = this.OperatorUser?.OperatorCompanyId;

            if (!companyId.HasValue)
            {
                throw new InvalidOperationException($"【{this.GetType().Name}】无法获取当前公司ID");
            }

            return companyId.Value;
        }

        protected Company CurrentCompany => this.CompanyCache.Company;

        #endregion Current Company

        #region Operator User

        /// <summary>
        /// 操作用户（可能为空）
        /// </summary>
        public new User? OperatorUser => base.OperatorUser as User;

        /// <summary>
        /// 操作用户ID（可能为空）
        /// </summary>
        public virtual Guid? OperatorUserId => this.OperatorUser?.ID;

        /// <summary>
        /// 操作员工（可能为空）
        /// </summary>

        public virtual Employee? OperatorEmployee => this.OperatorUser?.Employee;

        /// <summary>
        /// 操作员工ID（可能为空）
        /// </summary>
        public virtual Guid? OperatorEmployeeId => this.OperatorEmployee?.ID;

        #endregion Operator User

        #region Current User

        /// <summary>
        /// 当前用户（操作用户为空时报错）
        /// </summary>
        public new User CurrentUser => (User)base.CurrentUser;

        /// <summary>
        /// 当前用户ID（操作用户为空时报错）
        /// </summary>
        public virtual Guid CurrentUserId => this.CurrentUser.ID;

        /// <summary>
        /// 当前员工（为空时报错）
        /// </summary>
        public virtual Employee CurrentEmployee => this.CurrentUser.Employee ?? throw new InvalidOperationException("当前员工不存在");

        /// <summary>
        /// 当前员工ID（为空时报错）
        /// </summary>
        public virtual Guid CurrentEmployeeId => this.CurrentEmployee.ID;

        #endregion Current User

        #region SysCache

        private SysCache? _sysCache = null;

        protected virtual SysCache SysCache => _sysCache ??= this.GetRequiredService<SysCache>();

        protected virtual SysCache GetSysCache()
        {
            var cache = this.GetRequiredService<SysCache>();

            if (this.ServiceScope != null)
            {
                cache.SetServiceScope(this.ServiceScope);
            }

            return cache;
        }

        #endregion SysCache

        #region CompanyCache

        protected virtual CompanyCachePool CompanyCachePool => this.GetRequiredService<CompanyCachePool>();

        private CompanyCache? _companyCache;

        protected virtual CompanyCache CompanyCache => this.GetCompanyCache();

        protected virtual CompanyCache GetCompanyCache()
        {
            if (_companyCache == null || _companyCache.Company.ID != this.CurrentCompanyId)
            {
                _companyCache = this.GetCompanyCache(this.CurrentCompanyId);
            }

            return _companyCache;
        }

        protected virtual CompanyCache GetCompanyCache(Guid? companyId)
        {
            if (!companyId.HasValue || companyId.IsEmpty())
            {
                throw new InvalidOperationException($"【{this.GetType().Name}】:无法获取当前公司ID");
            }

            var cache = this.CompanyCachePool.GetCompanyCache(companyId.Value);

            if (this.ServiceScope != null && companyId == this.CurrentCompanyId)
            {
                cache.SetServiceScope(this.ServiceScope);
            }

            return cache;
        }

        #endregion CompanyCache

        #region IServicePack

        public override void ClearServiceScope()
        {
            base.ClearServiceScope();

            _sysCache?.ClearServiceScope();

            _companyCache?.ClearServiceScope();
        }

        #endregion IServicePack

        private string ExecSerialNumber(string prefix, int? count = null, DateTime? date = null, string? dateFormat = null, int? seedLength = null)
        {
            count ??= 1;
            date ??= SysDateTime.Now;

            var dbParams = new List<DbParameter>
            {
                this.CreateParameter("CompanyId", this.CurrentCompanyId),
                this.CreateParameter("Prefix", prefix),
                this.CreateParameter("Date", date),
                this.CreateParameter("Count", count),
                this.CreateParameter("OperatorUser", this.OperatorUniqueName)
            };

            if (!dateFormat.IsEmpty())
            {
                dbParams.Add(this.CreateParameter("DateFormat", dateFormat));
            }

            if (seedLength > 0)
            {
                dbParams.Add(this.CreateParameter("SeedLength", seedLength));
            }

            var snParam = this.CreateParameter("SerialNumber", DbType.String);

            dbParams.Add(snParam);

            lock (this.CompanyLocker)
            {
                this.ExecuteNonQuery(CommandType.StoredProcedure, "dbo.sp_GetSerialNumber", dbParams);
            }

            var value = snParam.Value.AsString();

            return value;
        }

        public virtual string GetSerialNumber(string prefix, DateTime? date = null, string? dateFormat = null, int? seedLength = null)
        {
            var sn = this.ExecSerialNumber(prefix, 1, date, dateFormat, seedLength);

            return sn;
        }

        public virtual List<string> GetSerialNumbers(string prefix, int count, DateTime? date = null, string? dateFormat = null, int? seedLength = null)
        {
            var sn = this.ExecSerialNumber(prefix, count, date, dateFormat, seedLength);

            var sns = sn.Split(';', options: StringSplitOptions.TrimEntries).ToList();

            return sns;
        }

        #region Save Attachment

        #region public

        /// <summary>
        /// 保存附件（文件必定会保存，附件[Attachment]只有当ObjectName和ObjectId不为空时才会保存，否则返回的ID为空的Attachment）
        /// </summary>
        /// <param name="entity">附件实体</param>
        /// <param name="fileStream">文件流</param>
        /// <param name="uniqueCheck">是否做唯一性检查</param>
        /// <returns></returns>
        public BizResult<Attachment> SaveAttachment(Attachment entity, Stream fileStream, bool uniqueCheck = true)
        {
            return this.SaveAttachment(entity, fileStream, string.Empty, string.Empty, uniqueCheck, true);
        }

        /// <summary>
        /// 保存附件（同时保存文件）
        /// </summary>
        /// <param name="entity">附件实体</param>
        /// <param name="fileStream">文件流</param>
        /// <param name="baseFolder">基本目录（不为空时，强制使用文件方式存储）</param>
        /// <param name="subFolder">子目录（以"\"分割的字符串，其中{}内为日期格式化字符串）</param>
        /// <param name="uniqueCheck">是否做唯一性检查</param>
        /// <returns></returns>
        public BizResult<Attachment> SaveAttachment(Attachment entity, Stream fileStream, string baseFolder, string subFolder, bool uniqueCheck = true)
        {
            return this.SaveAttachment(entity, fileStream, baseFolder, subFolder, uniqueCheck, true);
        }

        /// <summary>
        /// 删除附件（不删除实际文件）
        /// </summary>
        /// <param name="entity">附件实体</param>
        /// <returns></returns>
        public BizResult DeleteAttachment(Attachment entity)
        {
            return this.DeleteAttachment(entity.ID);
        }

        /// <summary>
        /// 删除附件（不删除实际文件）
        /// </summary>
        /// <param name="id">附件实体ID</param>
        /// <returns></returns>
        public BizResult DeleteAttachment(Guid id)
        {
            var result = new BizResult();

            if (!id.IsEmpty())
            {
                var dbEntity = this.Get<Attachment>(id);

                if (dbEntity == null)
                {
                    result.Error("附件不存在");
                }
                else
                {
                    this.Delete(dbEntity);
                }
            }

            return result;
        }

        #endregion public

        #region internal

        /// <summary>
        /// 保存附件（同时保存文件,ObjectName和ObjectId不为空时Attachment也会保存,否则不保存）
        /// </summary>
        /// <param name="entity">附件实体</param>
        /// <param name="fileStream">文件流</param>
        /// <param name="baseFolder">基本目录（不为空时，强制使用文件方式存储）</param>
        /// <param name="subFolder">子目录（以"\"分割的字符串，其中{}内为日期格式化字符串）</param>
        /// <param name="uniqueCheck">是否做唯一性检查</param>
        /// <param name="save">是否保存Attachment（无论是否保存，文件必定会保存）</param>
        /// <returns></returns>
        internal BizResult<Attachment> SaveAttachment(Attachment entity, Stream fileStream, bool uniqueCheck, bool save)
        {
            return this.SaveAttachment(entity, fileStream, string.Empty, string.Empty, uniqueCheck, save);
        }

        /// <summary>
        /// 保存附件（同时保存文件,ObjectName和ObjectId不为空时Attachment也会保存,否则不保存）
        /// </summary>
        /// <param name="entity">附件实体</param>
        /// <param name="fileStream">文件流</param>
        /// <param name="baseFolder">基本目录（不为空时，强制使用文件方式存储）</param>
        /// <param name="subFolder">子目录（以"\"分割的字符串，其中{}内为日期格式化字符串）</param>
        /// <param name="uniqueCheck">是否做唯一性检查</param>
        /// <param name="save">是否保存Attachment（无论是否保存，文件必定会保存）</param>
        /// <returns></returns>
        internal BizResult<Attachment> SaveAttachment(Attachment entity, Stream fileStream, string baseFolder, string subFolder, bool uniqueCheck, bool save)
        {
            var result = new BizResult<Attachment>();

            if (fileStream.Length <= 0)
            {
                result.Error("文件流不存在");
            }
            else if (entity.FileName.IsEmpty())
            {
                result.Error("文件名为空");
            }
            else if (entity.ContentType.IsEmpty())
            {
                result.Error("文件类型为空");
            }

            if (result.Success)
            {
                FileIndex fileIndex = new()
                {
                    ContentType = entity.ContentType,
                    FileName = entity.FileName,
                };

                var fileResult = this.FileBll.SaveFile(fileIndex, fileStream, baseFolder, subFolder, uniqueCheck);

                if (fileResult.Success)
                {
                    var file = fileResult.Data.Value();

                    entity.FileIndexId = file.ID;
                    entity.FileSize = file.FileSize;
                    entity.FileExt = entity.FileName.GetFileExtension();

                    if (!entity.ObjectType.IsEmpty() && !entity.ObjectId.IsEmpty())
                    {
                        entity.ID = CombGuid.NewGuid();

                        entity = this.Add(entity, save);
                    }
                    else
                    {
                        entity.ID = Guid.Empty;
                    }

                    result.Data = entity;
                }
                else
                {
                    result.CopyFrom(fileResult);
                }
            }

            return result;
        }

        #endregion internal

        #endregion Save Attachment

        protected override DbContextOptionsBuilder BuildDbOptions()
        {
            var optionsBuilder = new DbContextOptionsBuilder();

            optionsBuilder.UseDbConfig(nameof(BizDbContext), Config.DecryptConnStr);

            return optionsBuilder;
        }

        #region ReviewData

        #region Add

        protected virtual BizResult<ReviewIndex> AddReviewData(ReviewIndex reviewIndex, ReviewExtInfo reviewExtInfo,
            string oriDataJson, string newDataJson,
            List<Attachment>? attachs,
            Dictionary<string, List<Guid>>? auditTasks = null, bool save = false)
        {
            var result = new BizResult<ReviewIndex>();

            if (reviewIndex.ID.IsEmpty())
            {
                reviewIndex.ID = CombGuid.NewGuid();
            }

            reviewIndex.SubmitEmployeeId = this.OperatorEmployeeId;
            reviewIndex.EnumReviewStatus = ReviewStatus.Processing;

            this.Add(reviewIndex, false);

            reviewExtInfo.ID = reviewIndex.ID;

            this.Add(reviewExtInfo, false);

            if (attachs?.Count > 0)
            {
                var objType = nameof(ReviewIndex);
                var objId = reviewIndex.ID.AsString();

                var clones = attachs.Clone();

                foreach (var attach in clones)
                {
                    attach.ObjectType = objType;
                    attach.ObjectId = objId;
                    attach.GroupId = string.Empty;
                    attach.SrcAttachmentId = null;

                    if (!attach.ID.IsEmpty())
                    {
                        attach.SrcAttachmentId = attach.ID;
                    }

                    attach.ID = CombGuid.NewGuid();

                    this.Add(attach, false);
                }
            }

            if (auditTasks?.Count > 0)
            {
                var allAuditorIds = auditTasks.SelectMany(p => p.Value).Distinct().ToList();

                var allAuditors = this.GetEntities<Employee>(p => allAuditorIds.Contains(p.ID));

                var i = 1;
                foreach (var auditTask in auditTasks)
                {
                    var reviewTask = new ReviewTask
                    {
                        ID = CombGuid.NewGuid(),
                        ReviewIndexId = reviewIndex.ID,
                        Name = auditTask.Key,
                        Sequence = i * 10000,
                    };

                    var auditorIds = auditTask.Value;

                    var auditors = allAuditors
                        .Where(p => auditorIds.Contains(p.ID))
                        .OrderBy(p => p.DisplayName)
                        .ToList();

                    reviewTask.AuditorNames = string.Join(';', auditors.Select(p => p.DisplayName));

                    if (i == 1)
                    {
                        i++;
                        reviewTask.EnumReviewStatus = ReviewStatus.Processing;
                        reviewIndex.AuditorNames = reviewTask.AuditorNames;
                    }
                    else
                    {
                        reviewTask.EnumReviewStatus = ReviewStatus.Pending;
                    }

                    this.Add(reviewTask, false);

                    foreach (var auditor in auditors)
                    {
                        var reviewAuditor = new ReviewAuditor
                        {
                            ID = CombGuid.NewGuid(),
                            ReviewIndexId = reviewIndex.ID,
                            ReviewTaskId = reviewTask.ID,
                            EmployeeId = auditor.ID,
                        };
                        this.Add(reviewAuditor, false);
                    }
                }
            }

            var reviewData = new ReviewData
            {
                ID = reviewIndex.ID,
                OriData = oriDataJson,
                NewData = newDataJson,
            };

            this.Add(reviewData, false);

            this.SaveChanges(save);

            result.Data = reviewIndex;

            return result;
        }

        protected virtual BizResult<ReviewIndex> AddReviewData<TEntity>(ReviewType reviewType, string reviewOperation, TEntity clone,
            Dictionary<string, List<Guid>>? auditTasks = null, bool save = false)
            where TEntity : class, IEntity, ITable, IReviewEntity, new()
        {
            return this.AddReviewData(reviewType, reviewOperation, null, clone, null, auditTasks, save);
        }

        protected virtual BizResult<ReviewIndex> AddReviewData<TEntity>(ReviewType reviewType, string reviewOperation, TEntity clone,
            List<Attachment>? attachs = null,
            Dictionary<string, List<Guid>>? auditTasks = null, bool save = false)
            where TEntity : class, IEntity, ITable, IReviewEntity, new()
        {
            return this.AddReviewData(reviewType, reviewOperation, null, clone, attachs, auditTasks, save);
        }

        protected virtual BizResult<ReviewIndex> AddReviewData<TEntity>(ReviewType reviewType, string reviewOperation, TEntity? dbClone, TEntity clone,
            Dictionary<string, List<Guid>>? auditTasks = null, bool save = false)
            where TEntity : class, IEntity, ITable, IReviewEntity, new()
        {
            return this.AddReviewData(reviewType, reviewOperation, dbClone, clone, null, auditTasks, save);
        }

        protected virtual BizResult<ReviewIndex> AddReviewData<TEntity>(ReviewType reviewType, string reviewOperation, TEntity? dbClone, TEntity clone,
            List<Attachment>? attachs = null,
            Dictionary<string, List<Guid>>? auditTasks = null, bool save = false)
            where TEntity : class, IEntity, ITable, IReviewEntity, new()
        {
            var result = new BizResult<ReviewIndex>();

            TEntity? dbEntity = null;

            if (reviewType == ReviewType.Batch)
            {
                result.Error("单数据不可以进行批量操作");
            }
            else if (clone.ID.IsEmpty())
            {
                if (reviewType == ReviewType.Auto)
                {
                    reviewType = ReviewType.Add;
                }
                else if (reviewType != ReviewType.Add)
                {
                    result.Error("无主键数据只允许进行新增操作");
                }
            }
            else
            {
                dbEntity = this.Get<TEntity>(clone.ID);

                if (dbEntity == null)
                {
                    if (reviewType != ReviewType.Add && reviewType != ReviewType.Auto)
                    {
                        result.Error("原数据不存在");
                    }
                    else if (reviewType == ReviewType.Auto)
                    {
                        reviewType = ReviewType.Add;
                    }
                }
                else if (dbEntity.EnumReviewState == ReviewState.Reviewing)
                {
                    result.Error("原数据正在审核中，不可以添加新的审核");
                }
                else
                {
                    if (reviewType == ReviewType.Auto)
                    {
                        if (this.Entry(dbEntity).State == EntityState.Added)
                        {
                            reviewType = ReviewType.Add;
                        }
                        else if (clone is IDeleteable deleteable && deleteable.Deleted)
                        {
                            reviewType = ReviewType.Delete;
                        }
                        else
                        {
                            reviewType = ReviewType.Update;
                        }
                    }
                }
            }

            if (result.Success)
            {
                var reviewIndex = new ReviewIndex
                {
                    EnumReviewType = reviewType,
                    ReviewOperation = reviewOperation,
                    ReviewDataType = typeof(TEntity).Name,
                    ReviewDataId = clone.ID.IsEmpty() ? null : clone.ID,
                    IsCollection = false,
                    Count = 1,
                };

                var reviewExtInfo = clone.GetReviewExtInfo(this);

                var oriDataJson = string.Empty;

                if (dbEntity != null)
                {
                    dbClone ??= dbEntity.Clone();

                    if (reviewType != ReviewType.Add)
                    {
                        oriDataJson = dbClone.ToReviewJson();
                    }

                    dbEntity.EnumReviewState = ReviewState.Reviewing;

                    this.Update(dbEntity, false);
                }

                clone.ReviewType = reviewType;
                clone.ReviewOperation = reviewOperation;

                var newDataJson = clone.ToReviewJson();

                result = this.AddReviewData(reviewIndex, reviewExtInfo, oriDataJson, newDataJson, attachs, auditTasks, save);
            }

            return result;
        }

        protected virtual BizResult<ReviewIndex> AddReviewData<TEntity>(string reviewOperation, List<TEntity> entities,
            Dictionary<string, List<Guid>>? auditTasks = null, bool save = false)
            where TEntity : class, IEntity, ITable, IReviewEntity, new()
        {
            var result = new BizResult<ReviewIndex>();

            var keys = entities.Where(p => !p.ID.IsEmpty()).Select(p => p.ID).ToList();

            var dbEntities = this.GetEntities<TEntity>(p => keys.Contains(p.ID));

            if (dbEntities.Any(p => p.EnumReviewState == ReviewState.Reviewing))
            {
                result.Error("存在正在审核的原数据");
            }

            if (result.Success)
            {
                var oriClones = new List<TEntity>();

                for (var i = 0; i < entities.Count; i++)
                {
                    var entity = entities[i];
                    var dbEntity = dbEntities.FirstOrDefault(p => p.ID == entity.ID);

                    ReviewType reviewType;// = ReviewType.Add;

                    if (dbEntity == null || this.Entry(dbEntity).State == EntityState.Added)
                    {
                        reviewType = ReviewType.Add;
                    }
                    else if (entity is IDeleteable deleteable && deleteable.Deleted)
                    {
                        reviewType = ReviewType.Delete;
                    }
                    else
                    {
                        reviewType = ReviewType.Update;
                    }

                    entity.ReviewIndex = i;
                    entity.ReviewType = reviewType;
                    entity.ReviewOperation = reviewOperation;

                    if (dbEntity != null)
                    {
                        if (reviewType != ReviewType.Add)
                        {
                            var oriClone = dbEntity.Clone();

                            oriClones.Add(oriClone);
                        }
                    }
                }

                var reviewIndex = new ReviewIndex
                {
                    EnumReviewType = ReviewType.Batch,
                    ReviewOperation = reviewOperation,
                    ReviewDataType = typeof(TEntity).Name,
                    ReviewDataId = null,
                    IsCollection = true,
                    Count = entities.Count,
                };

                var reviewExtInfo = entities.Count > 0
                    ? entities.First().GetReviewExtInfo(this)
                    : new ReviewExtInfo();

                var oriDataJson = oriClones.ToReviewJson();
                var newDataJson = entities.ToReviewJson();

                result = this.AddReviewData(reviewIndex, reviewExtInfo, oriDataJson, newDataJson, null, auditTasks, save);
            }

            return result;
        }

        #endregion Add

        #region Audit

        protected virtual BizResult<ReviewTask> AuditReviewTask<TEntity>(ReviewTask reviewTask, List<int>? auditedIndexes,
            Func<ReviewStatus, ReviewIndex, TEntity?, TEntity, IRepo, bool>? auditedFunc = null, bool save = true)
            where TEntity : class, IEntity, ITable, IReviewEntity, new()
        {
            var result = new BizResult<ReviewTask>();

            var id = reviewTask.ID;

            var dbReviewTask = this.Get<ReviewTask>(id);
            if (dbReviewTask == null)
            {
                result.Error($"需要审核的任务不存在");
            }
            else if (dbReviewTask.ReviewIndex.ReviewData == null)
            {
                result.Error($"需要审核的数据不存在");
            }
            else if (dbReviewTask.EnumReviewStatus != ReviewStatus.Processing)
            {
                result.Error($"需要审核的任务状态不正确");
            }
            else if (dbReviewTask.ReviewIndex.ReviewDataType != typeof(TEntity).Name)
            {
                result.Error($"需要审核的数据类型不正确");
            }
            else if (reviewTask.EnumReviewStatus != ReviewStatus.Approved && reviewTask.EnumReviewStatus != ReviewStatus.Rejected)
            {
                result.Error($"必须选择同意或者拒绝的操作");
            }

            ReviewTask? nextTask = null;

            if (result.Success)
            {
                var reviewStatus = reviewTask.EnumReviewStatus;
                var reviewTime = SysDateTime.Now;
                var reviewEmployeedId = this.OperatorEmployeeId;
                var reviewRemark = reviewTask.ReviewRemark;

                dbReviewTask = dbReviewTask.Value();
                var dbReviewIndex = dbReviewTask.ReviewIndex;
                var reviewIndexId = dbReviewIndex.ID;

                var pendingTasks = this.GetEntities<ReviewTask>(p => p.ReviewIndexId == reviewIndexId
                    && p.EnumReviewStatus == ReviewStatus.Pending
                    && p.ID != id);

                dbReviewTask.EnumReviewStatus = reviewStatus;
                dbReviewTask.ReviewTime = reviewTime;
                dbReviewTask.ReviewEmployeeId = reviewEmployeedId;
                dbReviewTask.ReviewRemark = reviewRemark;

                ReviewIndex? reviewIndex = null;

                if (reviewStatus == ReviewStatus.Approved)
                {
                    nextTask = pendingTasks
                        .OrderBy(p => p.Sequence)
                        .FirstOrDefault();

                    if (nextTask != null)
                    {
                        nextTask.EnumReviewStatus = ReviewStatus.Processing;
                        dbReviewIndex.AuditorNames = nextTask.AuditorNames;
                    }
                    else
                    {
                        reviewIndex = new ReviewIndex();
                    }
                }
                else
                {
                    reviewIndex = new ReviewIndex();
                }

                if (reviewIndex != null)
                {
                    reviewIndex.ID = dbReviewTask.ReviewIndexId;
                    reviewIndex.EnumReviewStatus = reviewStatus;
                    reviewIndex.ReviewRemark = reviewRemark;

                    var saveResult = this.SaveReviewData(reviewIndex, auditedIndexes, auditedFunc, save: false);

                    if (!saveResult.Success)
                    {
                        result.Merge(saveResult);
                    }
                }
            }

            if (result.Success)
            {
                result.Data = nextTask;
                this.SaveChanges(save);
            }

            return result;
        }

        #endregion Audit

        #region Save

        protected virtual BizResult SaveReviewData<TEntity>(ReviewIndex reviewIndex, List<int>? auditedIndexes,
             Func<ReviewStatus, ReviewIndex, TEntity?, TEntity, IRepo, bool>? auditedFunc = null, List<Attachment>? attachments = null, bool save = false)
            where TEntity : class, IEntity, ITable, IReviewEntity
        {
            var result = new BizResult();

            var dbReviewData = this.Get<ReviewData>(reviewIndex.ID);

            List<TEntity>? clones = null;
            List<TEntity>? entities = null;
            List<TEntity>? dbEntities = null;

            if (dbReviewData == null)
            {
                result.Error("需要审核的数据不存在！");
            }
            else
            {
                if (dbReviewData.ReviewIndex.IsCollection)
                {
                    clones = dbReviewData.NewData.DeserializeJson<List<TEntity>>();
                    entities = dbReviewData.NewData.DeserializeJson<List<TEntity>>();
                }
                else
                {
                    var clone = dbReviewData.NewData.DeserializeJson<TEntity>();
                    var entity = dbReviewData.NewData.DeserializeJson<TEntity>();

                    if (entity != null)
                    {
                        entities = [entity];
                    }

                    if (clone != null)
                    {
                        clones = [clone];
                    }
                }

                if (entities == null)
                {
                    result.Error("审核数据出错");
                }
                else
                {
                    var ids = entities.Where(p => !p.ID.IsEmpty()).Select(p => p.ID).ToList();

                    dbEntities = this.GetEntities<TEntity>(p => ids.Contains(p.ID));

                    for (var i = 0; i < entities.Count; i++)
                    {
                        var entity = entities[i];

                        foreach (var column in entity.IgnoreChangedColumn)
                        {
                            entity.RemoveChangedColumn(column);
                        }

                        foreach (var column in OperateInfo.AllColumns)
                        {
                            entity.RemoveChangedColumn(column);
                        }

                        var props = typeof(IReviewEntity).GetProperties()
                            .Where(p => p.PropertyType.IsValueOrString())
                            .ToList();

                        foreach (var prop in props)
                        {
                            entity.RemoveChangedColumn(prop.Name);
                        }

                        if (!entity.ID.IsEmpty() && entity.ReviewType != ReviewType.Add)
                        {
                            var dbEntity = dbEntities.FirstOrDefault(p => p.ID == entity.ID);

                            if (dbEntity == null)
                            {
                                result.Error($"第{i + 1}行：不存在ID为[{entity.ID}]的【{typeof(TEntity).Name}】数据");
                            }
                        }
                    }
                }
            }

            if (result.Success)
            {
                dbReviewData = dbReviewData.Value();
                var dbReviewIndex = dbReviewData.ReviewIndex;

                clones = clones.Value();
                entities = entities.Value();
                dbEntities = dbEntities.Value();

                var reviewObjId = reviewIndex.ID.ToString();

                attachments ??= this.GetEntities<Attachment>(p => p.ObjectType == nameof(ReviewIndex) && p.ObjectId == reviewObjId);

                var reviewStatus = reviewIndex.EnumReviewStatus;
                var reviewTime = SysDateTime.Now;
                var reviewEmployeedId = this.OperatorEmployeeId;
                var reviewRemark = reviewIndex.ReviewRemark;

                if (reviewStatus == ReviewStatus.Approved || reviewStatus == ReviewStatus.PartialApproved)
                {
                    reviewStatus = ReviewStatus.Approved;
                }

                var approvedEntities = new List<TEntity>();

                for (var i = 0; i < entities.Count; i++)
                {
                    var clone = clones[i];
                    var entity = entities[i];

                    var reviewType = entity.ReviewType;

                    var dbEntity = entity.ID.IsEmpty()
                        ? null
                        : this.Get<TEntity>(entity.ID);

                    var completed = auditedFunc?.Invoke(reviewStatus, dbReviewIndex, dbEntity, entity, this) ?? true;

                    if (reviewStatus == ReviewStatus.Approved
                        && (auditedIndexes == null || auditedIndexes.Contains(entity.ReviewIndex)))
                    {
                        // 审批通过
                        approvedEntities.Add(clone);

                        if (completed)
                        {
                            switch (reviewType)
                            {
                                case ReviewType.Add:

                                    if (dbEntity == null)
                                    {
                                        if (entity.ID.IsEmpty())
                                        {
                                            entity.ID = CombGuid.NewGuid();
                                        }

                                        dbEntity = this.Add(entity, false);
                                        dbEntities.Add(dbEntity);
                                    }
                                    else
                                    {
                                        dbEntity = this.Update(entity, false);
                                    }

                                    break;

                                case ReviewType.Update:
                                    if (dbEntity != null)
                                    {
                                        dbEntity = this.Update(entity, false);
                                    }

                                    break;

                                case ReviewType.Delete:
                                    if (dbEntity != null)
                                    {
                                        if (entity is IDeleteable deleteable)
                                        {
                                            deleteable.Deleted = true;

                                            dbEntity = this.Update(entity, false);
                                        }
                                        else
                                        {
                                            this.Remove(entity, false);

                                            dbEntities.Remove(dbEntity);
                                        }
                                    }
                                    break;
                            }

                            if (attachments.Count > 0 && dbEntity != null)
                            {
                                var objId = dbEntity.ID.ToString();
                                var dbAttachments = this.GetEntities<Attachment>(p => p.ObjectType == nameof(TEntity) && p.ObjectId == objId);

                                foreach (var delAttachment in dbAttachments)
                                {
                                    this.Delete(delAttachment, false);
                                }

                                foreach (var attachment in attachments)
                                {
                                    var attchmentClone = attachment.Clone();

                                    attchmentClone.ID = CombGuid.NewGuid();
                                    attchmentClone.ObjectType = typeof(TEntity).Name;
                                    attchmentClone.ObjectId = entity.ID.ToString();

                                    this.Add(attchmentClone, save);
                                }
                            }
                        }
                    }

                    if (dbEntity != null)
                    {
                        if (completed)
                        {
                            dbEntity.EnumReviewState = ReviewState.Reviewed;
                        }

                        dbEntity.ReviewTime = reviewTime;
                        dbEntity.ReviewEmployeeId = reviewEmployeedId;
                        dbEntity.ReviewRemark = reviewRemark;

                        this.Update(dbEntity, false);
                    }
                }

                var approvedCount = 0;

                if (reviewStatus == ReviewStatus.Approved)
                {
                    if (reviewIndex.IsCollection)
                    {
                        approvedCount = approvedEntities.Count;

                        if (approvedCount < reviewIndex.Count)
                        {
                            reviewStatus = ReviewStatus.PartialApproved;
                        }

                        dbReviewData.ApprovedData = approvedEntities.ToJson();
                    }
                    else
                    {
                        dbReviewData.ApprovedData = approvedEntities.First().ToJson();
                        approvedCount = 1;
                    }

                    this.Update(dbReviewData, false);
                }

                dbReviewIndex.EnumReviewStatus = reviewStatus;
                dbReviewIndex.ReviewEmployeeId = reviewEmployeedId;
                dbReviewIndex.ReviewTime = reviewTime;
                dbReviewIndex.ReviewRemark = reviewRemark;
                dbReviewIndex.ApprovedCount = approvedCount;

                if (reviewStatus == ReviewStatus.Approved || reviewStatus == ReviewStatus.PartialApproved)
                {
                    var auditors = this.GetEntities<ReviewAuditor>(p => p.ReviewIndexId == reviewIndex.ID && p.EnumReviewStatus == ReviewStatus.Approved);
                    dbReviewIndex.AuditorNames = string.Join(',', auditors.Select(p => p.Employee.DisplayName));
                }
                else
                {
                    var auditors = this.GetEntities<ReviewAuditor>(p => p.ReviewIndexId == reviewIndex.ID && p.EnumReviewStatus == ReviewStatus.Rejected);
                    dbReviewIndex.AuditorNames = string.Join(',', auditors.Select(p => p.Employee.DisplayName));
                }

                this.Update(dbReviewIndex, false);

                this.SaveChanges(save);
            }

            return result;
        }

        #endregion Save

        #endregion ReviewData
    }
}
