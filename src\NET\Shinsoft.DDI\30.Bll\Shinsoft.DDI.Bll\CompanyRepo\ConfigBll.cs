﻿

namespace Shinsoft.DDI.Bll
{
    public class ConfigBll : BaseCompanyBll
    {
        #region Constructs

        public ConfigBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public ConfigBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public ConfigBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public ConfigBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region ReceiverClient

        /// <summary>
        /// 新增经销商配置
        /// </summary>
        /// <param name="entity">经销商配置实体</param>
        /// <returns>操作结果</returns>
        public BizResult<ReceiverClient> AddReceiverClient(ReceiverClient entity)
        {
            var result = new BizResult<ReceiverClient>();

            // 验证经销商是否存在
            var receiver = this.Get<Receiver>(entity.ID);
            if (receiver == null)
            {
                result.Error("经销商不存在");
                return result;
            }

            // 检查该经销商是否已有配置
            var existingConfig = this.Get<ReceiverClient>(entity.ID);
            if (existingConfig != null)
            {
                result.Error("该经销商已存在配置信息");
                return result;
            }

            // 设置默认值
            if (entity.VerifyFrequency <= 0)
            {
                entity.VerifyFrequency = 60; // 默认60分钟
            }

            if (string.IsNullOrEmpty(entity.UpdateToVersion))
            {
                entity.UpdateToVersion = "1.0.0";
            }

            if (string.IsNullOrEmpty(entity.UpdateUrl))
            {
                entity.UpdateUrl = "";
            }

            if (string.IsNullOrEmpty(entity.ScheduleType))
            {
                entity.ScheduleType = "daily";
            }

            if (string.IsNullOrEmpty(entity.ScheduleDay))
            {
                entity.ScheduleDay = "";
            }

            if (string.IsNullOrEmpty(entity.ScheduleTime))
            {
                entity.ScheduleTime = "";
            }

            if (string.IsNullOrEmpty(entity.SourceType))
            {
                entity.SourceType = "database";
            }

            if (string.IsNullOrEmpty(entity.TargetType))
            {
                entity.TargetType = "http";
            }

            if (string.IsNullOrEmpty(entity.OtherXml))
            {
                entity.OtherXml = "";
            }

            if (string.IsNullOrEmpty(entity.Status))
            {
                entity.Status = "正常";
            }

            try
            {
                entity = this.Add(entity);
                this.SaveChanges();

                // 清除缓存
                this.CompanyCache.RemoveCache<ReceiverClient>();

                result.Data = entity;
            }
            catch (Exception ex)
            {
                result.Error($"新增经销商配置失败：{ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 更新经销商配置
        /// </summary>
        /// <param name="entity">经销商配置实体</param>
        /// <returns>操作结果</returns>
        public BizResult<ReceiverClient> UpdateReceiverClient(ReceiverClient entity)
        {
            var result = new BizResult<ReceiverClient>();

            var dbEntity = this.Get<ReceiverClient>(entity.ID);
            if (dbEntity == null)
            {
                result.Error("经销商配置不存在");
                return result;
            }

            try
            {
                // 更新实体
                this.Update(entity);
                this.SaveChanges();

                // 清除缓存
                this.CompanyCache.RemoveCache<ReceiverClient>();

                result.Data = entity;
            }
            catch (Exception ex)
            {
                result.Error($"更新经销商配置失败：{ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 删除经销商配置
        /// </summary>
        /// <param name="id">经销商配置ID</param>
        /// <returns>操作结果</returns>
        public BizResult DeleteReceiverClient(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<ReceiverClient>(id);
            if (dbEntity == null)
            {
                result.Error("经销商配置不存在");
                return result;
            }

            try
            {
                // 检查是否有关联的业务数据
                // TODO: 根据业务需求添加相关检查

                this.Remove(dbEntity);

                // 清除缓存
                this.CompanyCache.RemoveCache<ReceiverClient>();
            }
            catch (Exception ex)
            {
                result.Error($"删除经销商配置失败：{ex.Message}");
            }

            return result;
        }

        #endregion
    }
}
