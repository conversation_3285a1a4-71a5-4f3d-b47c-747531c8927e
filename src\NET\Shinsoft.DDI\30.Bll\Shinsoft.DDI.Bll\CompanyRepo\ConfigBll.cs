﻿

namespace Shinsoft.DDI.Bll
{
    public class ConfigBll : BaseCompanyBll
    {
        #region Constructs

        public ConfigBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public ConfigBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public ConfigBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public ConfigBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region ReceiverClient

        /// <summary>
        /// 新增经销商配置
        /// </summary>
        /// <param name="entity">经销商配置实体</param>
        /// <returns>操作结果</returns>
        public BizResult<ReceiverClient> AddReceiverClient(ReceiverClient entity, List<ColumnMapping>? columnMappings)
        {
            var result = new BizResult<ReceiverClient>();

            // 验证经销商是否存在
            var receiver = this.Get<Receiver>(entity.ID);
            if (receiver == null)
            {
                result.Error("经销商不存在");
                return result;
            }

            // 检查该经销商是否已有配置
            var existingConfig = this.Get<ReceiverClient>(entity.ID);
            if (existingConfig != null)
            {
                result.Error("该经销商已存在配置信息");
                return result;
            }

            try
            {
                entity = this.Add(entity);

                // 更新列映射
                if (columnMappings?.Any() == true)
                {
                    foreach (var mapping in columnMappings)
                    {
                        if (mapping != null)
                        {
                            // 添加新映射
                            mapping.ID = CombGuid.NewGuid();
                            this.Add(mapping);
                        }
                    }
                }

                this.SaveChanges();

                // 清除缓存
                this.CompanyCache.RemoveCache<ReceiverClient>();

                result.Data = entity;
            }
            catch (Exception ex)
            {
                result.Error($"新增经销商配置失败：{ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 更新经销商配置
        /// </summary>
        /// <param name="entity">经销商配置实体</param>
        /// <param name="columnMappings">列映射实体</param>
        /// <returns>操作结果</returns>
        public BizResult<ReceiverClient> UpdateReceiverClient(ReceiverClient entity, List<ColumnMapping>? columnMappings)
        {
            var result = new BizResult<ReceiverClient>();

            // 验证实体是否存在
            var dbEntity = this.Get<ReceiverClient>(entity.ID);
            if (dbEntity == null)
            {
                result.Error("经销商配置不存在");
                return result;
            }

            // 验证关联的经销商是否存在
            var receiver = this.Get<Receiver>(entity.Receiver.ID);
            if (receiver == null)
            {
                result.Error("关联的经销商不存在");
                return result;
            }

            // 更新主实体
            this.Update(entity);

            // 更新列映射
            if (columnMappings?.Any() == true)
            {
                foreach (var mapping in columnMappings)
                {
                    if (mapping != null)
                    {
                        var existingMapping = this.GetEntity<ColumnMapping>(a =>
                            a.ReceiverId == mapping.ReceiverId &&
                            a.ColumnMappingTemplateId == mapping.ColumnMappingTemplateId);

                        if (existingMapping != null)
                        {
                            // 更新现有映射
                            existingMapping.DbFiled = mapping.DbFiled;
                            existingMapping.DateFormatType = mapping.DateFormatType;
                            this.Update(existingMapping);
                        }
                        else
                        {
                            // 添加新映射
                            mapping.ID = CombGuid.NewGuid();
                            this.Add(mapping);
                        }
                    }
                }
            }

            // 保存所有更改
            this.SaveChanges();

            // 清除缓存
            this.CompanyCache.RemoveCache<ReceiverClient>();

            result.Data = entity;

            return result;
        }

        /// <summary>
        /// 删除经销商配置
        /// </summary>
        /// <param name="id">经销商配置ID</param>
        /// <returns>操作结果</returns>
        public BizResult DeleteReceiverClient(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<ReceiverClient>(id);
            if (dbEntity == null)
            {
                result.Error("经销商配置不存在");
                return result;
            }

            try
            {
                // 检查是否有关联的业务数据
                // TODO: 根据业务需求添加相关检查

                this.Remove(dbEntity);

                // 清除缓存
                this.CompanyCache.RemoveCache<ReceiverClient>();
            }
            catch (Exception ex)
            {
                result.Error($"删除经销商配置失败：{ex.Message}");
            }

            return result;
        }

        #endregion
    }
}
