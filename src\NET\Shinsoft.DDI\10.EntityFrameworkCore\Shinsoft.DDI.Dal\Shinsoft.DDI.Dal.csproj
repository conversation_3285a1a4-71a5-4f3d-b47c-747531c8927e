<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Shinsoft.Core">
      <HintPath>..\..\00.Reference\net8.0\Shinsoft.Core.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Shinsoft.DDI.Entities\Shinsoft.DDI.Entities.csproj" />
  </ItemGroup>

  <ItemGroup>
	<Using Include="Microsoft.EntityFrameworkCore" />
	<Using Include="Shinsoft.Core" />
	<Using Include="Shinsoft.Core.EntityFrameworkCore" />
  </ItemGroup>

  <ItemGroup>
    <None Update="DbContext\Biz.DbContext.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Biz.DbContext.cs</LastGenOutput>
    </None>
    <None Update="DbContext\File.DbContext.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>File.DbContext.cs</LastGenOutput>
    </None>
    <None Update="DbContext\Log.DbContext.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Log.DbContext.cs</LastGenOutput>
    </None>
    <None Update="DbContext\Mail.DbContext.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Mail.DbContext.cs</LastGenOutput>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="DbContext\Biz.DbContext.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Biz.DbContext.tt</DependentUpon>
    </Compile>
    <Compile Update="DbContext\File.DbContext.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>File.DbContext.tt</DependentUpon>
    </Compile>
    <Compile Update="DbContext\Log.DbContext.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Log.DbContext.tt</DependentUpon>
    </Compile>
    <Compile Update="DbContext\Mail.DbContext.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Mail.DbContext.tt</DependentUpon>
    </Compile>
  </ItemGroup>

</Project>
