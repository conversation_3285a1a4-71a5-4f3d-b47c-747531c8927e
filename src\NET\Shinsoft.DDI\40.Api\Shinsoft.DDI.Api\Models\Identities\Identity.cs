﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    [MapFromType(typeof(IdentityUser), Reverse = false)]
    [MapFromType(typeof(User), Reverse = false)]
    public class Identity : IIdentityKey, IIdentity, IModel
    {
        /// <summary>
        /// 终端
        /// </summary>
        public ProgramFlag Program { get; set; }

        #region IIdentityKey

        public string Culture { get; set; } = string.Empty;

        public string UserId { get; set; } = string.Empty;

        public string EmployeeId { get; set; } = string.Empty;

        public string AgentId { get; set; } = string.Empty;

        public string RoleId { get; set; } = string.Empty;

        public int IdentitySeed { get; set; }

        [JsonIgnore, XmlIgnore, MapFromIgnore]
        public virtual IIdentityKey IdentityKey => new ApiIdentityKey
        {
            Program = this.Program,
            Culture = this.Culture,
            UserId = this.UserId,
            EmployeeId = this.EmployeeId,
            AgentId = this.AgentId,
            RoleId = this.RoleId,
        };

        #endregion IIdentityKey
    }
}
