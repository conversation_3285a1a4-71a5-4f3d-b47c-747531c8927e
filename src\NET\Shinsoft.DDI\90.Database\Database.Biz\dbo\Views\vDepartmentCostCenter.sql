﻿CREATE VIEW [dbo].[vDepartmentCostCenter] AS 

SELECT
	dc.ID,
	dc.CompanyId,
	c.Name AS [CompanyName],
	s.Code AS [SubCompanyCode],
	s.Name AS [SubCompanyName],
	d.Code AS [DepartmentCode],
	d.Name AS [DepartmentName],
	cc.Code AS [CostCenterCode],
	cc.Name AS [CostCenterName],
	dc<PERSON>,
	d.SubCompany<PERSON>d,
	dc.DepartmentId,
	dc.CostCenterId
FROM dbo.DepartmentCostCenter AS dc
INNER JOIN dbo.Company AS c ON c.ID = dc.CompanyId
INNER JOIN dbo.Department AS d ON d.ID = dc.DepartmentId
INNER JOIN dbo.CostCenter AS cc ON cc.ID = dc.CostCenterId
LEFT JOIN dbo.SubCompany AS s ON s.ID = d.SubCompanyId


