﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class CostCenterSelectorFilter
    {
        /// <summary>
        /// 分公司ID
        /// </summary>
        [Description("分公司ID")]
        [DynamicQueryColumn(typeof(CostCenter), CostCenter.Columns.SubCompanyId, Operation = Operation.Equal)]
        public Guid? SubCompanyId { get; set; }

        /// <summary>
        /// 关键词
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(CostCenter), CostCenter.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(CostCenter), CostCenter.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }

        /// <summary>
        /// 编码
        /// </summary>
        [Description("编码")]
        [DynamicQueryColumn(typeof(CostCenter), CostCenter.Columns.Code, Operation = Operation.StringIntelligence)]
        public string? Code { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        [DynamicQueryColumn(typeof(CostCenter), CostCenter.Columns.Name, Operation = Operation.StringContains)]
        public string? Name { get; set; }

        /// <summary>
        /// 排除被使用成本中心
        /// </summary>
        [Description("排除被使用成本中心")]
        public bool? ExcludeUsed { get; set; }
    }
}
