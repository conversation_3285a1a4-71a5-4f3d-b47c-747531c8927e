--员工岗位
CREATE TABLE [dbo].[EmployeeStation]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_EmployeeStation_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [EmployeeId]                UNIQUEIDENTIFIER            NOT NULL,
    [StationId]                 UNIQUEIDENTIFIER            NOT NULL,
    [StartDate]                 DATE                        NULL, 
    [EndDate]                   DATE                        NULL, 
    [Creator]                   NVARCHAR(50)                NULL,
    [CreateTime]                DATETIME                    NULL,
    [LastEditor]                NVARCHAR(50)                NULL,
    [LastEditTime]              DATETIME                    NULL,
    CONSTRAINT [PK_EmployeeStation] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_EmployeeStation_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
	CONSTRAINT [FK_EmployeeStation_Employee] FOREIGN KEY ([EmployeeId]) REFERENCES [dbo].[Employee] ([ID]),
	CONSTRAINT [FK_EmployeeStation_Station] FOREIGN KEY ([StationId]) REFERENCES [dbo].[Station] ([ID]),
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'员工岗位',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'EmployeeStation',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'员工ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'EmployeeStation',
    @level2type = N'COLUMN',
    @level2name = N'EmployeeId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'岗位ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'EmployeeStation',
    @level2type = N'COLUMN',
    @level2name = N'StationId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'起始日期',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'EmployeeStation',
    @level2type = N'COLUMN',
    @level2name = N'StartDate'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'截止日期',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'EmployeeStation',
    @level2type = N'COLUMN',
    @level2name = N'EndDate'
GO
