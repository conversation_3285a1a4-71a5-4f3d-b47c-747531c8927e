﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Common
{
    public static class PdfHelper
    {
        /// <summary>
        /// HTML文本内容转换为PDF
        /// </summary>
        /// <param name="strHtml">strHtml为空</param>
        /// <param name="letHeader">公司信息</param>
        /// <param name="rightHeader">单号</param>
        /// <param name="footer">单号</param>
        /// <param name="title">strHtml为空</param>
        /// <param name="savePath">完成路径，包含文件名</param>
        /// <returns></returns>
        public static bool HtmlTextConvertToPdf(string strHtml, string letHeader, string rightHeader, string footer, string title, string savePath)
        {
            var flag = false;
            try
            {
                string htmlPath = string.Empty;
                string contentUrl = string.Empty;
                if (!string.IsNullOrEmpty(strHtml))
                {
                    htmlPath = HtmlTextConvertFile(strHtml);
                    contentUrl = htmlPath;
                }

                flag = HtmlConvertToPdf(contentUrl, letHeader, rightHeader, footer, title, savePath);
                if (!string.IsNullOrEmpty(strHtml))
                {
                    File.Delete(htmlPath);
                }
            }
            catch
            {
                flag = false;
            }
            return flag;
        }

        /// <summary>
        /// HTML转换为PDF
        /// </summary>
        /// <param name="htmlPath">可以是本地路径，也可以是网络地址</param>
        /// <param name="savePath">PDF文件保存的路径</param>
        /// <returns></returns>
        private static bool HtmlConvertToPdf(string contentUrl, string letHeader, string rightHeader, string footer, string title, string savePath)
        {
            var flag = false;
            //验证保存路径是否正确
            CheckFilePath(savePath);
            string exePath = AppDomain.CurrentDomain.BaseDirectory.ToString() + "Pdf/Tool/wkhtmltopdf.exe";
            if (!File.Exists(exePath))
            {
                throw new Exception("没有找到wkhtmltopdf.exe应用程序");
            }

            try
            {
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = exePath,
                    WorkingDirectory = Path.GetDirectoryName(exePath),
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardInput = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    Arguments = GetArguments(contentUrl, letHeader, rightHeader, footer, title, savePath)
                };

                var process = new Process
                {
                    StartInfo = processStartInfo
                };
                process.Start();
                process.BeginErrorReadLine();
                process.BeginOutputReadLine();
                process.WaitForExit();

                ///用于查看是否返回错误信息
                //StreamReader srone = process.StandardError;
                //StreamReader srtwo = process.StandardOutput;
                //string ss1 = srone.ReadToEnd();
                //string ss2 = srtwo.ReadToEnd();
                //srone.Close();
                //srone.Dispose();
                //srtwo.Close();
                //srtwo.Dispose();

                process.Close();
                process.Dispose();
                flag = true;
            }
            catch
            {
                flag = false;
            }
            return flag;
        }

        /// <summary>
        /// 获取命令行参数
        /// </summary>
        /// <param name="contentUrl">可以是本地路径，也可以是网络地址</param>
        /// <param name="header">可以是本地路径，也可以是网络地址</param>
        /// <param name="footer">可以是本地路径，也可以是网络地址</param>
        /// <param name="savePath"></param>
        /// <returns></returns>
        private static string GetArguments(string contentUrl, string letHeader, string rightHeader, string footer, string title, string savePath)
        {
            if (string.IsNullOrEmpty(contentUrl))
            {
                throw new Exception("HTML本地路径或网络地址不能为空.");
            }

            if (string.IsNullOrEmpty(savePath))
            {
                throw new Exception("PDF文档保存的路径不能为空.");
            }

            StringBuilder stringBuilder = new StringBuilder();

            stringBuilder.Append(" --page-size A4 ");         //打印页面大小

            if (!string.IsNullOrEmpty(letHeader))
            {
                stringBuilder.Append(" --header-html " + letHeader);       //加载本地资源
            }
            //stringBuilder.Append(" --header-left " + letHeader);   //公司信息
            //stringBuilder.Append(" --header-right " + rightHeader);   //单号
            //stringBuilder.Append(" --header-font-size 15 ");
            //stringBuilder.Append(" --header-spacing 3 --footer-spacing 3 ");       //页脚和内容之间显示一条直线
            stringBuilder.Append(" --header-line ");       //页脚和内容之间显示一条直线

            stringBuilder.Append(" --footer-html " + AppDomain.CurrentDomain.BaseDirectory.ToString() + @"Pdf/Tool/footer.html");       //加载本地资源

            //stringBuilder.Append(" --no-stop-slow-scripts ");         //不终止运行慢的js
            //stringBuilder.Append(" --footer-line ");       //页脚和内容之间显示一条直线
            stringBuilder.Append(" --footer-spacing 1 ");        //允许访问的资源
            //stringBuilder.Append(" --footer-center [page]/[topage]");
            if (!string.IsNullOrEmpty(title))
            {
                stringBuilder.Append(" --title " + title);
            }
            //stringBuilder.Append(" --header-right '[page]/[topage]'");
            //stringBuilder.Append(" --javascript-delay 2000 ");        //等待js延迟
            //stringBuilder.Append(" --enable-plugins ");       //允许使用插件
            //stringBuilder.Append(" --enable-javascript ");       //允许执行js
            //stringBuilder.Append(" --enable-local-file-access ");       //加载本地资源
            stringBuilder.Append(" " + contentUrl + " ");       //本地 HTML 的文件路径或网页 HTML 的URL地址
            stringBuilder.Append(" " + savePath + " ");       //生成的 PDF 文档的保存路径
            return stringBuilder.ToString();
        }

        /// <summary>
        /// 验证保存路径
        /// </summary>
        /// <param name="savePath">需要验证的路径</param>
        private static void CheckFilePath(string savePath)
        {
            var ext = string.Empty;
            var path = string.Empty;
            var fileName = string.Empty;

            ext = Path.GetExtension(savePath);
            if (string.IsNullOrEmpty(ext) || ext.ToLower() != ".pdf")
            {
                throw new Exception("此方法用于生成PDF文件.");
            }

            fileName = Path.GetFileName(savePath);
            if (string.IsNullOrEmpty(fileName))
            {
                throw new Exception("文件名为空.");
            }

            try
            {
                path = savePath.Substring(0, savePath.IndexOf(fileName));
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
            }
            catch
            {
                throw new Exception("文件路径不存在.");
            }
        }

        /// <summary>
        /// HTML文本内容转HTML文件
        /// </summary>
        /// <param name="strHtml">HTML文本内容</param>
        /// <returns>HTML文件的路径</returns>
        private static string HtmlTextConvertFile(string strHtml)
        {
            if (string.IsNullOrEmpty(strHtml))
            {
                throw new Exception("HTML文本内容不能为空.");
            }

            try
            {
                string path = Config.File.PdfSavePath;
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
                string fileName = path + DateTime.Now.ToString("P-yyyyMMddHHmmssfff") + new Random().Next(1000, 10000) + ".html";
                FileStream fileStream = new FileStream(fileName, FileMode.Create, FileAccess.ReadWrite, FileShare.ReadWrite);
                StreamWriter streamWriter = new StreamWriter(fileStream, Encoding.Default);
                streamWriter.Write(strHtml);
                streamWriter.Flush();

                streamWriter.Close();
                streamWriter.Dispose();
                fileStream.Close();
                fileStream.Dispose();
                return fileName;
            }
            catch
            {
                throw new Exception("HTML文本内容错误.");
            }
        }
    }
}