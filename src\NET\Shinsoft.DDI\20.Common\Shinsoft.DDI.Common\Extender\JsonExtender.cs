﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Shinsoft.Core.EntityFrameworkCore;

namespace Shinsoft.DDI.Common
{
    public static class JsonExtender
    {
        public static string ToReviewJson<TEntity>(this TEntity entity, JsonSerializerOptions? options = null)
            where TEntity : class, IEntity
        {
            return entity.ToTypeJson(options);
        }

        public static string ToReviewJson<TEntity>(this List<TEntity> entities, JsonSerializerOptions? options = null)
            where TEntity : class, IEntity
        {
            return entities.ToTypeJson(options);
        }

        public static TEntity? DeserializeReviewEntity<TEntity>(this string json, JsonSerializerOptions? options = null)
            where TEntity : class, IEntity
        {
            return json.DeserializeJson<TEntity>(options);
        }

        public static List<TEntity>? DeserializeReviewEntities<TEntity>(this string json, JsonSerializerOptions? options = null)
            where TEntity : class, IEntity
        {
            return json.DeserializeJson<List<TEntity>>(options);
        }
    }
}