﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    [AllowAnonymous]
    [Route("schedule/[action]")]
    [ApiExplorerSettings(GroupName = "计划任务")]
    public class ScheduleService : BaseApiController<SysBll>
    {
        protected BizResult WriteLog(Company company, string operate, BizResult taskResult)
        {
            var result = new BizResult();

            var msg = new StringBuilder();

            NLog.LogLevel logLevel = NLog.LogLevel.Info;

            if (taskResult.Success)
            {
                msg.Append("成功");
            }
            else
            {
                msg.Append("失败");
                logLevel = NLog.LogLevel.Error;

                foreach (var m in taskResult.Messages)
                {
                    result.Error($"【{company.Name}】{operate}：{m}");
                    msg.AppendFormat(",{0}", m);
                }
            }

            var logEvent = NLogHelper.GetLogEvent<JobLogEvent>(operate, msg.ToString());

            logEvent.Level = logLevel;
            logEvent.CompanyId = company.ID.ToString();
            logEvent.CompanyCode = company.Code;
            logEvent.CompanyName = company.Name;

            NLogHelper.Log(logEvent, skipFrames: 2);

            return result;
        }

        /// <summary>
        /// 日常任务
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Special, Operate = "日常任务")]
        public BizResult DailyTask()
        {
            var result = new BizResult();

            var companies = this.SysCache.GetValidCompanies();
            foreach (var company in companies)
            {
                var repo = this.GetRepo<ScheduleBll>(company.ID);
            }

            return result;
        }

        /// <summary>
        /// 定计任务
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Special, Operate = "定计任务")]
        public BizResult TimeTask()
        {
            var result = new BizResult();

            var companies = this.SysCache.GetValidCompanies();
            foreach (var company in companies)
            {
                var cc = this.GetCompanyCache(company.ID);
                var repo = this.GetRepo<ScheduleBll>(company.ID);

                BizResult? taskResult = null;
                // 手动方式调用，统一在外面记录日志

                if (cc.MailSendType == MailSendType.SMTP)
                {
                    taskResult = repo.SendMails(true);
                    result.Merge(this.WriteLog(company, "定时发送邮件", taskResult));
                }
            }

            return result;
        }

        /// <summary>
        /// 发送邮件
        /// </summary>
        [AllowAnonymous]
        [HttpGet]
        [LogApi(ApiType.Special, Operate = "发送邮件")]
        public BizResult SendMails([FromQuery] MailFilter filter)
        {
            var result = new BizResult();

            var companies = this.SysCache.GetValidCompanies();
            foreach (var company in companies)
            {
                var repo = this.GetRepo<ScheduleBll>(company.ID);
                var exps = this.NewExps<Mail>();
                //把filter转换成exps
                exps = filter.GetDynamicQueryExps<Mail>(exps);
                if (exps.Count == 0)
                {
                    var time = DateTime.Now.Date.AddDays(1);
                    exps.Add(x => x.CreateTime >= DateTime.Now.Date && x.CreateTime < time);
                }

                if (filter.Statuses?.Any() != true)
                {
                    exps.Add(x => x.EnumStatus == MailStatus.Failed);
                }

                var companyResult = repo.SendMails(exps, filter.MaxSendCount, true);

                var succeed = result.Success ? "成功" : "失败";

                foreach (var msg in companyResult.Messages)
                {
                    result.Info($"【{company.Name}】：{msg}");
                }

                result.Info($"【{company.Name}】：同步{succeed}");
            }

            return result;
        }
    }
}