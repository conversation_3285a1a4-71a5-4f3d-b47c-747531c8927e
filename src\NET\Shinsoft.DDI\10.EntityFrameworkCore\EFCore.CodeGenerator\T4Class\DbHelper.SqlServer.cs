﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;

namespace EntityFrameworkCore.CodeGenerator.T4Class.SqlServer
{
#nullable enable

    public class DbHelper : BaseDbHelper
    {
        public DbHelper(string connectionString, string database = "")
            : base(new SqlConnection(connectionString), database)
        {
        }

        protected override List<Table> GetAllTables()
        {
            var sql =
                @"SELECT
                    t.TABLE_CATALOG,
                    t.TABLE_SCHEMA,
                    t.TABLE_NAME,
                    t.TABLE_TYPE,
                    ISNULL(ep.value,N'') AS TABLE_COMMENT
                FROM INFORMATION_SCHEMA.TABLES AS t
                LEFT OUTER JOIN sys.extended_properties AS ep ON ep.name = N'MS_Description' AND ep.major_id = OBJECT_ID(CONCAT(t.TABLE_CATALOG,'.',t.TABLE_SCHEMA,'.',t.TABLE_NAME)) AND ep.minor_id = 0";

            if (!string.IsNullOrEmpty(this.Database))
            {
                sql += $" WHERE t.TABLE_CATALOG = '{this.Database}'";
            }

            sql += " ORDER BY t.TABLE_TYPE, t.TABLE_NAME";

            var dr = this.ExecuteReader(sql);

            var tables = new List<Table>();

            while (dr.Read())
            {
                var table = new Table
                {
                    IsView = dr["TABLE_TYPE"].ToString() == "VIEW",
                    Schema = dr["TABLE_SCHEMA"].ToString() ?? string.Empty,
                    Name = dr["TABLE_NAME"].ToString() ?? string.Empty,

                    Comment = dr["TABLE_COMMENT"].ToString()
                };

                tables.Add(table);
            }

            dr.Close();

            return tables;
        }

        protected override List<Column> GetAllColums()
        {
            var sql =
                @"SELECT
                    c.TABLE_CATALOG,
                    c.TABLE_SCHEMA,
                    c.TABLE_NAME,
                    c.ORDINAL_POSITION,
                    c.COLUMN_NAME,
                    ISNULL(ep.value,N'') AS COLUMN_COMMENT,
                    c.DATA_TYPE,
                    c.CHARACTER_MAXIMUM_LENGTH,
                    c.NUMERIC_PRECISION,
                    c.NUMERIC_SCALE,
                    CAST(CASE c.IS_NULLABLE
                        WHEN N'YES' THEN 1
                        ELSE 0
                    END AS BIT) AS IS_NULLABLE,
                    COLUMNPROPERTY(OBJECT_ID(c.TABLE_SCHEMA+'.'+c.TABLE_NAME),c.COLUMN_NAME,'IsIdentity') AS IS_IDENTITY,
                    CAST(CASE tc.CONSTRAINT_TYPE
                        WHEN N'PRIMARY KEY' THEN 1
                        ELSE 0
                    END AS BIT) AS IS_PRIMARY_KEY,
                    CAST(CASE tc.CONSTRAINT_TYPE
                        WHEN N'FOREIGN KEY' THEN 1
                        ELSE 0
                    END AS BIT) AS IS_FOREIGN_KEY,
                    kcu.CONSTRAINT_NAME,
                    f_kcu.TABLE_CATALOG AS FOREIGN_TABLE_CATALOG,
                    f_kcu.TABLE_SCHEMA AS FOREIGN_TABLE_SCHEMA,
                    f_kcu.TABLE_NAME AS FOREIGN_TABLE_NAME
                FROM INFORMATION_SCHEMA.COLUMNS AS c
                LEFT OUTER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE AS kcu ON kcu.TABLE_CATALOG = c.TABLE_CATALOG AND kcu.TABLE_SCHEMA = c.TABLE_SCHEMA AND kcu.TABLE_NAME = c.TABLE_NAME AND kcu.COLUMN_NAME = c.COLUMN_NAME
                LEFT OUTER JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS AS tc ON tc.CONSTRAINT_CATALOG = kcu.CONSTRAINT_CATALOG AND tc.CONSTRAINT_SCHEMA = kcu.CONSTRAINT_SCHEMA AND kcu.CONSTRAINT_NAME = tc.CONSTRAINT_NAME
                LEFT OUTER JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS AS rc ON tc.CONSTRAINT_TYPE = N'FOREIGN KEY' AND rc.CONSTRAINT_CATALOG = tc.CONSTRAINT_CATALOG AND rc.CONSTRAINT_SCHEMA = tc.CONSTRAINT_SCHEMA AND rc.CONSTRAINT_NAME = tc.CONSTRAINT_NAME
                LEFT OUTER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE AS f_kcu ON f_kcu.CONSTRAINT_CATALOG = rc.UNIQUE_CONSTRAINT_CATALOG AND f_kcu.CONSTRAINT_SCHEMA = rc.UNIQUE_CONSTRAINT_SCHEMA AND f_kcu.CONSTRAINT_NAME = rc.UNIQUE_CONSTRAINT_NAME
				LEFT OUTER JOIN sys.columns AS sc ON sc.object_id = OBJECT_ID(CONCAT(c.TABLE_CATALOG, '.', c.TABLE_SCHEMA, '.', c.TABLE_NAME))  AND sc.name =  c.COLUMN_NAME
                LEFT OUTER JOIN sys.extended_properties AS ep ON ep.name = N'MS_Description' AND ep.major_id = OBJECT_ID(CONCAT(c.TABLE_CATALOG, '.', c.TABLE_SCHEMA, '.', c.TABLE_NAME)) AND ep.minor_id = sc.column_id";

            if (!string.IsNullOrEmpty(this.Database))
            {
                sql += $" WHERE c.TABLE_CATALOG = '{this.Database}'";
            }

            sql += " ORDER BY c.TABLE_CATALOG, c.TABLE_SCHEMA, c.TABLE_NAME, c.ORDINAL_POSITION";

            var dr = this.ExecuteReader(sql);

            var columns = new List<Column>();

            while (dr.Read())
            {
                var column = new Column
                {
                    TableSchema = dr["TABLE_SCHEMA"].ToString() ?? string.Empty,
                    TableName = dr["TABLE_NAME"].ToString() ?? string.Empty,
                    OrdinalPosition = Convert.ToInt32(dr["ORDINAL_POSITION"]),
                    Name = dr["COLUMN_NAME"].ToString() ?? string.Empty,
                    DataType = dr["DATA_TYPE"].ToString() ?? string.Empty,
                    MaxLength = dr["CHARACTER_MAXIMUM_LENGTH"] == DBNull.Value
                                ? null
                                : Convert.ToInt64(dr["CHARACTER_MAXIMUM_LENGTH"]),
                    NumPrecision = dr["NUMERIC_PRECISION"] == DBNull.Value
                                ? null
                                : Convert.ToInt32(dr["NUMERIC_PRECISION"]),
                    NumScale = dr["NUMERIC_SCALE"] == DBNull.Value
                                ? null
                                : Convert.ToInt32(dr["NUMERIC_SCALE"]),
                    IsNullable = Convert.ToBoolean(dr["IS_NULLABLE"]),
                    IsIdentity = Convert.ToBoolean(dr["IS_IDENTITY"]),
                    IsPrimaryKey = Convert.ToBoolean(dr["IS_PRIMARY_KEY"]),
                    IsForeignKey = Convert.ToBoolean(dr["IS_FOREIGN_KEY"]),
                    ConstraintName = dr["CONSTRAINT_NAME"].ToString(),
                    ForeignTableSchema = dr["FOREIGN_TABLE_SCHEMA"].ToString(),
                    ForeignTableName = dr["FOREIGN_TABLE_NAME"].ToString(),
                    Comment = dr["COLUMN_COMMENT"].ToString()
                };

                columns.Add(column);
            }

            dr.Close();

            return columns;
        }

        protected override DbType GetColumnDbType(string dataType, long? maxLength)
        {
            DbType dbType = default;

            switch (dataType.ToLower())
            {
                case "int":
                    dbType = DbType.Int32;
                    break;

                case "varchar":
                    dbType = DbType.String;
                    break;

                case "bit":
                    dbType = DbType.Boolean;
                    break;

                case "datetime":
                    dbType = DbType.DateTime;
                    break;

                case "time":
                    dbType = DbType.Time;
                    break;

                case "decimal":
                    dbType = DbType.Decimal;
                    break;

                case "float":
                    dbType = DbType.Double;
                    break;

                case "image":
                    dbType = DbType.Binary;
                    break;

                case "money":
                    dbType = DbType.Decimal;
                    break;

                case "ntext":
                    dbType = DbType.String;
                    break;

                case "nvarchar":
                    dbType = DbType.String;
                    break;

                case "smalldatetime":
                    dbType = DbType.DateTime;
                    break;

                case "smallint":
                    dbType = DbType.Int16;
                    break;

                case "text":
                    dbType = DbType.String;
                    break;

                case "bigint":
                    dbType = DbType.Int64;
                    break;

                case "binary":
                    dbType = DbType.Binary;
                    break;

                case "char":
                    dbType = DbType.String;
                    break;

                case "nchar":
                    dbType = DbType.String;
                    break;

                case "numeric":
                    dbType = DbType.Decimal;
                    break;

                case "real":
                    dbType = DbType.Single;
                    break;

                case "smallmoney":
                    dbType = DbType.Decimal;
                    break;

                case "sql_variant":
                    dbType = DbType.Object;
                    break;

                case "timestamp":
                    dbType = DbType.Binary;
                    break;

                case "tinyint":
                    dbType = DbType.Byte;
                    break;

                case "uniqueidentifier":
                    dbType = DbType.Guid;
                    break;

                case "varbinary":
                    dbType = DbType.Binary;
                    break;

                case "xml":
                    dbType = DbType.Xml;
                    break;
            }

            return dbType;
        }
    }

#nullable restore
}
