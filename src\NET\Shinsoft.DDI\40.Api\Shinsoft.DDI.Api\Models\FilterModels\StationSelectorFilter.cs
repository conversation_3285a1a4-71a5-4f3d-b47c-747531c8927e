﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class StationSelectorFilter
    {
        /// <summary>
        /// 关键词
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(Station), Station.Columns.Name, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Department, Department.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Department, Department.Columns.Name, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Position, Position.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Position, Position.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        [DynamicQueryColumn(typeof(Station), Station.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Name { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [Description("部门")]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Department, Department.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Department, Department.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? DepartmentKeywords { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        [Description("职位")]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Position, Position.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Position, Position.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? PositionKeywords { get; set; }

        /// <summary>
        /// 有效性
        /// </summary>
        [Description("有效性")]
        [DynamicQueryColumn(typeof(Station), Station.Columns.Valid, Operation = Operation.In)]
        public List<bool>? Valids { get; set; }
    }
}
