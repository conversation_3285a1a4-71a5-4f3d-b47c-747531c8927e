CREATE TABLE [dbo].[VueRouteMeta] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_VueRouteMeta_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [Title]                     NVARCHAR(200)		        NOT NULL,
    [Icon]                      NVARCHAR(500)		        NULL,
    [Rank]                      INT		                    NULL,
    [ShowLink]                  BIT                         NULL,       --是否在菜单中显示（默认`true`）`可选`
    [AlwaysShow]                BIT                         NULL,       --是否当子菜单中只有一项时，也显示折叠菜单（默认`false`）`可选`
    [Anonymous]                 BIT                         NULL,       --是否允许匿名访问 `可选`
    [IsBreadcrumbLink]          BIT                         NULL,       --是否为面包屑链接（默认`true`）`可选`
    [Auths]                     NVARCHAR(500)		        NULL,       --权限设置 `可选`,例如（单个）："权限" | { "权限", undefined | null | allowAllTags | "标签" | ["标签1","标签2"]},例如（多个）：["权限" | { "权限", undefined | null | allowAllTags | "标签" | ["标签1","标签2"]}]
    CONSTRAINT [PK_VueRouteMeta] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_VueRouteMeta_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_VueRouteMeta_VueRoute_00_Route_Meta] FOREIGN KEY ([ID]) REFERENCES [dbo].[VueRoute] ([ID]),
)
