<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;1591;1416;IDE0130</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <NoWarn>1701;1702;1591;1416;IDE0130</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;1591;1416;IDE0130</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <NoWarn>1701;1702;1591;1416;IDE0130</NoWarn>
  </PropertyGroup>
	
  <ItemGroup>
	<Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Shinsoft.Core">
      <HintPath>..\..\00.Reference\net8.0\Shinsoft.Core.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\10.EntityFrameworkCore\Shinsoft.DDI.Entities\Shinsoft.DDI.Entities.csproj" />
    <ProjectReference Include="..\..\20.Common\Shinsoft.DDI.Common\Shinsoft.DDI.Common.csproj" />
    <ProjectReference Include="..\..\30.Bll\Shinsoft.DDI.Bll\Shinsoft.DDI.Bll.csproj" />
  </ItemGroup>
	  
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.14" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
    <PackageReference Include="NLog.Database" Version="5.4.0" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.4.0" />
    <PackageReference Include="Quartz" Version="3.14.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="8.0.2" />

  </ItemGroup>
	
  <ItemGroup>
	<Using Include="System.ComponentModel" />
	<Using Include="System.ComponentModel.DataAnnotations" />
	<Using Include="System.Linq.Expressions" />
	<Using Include="System.Text.Json" />
	<Using Include="System.Text.Json.Serialization" />
	<Using Include="System.Xml.Serialization" />
	<Using Include="Microsoft.AspNetCore.Authorization" />
	<Using Include="Microsoft.AspNetCore.Mvc" />
	<Using Include="Shinsoft.Core" />
	<Using Include="Shinsoft.Core.AutoMapper" />
	<Using Include="Shinsoft.Core.DynamicQuery" />
	<Using Include="Shinsoft.Core.EntityFrameworkCore" />
	<Using Include="Shinsoft.Core.Hosting" />
	<Using Include="Shinsoft.Core.Linq" />
	<Using Include="Shinsoft.Core.Mail" />
	<Using Include="Shinsoft.Core.Mvc" />
	<Using Include="Shinsoft.Core.NLog" />
	<Using Include="Shinsoft.Core.Json" />
	<Using Include="Shinsoft.DDI.Entities" />
	<Using Include="Shinsoft.DDI.Common" />
	<Using Include="Shinsoft.DDI.Common.Configration" />
	<Using Include="Shinsoft.DDI.Bll" />
	<Using Include="Shinsoft.DDI.Bll.Caching" />
	<Using Include="Shinsoft.DDI.Api.Models" />
  </ItemGroup>

	
	  
  <ItemGroup>
    <None Update="Models\Biz.Model.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Biz.Model.cs</LastGenOutput>
    </None>
    <None Update="Models\File.Model.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>File.Model.cs</LastGenOutput>
    </None>
    <None Update="Models\Log.Model.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Log.Model.cs</LastGenOutput>
    </None>
    <None Update="Models\Mail.Model.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Mail.Model.cs</LastGenOutput>
    </None>
  </ItemGroup>
  
  <ItemGroup>
    <Compile Update="Models\Biz.Model.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Biz.Model.tt</DependentUpon>
    </Compile>
    <Compile Update="Models\File.Model.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>File.Model.tt</DependentUpon>
    </Compile>
    <Compile Update="Models\Log.Model.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Log.Model.tt</DependentUpon>
    </Compile>
    <Compile Update="Models\Mail.Model.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Mail.Model.tt</DependentUpon>
    </Compile>
  </ItemGroup>

</Project>
