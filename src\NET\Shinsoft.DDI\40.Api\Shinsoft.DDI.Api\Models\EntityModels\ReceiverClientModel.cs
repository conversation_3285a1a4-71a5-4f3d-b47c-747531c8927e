﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class ReceiverClientModel
    {
        [MapFromProperty(typeof(ReceiverClient), ReceiverClient.Foreigns.Receiver, Receiver.Columns.Address)]
        public string? DistributorAdress { get; set; }

        /// <summary>
        /// 列映射model
        /// </summary>
        public List<ColumnMappingTemplateModel>? ColumnMappingTemplateModels { get; set; }
    }
}
