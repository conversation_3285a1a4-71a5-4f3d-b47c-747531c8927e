CREATE TABLE [dbo].[EmployeeDelegateAuthTag] (
    [ID]                            UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_EmployeeDelegateAuthTag_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                     UNIQUEIDENTIFIER            NOT NULL,
    [EmployeeDelegateId]            UNIQUEIDENTIFIER            NOT NULL,
    [AuthId]                        UNIQUEIDENTIFIER            NOT NULL,
    [AuthTagId]                     UNIQUEIDENTIFIER            NOT NULL,
    CONSTRAINT [PK_EmployeeDelegateAuthTag] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_EmployeeDelegateAuthTag_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_EmployeeDelegateAuthTag_EmployeeDelegate_00_EmployeeDelegate] FOREIGN KEY ([EmployeeDelegateId]) REFERENCES [dbo].[EmployeeDelegate] ([ID]),
    CONSTRAINT [FK_EmployeeDelegateAuthTag_Auth_00_Auth] FOREIGN KEY ([AuthId]) REFERENCES [dbo].[Auth] ([ID]),
    CONSTRAINT [FK_EmployeeDelegateAuthTag_AuthTag_00_AuthTag] FOREIGN KEY ([AuthTagId]) REFERENCES [dbo].[AuthTag] ([ID]),
)

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'员工代理权限标签',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'EmployeeDelegateAuthTag',
    @level2type = NULL,
    @level2name = NULL
