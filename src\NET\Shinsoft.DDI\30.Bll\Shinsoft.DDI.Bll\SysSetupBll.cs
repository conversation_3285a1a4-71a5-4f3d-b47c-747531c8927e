﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Shinsoft.DDI.Entities;
using AutoMapper.Execution;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Shinsoft.Core;
using ColumnAttribute = Shinsoft.Core.EntityFrameworkCore.ColumnAttribute;

namespace Shinsoft.DDI.Bll
{
    public class SysSetupBll : BaseBll
    {
        #region Constructs

        public SysSetupBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        public SysSetupBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        public SysSetupBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        public SysSetupBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs

        protected static BizResult CheckCompanyEntity(ICompanyEntity<Guid> entity, BizResult? result = null)
        {
            result ??= new BizResult();

            if (entity.CompanyId.IsEmpty())
            {
                result.Error("请选择公司");
            }

            return result;
        }

        #region Company 公司

        protected BizResult<Company> CheckCompany(Company entity, CompanyCfg cfg, BizResult<Company>? result = null)
        {
            result ??= new BizResult<Company>();

            if (entity.Code.IsEmpty())
            {
                result.Error("请输入编码");
            }

            if (entity.Name.IsEmpty())
            {
                result.Error("请输入名称");
            }

            if (entity.StdCurrency.IsEmpty())
            {
                entity.StdCurrency = "CNY";
            }

            if (result.Success)
            {
                var exist = this.GetEntities<Company>(p => p.Code == entity.Code || p.Name == entity.Name);

                if (exist.Any(p => p.ID != entity.ID && p.Code == entity.Code))
                {
                    result.Error("已存在相同编码的公司");
                }

                if (exist.Any(p => p.ID != entity.ID && p.Name == entity.Name))
                {
                    result.Error("已存在相同名称的公司");
                }
            }

            if (result.Success)
            {
                if (entity.ParentId.HasValue)
                {
                    var parent = entity.Parent ?? this.Get<Company>(entity.ParentId);

                    if (parent == null)
                    {
                        result.Error("上级公司不存在");
                    }
                    else
                    {
                        if (entity.Uid > 0 && parent.UidPath.Contains($"|{entity.Uid}|"))
                        {
                            result.Error("上级公司不可以选择当前公司及其下级公司");
                        }
                    }
                }
            }

            return result;
        }

        public BizResult<Company> AddCompany(Company entity, CompanyCfg cfg)
        {
            var result = this.CheckCompany(entity, cfg);

            if (result.Success)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                if (entity.ParentId.HasValue)
                {
                    var parent = this.Get<Company>(entity.ParentId);

                    entity.GroupId = parent!.GroupId;
                    entity.Rank = parent.Rank + 1;
                }
                else
                {
                    entity.GroupId = entity.ID;
                    entity.Rank = 1;
                }

                entity = this.Add(entity, false);

                cfg.ID = entity.ID;

                this.Add(cfg, false);

                if (result.Success)
                {
                    using (var trans = new TransactionScope())
                    {
                        this.SaveChanges();

                        entity.CalcUidPath();

                        entity = this.Update(entity);

                        trans.Complete();

                        result.Data = entity;
                    }

                    this.SysCache.RemoveCache<Company>();
                }
            }

            return result;
        }

        public BizResult<Company> UpdateCompany(Company entity, CompanyCfg cfg)
        {
            var result = new BizResult<Company>();

            var id = entity.ID;
            var dbEntity = this.Get<Company>(id);
            var dbCfg = this.Get<CompanyCfg>(id);

            if (dbEntity == null)
            {
                result.Error("公司不存在");
            }
            else
            {
                entity.RemoveChangedColumn(Company.Columns.ParentId);
                entity.RemoveChangedColumn(Company.Columns.GroupId);
                entity.RemoveChangedColumn(Company.Columns.UidPath);

                var changed = false;

                if (dbCfg == null)
                {
                    changed = true;
                    cfg.ID = id;
                    this.Add(cfg, false);
                }
                else if (this.Update(ref cfg, false))
                {
                    changed = true;
                }

                if (this.Update(ref entity, false))
                {
                    changed = true;
                }

                if (changed)
                {
                    result = this.CheckCompany(entity, cfg, result);
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    this.SysCache.RemoveCache<Company>();

                    result.Data = entity;
                }
                else
                {
                    this.RollbackChanges();
                }
            }

            return result;
        }

        public BizResult DeleteCompany(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<Company>(id);

            if (dbEntity == null)
            {
                result.Error("公司不存在");
            }
            else
            {
                this.Delete(dbEntity, false);

                var children = this.GetEntities<Company>(p => p.ID != id && p.UidPath.StartsWith(dbEntity.UidPath));

                foreach (var child in children)
                {
                    this.Delete(child, false);
                }
                this.SaveChanges();

                this.SysCache.RemoveCache<Company>();
            }

            return result;
        }

        public BizResult MoveCompany(Guid id, Guid? parentId)
        {
            var result = new BizResult();

            var dbEntity = this.Get<Company>(id);
            Company? parent = null;

            if (dbEntity == null)
            {
                result.Error("公司不存在");
            }
            else if (dbEntity.ParentId != parentId)
            {
                if (parentId.HasValue)
                {
                    parent = this.Get<Company>(parentId);

                    if (parent == null)
                    {
                        result.Error("上级公司不存在");
                    }
                    else if (parent.UidPath.Contains($"|{dbEntity.Uid}|"))
                    {
                        result.Error("上级公司不可以选择当前公司及其下级公司");
                    }
                }
            }

            if (result.Success)
            {
                dbEntity = dbEntity.Value();

                dbEntity.ParentId = parent?.ID;
                dbEntity.GroupId = parent?.GroupId ?? dbEntity.ID;
                dbEntity.Rank = (parent?.Rank ?? 0) + 1;

                var descendants = this.GetEntities<Company>(p => p.UidPath.Contains($"|{dbEntity.Uid}|"));

                dbEntity.CalcUidPath(descendants);

                this.Update(dbEntity);

                this.SysCache.RemoveCache<Company>();
            }
            return result;
        }

        public BizResult<Company> SetCompanyValid(Company entity)
        {
            var result = new BizResult<Company>();

            var dbEntity = this.Get<Company>(entity.ID);

            if (dbEntity == null)
            {
                result.Error("公司不存在");
            }
            else
            {
                dbEntity.Valid = entity.Valid;

                this.Update(dbEntity);
            }

            return result;
        }

        #endregion Company 公司

        #region User

        public BizResult<User> AddUser(User entity)
        {
            var result = new BizResult<User>();

            if (entity.ID.IsEmpty())
            {
                entity.ID = CombGuid.NewGuid();
            }

            entity.EnumPwdType = PwdType.SHA1;

            if (entity.PwdText.IsEmpty())
            {
                entity.PwdText = SysDateTime.Now.ToString(Config.DefaultPwdFormat);
            }

            if (entity.EnumStatus == UserStatus.None)
            {
                entity.EnumStatus = UserStatus.Valid;
            }

            if (entity.EnumType == UserType.None)
            {
                result.Error("请选择用户类型");
            }
            else if (entity.EnumType == UserType.Employee)
            {
                #region 员工账号

                if (!entity.DefaultEmployeeId.HasValue)
                {
                    result.Error("请选择员工");
                }
                else
                {
                    var employee = this.Get<Employee>(entity.DefaultEmployeeId.Value);

                    if (employee == null)
                    {
                        result.Error("员工不存在");
                    }
                    else
                    {
                        if (!Config.AllowLoginEmployeeStatus.Contains(employee.EnumStatus))
                        {
                            result.Error("员工不可用");
                        }
                        else if (employee.UserId.HasValue)
                        {
                            result.Error("该员工已开通账号");
                        }

                        var exist = this.GetEntities<User>(p => p.LoginName == employee.JobNo);

                        if (exist != null)
                        {
                            result.Error("存在与当前员工工号相同的用户名");
                        }

                        if (result.Success)
                        {
                            entity.CopyFrom(employee);

                            this.Add(entity, false);
                            employee.UserId = entity.ID;

                            this.Update(employee, false);
                            this.SaveChanges();

                            result.Data = entity;
                        }
                    }
                }

                #endregion 员工账号
            }
            else
            {
                #region 非员工账号

                if (entity.LoginName.IsEmpty())
                {
                    result.Error("用户名不可以为空");
                }

                if (entity.DisplayName.IsEmpty())
                {
                    result.Error("姓名不可以为空");
                }

                var exist = this.GetEntity<User>(p => p.LoginName == entity.LoginName);

                if (exist != null)
                {
                    result.Error("用户名重复");
                }
                var existEmployee = this.GetEntity<Employee>(p => p.JobNo == entity.LoginName);

                if (existEmployee != null)
                {
                    result.Error("用户名与现有员工编号重复");
                }

                if (result.Success)
                {
                    entity.DefaultEmployeeId = null;
                    this.Add(entity);

                    result.Data = entity;
                }

                #endregion 非员工账号
            }

            return result;
        }

        public BizResult<User> UpdateUser(User entity)
        {
            var result = new BizResult<User>();

            var id = entity.ID;
            var dbEntity = this.Get<User>(id);

            if (dbEntity == null)
            {
                result.Error("用户不存在");
            }
            else if (dbEntity.EnumType == UserType.Employee)
            {
                result.Error("员工账号不可以编辑");
            }
            else
            {
                // 更新用户时不更新密码

                entity.RemoveChangedColumn(User.Columns.EnumPwdType);
                entity.RemoveChangedColumn(User.Columns.PwdText);

                if (this.Update(ref entity, false))
                {
                    switch (entity.EnumType)
                    {
                        case UserType.None:
                            result.Error("请选择用户类型");
                            break;

                        case UserType.Employee:
                            result.Error("不可以将非员工账号改为员工账号");
                            break;
                    }

                    if (entity.LoginName.IsEmpty())
                    {
                        result.Error("用户名不可以为空");
                    }

                    if (entity.DisplayName.IsEmpty())
                    {
                        result.Error("姓名不可以为空");
                    }

                    var exist = this.GetEntity<User>(p => p.ID != id && p.LoginName == entity.LoginName);

                    if (exist != null)
                    {
                        result.Error("用户名重复");
                    }
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                }
            }

            return result;
        }

        public BizResult DeleteUser(User entity)
        {
            var result = new BizResult();

            var id = entity.ID;
            var dbEntity = this.Get<User>(id);

            if (dbEntity == null)
            {
                result.Error("用户不存在");
            }
            else if (dbEntity.EnumType == UserType.Employee)
            {
                result.Error("不可以删除员工账号");
            }
            else
            {
                if (result.Success)
                {
                    this.Delete(entity);
                }
            }

            return result;
        }

        public BizResult ResetUserPwd(Guid userId, string newPwd)
        {
            var result = new BizResult();

            var dbEntity = this.Get<User>(userId);

            if (dbEntity == null)
            {
                result.Error("用户不存在");
            }
            else if (dbEntity.EnumStatus != UserStatus.Valid)
            {
                result.Error("用户不可用");
            }
            else
            {
                var user = new User
                {
                    ID = userId,
                    EnumPwdType = PwdType.SHA1,
                    PwdText = newPwd.ToSHA1()
                };

                this.Update(ref user, false);

                user.IncreaseSeed();

                this.SaveChanges();
            }

            return result;
        }

        #endregion User

        #region I18n

        private void InitEnumI18n(List<I18n> dbEntities)
        {
            //这里定义的枚举可以和EFCore.CodeGenerator中定义的枚举一致
            var assemblyTuples = new List<Tuple<string, string>>
            {
                // Shinsoft.DDI.Entities
                Tuple.Create("Shinsoft.DDI.Entities","Shinsoft.DDI.Entities"),
                // Shinsoft.Core.Mail
                Tuple.Create("Shinsoft.Core","Shinsoft.Core.Mail"),
                Tuple.Create("Shinsoft.DDI.Entities","Shinsoft.DDI.Entities.Workflow"),
                Tuple.Create("Shinsoft.DDI.Common","Shinsoft.DDI.Common")
            };

            var root = dbEntities.FirstOrDefault(p => !p.ParentId.HasValue && p.Key == "Enums");

            if (root == null)
            {
                root = new I18n
                {
                    ID = CombGuid.NewGuid(),
                    EnumCategory = I18nCategory.Enum,
                    EnumType = I18nType.Group,
                    EnumFlags = I18nFlag.Server | I18nFlag.Client,
                    Key = "Enums",
                    Name = "枚举",
                    Rank = 1,
                    Valid = true,
                    IsSys = true,
                };

                this.Add(root);

                dbEntities.Add(root);
            }

            var typeDbEntities = dbEntities.Where(p => p.ParentId == root.ID).ToList();

            var entities = new List<I18n>();

            foreach (var assemblyTuple in assemblyTuples)
            {
                var assemblyName = assemblyTuple.Item1;
                var assemblyNamespace = assemblyTuple.Item2;

                var assembly = Assembly.Load(assemblyName);

                if (assembly != null)
                {
                    var types = assembly.GetTypes();

                    foreach (var type in types)
                    {
                        if (type.IsEnum && type.Namespace == assemblyNamespace)
                        {
                            var entity = new I18n
                            {
                                ParentId = root.ID,
                                EnumCategory = root.EnumCategory,
                                EnumType = I18nType.Group,
                                Group = root.Key,
                                Key = type.Name,
                                Rank = root.Rank + 1,
                                Valid = true,
                                IsSys = true,
                            };

                            entities.Add(entity);

                            var enumValues = type.GetEnumValues().Cast<Enum>().ToList();

                            var enumEntities = new List<I18n>();

                            var desc = type.GetDesc();

                            if (desc.IsEmpty())
                            {
                                desc = type.Name;
                            }
                            else
                            {
                                enumEntities.Add(new I18n
                                {
                                    EnumType = I18nType.Culture,
                                    EnumCategory = entity.EnumCategory,
                                    Group = $"{entity.Group}.{entity.Key}",
                                    Key = "_Enum",
                                    Name = type.Name,
                                    Rank = entity.Rank + 1,
                                    Valid = true,
                                    IsSys = true
                                });
                            }

                            foreach (var enumValue in enumValues)
                            {
                                var enumName = Enum.GetName(type, enumValue);
                                var enumDesc = enumValue.GetDesc();
                                var value = enumValue.GetHashCode();

                                var field = type.GetField(enumName!);

                                var descAttr = field?.GetAttribute<DescriptionAttribute>();

                                if (descAttr != null)
                                {
                                    var enumEntity = new I18n
                                    {
                                        EnumType = I18nType.Culture,
                                        EnumCategory = entity.EnumCategory,
                                        Group = $"{entity.Group}.{entity.Key}",
                                        Key = enumName,
                                        Name = enumDesc.IsEmpty()
                                            ? enumName
                                            : enumDesc,
                                        Rank = entity.Rank + 1,
                                        Valid = true,
                                        IsSys = true
                                    };

                                    enumEntities.Add(enumEntity);
                                }
                            }

                            if (enumEntities.Count > 0)
                            {
                                var dbEntity = typeDbEntities.FirstOrDefault(p => p.Key == entity.Key);

                                if (dbEntity == null)
                                {
                                    entity.ID = CombGuid.NewGuid();

                                    entity.Name = desc;

                                    this.Add(entity);

                                    dbEntities.Add(entity);
                                }
                                else
                                {
                                    entity.ID = dbEntity.ID;

                                    this.Update(ref entity, false);
                                }
                            }

                            var enumDbEntities = dbEntities.Where(p => p.ParentId == entity.ID).ToList();

                            foreach (var enumEntity in enumEntities)
                            {
                                var dbEnumEntity = enumDbEntities.FirstOrDefault(p => p.Key == enumEntity.Key);

                                if (dbEnumEntity == null)
                                {
                                    enumEntity.ID = CombGuid.NewGuid();
                                    enumEntity.ParentId = entity.ID;

                                    if (enumEntity.Key == "_Enum")
                                    {
                                        enumEntity.Text = desc;
                                    }
                                    else
                                    {
                                        enumEntity.Text = enumEntity.Name;
                                    }

                                    this.Add(enumEntity);

                                    dbEntities.Add(enumEntity);
                                }
                                else
                                {
                                    enumEntity.ID = dbEnumEntity.ID;

                                    this.Update(enumEntity, false);
                                }
                            }

                            var invalidEnumEntities = enumDbEntities.Where(p => p.Valid && p.IsSys && !enumEntities.Any(p1 => p1.ID == p.ID)).ToList();

                            foreach (var invalidEnumEntity in invalidEnumEntities)
                            {
                                invalidEnumEntity.Valid = false;
                                this.Update(invalidEnumEntity, false);
                            }
                        }
                    }
                }
            }

            var invalidEntities = typeDbEntities.Where(p => p.Valid && p.IsSys && !entities.Any(p1 => p1.ID == p.ID)).ToList();

            foreach (var invalidEntity in invalidEntities)
            {
                invalidEntity.Valid = false;
                this.Update(invalidEntity, false);
            }

            this.SaveChanges();
        }

        private void InitEntityI18n(List<I18n> dbEntities)
        {
            var root = dbEntities.FirstOrDefault(p => !p.ParentId.HasValue && p.Key == nameof(Entity));

            if (root == null)
            {
                root = new I18n
                {
                    ID = CombGuid.NewGuid(),
                    EnumCategory = I18nCategory.Entity,
                    EnumType = I18nType.Group,
                    EnumFlags = I18nFlag.Server | I18nFlag.Client,
                    Key = nameof(Entity),
                    Name = "数据实体",
                    Rank = 1,
                    Valid = true,
                    IsSys = true,
                };

                this.Add(root);

                dbEntities.Add(root);
            }

            var typeDbEntities = dbEntities.Where(p => p.ParentId == root.ID).ToList();

            //这里定义的枚举可以和EFCore.CodeGenerator中定义的枚举一致
            var assemblyTuples = new List<Tuple<string, string>>
            {
                // Shinsoft.DDI.Entities
                Tuple.Create("Shinsoft.DDI.Entities","Shinsoft.DDI.Entities"),
            };

            var ignoreProps = new List<string>
            {
                nameof(ICompanyEntity<Guid>.CompanyId),
                nameof(ICompanyEntity<Guid,Company>.Company),
                nameof(IUid.Uid),
                nameof(IUid.UidPath),
                nameof(IDeleteable.Deleted),
                nameof(IOperateInfo.Creator),
                nameof(IOperateInfo.CreateTime),
                nameof(IOperateInfo.LastEditor),
                nameof(IOperateInfo.LastEditTime),
            };

            var validDbTypes = new List<DbType>
            {
                DbType.String,
                DbType.Int16,
                DbType.Int32,
                DbType.Int64,
                DbType.Decimal,
                DbType.Double,
                DbType.Boolean,
                DbType.Date,
                DbType.DateTime,
                DbType.Time,
                DbType.DateTimeOffset
            };

            var baseType = typeof(Entity);
            var entities = new List<I18n>();

            foreach (var assemblyTuple in assemblyTuples)
            {
                var assemblyName = assemblyTuple.Item1;
                var assemblyNamespace = assemblyTuple.Item2;

                var assembly = Assembly.Load(assemblyName);

                if (assembly != null)
                {
                    var types = assembly.GetTypes();

                    foreach (var type in types)
                    {
                        if (type.Namespace == assemblyNamespace && !type.IsAbstract && baseType.IsAssignableFrom(type))
                        {
                            var isView = typeof(IView).IsAssignableFrom(type);

                            var entity = new I18n
                            {
                                ParentId = root.ID,
                                EnumCategory = root.EnumCategory,
                                EnumType = I18nType.Group,
                                Group = root.Key,
                                Key = type.Name,
                                Rank = root.Rank + 1,
                                Valid = true,
                                IsSys = true,
                            };

                            entities.Add(entity);

                            var propDescs = type.GetPropDescs()!;
                            var propColumns = type.GetEntityColumns()!;

                            var propEntities = new List<I18n>();

                            var desc = type.GetDesc();

                            if (desc.IsEmpty())
                            {
                                desc = type.Name;
                            }
                            else
                            {
                                propEntities.Add(new I18n
                                {
                                    EnumType = I18nType.Culture,
                                    EnumCategory = entity.EnumCategory,
                                    Group = $"{entity.Group}.{entity.Key}",
                                    Key = "_Entity",
                                    Name = type.Name,
                                    Rank = entity.Rank + 1,
                                    Valid = true,
                                    IsSys = true
                                });
                            }

                            var props = type.GetProperties()
                                .Where(p => !ignoreProps.Contains(p.Name))
                                .ToList();

                            foreach (var prop in props)
                            {
                                var propDesc = prop.GetDesc();

                                var colunmAttr = prop.GetAttribute<ColumnAttribute>();

                                if (colunmAttr != null)
                                {
                                    // 表字段

                                    if (!propDesc.IsEmpty() || isView)
                                    {
                                        if (colunmAttr.IsPrimaryKey == false && validDbTypes.Contains(colunmAttr.DbType))
                                        {
                                            var propEntity = new I18n
                                            {
                                                EnumType = I18nType.Culture,
                                                EnumCategory = entity.EnumCategory,
                                                Group = $"{entity.Group}.{entity.Key}",
                                                Key = prop.Name,
                                                Name = propDesc.IsEmpty() ? prop.Name : propDesc,
                                                Rank = entity.Rank + 1,
                                                Valid = true,
                                                IsSys = true
                                            };

                                            propEntities.Add(propEntity);
                                        }
                                    }
                                }
                                else if (prop.HasAttribute<ForeignKeyAttribute>() || prop.HasAttribute<InversePropertyAttribute>())
                                {
                                    // 外键 ， 外键返回

                                    if (!propDesc.IsEmpty())
                                    {
                                        var propEntity = new I18n
                                        {
                                            EnumType = I18nType.Culture,
                                            EnumCategory = entity.EnumCategory,
                                            Group = $"{entity.Group}.{entity.Key}",
                                            Key = prop.Name,
                                            Name = propDesc.IsEmpty() ? prop.Name : propDesc,
                                            Rank = entity.Rank + 1,
                                            Valid = true,
                                            IsSys = true
                                        };

                                        propEntities.Add(propEntity);
                                    }
                                }
                            }

                            if (propEntities.Count > 0)
                            {
                                var dbEntity = typeDbEntities.FirstOrDefault(p => p.Key == entity.Key);

                                if (dbEntity == null)
                                {
                                    entity.ID = CombGuid.NewGuid();

                                    entity.Name = desc;

                                    this.Add(entity);

                                    dbEntities.Add(entity);
                                }
                                else
                                {
                                    entity.ID = dbEntity.ID;

                                    this.Update(ref entity, false);
                                }
                            }

                            var propDbEntities = dbEntities.Where(p => p.ParentId == entity.ID).ToList();

                            foreach (var propEntity in propEntities)
                            {
                                var dbPropEntity = propDbEntities.FirstOrDefault(p => p.Key == propEntity.Key);

                                if (dbPropEntity == null)
                                {
                                    propEntity.ID = CombGuid.NewGuid();
                                    propEntity.ParentId = entity.ID;

                                    if (propEntity.Key == "_Entity")
                                    {
                                        propEntity.Text = desc;
                                    }
                                    else
                                    {
                                        propEntity.Text = propEntity.Name;
                                    }

                                    this.Add(propEntity);

                                    dbEntities.Add(propEntity);
                                }
                                else
                                {
                                    propEntity.ID = dbPropEntity.ID;

                                    this.Update(propEntity, false);
                                }
                            }

                            var invalidPropEntities = propDbEntities.Where(p => p.Valid && p.IsSys && !propEntities.Any(p1 => p1.ID == p.ID)).ToList();

                            foreach (var invalidPropEntity in invalidPropEntities)
                            {
                                invalidPropEntity.Valid = false;
                                this.Update(invalidPropEntity, false);
                            }
                        }
                    }
                }
            }

            var invalidEntities = typeDbEntities.Where(p => p.Valid && p.IsSys && !entities.Any(p1 => p1.ID == p.ID)).ToList();

            foreach (var invalidEntity in invalidEntities)
            {
                invalidEntity.Valid = false;
                this.Update(invalidEntity, false);
            }

            this.SaveChanges();
        }

        private void InitSysI18n(Type parentType, I18n? parent, List<I18n> dbEntities)
        {
            var query = dbEntities.AsQueryable();

            if (parent == null)
            {
                query = query.Where(p => !p.ParentId.HasValue);
            }
            else
            {
                query = query.Where(p => p.ParentId == parent.ID);
            }

            var currDbEntities = query.ToList();
            var currRank = (parent?.Rank ?? 0) + 1;

            var members = parentType.GetMembers();

            var entities = new List<I18n>();

            foreach (var member in members)
            {
                I18n? entity = null;

                switch (member.MemberType)
                {
                    case MemberTypes.NestedType:
                        entity = new I18n
                        {
                            EnumType = I18nType.Group,
                            Key = member.Name
                        };

                        break;

                    case MemberTypes.Field:
                        var key = ((FieldInfo)member).GetValue(null).AsString();
                        var lastDot = key.LastIndexOf('.');

                        if (lastDot >= 0)
                        {
                            key = key[(lastDot + 1)..];
                        }

                        entity = new I18n
                        {
                            EnumType = I18nType.Culture,
                            Key = key,
                        };
                        break;
                }

                if (entity != null && !entity.Key.IsEmpty())
                {
                    entity.ParentId = parent?.ID;
                    entity.Rank = currRank;
                    entity.Valid = true;
                    entity.IsSys = true;

                    if (parent == null)
                    {
                        entity.EnumCategory = entity.Key!.ToEnum<I18nCategory>();
                        entity.Group = string.Empty;
                    }
                    else
                    {
                        entity.EnumCategory = parent.EnumCategory;
                        entity.Group = parent.Group.IsEmpty()
                            ? parent.Key
                            : $"{parent.Group}.{parent.Key}";
                    }

                    var desc = member.GetDesc();

                    var dbEntity = currDbEntities.FirstOrDefault(p => p.Key == entity.Key);

                    if (dbEntity == null)
                    {
                        entity.ID = CombGuid.NewGuid();

                        if (parent == null)
                        {
                            switch (entity.EnumCategory)
                            {
                                case I18nCategory.Enum:
                                case I18nCategory.Entity:
                                case I18nCategory.Rule:
                                    entity.EnumFlags = I18nFlag.Server | I18nFlag.Client;
                                    break;

                                case I18nCategory.Message:
                                    entity.EnumFlags = I18nFlag.Server;
                                    break;

                                case I18nCategory.UI:
                                    entity.EnumFlags = I18nFlag.Client;
                                    break;
                            }
                        }

                        entity.Name = desc;

                        this.Add(entity);

                        dbEntities.Add(entity);
                    }
                    else
                    {
                        entity.ID = dbEntity.ID;

                        this.Update(ref entity, false);
                    }

                    if (entity.EnumType == I18nType.Culture)
                    {
                        entity.Text = desc;
                    }
                    else
                    {
                        this.InitSysI18n((Type)member, entity, dbEntities);
                    }

                    entities.Add(entity);
                }
            }

            if (parent != null)
            {
                switch (parent.EnumCategory)
                {
                    case I18nCategory.Rule:
                    case I18nCategory.Message:
                    case I18nCategory.UI:
                        var invalidEntities = currDbEntities.Where(p => p.Valid && p.IsSys && !entities.Any(p1 => p1.ID == p.ID)).ToList();

                        foreach (var entity in invalidEntities)
                        {
                            entity.Valid = false;

                            this.Update(entity, false);
                        }
                        break;
                }
            }
        }

        public void InitSysI18ns()
        {
            var dbEntities = this.GetEntities<I18n>(p => !p.CompanyId.HasValue);

            // 初始化枚举多语言
            this.InitEnumI18n(dbEntities);

            // 初始化数据库字段多语言
            this.InitEntityI18n(dbEntities);

            // 初始化自定义多语言（必须在最后，防止增加的自定义内容被无效化）
            var topMember = typeof(I18ns);

            this.InitSysI18n(topMember, null, dbEntities);

            this.SaveChanges();

            var sc = this.SysCache;

            sc.RemoveI18nCache();

            foreach (var company in sc.GetValidCompanies())
            {
                var cc = CompanyCache.GetCompanyCache(company.ID);

                cc.RemoveI18nCache();
            }
        }

        //public BizResult<I18n> AddI18n(I18n entity)
        //{
        //    var result = new BizResult<I18n>();

        //    if (entity.Key.IsEmpty())
        //    {
        //        result.Error("请输入键值");
        //    }
        //    else
        //    {
        //        Regex regex = new(I18nConsts.KeyRegex);

        //        if (!regex.IsMatch(entity.Key))
        //        {
        //            result.Error("键值必须以字母开始，可以包含大小写字母和数字，且不超过50个字符");
        //        }
        //    }

        //    if (result.Success)
        //    {
        //        var exist = this.GetEntity<I18n>(p => !p.CompanyId.HasValue && p.Key == entity.Key);

        //        if (exist != null)
        //        {
        //            result.Error("已存在相同键值的I18n");
        //        }

        //        if (entity.ParentId.HasValue)
        //        {
        //            var parent = this.Get<I18n>(entity.ParentId.Value);

        //            if (parent == null)
        //            {
        //                result.Error("父I18n不存在");
        //            }
        //        }
        //    }

        //    if (result.Success)
        //    {
        //        entity.CompanyId = null;

        //        entity = this.Add(entity);

        //        result.Data = entity;
        //    }

        //    return result;
        //}

        //public BizResult<I18n> UpdateI18n(I18n entity)
        //{
        //    var result = new BizResult<I18n>();

        //    var id = entity.ID;
        //    var dbEntity = this.Get<I18n>(id);

        //    if (dbEntity == null)
        //    {
        //        result.Error("I18n不存在");
        //    }
        //    else if (dbEntity.CompanyId.HasValue)
        //    {
        //        result.Error("不可以编辑公司的I18n");
        //    }
        //    else
        //    {
        //        entity.CompanyId = null;

        //        // 不更新公司ID,上级ID
        //        entity.RemoveChangedColumn(I18n.Columns.CompanyId);
        //        entity.RemoveChangedColumn(I18n.Columns.ParentId);

        //        if (this.Update(ref entity, false))
        //        {
        //            if (entity.Key.IsEmpty())
        //            {
        //                result.Error("请输入键值");
        //            }
        //            else
        //            {
        //                var regex = new Regex(I18nConsts.KeyRegex);

        //                if (!regex.IsMatch(entity.Key))
        //                {
        //                    result.Error("键值必须以字母开始，可以包含大小写字母和数字，且不超过50个字符");
        //                }
        //            }

        //            var exist = this.GetEntity<I18n>(p => p.ID != id && !p.CompanyId.HasValue && p.Key == entity.Key);

        //            if (exist != null)
        //            {
        //                result.Error("已存在相同键值的I18n");
        //            }
        //        }

        //        if (result.Success)
        //        {
        //            this.SaveChanges();

        //            result.Data = entity;
        //        }
        //        else
        //        {
        //            this.Detach(entity);
        //        }
        //    }

        //    return result;
        //}

        //public BizResult<I18n> ToggleI18n(Guid id)
        //{
        //    var result = new BizResult<I18n>();

        //    var dbEntity = this.Get<I18n>(id);

        //    if (dbEntity == null)
        //    {
        //        result.Error("I18n不存在");
        //    }
        //    else if (dbEntity.CompanyId.HasValue)
        //    {
        //        result.Error("不可以编辑公司的I18n");
        //    }
        //    else
        //    {
        //        dbEntity.Vaild = !dbEntity.Vaild;

        //        result.Data = this.Update(dbEntity);
        //    }

        //    return result;
        //}

        //public BizResult RemoveI18n(Guid id)
        //{
        //    var result = new BizResult();

        //    var dbEntity = this.Get<I18n>(id);

        //    if (dbEntity == null)
        //    {
        //        result.Error("I18n不存在");
        //    }
        //    else if (dbEntity.CompanyId.HasValue)
        //    {
        //        result.Error("不可以编辑公司I18n");
        //    }
        //    else
        //    {
        //        this.Remove(dbEntity);
        //    }

        //    return result;
        //}

        /// <summary>
        /// 新增多语言主表数据
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<I18n> AddI18n(I18n entity)
        {
            var result = new BizResult<I18n>();

            if (entity.Key.IsEmpty())
            {
                result.Error("请输入Key值");
            }

            if (entity.Name.IsEmpty())
            {
                result.Error("请输入名称");
            }

            if (entity.Text.IsEmpty())
            {
                result.Error("请输入默认值");
            }

            if (entity.EnumCategory.IsEmpty() || entity.EnumCategory == I18nCategory.None)
            {
                result.Error("请选择分类");
            }

            if (result.Success)
            {
                var exist = this.GetEntity<I18n>(p => p.Key == entity.Key);

                if (exist != null)
                {
                    result.Error("已存在相同Key值的数据");
                }
            }

            if (result.Success)
            {
                if (entity.ParentId.HasValue)
                {
                    var parent = this.Get<I18n>(entity.ParentId.Value);

                    if (parent == null)
                    {
                        result.Error("父级不存在");
                    }
                    else
                    {
                        entity.Rank = parent.Rank + 1;
                    }
                }
                else
                {
                    entity.Rank = 1;
                }

                if (result.Success)
                {
                    if (entity.ID.IsEmpty())
                    {
                        entity.ID = CombGuid.NewGuid();
                    }

                    entity = this.Add(entity);

                    this.SysCache.RemoveCache<I18n>();

                    result.Data = entity;
                }
            }

            return result;
        }

        /// <summary>
        /// 编辑多语言主表数据
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<I18n> UpdateI18n(I18n entity)
        {
            var result = new BizResult<I18n>();

            var dbEntity = this.Get<I18n>(entity.ID);

            if (dbEntity == null)
            {
                result.Error("数据不存在");
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.Key.IsEmpty())
                    {
                        result.Error("请输入Key值");
                    }

                    if (entity.Name.IsEmpty())
                    {
                        result.Error("请输入名称");
                    }

                    if (entity.Text.IsEmpty())
                    {
                        result.Error("请输入默认值");
                    }

                    if (entity.EnumCategory.IsEmpty() || entity.EnumCategory == I18nCategory.None)
                    {
                        result.Error("请选择分类");
                    }

                    if (result.Success)
                    {
                        var exist = this.GetEntity<I18n>(p => p.ID != entity.ID && p.Key == entity.Key);

                        if (exist != null)
                        {
                            result.Error("已存在相同Key值的数据");
                        }

                        if (entity.ParentId.HasValue)
                        {
                            var parent = this.Get<I18n>(entity.ParentId.Value);

                            if (parent == null)
                            {
                                result.Error("父级不存在");
                            }
                            else
                            {
                                entity.Rank = parent.Rank + 1;
                            }
                        }
                        else
                        {
                            entity.Rank = 1;
                        }
                    }
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    this.SysCache.RemoveCache<I18n>();

                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                }
            }

            return result;
        }

        /// <summary>
        /// 删除多语言主表数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public BizResult RemoveI18n(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<I18n>(id);

            if (dbEntity == null)
            {
                result.Error("数据不存在");
            }
            else
            {
                if (result.Success)
                {
                    var exist = this.GetEntities<I18nCulture>(a => a.I18nId == id);
                    if (exist.Count != 0)
                    {
                        result.Error("存在子表数据不可以直接删除");
                    }
                    if (result.Success)
                    {
                        this.Remove(dbEntity);
                        this.SysCache.RemoveCache<I18n>();
                    }
                }
            }

            return result;
        }

        #endregion I18n

        #region I18nCulture

        /// <summary>
        /// 新增多语言内容
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<I18nCulture> AddI18nCulture(I18nCulture entity)
        {
            var result = new BizResult<I18nCulture>();

            if (entity.Culture.IsEmpty())
            {
                result.Error("请输入语言类型");
            }

            if (entity.Text.IsEmpty())
            {
                result.Error("请输入多语言数据内容");
            }

            if (result.Success)
            {
                var exist = this.GetEntity<I18nCulture>(p => p.I18nId == entity.I18nId && p.Culture == entity.Culture);

                if (exist != null)
                {
                    result.Error("已存在相同语言的数据");
                }
            }

            if (result.Success)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity);

                result.Data = entity;
            }

            return result;
        }

        /// <summary>
        /// 编辑多语言内容
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<I18nCulture> UpdateI18nCulture(I18nCulture entity)
        {
            var result = new BizResult<I18nCulture>();

            var dbEntity = this.Get<I18nCulture>(entity.ID);

            if (dbEntity == null)
            {
                result.Error("多语言不存在");
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.Culture.IsEmpty())
                    {
                        result.Error("请输入语言类型");
                    }

                    if (entity.Text.IsEmpty())
                    {
                        result.Error("请输入多语言数据内容");
                    }

                    if (result.Success)
                    {
                        var exist = this.GetEntity<I18nCulture>(p => p.ID != entity.ID && p.I18nId == entity.I18nId && p.Culture == entity.Culture);

                        if (exist != null)
                        {
                            result.Error("已存在相同语言的数据");
                        }
                    }
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                }
            }

            return result;
        }

        /// <summary>
        /// 删除多语言内容
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public BizResult RemoveI18nCulture(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<I18nCulture>(id);

            if (dbEntity == null)
            {
                result.Error("多语言内容不存在");
            }
            else
            {
                if (result.Success)
                {
                    this.Remove(dbEntity);
                }
            }

            return result;
        }

        #endregion I18nCulture
    }
}