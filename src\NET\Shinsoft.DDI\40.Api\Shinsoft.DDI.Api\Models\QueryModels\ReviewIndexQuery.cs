﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class ReviewIndexQuery
    {
        public ReviewExtInfo? ReviewExtInfo { get; set; }

        [MapFromProperty(typeof(ReviewIndex), ReviewIndex.Foreigns.SubmitEmployee, Employee.Columns.DisplayName)]
        public string? SubmitEmployeeDisplayName { get; set; }

        [MapFromProperty(typeof(ReviewIndex), ReviewIndex.Foreigns.ReviewEmployee, Employee.Columns.DisplayName)]
        public string? ReviewEmployeeDisplayName { get; set; }
    }
}