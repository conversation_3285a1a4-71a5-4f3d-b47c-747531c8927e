﻿using System.Reflection;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using Shinsoft.DDI.Api;
using Shinsoft.DDI.Api.Providers;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi;
using Microsoft.OpenApi.Models;
using NLog.Config;
using NLog.Extensions.Logging;
using NLog.Targets;
using NLog.Targets.Wrappers;
using Shinsoft.Core.AutoMapper;
using Shinsoft.Core.I18n;
using Shinsoft.Core.Json.Converters;
using Shinsoft.Core.Mvc.Filters;
using Swashbuckle.AspNetCore.Filters;
using Swashbuckle.AspNetCore.SwaggerGen;

var builder = WebApplication.CreateBuilder(args);

// 注册NLog
var logConfig = new XmlLoggingConfiguration("_config/NLog.config");

foreach (var target in logConfig.AllTargets)
{
    var name = target.Name;

    DatabaseTarget? dbTarget = null;

    if (target is DatabaseTarget dbTarget1)
    {
        dbTarget = dbTarget1;
    }
    else if (target is AsyncTargetWrapper wrapper && wrapper.WrappedTarget is DatabaseTarget dbTarget2)
    {
        dbTarget = dbTarget2;
    }

    if (dbTarget != null)
    {
        dbTarget.ConnectionString = Config.GetConnectionString("LogDbContext");
    }
}

builder.Logging.AddNLog(logConfig);

//builder.Logging.AddNLog("_config/NLog.config");

// Add services to the container.

// 注册 Controller 和 JSON
builder
    .Services.AddControllers(options =>
    {
        options.Filters.Add<ApiExceptionFilter>();
        options.Filters.Add<InputTrimFilter>();
        options.Filters.Add<ApiOperateFilter>();
        options.Filters.Add<AuthorizeFilter>();
    })
    .AddJsonOptions(options =>
    {
        //允许对象或数组中 JSON 值的列表末尾多余的逗号
        options.JsonSerializerOptions.AllowTrailingCommas = true;
        // 设置值为null时如何处理
        //options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
        //属性转换JSON命名规则
        options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        //字典转换JSON命名规则
        //options.JsonSerializerOptions.DictionaryKeyPolicy = JsonNamingPolicy.CamelCase;
        //编码规则，UnsafeRelaxedJsonEscaping 不转义
        options.JsonSerializerOptions.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
        //是否格式化JSON格式
        options.JsonSerializerOptions.WriteIndented = true;

        // 反序列化不区分大小写
        options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
        // 允许在反序列化的时候原本应为数字的字符串（带引号的数字）转为数字
        options.JsonSerializerOptions.NumberHandling = JsonNumberHandling.AllowReadingFromString;

        options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;

        options.JsonSerializerOptions.Converters.Add(new NullableConverterFactory());
        options.JsonSerializerOptions.Converters.Add(new EnumConverterFactory());
        options.JsonSerializerOptions.Converters.Add(new DateTimeJsonConverter());
        options.JsonSerializerOptions.Converters.Add(new GuidJsonConverter());
        options.JsonSerializerOptions.Converters.Add(new BooleanJsonConverter());
        options.JsonSerializerOptions.Converters.Add(new UserAuthConverter());
        options.JsonSerializerOptions.Converters.Add(new I18nConverters<I18nText>());

        Config.SetJsonSerializerOptions(options.JsonSerializerOptions);
    });

// 自定义 MvcHttpContext
builder.Services.AddMvcHttpContext();

// 注册 EFCore，Bll
builder.Services.AddBllServics();

// 注册 AutoMapper
builder.Services.AddAutoMapper<ConfigProfile>();

// 注册 User Provider
builder.Services.AddUserProvider<UserProvider>();

// 注册 I18n Provider
//builder.Services.AddSingleton<II18nProvider, I18nProvider>();

// 注册 JWT
builder
    .Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = Config.Jwt.ValidateIssuer,
            ValidateAudience = Config.Jwt.ValidateAudience,
            //是否验证Token有效期，使用当前时间与Token的Claims中的NotBefore和Expires对比
            ValidateLifetime = Config.Jwt.ValidateLifetime,
            ValidateIssuerSigningKey = Config.Jwt.ValidateIssuerSigningKey,
            ValidIssuer = Config.Jwt.Issuer,
            ValidAudience = Config.Jwt.Audience,
            IssuerSigningKey = Config.Jwt.IssuerSigningKey
        };

        if (Config.Jwt.ClockSkew.HasValue)
        {
            //过期偏移量，不设置则默认五分钟
            options.TokenValidationParameters.ClockSkew = Config.Jwt.ClockSkew.Value;
        }

        options.Events = new JwtBearerEvents
        {
            OnChallenge = context =>
            {
                return Task.CompletedTask;
            },
            OnAuthenticationFailed = context =>
            {
                //失效了
                if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))
                {
                    //jwt失效，to do
                    context.Response.Headers.Append("Access-Control-Allow-Origin", "*");
                }
                return Task.CompletedTask;
            },
            OnMessageReceived = context =>
            {
                return Task.CompletedTask;
            },
            //验证成功
            OnTokenValidated = context =>
            {
                return Task.CompletedTask;
            }
        };
    });

// 配置 Quartz 任务
builder.Services.UseQuartzAsync();

// 配置跨域处理，允许所有来源
builder.Services.AddCors(options =>
    options.AddPolicy(
        "cors",
        policy => policy.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod()
    //.AllowCredentials()
    )
);

List<string> swaggerDocs = ["Sys"];

if (Config.UseSwagger)
{
    var baseType = typeof(ControllerBase);
    var types = Assembly.GetExecutingAssembly().GetTypes();

    foreach (var type in types)
    {
        if (!type.IsAbstract && baseType.IsAssignableFrom(type))
        {
            var attr = type.GetAttribute<ApiExplorerSettingsAttribute>() ?? type.GetAttribute<ApiExplorerSettingsAttribute>(true);

            var doc = (attr?.GroupName).IsEmpty() ? "Main" : (attr?.GroupName).Value();

            if (!swaggerDocs.Contains(doc))
            {
                swaggerDocs.Add(doc);
            }
        }
    }

    swaggerDocs =
    [
        .. swaggerDocs
            .OrderBy(p =>
                p == "Sys"
                    ? 0
                    : p == "Main"
                        ? 1
                        : 2
            )
            .ThenBy(p => p)
    ];

    //注册 Swagger
    // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
    builder.Services.AddSwaggerGen(options =>
    {
        // options.SwaggerGeneratorOptions.
        foreach (var doc in swaggerDocs)
        {
            options.SwaggerDoc(doc, new OpenApiInfo { Title = $"{doc} API" });
        }

        options.DocInclusionPredicate(
            (docName, apiDesc) =>
            {
                if (!apiDesc.TryGetMethodInfo(out MethodInfo methodInfo) || methodInfo.ReflectedType?.IsAbstract == true)
                {
                    return false;
                }
                var names = methodInfo.GetPriorityAttributes<ApiExplorerSettingsAttribute>().Select(attr => attr.GroupName).ToList();

                return names.Contains(docName) || names.Any(p => p.IsEmpty()) || (names.Count == 0 && docName == "Main");
            }
        );

        options.OperationFilter<SecurityRequirementsOperationFilter>();

        //给api添加token令牌证书
        options.AddSecurityDefinition(
            "oauth2",
            new OpenApiSecurityScheme
            {
                Description = "JWT授权(数据将在请求头中进行传输) 直接在下框中输入Bearer {token}（注意两者之间是一个空格）",
                Name = "Authorization", //jwt默认的参数名称
                In = ParameterLocation.Header, //jwt默认存放Authorization信息的位置(请求头中)
                Type = SecuritySchemeType.ApiKey,
            }
        );

        // 为 Swagger JSON and UI设置xml文档注释路径
        var basePath = AppContext.BaseDirectory;

        DirectoryInfo baseFolder = new(basePath);

        foreach (FileInfo file in baseFolder.GetFiles("*.xml"))
        {
            options.IncludeXmlComments(file.FullName);
        }
    });
}

var app = builder.Build();

// 注册全局 Host
HostContext.SetHost(app);

//var schedulerFactory = HostContext.GetRequiredService<ISchedulerFactory>();
//var scheduler = schedulerFactory.GetScheduler().Result;

//await scheduler.Start();//开启调度器

// 自定义 MvcHttpContext
app.UseMvcHttpContext();

// 允许所有跨域，cors是在ConfigureServices方法中配置的跨域策略名称,要写在MapControllers之前
app.UseCors("cors");

if (Config.UseSwagger)
{
    //// Enable middleware to serve generated Swagger as a JSON endpoint.
    app.UseSwagger(c =>
    {
        c.OpenApiVersion = OpenApiSpecVersion.OpenApi3_0;
    });

    // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.), specifying the Swagger JSON endpoint.
    app.UseSwaggerUI(c =>
    {
        if (Config.Debug)
        {
            c.DocumentTitle = $"【测试版】{c.DocumentTitle}";
        }

        if (!Config.Title.IsEmpty())
        {
            c.DocumentTitle = $"{c.DocumentTitle} - {Config.Title}";
        }

        foreach (var doc in swaggerDocs)
        {
            c.SwaggerEndpoint($"{doc}/swagger.json", $"{doc} API");
        }
    });
}

app.UseHttpsRedirection();

//启用jwt认证，必须写在 app.UseAuthorization();之前
app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();

app.Run();