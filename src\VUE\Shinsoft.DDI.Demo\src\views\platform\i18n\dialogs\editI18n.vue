<script setup lang="ts">
import { sysSetupApi } from "@/api/sysSetup";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { useEnumStoreHook } from "@/store/modules/enum";
import { ElMessage, FormInstance } from "element-plus";
import { EnumFlagsCheckbox } from "@/components/EnumFlags";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "i18n:edit"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.model?.id ? `${t("operate.edit")}` : `${t("operate.add")}`;
});

/**
 * 当前用户存储
 */
const userStore = useUserStoreHook();
const enumStore = useEnumStoreHook();

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      enumType: 0,
      enumFlags: 0,
      parentId: "",
      valid: true,
      isSys: true
    }
  },
  // 枚举定义
  enums: {
    i18nCategorys: [],
    i18nTypes: [],
    enumFlags: []
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  },
  // 按钮权限
  btn: {
    save: computed(() => {
      return state.model.id
        ? userStore.hasAnyAuth(["Platform:I18n:Manage"])
        : userStore.hasAnyAuth(["Platform:I18n:Manage"]);
    })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  model: cfg.default.model as Record<string, any>
});

/**
 * 验证规则
 */
const rules = {
  form: {
    key: [
      { required: true, message: "Key值不可以为空", trigger: "blur" },
      { max: 200, message: "Key值长度不可超过200", trigger: "blur" }
    ],
    name: [
      { required: true, message: "名称不可以为空", trigger: "blur" },
      { max: 200, message: "名称长度不可超过200", trigger: "blur" }
    ],
    text: [
      { required: true, message: "默认值不可以为空", trigger: "blur" },
      { max: 500, message: "默认值长度不可超过200", trigger: "blur" }
    ],
    remark: [{ max: 500, message: tt("Rule.Position.Remark:Length"), trigger: "blur" }]
  }
};

/**
 * 当前组件ref
 */
const formRef = ref<FormInstance>();

/**
 * 初始化组件（异步）
 */
const init = () => {
  // 防止之前打开的数据残留，因此初始化时赋予model默认值
  initState();

  cfg.loading.form = true;

  const initI18nCategory = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("I18nCategory").then(enumInfos => {
        cfg.enums.enumCategorys = enumInfos;
        resolve();
      });
    });
  };

  const initI18nType = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("I18nType").then(enumInfos => {
        cfg.enums.enumTypes = enumInfos;
        resolve();
      });
    });
  };

  const initI18nFlag = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("I18nFlag").then(enumInfos => {
        cfg.enums.enumFlags = enumInfos;
        resolve();
      });
    });
  };

  const allInits = [initI18nCategory(), initI18nType(), initI18nFlag()];

  return new Promise<void>(() => {
    Promise.all(allInits).then(() => {
      cfg.loading.form = false;
      get();
    });
  });
};

/**
 * 设置model数据
 */
const setModel = (data: any) => {
  state.model = data;
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 初始化state数据
 */
const initState = () => {
  state.model = cfg.default.model;
};

/**
 * 清空mstate数据
 */
const clearState = () => {
  initState();
  formRef.value.clearValidate();
  formRef.value.resetFields();
};

/**
 * 获取model数据
 */
const get = () => {
  if (state.id) {
    cfg.loading.form = true;
    sysSetupApi
      .GetI18n(state.id)
      .then(res => {
        if (res.success) {
          setModel(res.data);
        } else {
          close();
        }
      })
      .finally(() => {
        cfg.loading.form = false;
      });
  }
};

/**
 * 新增数据
 */
const add = () => {
  cfg.loading.btn = true;

  const data = getModel();
  sysSetupApi
    .AddI18n(data, { messageBoxResult: true })
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh(t("operate.message.success"), "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 更新数据
 */
const update = () => {
  cfg.loading.btn = true;

  const data = getModel();

  sysSetupApi
    .UpdateI18n(data, { messageBoxResult: true })
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh(t("operate.message.success"), "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 按钮事件：【保存】
 */
const save = async () => {
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      if (state.model.id) {
        update();
      } else {
        add();
      }
    }
  });
};

/**
 * dialog:显示(父组件调用)
 */
const open = (id?: string, parentId?: string, group?: string, enumCategory?: any) => {
  cfg.dialog.visible = true;
  cfg.default.model.parentId = parentId;
  cfg.default.model.group = group;
  state.model.enumCategory = enumCategory;
  state.id = id;
  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        v-loading="cfg.loading.form"
        :rules="rules.form"
        :model="state.model"
        label-position="right"
        :label-width="formLabelWidth"
        class="el-dialog-form"
      >
        <el-row :gutter="formGutter">
          <el-col :span="formColSpan">
            <el-form-item prop="key" label="Key">
              <el-input v-model="state.model.key" placeholder="Key" maxlength="200" />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="name" label="名称">
              <el-input v-model="state.model.name" placeholder="Name" maxlength="200" />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="group" label="分组">
              {{ state.model.group }}
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="enumFlags" label="标签">
              <enum-flags-checkbox
                v-model="state.model.enumFlags"
                enumType="I18nFlag"
                :enums="cfg.enums.enumFlags"
                border
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="enumCategory" label="分类">
              <el-select v-model="state.model.enumCategory" placeholder="分类">
                <el-option
                  v-for="item in cfg.enums.enumCategorys"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="enumType" label="类型">
              <el-select v-model="state.model.enumType" placeholder="类型">
                <el-option
                  v-for="item in cfg.enums.enumTypes"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="valid" label="有效性">
              <el-switch
                v-model="state.model.valid"
                class="ml-2"
                inline-prompt
                style="--el-switch-on-color: #13ce66"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="isSys" label="是否系统">
              <el-switch
                v-model="state.model.isSys"
                class="ml-2"
                inline-prompt
                style="--el-switch-on-color: #13ce66"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="formGutter">
          <el-col :span="24">
            <el-form-item prop="text" label="默认值">
              <el-input v-model="state.model.text" placeholder="Text" maxlength="500" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="formGutter">
          <el-col :span="24">
            <el-form-item prop="remark" label="备注">
              <el-input v-model="state.model.remark" placeholder="remark" maxlength="500" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.save"
            class="save"
            :round="btnRound"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnSaveIcon)"
            @click="save()"
          >
            {{ t("operate.save") }}
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="css">
.is-not-error > .el-input__wrapper {
  box-shadow: #dcdfe6 !important;
}
</style>
