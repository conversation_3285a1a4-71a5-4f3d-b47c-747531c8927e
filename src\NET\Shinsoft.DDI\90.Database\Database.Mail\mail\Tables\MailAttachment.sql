﻿CREATE TABLE [mail].[MailAttachment]
(
    [ID]					UNIQUEIDENTIFIER        	NOT NULL    CONSTRAINT [DF_MailAttachment_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]             UNIQUEIDENTIFIER        	NOT NULL,
    [MailId]                UNIQUE<PERSON>ENTIFIER        	NOT NULL,
	[FileIndexId]		    UNIQUEIDENTIFIER			NOT NULL,
	[FileSize]			    BIGINT			            NOT NULL,
	[ContentType]		    NVARCHAR(200)		        NOT NULL,
	[FileName]			    NVARCHAR(500)		        NOT NULL,
	[FileExt]			    NVARCHAR(50)		        NOT NULL,
    [Deleted]				BIT					        NOT NULL, 
    [Creator]			    NVARCHAR(50)		        NULL, 
    [CreateTime]			DATETIME			        NULL, 
    [LastEditor]			NVARCHAR(50)		        NULL, 
    [LastEditTime]		    DATETIME			        NULL, 
    CONSTRAINT [PK_MailAttachment] PRIMARY KEY CLUSTERED ([ID]) , 
    CONSTRAINT [FK_MailAttachment_Mail_00_Mail_Attachments] FOREIGN KEY ([MailId]) REFERENCES [mail].[Mail] ([ID]),
)
