﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.ServiceModel.Channels;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class BasicMasterDataBll : BaseCompanyBll
    {
        #region Constructs

        public BasicMasterDataBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        public BasicMasterDataBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        public BasicMasterDataBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        public BasicMasterDataBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs

        #region Calendar 日历

        public void InitCalendar(DateTime date)
        {
            var cc = this.CompanyCache;
            date = date.Date;

            var dbEntity = this.GetEntity<Calendar>(p => p.Date == date);

            if (dbEntity == null)
            {
                var min = cc.Calendars.OrderBy(p => p.Date).FirstOrDefault();
                var max = cc.Calendars.OrderByDescending(p => p.Date).FirstOrDefault();

                var startYear = date.Year;
                var endYear = date.Year;

                if (min?.Year > endYear)
                {
                    endYear = min.Year - 1;
                }

                if (max?.Year < startYear)
                {
                    startYear = max.Year + 1;
                }

                var entities = new List<Calendar>();

                for (var year = startYear; year <= endYear; year++)
                {
                    var start = new DateTime(year, 1, 1);
                    var end = new DateTime(year, 12, 31);

                    var companyId = this.CurrentCompanyId;

                    var day = start;

                    var weekOfMonth = 1;
                    var weekOfYear = 1;

                    while (day <= end)
                    {
                        var dayOfWeek = (int)day.DayOfWeek;
                        var dayOfYear = (int)day.DayOfYear;

                        if (day != start)
                        {
                            if (day.Day == 1)
                            {
                                weekOfMonth = 1;
                            }
                            else if (dayOfWeek == Config.Calendar.StartDayOfWeek)
                            {
                                weekOfMonth += 1;
                            }

                            if (dayOfWeek == Config.Calendar.StartDayOfWeek)
                            {
                                weekOfYear += 1;
                            }
                        }

                        var entity = new Calendar
                        {
                            ID = CombGuid.NewGuid(),
                            CompanyId = companyId,
                            Date = day,
                            Year = year,
                            Month = day.Month,
                            Day = day.Day,
                            DayOfWeek = dayOfWeek,
                            DayOfYear = dayOfYear,
                            WeekOfMonth = weekOfMonth,
                            WeekOfYear = weekOfYear
                        };

                        entities.Add(entity);
                        day = day.AddDays(1);
                    }
                }

                this.AddRange(entities);

                //   this.SqlBulkInsert(entities);
            }
        }

        #endregion Calendar 日历

        #region SubCompany 分公司

        protected BizResult<SubCompany> CheckSubCompany(SubCompany entity, BizResult<SubCompany>? result = null)
        {
            result ??= new BizResult<SubCompany>();

            if (entity.Code.IsEmpty())
            {
                result.Error(I18ns.Rule.SubCompany.Code_Required);
            }

            if (entity.Name.IsEmpty())
            {
                result.Error(I18ns.Rule.SubCompany.Name_Required);
            }

            if (result.Success)
            {
                var exist = this.GetEntities<SubCompany>(p => p.Code == entity.Code || p.Name == entity.Name);

                if (exist.Any(p => p.ID != entity.ID && p.Code == entity.Code))
                {
                    result.Error(I18ns.Message.SubCompany.HasSameCodeSubCompany);
                }

                if (exist.Any(p => p.ID != entity.ID && p.Name == entity.Name))
                {
                    result.Error(I18ns.Message.SubCompany.HasSameNameSubCompany);
                }
            }

            return result;
        }

        public BizResult<SubCompany> AddSubCompany(SubCompany entity)
        {
            var result = this.CheckSubCompany(entity);

            if (result.Success)
            {
                entity = this.Add(entity, false);

                if (result.Success)
                {
                    this.SaveChanges();

                    result.Data = entity;

                    this.CompanyCache.RemoveCache<SubCompany>();
                }
            }

            return result;
        }

        public BizResult<SubCompany> UpdateSubCompany(SubCompany entity)
        {
            var result = new BizResult<SubCompany>();

            var id = entity.ID;
            var dbEntity = this.Get<SubCompany>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, CostCenter.I18ns._Entity);
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    result = this.CheckSubCompany(entity, result);

                    if (result.Success)
                    {
                        this.CompanyCache.RemoveCache<SubCompany>();
                    }
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.RollbackChanges();
                }
            }

            return result;
        }

        public BizResult DeleteSubCompany(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<SubCompany>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, CostCenter.I18ns._Entity);
            }
            else
            {
                this.Delete(dbEntity);
                this.CompanyCache.RemoveCache<SubCompany>();
            }

            return result;
        }

        public BizResult<SubCompany> SetSubCompanyValid(SubCompany entity)
        {
            var result = new BizResult<SubCompany>();

            var dbEntity = this.Get<SubCompany>(entity.ID);

            if (dbEntity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, CostCenter.I18ns._Entity);
            }
            else
            {
                dbEntity.Valid = entity.Valid;

                this.Update(dbEntity);
            }

            return result;
        }

        #endregion SubCompany 分公司

        #region Department

        public BizResult<Department> AddDepartment(Department entity)
        {
            var result = new BizResult<Department>();

            if (entity.Code.IsEmpty())
            {
                result.Error("请输入编码");
            }

            if (entity.Name.IsEmpty())
            {
                result.Error("请输入名称");
            }

            if (result.Success)
            {
                var exist = this.GetEntity<Department>(p => p.Code == entity.Code);

                if (exist != null)
                {
                    result.Error("已存在相同编码的部门");
                }

                if (entity.ParentId.HasValue)
                {
                    var parent = this.Get<Department>(entity.ParentId.Value);

                    if (parent == null)
                    {
                        result.Error("父部门不存在");
                    }
                    else
                    {
                        entity.Rank = parent.Rank + 1;
                    }
                }
                else
                {
                    entity.Rank = 1;
                }
            }

            if (result.Success)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity, false);

                if (entity.DefaultCostCenterId.HasValue)
                {
                    var departmentCostCenter = new DepartmentCostCenter
                    {
                        DepartmentId = entity.ID,
                        CostCenterId = entity.DefaultCostCenterId.Value,
                    };

                    var defaultResult = this.SaveDefaultDepartmentCostCenter(departmentCostCenter, false);

                    if (!defaultResult.Success)
                    {
                        result.CopyFrom(defaultResult);
                    }
                }

                if (result.Success)
                {
                    using (var trans = new TransactionScope())
                    {
                        this.SaveChanges();

                        entity.CalcUidPath();

                        entity = this.Update(entity);

                        trans.Complete();

                        result.Data = entity;
                    }

                    this.CompanyCache.RemoveCache<Department>();
                    this.CompanyCache.RemoveCache<VwOrganization>();
                }
            }

            return result;
        }

        public BizResult<Department> UpdateDepartment(Department entity)
        {
            var result = new BizResult<Department>();

            var id = entity.ID;
            var dbEntity = this.Get<Department>(id);

            if (dbEntity == null)
            {
                result.Error("部门不存在");
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.Code.IsEmpty())
                    {
                        result.Error("请输入名称");
                    }

                    if (entity.Name.IsEmpty())
                    {
                        result.Error("请输入名称");
                    }

                    if (result.Success)
                    {
                        var exist = this.GetEntity<Department>(p => p.ID != entity.ID && p.Code == entity.Code);

                        if (exist != null)
                        {
                            result.Error("已存在相同名称的部门");
                        }

                        if (entity.ParentId.HasValue)
                        {
                            var parent = this.Get<Department>(entity.ParentId.Value);

                            if (parent == null)
                            {
                                result.Error("父部门不存在");
                            }
                            else
                            {
                                entity.Rank = parent.Rank + 1;

                                if (parent.UidPath.Contains($"|{entity.Uid}|"))
                                {
                                    result.Error("不可以将部门移动至当前下级部门");
                                }
                            }
                        }
                        else
                        {
                            entity.Rank = 1;
                        }
                    }

                    if (result.Success)
                    {
                        if (entity.DefaultCostCenterId.HasValue)
                        {
                            var departmentCostCenter = new DepartmentCostCenter
                            {
                                DepartmentId = entity.ID,
                                CostCenterId = entity.DefaultCostCenterId.Value,
                            };

                            var defaultResult = this.SaveDefaultDepartmentCostCenter(departmentCostCenter, false);

                            if (!defaultResult.Success)
                            {
                                result.CopyFrom(defaultResult);
                            }
                        }
                    }

                    if (result.Success)
                    {
                        var descendants = this.GetEntities<Department>(p => p.UidPath.Contains($"|{entity.Uid}|"));

                        entity.CalcUidPath(descendants);

                        this.CompanyCache.RemoveCache<Department>();
                    }
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                }
            }

            return result;
        }

        public BizResult DeleteDepartment(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<Department>(id);

            if (dbEntity == null)
            {
                result.Error("部门不存在");
            }
            else
            {
                var exist = this.GetEntities<Department>(p => p.ParentId == id);

                if (exist != null && exist.Any())
                {
                    result.Error("存在下级部门不可直接删除");
                }
                if (result.Success)
                {
                    var existDept = this.GetEntities<Station>(p => p.DepartmentId == id);
                    if (existDept != null && existDept.Any())
                    {
                        result.Error("该部门下存在岗位不可直接删除");
                    }

                    if (result.Success)
                    {
                        this.Delete(dbEntity);

                        this.CompanyCache.RemoveCache<Department>();
                        this.CompanyCache.RemoveCache<VwOrganization>();
                    }
                }
            }

            return result;
        }

        public BizResult<Department> SetDepartmentValid(Department entity)
        {
            var result = new BizResult<Department>();

            var dbEntity = this.Get<Department>(entity.ID);

            if (dbEntity == null)
            {
                result.Error("部门不存在");
            }
            else
            {
                dbEntity.Valid = entity.Valid;

                this.Update(dbEntity);
            }

            return result;
        }

        #endregion Department

        #region CostCenter

        public BizResult<CostCenter> AddCostCenter(CostCenter entity)
        {
            var result = new BizResult<CostCenter>();

            if (entity.Code.IsEmpty())
            {
                result.Error(I18ns.Rule.CostCenter.Code_Required);
            }

            if (entity.Name.IsEmpty())
            {
                result.Error(I18ns.Rule.CostCenter.Name_Required);
            }

            if (result.Success)
            {
                var exist = this.GetEntity<CostCenter>(p => p.Code == entity.Code);

                if (exist != null)
                {
                    result.Error(I18ns.Message.CostCenter.HasSameCodeCostCenter);
                }

                if (entity.ParentId.HasValue)
                {
                    var parent = this.Get<CostCenter>(entity.ParentId.Value);

                    if (parent == null)
                    {
                        result.Error(I18ns.Message.CostCenter.HasParentCostCenterNoExist);
                    }
                    else
                    {
                        entity.Rank = parent.Rank + 1;
                    }
                }
                else
                {
                    entity.Rank = 1;
                }
            }

            if (result.Success)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity, false);

                if (result.Success)
                {
                    using (var trans = new TransactionScope())
                    {
                        this.SaveChanges();

                        entity.CalcUidPath();

                        entity = this.Update(entity);

                        trans.Complete();

                        result.Data = entity;
                    }
                    this.CompanyCache.RemoveCache<CostCenter>();
                }
            }

            return result;
        }

        public BizResult<CostCenter> UpdateCostCenter(CostCenter entity)
        {
            var result = new BizResult<CostCenter>();

            var id = entity.ID;
            var dbEntity = this.Get<CostCenter>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist,CostCenter.I18ns._Entity);
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.Code.IsEmpty())
                    {
                        result.Error(I18ns.Rule.CostCenter.Code_Required);
                    }

                    if (entity.Name.IsEmpty())
                    {
                        result.Error(I18ns.Rule.CostCenter.Name_Required);
                    }

                    if (result.Success)
                    {
                        var exist = this.GetEntity<CostCenter>(p => p.ID != entity.ID && p.Code == entity.Code);

                        if (exist != null)
                        {
                            result.Error(I18ns.Message.CostCenter.HasSameNameCostCenter);
                        }

                        if (entity.ParentId.HasValue)
                        {
                            var parent = this.Get<CostCenter>(entity.ParentId.Value);

                            if (parent == null)
                            {
                                result.Error(I18ns.Message.CostCenter.HasParentCostCenterNoExist);
                            }
                            else
                            {
                                entity.Rank = parent.Rank + 1;

                                if (parent.UidPath.Contains($"|{entity.Uid}|"))
                                {
                                    result.Error(I18ns.Message.CostCenter.HasMoveCostCenterToChildCostCenter);
                                }
                            }
                        }
                        else
                        {
                            entity.Rank = 1;
                            entity.ParentId = null;
                        }
                    }

                    if (result.Success)
                    {
                        var descendants = this.GetEntities<CostCenter>(p => p.UidPath.Contains($"|{entity.Uid}|"));

                        entity.CalcUidPath(descendants);

                        this.CompanyCache.RemoveCache<CostCenter>();
                    }
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                }
            }

            return result;
        }

        public BizResult DeleteCostCenter(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<CostCenter>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist,CostCenter.I18ns._Entity);
            }
            else
            {
                if (result.Success)
                {
                    var exist = this.GetEntities<CostCenter>(a => a.ParentId.HasValue && a.ParentId.Value == id);
                    if(exist.Count != 0)
                    {
                        result.Error(I18ns.Message.CostCenter.HasChildCostCenterNoDelete);
                    }
                    else
                    {
                        this.Delete(dbEntity);
                        this.CompanyCache.RemoveCache<CostCenter>();
                    }
                }
            }

            return result;
        }

        public BizResult<CostCenter> SetCostCenterValid(CostCenter entity)
        {
            var result = new BizResult<CostCenter>();

            var dbEntity = this.Get<CostCenter>(entity.ID);

            if (dbEntity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist,CostCenter.I18ns._Entity);
            }
            else
            {
                dbEntity.Valid = dbEntity.Valid ? false : true;

                this.Update(dbEntity);
                this.CompanyCache.RemoveCache<CostCenter>();
            }

            return result;
        }

        #endregion CostCenter

        #region DepartmentCostCenter

        protected BizResult<DepartmentCostCenter> SaveDefaultDepartmentCostCenter(DepartmentCostCenter entity, bool save = true)
        {
            var result = new BizResult<DepartmentCostCenter>();

            if (entity.DepartmentId.IsEmpty())
            {
                result.Error("请选择部门");
            }

            if (entity.CostCenterId.IsEmpty())
            {
                result.Error("请选择成本中心");
            }

            if (result.Success)
            {
                entity.IsDefault = true;

                var dbEntities = this.GetEntities<DepartmentCostCenter>(p =>
                    p.DepartmentId == entity.DepartmentId && (p.CostCenterId == entity.CostCenterId || p.IsDefault)
                );

                var dbEntity = dbEntities.Where(p => p.CostCenterId == entity.CostCenterId).FirstOrDefault();

                if (dbEntity == null)
                {
                    if (entity.ID.IsEmpty())
                    {
                        entity.ID = CombGuid.NewGuid();
                    }
                    entity = this.Add(entity, false);
                }
                else
                {
                    entity.ID = dbEntity.ID;
                    entity = this.Update(entity, false);
                }

                var orgDefaultEntities = dbEntities.Where(p => p.ID != entity.ID);

                foreach (var orgDefaultEntity in orgDefaultEntities)
                {
                    orgDefaultEntity.IsDefault = false;

                    this.Update(orgDefaultEntity, false);
                }

                this.SaveChanges(save);

                result.Data = dbEntity;
            }

            return result;
        }

        public BizResult<DepartmentCostCenter> AddDepartmentCostCenter(DepartmentCostCenter entity)
        {
            var result = new BizResult<DepartmentCostCenter>();

            if (entity.DepartmentId.IsEmpty())
            {
                result.Error("请选择部门");
            }

            if (entity.CostCenterId.IsEmpty())
            {
                result.Error("请选择成本中心");
            }

            if (result.Success)
            {
                var exist = this.GetEntity<DepartmentCostCenter>(p => p.DepartmentId == entity.DepartmentId && p.CostCenterId == entity.CostCenterId);

                if (exist != null)
                {
                    result.Error("该部门已经包含了此成本中心");
                }
            }

            if (result.Success)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                if (entity.IsDefault)
                {
                    var orgDefaultEntities = this.GetEntities<DepartmentCostCenter>(p => p.DepartmentId == entity.DepartmentId && p.IsDefault);

                    foreach (var orgDefaultEntity in orgDefaultEntities)
                    {
                        orgDefaultEntity.IsDefault = false;

                        this.Update(orgDefaultEntity, false);
                    }

                    entity = this.Add(entity, false);

                    var department = this.Get<Department>(entity.DepartmentId);

                    if (department != null)
                    {
                        department.DefaultCostCenterId = entity.CostCenterId;
                        ;

                        this.Update(department, false);
                    }

                    this.SaveChanges();
                }
                else
                {
                    entity = this.Add(entity);
                }

                this.CompanyCache.RemoveCache<Department>();

                result.Data = entity;
            }

            return result;
        }

        public BizResult<List<DepartmentCostCenter>> AddDepartmentCostCenters(List<DepartmentCostCenter> entities)
        {
            var result = new BizResult<List<DepartmentCostCenter>>();

            if (result.Success)
            {
                foreach (var entity in entities)
                {
                    var exist = this.GetEntity<DepartmentCostCenter>(p => p.DepartmentId == entity.DepartmentId && p.CostCenterId == entity.CostCenterId);

                    if (exist != null)
                    {
                        result.Error("该部门已经包含了此成本中心");
                    }
                    entity.ID = CombGuid.NewGuid();
                }
            }

            if (result.Success)
            {
                var resultEntities = this.AddRange(entities);

                result.Data = resultEntities;
            }

            return result;
        }

        public BizResult RemoveDepartmentCostCenter(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<DepartmentCostCenter>(DepartmentCostCenter.Foreigns.Department, id);

            if (dbEntity == null)
            {
                result.Error("该部门下不存在此成本中心");
            }
            else
            {
                var department = dbEntity.Department;

                if (department.DefaultCostCenterId == dbEntity.CostCenterId)
                {
                    department.DefaultCostCenterId = null;

                    this.Update(department, false);
                }

                this.Remove(dbEntity, false);

                this.SaveChanges();
            }

            return result;
        }

        public BizResult<DepartmentCostCenter> SetDepartmentCostCenterDefault(DepartmentCostCenter entity)
        {
            var result = new BizResult<DepartmentCostCenter>();

            var dbEntity = this.Get<DepartmentCostCenter>(DepartmentCostCenter.Foreigns.Department, entity.ID);

            if (dbEntity == null)
            {
                result.Error("该部门下不存在此成本中心");
            }
            else
            {
                if (dbEntity.IsDefault != entity.IsDefault)
                {
                    var department = dbEntity.Department;

                    if (dbEntity.IsDefault)
                    {
                        department.DefaultCostCenterId = null;
                    }
                    else
                    {
                        var orgDefaultEntities = this.GetEntities<DepartmentCostCenter>(p => p.DepartmentId == entity.DepartmentId && p.IsDefault);

                        foreach (var orgDefaultEntity in orgDefaultEntities)
                        {
                            orgDefaultEntity.IsDefault = false;

                            this.Update(orgDefaultEntity, false);
                        }

                        department.DefaultCostCenterId = dbEntity.CostCenterId;
                        dbEntity.IsDefault = true;
                    }

                    this.Update(dbEntity, false);
                    this.Update(department, false);

                    this.SaveChanges();
                }

                result.Data = dbEntity;
            }

            return result;
        }

        #endregion DepartmentCostCenter

        #region Position

        /// <summary>
        /// 新增职位
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<Position> AddPosition(Position entity)
        {
            var result = new BizResult<Position>();

            if (entity.Code.IsEmpty())
            {
                result.Error(I18ns.Rule.Position.Code_Required);
            }

            if (entity.Name.IsEmpty())
            {
                result.Error(I18ns.Rule.Position.Name_Required);
            }

            if (result.Success)
            {
                var exist = this.GetEntity<Position>(p => p.Code == entity.Code);

                if (exist != null)
                {
                    result.Error(I18ns.Message.Position.HasSamePosition);
                }
            }

            if (result.Success)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity, false);

                if (result.Success)
                {
                    using (var trans = new TransactionScope())
                    {
                        this.SaveChanges();

                        entity.CalcUidPath();

                        entity = this.Update(entity);

                        trans.Complete();

                        result.Data = entity;
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 编辑职位
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<Position> UpdatePosition(Position entity)
        {
            var result = new BizResult<Position>();

            var id = entity.ID;
            var dbEntity = this.Get<Position>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, Position.I18ns._Entity);
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.Code.IsEmpty())
                    {
                        result.Error(I18ns.Rule.Position.Code_Required);
                    }

                    if (entity.Name.IsEmpty())
                    {
                        result.Error(I18ns.Rule.Position.Name_Required);
                    }

                    if (result.Success)
                    {
                        var exist = this.GetEntity<Position>(p => p.ID != entity.ID && p.Code == entity.Code);

                        if (exist != null)
                        {
                            result.Error(I18ns.Message.Position.HasSamePosition);
                        }
                    }

                    if (result.Success)
                    {
                        var descendants = this.GetEntities<Position>(p => p.UidPath.Contains($"|{entity.Uid}|"));

                        entity.CalcUidPath(descendants);
                    }
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                }
            }

            return result;
        }

        /// <summary>
        /// 删除职位
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public BizResult DeletePosition(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<Position>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, Position.I18ns._Entity);
            }
            else
            {
                if (result.Success)
                {
                    var exist = this.GetEntities<Position>(a => a.ParentId.HasValue && a.ParentId.Value == id);

                    if (exist.Count != 0)
                    {
                        result.Error(I18ns.Message.Position.HasChildPosition);
                    }

                    if (result.Success)
                    {
                        this.Delete(dbEntity);
                    }
                }
            }

            return result;
        }

        #endregion Position

        #region EmployeeStation

        /// <summary>
        /// 删除人员挂岗信息
        /// </summary>
        /// <param name="employeeId"></param>
        /// <param name="stationId"></param>
        /// <returns></returns>
        public BizResult DeleteEmployeeStation(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<EmployeeStation>(id);

            if (dbEntity == null)
            {
                result.Error("人员挂岗信息不存在");
            }
            else
            {
                if (result.Success)
                {
                    this.Remove(dbEntity);
                }
            }

            return result;
        }

        /// <summary>
        /// 人员挂岗
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public BizResult AddEmployeeStation(List<EmployeeStation> entities)
        {
            var result = new BizResult();

            var employeeIds = entities.Select(a => a.EmployeeId);

            //获取当前、上级以及子级部门ID；
            var topUid = this.Get<Station>(entities.First().StationId)?.Department.UidPath.Split('|')[1];
            var topDepartmentUidPath = $"|{topUid}|";
            var sameTopDepartmentIds = this.GetEntities<Department>(a => a.UidPath.Contains(topDepartmentUidPath)).Select(g => g.ID);

            var employeeStations = this.GetEntities<EmployeeStation>(a => a.CompanyId == this.CurrentCompanyId && sameTopDepartmentIds.Contains(a.Station.DepartmentId) && employeeIds.Contains(a.EmployeeId) && (!a.EndDate.HasValue || a.EndDate > SysDateTime.Today));

            if (employeeStations.Any())
            {
                var repeatNames = employeeStations.Select(a => a.Employee.DisplayName);

                result.Error($"{string.Join(',', repeatNames)}已经在同部门挂岗");
            }

            if (result.Success)
            {
                foreach (var entity in entities)
                {
                    entity.ID = CombGuid.NewGuid();
                    entity.StartDate = SysDateTime.Today;
                }

                this.AddRange(entities);
            }
            return result;
        }

        /// <summary>
        /// 编辑人员挂岗信息
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<EmployeeStation> UpdateEmployeeStation(EmployeeStation entity)
        {
            var result = new BizResult<EmployeeStation>();

            var id = entity.ID;
            var dbEntity = this.Get<EmployeeStation>(id);

            if (dbEntity == null)
            {
                result.Error("人员挂岗不存在");
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (!entity.StartDate.IsEmpty() && !entity.EndDate.IsEmpty() && entity.StartDate > entity.EndDate)
                    {
                        result.Error("人员挂岗开始日期不能小于截止日期");
                    }

                    if (result.Success)
                    {
                        this.SaveChanges();
                        result.Data = entity;
                    }
                    else
                    {
                        this.Detach(entity);
                    }
                }
                
            }

            return result;
        }

        #endregion

        #region Station

        /// <summary>
        /// 新增岗位
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<Station> AddStation(Station entity)
        {
            var result = new BizResult<Station>();

            if (entity.Name.IsEmpty())
            {
                result.Error("请输入名称");
            }

            if (entity.PositionId.IsEmpty())
            {
                result.Error("请选择职位");
            }

            if (entity.Remark.IsEmpty())
            {
                result.Error("请输入备注");
            }

            if (result.Success)
            {
                var exist = this.GetEntity<Station>(p => p.Name == entity.Name);

                if (exist != null)
                {
                    result.Error("已存在相同名称的岗位");
                }
            }

            if (result.Success)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity, false);

                if (result.Success)
                {
                    using (var trans = new TransactionScope())
                    {
                        this.SaveChanges();

                        entity.CalcUidPath();

                        entity = this.Update(entity);

                        trans.Complete();

                        result.Data = entity;
                    }
                    this.CompanyCache.RemoveCache<Station>();
                    this.CompanyCache.RemoveCache<VwOrganization>();
                }
            }

            return result;
        }

        /// <summary>
        /// 编辑岗位
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public BizResult<Station> UpdateStation(Station entity)
        {
            var result = new BizResult<Station>();

            var id = entity.ID;
            var dbEntity = this.Get<Station>(id);

            if (dbEntity == null)
            {
                result.Error("岗位不存在");
            }
            else
            {
                if (this.Update(ref entity, false))
                {
                    if (entity.Name.IsEmpty())
                    {
                        result.Error("请输入名称");
                    }

                    if (result.Success)
                    {
                        var exist = this.GetEntity<Station>(p => p.ID != entity.ID && p.Name == entity.Name);

                        if (exist != null)
                        {
                            result.Error("已存在相同名称的岗位");
                        }
                    }

                    if (result.Success)
                    {
                        var descendants = this.GetEntities<Station>(p => p.UidPath.Contains($"|{entity.Uid}|"));

                        entity.CalcUidPath(descendants);
                    }
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    result.Data = entity;

                    this.CompanyCache.RemoveCache<Station>();
                    this.CompanyCache.RemoveCache<VwOrganization>();
                }
                else
                {
                    this.Detach(entity);
                }
            }

            return result;
        }

        /// <summary>
        /// 删除岗位
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public BizResult DeleteStation(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<Station>(id);

            if (dbEntity == null)
            {
                result.Error("岗位不存在");
            }
            else
            {
                if (result.Success)
                {
                    var exist = this.GetEntities<Station>(a => a.ParentId.HasValue && a.ParentId.Value == id);

                    if (exist.Count != 0)
                    {
                        result.Error("存在下级岗位不可以直接删除");
                    }

                    if (result.Success)
                    {
                        var existEmployeeStation = this.GetEntities<EmployeeStation>(a => a.StationId == id && (!a.EndDate.HasValue || a.EndDate > SysDateTime.Today));

                        if (existEmployeeStation.Count != 0)
                        {
                            result.Error("该岗位有挂岗人员不可以直接删除");
                        }

                        if (result.Success)
                        {
                            this.Delete(dbEntity);

                            this.CompanyCache.RemoveCache<Station>();
                            this.CompanyCache.RemoveCache<VwOrganization>();
                        }
                    }
                }
            }

            return result;
        }

        #endregion
    }
}
