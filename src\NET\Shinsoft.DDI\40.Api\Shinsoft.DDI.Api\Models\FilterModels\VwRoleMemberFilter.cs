﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class VwRoleMemberFilter
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        [Description("角色ID")]
        [Required]
        [DynamicQueryColumn(typeof(VwRoleMember), VwRoleMember.Columns.RoleId, Operation = Operation.Equal)]
        public virtual Guid RoleId { get; set; }

        /// <summary>
        /// 成员类型
        /// </summary>
        [Description("成员类型")]
        [DynamicQueryColumn(typeof(VwRoleMember), VwRoleMember.Columns.EnumType, Operation = Operation.In)]
        public virtual List<RoleMemberType>? EnumTypes { get; set; }

        /// <summary>
        /// 成员名
        /// </summary>
        [Description("成员名")]
        [DynamicQueryColumn(typeof(VwRoleMember), VwRoleMember.Columns.MemberName, Operation = Operation.StringIntelligence)]
        public virtual string? MemberName { get; set; }
    }
}