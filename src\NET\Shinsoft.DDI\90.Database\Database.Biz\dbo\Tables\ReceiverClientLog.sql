﻿CREATE TABLE [dbo].[ReceiverClientLog] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_ReceiverClientLog_ID]  DEFAULT (NEWSEQUENTIALID()),
    [ReceiverCode]              NVARCHAR(50)                NOT NULL,
    [ReceiverName]              NVARCHAR(500)               NOT NULL,
    [LogTime]                   DATETIME                    NOT NULL,
    [Message]                   NVARCHAR(MAX)               NULL,
    [Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL, 
    [CreateTime]                DATETIME                    NULL, 
    [LastEditor]                NVARCHAR(50)                NULL, 
    [LastEditTime]              DATETIME                    NULL, 
    CONSTRAINT [PK_ReceiverClientLog] PRIMARY KEY CLUSTERED ([ID] ASC)
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'客户端日志',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClientLog',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClientLog',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'客户编码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClientLog',
    @level2type = N'COLUMN',
    @level2name = N'ReceiverCode'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'客户名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClientLog',
    @level2type = N'COLUMN',
    @level2name = N'ReceiverName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'日志时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClientLog',
    @level2type = N'COLUMN',
    @level2name = N'LogTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'日志内容',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClientLog',
    @level2type = N'COLUMN',
    @level2name = N'Message'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClientLog',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClientLog',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClientLog',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClientLog',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClientLog',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'
