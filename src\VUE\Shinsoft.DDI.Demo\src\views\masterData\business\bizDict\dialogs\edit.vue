<script setup lang="ts">
import { useUserStoreHook } from "@/store/modules/user";
import { bizMasterDataApi } from "@/api/bizMasterData";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElMessage } from "element-plus";
import { getColumnOrder, getDefaultOrder } from "@/utils/table";
import Add from "./add.vue";
import Edit from "./editdict.vue";
import View from "./view.vue";
import { allowEdit } from "@/utils/auth";

defineOptions({
  name: "bizDict:edit"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
defineProps({
  // 标题
  editTitle: {
    type: String,
    default: ""
  },
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "150px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.name;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    datas: [],
    total: 0,
    model: {
      enumEditFlags: -1
    }
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  filter: {
    gutter: 3,
    span: 6
  },
  list: {
    height: 300,
    defaultSort: { prop: "name", order: "ascending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.edit ? 45 : 0))
    }
  }, // loading：控制变量
  loading: {
    filter: false,
    list: false,
    btn: false
  },
  // 按钮权限
  btn: {
    edit: computed(() => {
      return userStore.hasAnyAuth(["BizDict:Manage", "BizDict:Manage:Edit"]);
    }),
    delete: computed(() => {
      return userStore.hasAnyAuth(["BizDict:Manage", "BizDict:Manage:Delete"]);
    }),
    add: computed(() => {
      return userStore.hasAnyAuth(["BizDict:Manage", "BizDict:Manage:Add"]);
    })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  name: "",
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: [],
  model: cfg.default.model as Record<string, any>
});

/**
 * 当前用户存储
 */
const userStore = useUserStoreHook();

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string, name: string) => {
  cfg.dialog.visible = true;
  state.id = id;
  state.name = name;
  filter.parentId = id;
  init();
};

/**
 * 当前组件ref
 */
const editRef = ref();
const listRef = ref();
const listContainerRef = ref();
const filterRef = ref();
const addRef = ref();
const viewRef = ref();

/**
 * 初始化组件
 */
const init = () => {
  initFilter();
  query();
  get();
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  //cfg.loading.filter = true;
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  bizMasterDataApi
    .QueryBizDict(filter)
    .then(res => {
      state.datas = res.datas;
      state.total = res.total;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 设置model数据
 */
const setModel = (data: any) => {
  state.model = data;
};

/**
 * 获取model数据
 */
const get = () => {
  if (state.id) {
    cfg.loading.form = true;
    bizMasterDataApi
      .GetBizDict(state.id)
      .then(res => {
        if (res.success) {
          setModel(res.data);
        } else {
          close();
        }
      })
      .finally(() => {
        cfg.loading.form = false;
      });
  }
};

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  parentId: "",
  pageSize: 5
});

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

/**
 * 添加：点击列表中【添加】按钮事件
 */
const addRow = () => {
  addRef.value?.open(state.id, state.name);
};

/**
 * 序号
 */
const indexMethod = index => {
  return (filter.pageIndex - 1) * filter.pageSize + index + 1;
};

/**
 * 查看：点击列表中【查看】按钮事件
 */
const viewRow = (row: any) => {
  viewRef.value?.open(row.id);
};

/**
 * 编辑：点击列表中【编辑】按钮事件
 */
const editRow = (row: any) => {
  editRef.value?.open(row.id, row.name);
};

/**
 * 删除：点击列表中【删除】按钮事件
 */
const deleteRow = (row: any) => {
  viewRef.value?.del(row);
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 初始化state
 */
const initState = () => {
  state.datas = cfg.default.datas;
  state.total = cfg.default.total;
};

/**
 * 清除state数据
 */
const clearState = () => {
  initState();
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <div class="filter-container">
        <el-form
          ref="filterRef"
          v-loading="cfg.loading.filter"
          class="filter-form"
          :rules="rules.filter"
          :model="filter"
        >
          <el-row :gutter="cfg.filter.gutter" type="flex">
            <el-col :span="cfg.filter.span">
              <el-input
                v-model="filter.keywords"
                clearable
                placeholder="关键字(编码、名称)"
                class="filter-item"
                @keyup.enter="query"
              />
            </el-col>
            <el-col :span="cfg.filter.span">
              <el-button
                class="query"
                :loading="cfg.loading.list"
                :icon="useRenderIcon('ep:search')"
                @click="query"
              >
                查询
              </el-button>
            </el-col>
            <el-col :span="24" class="buttonbar">
              <el-button
                v-if="state.model.enumEditFlags == 1024"
                class="new"
                :icon="useRenderIcon('ep:document-add')"
                @click="addRow"
              >
                新建
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div ref="listContainerRef" class="list-container">
        <el-table
          ref="listRef"
          v-loading="cfg.loading.list"
          :data="state.datas"
          row-key="id"
          stripe
          border
          class-name="list"
          style="width: 100%"
          :height="cfg.list.height"
          :default-sort="cfg.list.defaultSort"
          @sort-change="sortChange"
        >
          <template #empty>
            <NoDatas />
          </template>
          <el-table-column
            fixed
            label="序号"
            :index="indexMethod"
            type="index"
            width="70"
            align="center"
          />
          <el-table-column fixed sortable="custom" prop="name" label="名称" width="160" />
          <el-table-column fixed sortable="custom" prop="shortName" label="简称" width="170" />
          <el-table-column fixed sortable="custom" prop="code" label="编号" width="170" />
          <el-table-column fixed sortable="custom" prop="ordinal" label="顺序" width="170" />
          <el-table-column fixed sortable="custom" prop="remark" label="备注" width="170" />
          <el-table-column fixed="right" label="操作" class-name="operate" align="center">
            <template #default="{ row }">
              <el-button
                class="view"
                size="small"
                :circle="true"
                title="查看"
                :icon="useRenderIcon('ep:document')"
                @click="viewRow(row)"
              />
              <el-button
                v-if="cfg.btn.edit && allowEdit(row, 2 | 4 | 16 | 64)"
                class="edit"
                size="small"
                :circle="true"
                title="编辑"
                :icon="useRenderIcon('ep:edit')"
                @click="editRow(row)"
              />
              <el-button
                v-if="cfg.btn.delete && allowEdit(row, 1)"
                class="delete"
                size="small"
                :circle="true"
                title="删除"
                :icon="useRenderIcon('ep:delete')"
                @click="deleteRow(row)"
              />
            </template>
          </el-table-column>
        </el-table>
        <Pagination v-model:filter="filter" :total="state.total" @change="getList" />
      </div>
      <Add ref="addRef" @refresh="getList" />
      <Edit ref="editRef" @refresh="getList" />
      <View ref="viewRef" @refresh="getList" />
      <template #footer>
        <div>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
