﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class EmployeeDelegateMeta
    {
        /// <summary>
        /// 授权人
        /// </summary>
        [MapFromProperty(typeof(EmployeeDelegate), EmployeeDelegate.Foreigns.Employee, Employee.Columns.DisplayName)]
        public virtual string? EmployeeName { get; set; }

        /// <summary>
        /// 代理人
        /// </summary>
        [MapFromProperty(typeof(EmployeeDelegate), EmployeeDelegate.Foreigns.Agent, Employee.Columns.DisplayName)]
        public virtual string? AgentName { get; set; }
    }
}
