CREATE VIEW [dbo].[vCompanyAuth] AS

SELECT 
	ca.ID,
	ca.CompanyId,
	c.Code AS [CompanyCode],
	c.Name AS [CompanyName],
	a.[Group] AS [AuthGroup],
	a.Code AS [AuthCode],
	a.Name AS [AuthName],
    CONVERT(BIT, CASE
            WHEN a.Valid = 1 AND ca.Valid = 1 THEN 1
            ELSE 0
        END) AS [Valid],
	a.<PERSON>dina<PERSON>,
	a.<PERSON>,
	a.<PERSON>umProgramFlags,
	a.<PERSON>Flags,
    a.EnumAuthTagType,
    ca.Valid AS [CompanyValid],
    a.Valid AS [AuthValid],
	a.<PERSON><PERSON>,
	a.<PERSON>,
	ca.AuthId,
	a.C<PERSON>,
	a.CreateTime,
	a.<PERSON>Editor,
	a.LastEditTime
FROM dbo.CompanyAuth AS ca
INNER JOIN dbo.Company AS c ON c.ID = ca.CompanyId
INNER JOIN dbo.vAuth AS a ON a.ID = ca.AuthId
WHERE c.Deleted = 0


