CREATE TABLE [log].[LogWeb] (
    [ID]                        UNIQUEIDENTIFIER        NOT NULL,
    [CompanyId]                 NVARCHAR (50)           NULL,
    [Url]                       NVARCHAR (MAX)          NOT NULL,
    [Controller]                NVARCHAR (500)          NULL,
    [Action]                    NVARCHAR (500)          NULL,
    [Method]                    NVARCHAR (50)           NULL,
    [Headers]                   NVARCHAR (MAX)          NULL,
    [IsAuthenticated]           BIT                     NOT NULL,
    [QueryString]               NVARCHAR (MAX)          NULL,
    [UserAgent]                 NVARCHAR (500)          NULL,
    [Identity]                  NVARCHAR (500)          NULL,
    [Host]                      NVARCHAR (200)          NULL,
    [IP]                        NVARCHAR (50)           NULL,
    CONSTRAINT [PK_LogWeb] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_LogWeb_Log] FOREIGN KEY ([ID]) REFERENCES [log].[Log] ([ID])
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'网页日志',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogWeb';


GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'Url地址',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogWeb',
    @level2type = N'COLUMN',
    @level2name = N'Url'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'控制类',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogWeb',
    @level2type = N'COLUMN',
    @level2name = N'Controller'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'活动方式',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogWeb',
    @level2type = N'COLUMN',
    @level2name = N'Action'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'方法',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogWeb',
    @level2type = N'COLUMN',
    @level2name = N'Method'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'头文件',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogWeb',
    @level2type = N'COLUMN',
    @level2name = N'Headers'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否认证',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogWeb',
    @level2type = N'COLUMN',
    @level2name = N'IsAuthenticated'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'查询条件',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogWeb',
    @level2type = N'COLUMN',
    @level2name = N'QueryString'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'用户代理',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogWeb',
    @level2type = N'COLUMN',
    @level2name = N'UserAgent'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'认证',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogWeb',
    @level2type = N'COLUMN',
    @level2name = N'Identity'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主机名称',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogWeb',
    @level2type = N'COLUMN',
    @level2name = N'Host'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主机IP',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogWeb',
    @level2type = N'COLUMN',
    @level2name = N'IP'
