/**
 * 框架改造
 * 添加清除所有标签页的方法（切换身份/代理需要时调用）
 * 设置当前标签页的标题
 */
import { defineStore } from "pinia";
import {
  type multiType,
  type positionType,
  store,
  isUrl,
  isEqual,
  isNumber,
  isBoolean,
  getConfig,
  routerArrays,
  storageLocal,
  responsiveStorageNameSpace
} from "../utils";
import { usePermissionStoreHook } from "./permission";
import type { RouteLocationNormalizedLoadedGeneric } from "vue-router";

export const useMultiTagsStore = defineStore({
  id: "pure-multiTags",
  state: () => ({
    // 存储标签页信息（路由信息）
    multiTags: storageLocal().getItem<StorageConfigs>(`${responsiveStorageNameSpace()}configure`)
      ?.multiTagsCache
      ? storageLocal().getItem<StorageConfigs>(`${responsiveStorageNameSpace()}tags`)
      : [
          ...routerArrays,
          ...usePermissionStoreHook().flatteningRoutes.filter(v => v?.meta?.fixedTag)
        ],
    multiTagsCache: storageLocal().getItem<StorageConfigs>(
      `${responsiveStorageNameSpace()}configure`
    )?.multiTagsCache
  }),
  getters: {
    getMultiTagsCache(state) {
      return state.multiTagsCache;
    }
  },
  actions: {
    multiTagsCacheChange(multiTagsCache: boolean) {
      this.multiTagsCache = multiTagsCache;
      if (multiTagsCache) {
        storageLocal().setItem(`${responsiveStorageNameSpace()}tags`, this.multiTags);
      } else {
        storageLocal().removeItem(`${responsiveStorageNameSpace()}tags`);
      }
    },
    tagsCache(multiTags) {
      this.getMultiTagsCache &&
        storageLocal().setItem(`${responsiveStorageNameSpace()}tags`, multiTags);
    },
    handleTags<T>(mode: string, value?: T | multiType, position?: positionType): T {
      switch (mode) {
        case "equal":
          this.multiTags = value;
          this.tagsCache(this.multiTags);
          break;
        case "push":
          {
            const tagVal = value as multiType;
            // 不添加到标签页
            if (tagVal?.meta?.hiddenTag) return;
            // 如果是外链无需添加信息到标签页
            if (isUrl(tagVal?.name)) return;
            // 如果title为空拒绝添加空信息到标签页
            if (tagVal?.meta?.title.length === 0) return;
            // showLink:false 不添加到标签页
            if (isBoolean(tagVal?.meta?.showTag) && !tagVal?.meta?.showTag) return;
            const tagPath = tagVal.path;
            // 判断tag是否已存在
            const tagHasExits = this.multiTags.some(tag => {
              return tag.path === tagPath;
            });

            // 判断tag中的query键值是否相等
            const tagQueryHasExits = this.multiTags.some(tag => {
              return isEqual(tag?.query, tagVal?.query);
            });

            // 判断tag中的params键值是否相等
            const tagParamsHasExits = this.multiTags.some(tag => {
              return isEqual(tag?.params, tagVal?.params);
            });

            if (tagHasExits && tagQueryHasExits && tagParamsHasExits) return;

            // 动态路由可打开的最大数量
            const dynamicLevel = tagVal?.meta?.dynamicLevel ?? -1;
            if (dynamicLevel > 0) {
              if (this.multiTags.filter(e => e?.path === tagPath).length >= dynamicLevel) {
                // 如果当前已打开的动态路由数大于dynamicLevel，替换第一个动态路由标签
                const index = this.multiTags.findIndex(item => item?.path === tagPath);
                index !== -1 && this.multiTags.splice(index, 1);
              }
            }

            // 框架改造，重新赋值meta，防止 更改标签名称时把路由上的title改了
            const meta = {};

            Object.keys(tagVal.meta).forEach(key => {
              meta[key] = tagVal.meta[key];
            });

            tagVal.meta = meta;

            this.multiTags.push(value);
            this.tagsCache(this.multiTags);
            if (getConfig()?.MaxTagsLevel && isNumber(getConfig().MaxTagsLevel)) {
              if (this.multiTags.length > getConfig().MaxTagsLevel) {
                this.multiTags.splice(1, 1);
              }
            }
          }
          break;
        case "splice":
          if (!position) {
            const index = this.multiTags.findIndex(v => v.path === value);
            if (index === -1) return;
            this.multiTags.splice(index, 1);
          } else {
            this.multiTags.splice(position?.startIndex, position?.length);
          }
          this.tagsCache(this.multiTags);
          return this.multiTags;
        case "slice":
          return this.multiTags.slice(-1);
      }
    },
    clearAllTags() {
      this.handleTags("splice", "", {
        startIndex: 1,
        length: this.multiTags.length
      });
    },
    setTitle(title: string, force?: boolean, route?: RouteLocationNormalizedLoadedGeneric) {
      route ??= useRoute();

      force ??= true;

      if (!isBoolean(route?.meta?.showLink) || route?.meta?.showLink === false) {
        let tags = this.multiTags.filter(tag => tag.path === route.path);
        if (Object.keys(route.query).length > 0) {
          tags = tags.filter(tag => isEqual(route.query, tag.query));
        }

        if (Object.keys(route.params).length > 0) {
          tags = tags.filter(tag => isEqual(route.params, tag.params));
        }

        if (tags.length === 0) {
          const tag: multiType = {
            name: route.name.toString(),
            path: route.path,
            query: route.query,
            params: route.params,
            meta: {
              title: title || route.meta?.title
            }
          };

          // 保存信息到标签页
          useMultiTagsStoreHook().handleTags("push", tag);
        } else if (force) {
          tags[0].meta.title = title;
        }
      }
    },
    replaceTag(oriTag: multiType, newTag: multiType) {
      let tags = this.multiTags.filter(tag => tag.path === oriTag.path);
      if (Object.keys(oriTag.query).length > 0) {
        tags = tags.filter(tag => isEqual(oriTag.query, tag.query));
      }

      if (Object.keys(oriTag.params).length > 0) {
        tags = tags.filter(tag => isEqual(oriTag.params, tag.params));
      }

      if (tags.length === 0) {
        const tag: multiType = {
          name: newTag.name.toString(),
          path: newTag.path,
          query: newTag.query,
          params: newTag.params,
          meta: {
            title: newTag.meta?.title
          }
        };

        // 保存信息到标签页
        useMultiTagsStoreHook().handleTags("push", tag);
      } else {
        const tag = tags[0];

        tag.name = newTag.name;
        tag.path = newTag.path;
        tag.query = newTag.query;
        tag.params = newTag.params;

        tag.meta.title = newTag.meta.title;
      }
    }
  }
});

export function useMultiTagsStoreHook() {
  return useMultiTagsStore(store);
}
