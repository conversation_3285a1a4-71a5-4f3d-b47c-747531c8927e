<!--
/**
 * 药企管理对话框组件
 * 支持新增和编辑功能
 * 根据传入的recordId判断是新增还是编辑模式
 */
-->
<template>
  <el-dialog
    :title="isEdit ? '编辑药企' : '新增药企'"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="manufacturerFormRef"
      :model="manufacturerForm"
      :rules="formRules"
      label-width="120px"
    >
      <!-- 基本信息 -->
      <el-row :gutter="16">
        <!-- 药企编码 - 仅编辑时显示且不可编辑 -->
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="药企编码" prop="code">
            <el-input v-model="manufacturerForm.code" placeholder="请输入药企编码" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="isEdit ? 12 : 24">
          <el-form-item label="药企名称" prop="name">
            <el-input v-model="manufacturerForm.name" placeholder="请输入药企名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="药企简称" prop="shortName">
            <el-input v-model="manufacturerForm.shortName" placeholder="请输入药企简称" />
          </el-form-item>
        </el-col>
        <!-- 状态 - 仅编辑时显示且可编辑 -->
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="状态" prop="enumStatus">
            <el-select v-model="manufacturerForm.enumStatus" placeholder="请选择状态" style="width: 100%">
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.text"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="国家" prop="country">
            <el-input v-model="manufacturerForm.country" placeholder="请输入国家" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="manufacturerForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { manufacturerApi } from '@/api/manufacturerApi'
import { selectorApi } from '@/api/selectorApi'
import constDefinition from '@/utils/constDefinition'

export default {
  name: 'manufacturerDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    // 表单引用
    const manufacturerFormRef = ref(null)

    // 状态选项数据
    const statusOptions = ref([])
    
    // 是否为编辑模式
    const isEdit = computed(() => !!props.recordId)
    
    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })
    
    // 表单数据
    const manufacturerForm = reactive({
      id: null,
      code: '',
      name: '',
      shortName: '',
      country: '',
      enumStatus: null,
      remark: ''
    })

    // 表单验证规则 - 静态规则，避免动态规则导致的验证问题
    const formRules = {
      code: [], // 编辑模式下药企编码不可编辑，不需要验证
      name: [
        { required: true, message: '请输入药企名称', trigger: 'blur' },
        { max: 300, message: '名称长度不能超过300个字符', trigger: 'blur' }
      ],
      shortName: [
        { max: 50, message: '简称长度不能超过50个字符', trigger: 'blur' }
      ],
      country: [
        { max: 50, message: '国家长度不能超过50个字符', trigger: 'blur' }
      ],
      enumStatus: [
        { required: false, message: '请选择状态', trigger: 'change' }
      ], // 移除动态验证，在保存时手动验证
      remark: [
        { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
      ]
    }

    /**
     * 初始化状态选项
     */
    const initStatusOptions = async () => {
      try {
        const response = await selectorApi.getEnumInfos({ enumType: 'ManufacturerStatus' })

        if (response.data && response.data.success !== false) {
          const data = response.data.data || response.data
          statusOptions.value = data.map(item => ({
            value: item.value,
            text: item.text
          }))
        } else {
          console.error('获取状态选项失败:', response.data?.message)
          statusOptions.value = []
        }
      } catch (error) {
        console.error('获取状态选项失败:', error)
        statusOptions.value = []
      }
    }

    /**
     * 重置表单
     */
    const resetForm = () => {
      Object.assign(manufacturerForm, {
        id: null,
        code: '',
        name: '',
        shortName: '',
        country: '',
        enumStatus: isEdit.value ? null : constDefinition.manufacturerDefaultStatus, // 新增时设置默认状态
        remark: ''
      })

      if (manufacturerFormRef.value) {
        manufacturerFormRef.value.clearValidate()
      }
    }

    /**
     * 加载记录数据（编辑模式）
     */
    const loadRecordData = async (recordId) => {
      try {
        const response = await manufacturerApi.getManufacturer(recordId)

        if (response.data && response.data.success === true) {
          // 后端返回的对象首字母会自动变为小写，直接赋值给表单
          const data = response.data.data || response.data
          Object.assign(manufacturerForm, {
            id: data.id,
            code: data.code,
            name: data.name,
            shortName: data.shortName,
            country: data.country,
            enumStatus: data.enumStatus !== undefined ? data.enumStatus : null,
            remark: data.remark
          })
        } else {
          // 处理错误信息，显示messages数组中的信息
          let errorMessage = '加载数据失败'
          if (response.data?.messages && response.data.messages.length > 0) {
            errorMessage = response.data.messages.join('; ')
          }
          ElMessage.error(errorMessage)
        }
      } catch (error) {
        ElMessage.error('加载数据失败：' + (error.message || '网络错误'))
      }
    }

    /**
     * 保存药企信息
     */
    const handleSave = () => {
      manufacturerFormRef.value.validate(async (valid) => {
        if (valid) {
          // 编辑模式下手动验证状态字段
          if (isEdit.value && !manufacturerForm.enumStatus) {
            ElMessage.error('请选择状态')
            return
          }
          
          try {
            // 编辑模式需要包含ID
            if (isEdit.value) {
              manufacturerForm.id = props.recordId
            } else {
              // 新增模式设置默认状态
              manufacturerForm.enumStatus = constDefinition.manufacturerDefaultStatus
            }

            // 调用对应的API接口
            const response = isEdit.value
              ? await manufacturerApi.editManufacturer(manufacturerForm)
              : await manufacturerApi.addManufacturer(manufacturerForm)

            if (response.data && response.data.success === true) {
              ElMessage.success(isEdit.value ? '药企信息更新成功' : '药企添加成功')
              emit('success')
              handleClose()
            } else {
              // 处理错误信息，显示messages数组中的信息
              let errorMessage = '操作失败'
              if (response.data?.messages && response.data.messages.length > 0) {
                errorMessage = response.data.messages.join('; ')
              }
              ElMessage.error(errorMessage)
            }
          } catch (error) {
            console.error('保存药企信息失败:', error)
            ElMessage.error('操作失败：' + (error.message || '网络错误'))
          } finally {
            
          }
        }
      })
    }

    /**
     * 关闭对话框
     */
    const handleClose = () => {
      // 立即清除验证状态
      if (manufacturerFormRef.value) {
        manufacturerFormRef.value.clearValidate()
      }
      resetForm()
      emit('update:visible', false)
    }

    // 监听对话框显示状态
    watch(() => props.visible, async (newVal) => {
      if (newVal) {
        // 延迟执行，确保DOM渲染完成
        setTimeout(async () => {
          // 先初始化状态选项
          await initStatusOptions()

          if (isEdit.value && props.recordId) {
            // 编辑模式，加载数据
            loadRecordData(props.recordId)
          } else {
            // 新增模式，重置表单
            resetForm()
          }
        }, 50)
      } else {
        // 对话框关闭时清除验证状态
        if (manufacturerFormRef.value) {
          manufacturerFormRef.value.clearValidate()
        }
      }
    })

    return {
      manufacturerFormRef,      
      isEdit,
      dialogVisible,
      manufacturerForm,
      formRules,
      statusOptions,
      handleSave,
      handleClose,
      resetForm
    }
  }
}
</script>
