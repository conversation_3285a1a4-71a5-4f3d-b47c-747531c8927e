﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll.Caching
{
    public class CompanyCachePool

    {
        private readonly object _locker = new();

        protected readonly ConcurrentDictionary<Guid, CompanyCache> _caches = new();

        private SysCache? _sysCache;

        protected SysCache SysCache => _sysCache ??= MvcHttpContext.GetService<SysCache>() ?? HostContext.GetRequiredService<SysCache>();

        public CompanyCache GetCompanyCache(Guid companyId)
        {
            CompanyCache? cache;

            lock (_locker)
            {
                if (!_caches.TryGetValue(companyId, out cache))
                {
                    Company company = this.SysCache.GetCompany(companyId);

                    cache = new CompanyCache(company);

                    _caches.TryAdd(companyId, cache);
                }
            }

            return cache;
        }

        public void FlushCache(Guid companyId)
        {
            lock (_locker)
            {
                if (_caches.Remove(companyId, out CompanyCache? cache))
                {
                    cache.FlushCache();
                    cache.Dispose();
                }
            }
        }

        public void FlushAll()
        {
            foreach (var key in _caches.Keys)
            {
                this.FlushCache(key);
            }
        }

        public CompanyCache CurrentCompanyCache
        {
            get
            {
                var companyId = MvcHttpContext.GetCurrentUser<User>()?.CurrentCompanyId;

                if (!companyId.HasValue)
                {
                    throw new InvalidOperationException($"{nameof(CompanyCachePool)}:无法获取当前公司ID");
                }

                return this.GetCompanyCache(companyId.Value);
            }
        }
    }
}