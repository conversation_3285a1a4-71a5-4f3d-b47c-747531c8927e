﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class PositionMeta
    {
        [MapFromProperty(typeof(Position), nameof(Position.Text))]
        public string? Text { get; set; }

        /// <summary>
        /// 上级职位
        /// </summary>
        [MapFromProperty(typeof(Position), Position.Foreigns.Parent, Position.Columns.Name)]
        public string? ParentName { get; set; }
    }
}
