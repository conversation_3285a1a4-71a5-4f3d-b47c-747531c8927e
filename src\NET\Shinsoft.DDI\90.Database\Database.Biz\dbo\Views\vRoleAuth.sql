CREATE VIEW [dbo].[vRoleAuth] AS

SELECT
	ra.ID,
	ra.CompanyId,
	r.Code AS [RoleCode],
	r.Name AS [RoleName],
	r.EnumFlags AS [EnumRoleFlags],
	ca.AuthGroup,
	ca.AuthCode,
	ca.AuthName,
	ca.Valid,
    ra.AllowAllTags,
	ca.Ordinal,
	ca.Remark,
	ca.EnumProgramFlags,
	ca.EnumAuthFlags,
    ca.EnumAuthTagType,
	ca.Uid AS [AuthUid],
	ca.UidPath AS [AuthUidPath],
	ra.RoleId,
	ra.AuthId
FROM dbo.RoleAuth AS ra
INNER JOIN dbo.Role AS r ON r.ID = ra.RoleId AND r.CompanyId = ra.CompanyId
INNER JOIN dbo.vCompanyAuth AS ca ON ca.AuthId = ra.AuthId AND ca.CompanyId = ra.CompanyId
