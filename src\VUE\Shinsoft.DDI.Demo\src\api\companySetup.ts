import { useHttpApi } from "./libs/httpApi";

const controller = "CompanySetup";

const api = useHttpApi(controller);

export const companySetupApi = {
  QueryCompanySetting(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryCompanySetting", data, config);
  },
  GetCompanySetting(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetCompanySetting", { id }, config);
  },
  UpdateCompanySetting(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateCompanySetting", data, config);
  }
};
