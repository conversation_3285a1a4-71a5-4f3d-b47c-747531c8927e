﻿--Platform
EXEC dbo.usp_AddAuth @ID = '01000000-0000-0000-0000-010000000000',              -- uniqueidentifier
                     @ParentFullCode = N'',						                -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 1,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform',						                -- nvarchar(50)
                     @Name = N'平台管理',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)



--Platform => Platform:Setup
EXEC dbo.usp_AddAuth @ID = '01001000-0000-0000-0000-010010000000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform',					            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:Setup',						            -- nvarchar(50)
                     @Name = N'平台系统设置',					                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)


--Platform => Platform:Setup => Platform:Auth:Define
EXEC dbo.usp_AddAuth @ID = '01001001-0000-0000-0000-010010010000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:Setup',	            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:Auth:Define',				            -- nvarchar(50)
                     @Name = N'权限定义',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N'不可新增/删除，权限颗粒度定义，必须配合代码实现'		-- nvarchar(500)

--Platform => Platform:Setup => Platform:SysSetting:Manage
EXEC dbo.usp_AddAuth @ID = '01001002-1000-0000-0000-010010021000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:Setup',	            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:SysSetting:Manage',			            -- nvarchar(50)
                     @Name = N'系统扩展配置管理',					            -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 21,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Platform => Platform:Setup => Platform:SysCulture:Manage
EXEC dbo.usp_AddAuth @ID = '01001003-1000-0000-0000-010010031000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:Setup',	            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:SysCulture:Manage',			            -- nvarchar(50)
                     @Name = N'系统多语言管理',					                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 31,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Platform => Platform:Setup => Platform:I18n:Manage
EXEC dbo.usp_AddAuth @ID = '01001004-1000-0000-0000-010010041000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:Setup',	            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:I18n:Manage',				            -- nvarchar(50)
                     @Name = N'多语言文本管理',					                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 41,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Platform => Platform:Setup => Platform:SysDict:Config
EXEC dbo.usp_AddAuth @ID = '01001005-1000-0000-0000-010010051000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:Setup',	            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:SysDict:Config',						-- nvarchar(50)
                     @Name = N'系统字典配置',				                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 51,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Platform => Platform:Setup => Platform:SysCache:Operate
EXEC dbo.usp_AddAuth @ID = '01001009-0000-0000-0000-010010090000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:Setup',	            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:SysCache:Operate',			            -- nvarchar(50)
                     @Name = N'系统缓存操作',					                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 90,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)



--Platform => Platform:Company
EXEC dbo.usp_AddAuth @ID = '01002000-0000-0000-0000-010020000000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform',					            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:Company',				                -- nvarchar(50)
                     @Name = N'平台公司管理',					                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 20,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)


--Platform => Platform:Company => Platform:Company:Config
EXEC dbo.usp_AddAuth @ID = '01002001-1000-0000-0000-010020011000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:Company',		    -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:Company:Config',						-- nvarchar(50)
                     @Name = N'公司配置',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 11,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Platform => Platform:Company => Platform:CompanySetting:Config
EXEC dbo.usp_AddAuth @ID = '01002002-1000-0000-0000-010020021000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:Company',		    -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:CompanySetting:Config',					-- nvarchar(50)
                     @Name = N'公司扩展配置',					                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 21,							                    -- int
                     @Remark = N'可以进行新增/编辑/删除操作（平台管理员使用）'	-- nvarchar(500)



--Platform => Platform:User
EXEC dbo.usp_AddAuth @ID = '01003000-0000-0000-0000-010030000000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform',						        -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:User',						            -- nvarchar(50)
                     @Name = N'用户',					                        -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 30,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)


--Platform => Platform:User => Platform:User:Query
EXEC dbo.usp_AddAuth @ID = '01003001-0000-0000-0000-010030010000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:User',				-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:User:Query',						    -- nvarchar(50)
                     @Name = N'用户查询',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Platform => Platform:User => Platform:User:Manage
EXEC dbo.usp_AddAuth @ID = '01003001-1000-0000-0000-010030011000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:User',				-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:User:Manage',						    -- nvarchar(50)
                     @Name = N'用户管理',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 11,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Platform => Platform:User => Platform:User:Manage:Add
EXEC dbo.usp_AddAuth @ID = '01003001-2000-0000-0000-010030012000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:User',				-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:User:Manage:Add',						-- nvarchar(50)
                     @Name = N'用户管理:新增',					                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 12,							                    -- int
                     @Remark = N''		                                        -- nvarchar(500)

--Platform => Platform:User => Platform:User:Manage:Edit
EXEC dbo.usp_AddAuth @ID = '01003001-3000-0000-0000-010030013000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:User',				-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:User:Manage:Edit',						-- nvarchar(50)
                     @Name = N'用户管理:编辑',					                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 13,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)
                     
--Platform => Platform:User => Platform:User:Manage:Delete
EXEC dbo.usp_AddAuth @ID = '01003001-4000-0000-0000-010030014000',              -- uniqueidentifier
                     @ParentFullCode = N'Platform/Platform:User',				-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Platform:User:Manage:Delete',					-- nvarchar(50)
                     @Name = N'用户管理:删除',					                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 14,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)		



--System
EXEC dbo.usp_AddAuth @ID = '11000000-0000-0000-0000-110000000000',              -- uniqueidentifier
                     @ParentFullCode = N'',						                -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'System',						                    -- nvarchar(50)
                     @Name = N'系统管理',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 110,							                -- int
                     @Remark = N''							                    -- nvarchar(500)


--System => Setup
EXEC dbo.usp_AddAuth @ID = '11001000-0000-0000-0000-110010000000',              -- uniqueidentifier
                     @ParentFullCode = N'System',						        -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Setup',						                    -- nvarchar(50)
                     @Name = N'系统设置',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--System => Setup => CompanySetting:Maintain
EXEC dbo.usp_AddAuth @ID = '11001001-0000-0000-0000-110010010000',              -- uniqueidentifier
                     @ParentFullCode = N'System/Setup',						    -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'CompanySetting:Maintain',						-- nvarchar(50)
                     @Name = N'公司配置维护',					                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N'只可以维护配置的值，不可以进行新增/删除操作'	-- nvarchar(500)

--System => Setup => CompanyCache:Operate
EXEC dbo.usp_AddAuth @ID = '11001002-0000-0000-0000-110010020000',              -- uniqueidentifier
                     @ParentFullCode = N'System/Setup',						    -- nvarchar(50)
                     @EnumProgramFlags = 0,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'CompanyCache:Operate',						    -- nvarchar(50)
                     @Name = N'公司缓存操作',					                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 20,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)




--System => Announcement
EXEC dbo.usp_AddAuth @ID = '11007000-0000-0000-0000-110070000000',              -- uniqueidentifier
                     @ParentFullCode = N'System',						        -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Announcement',						            -- nvarchar(50)
                     @Name = N'公告',					                        -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 70,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--System => Announcement => Announcement:Query
EXEC dbo.usp_AddAuth @ID = '11007001-0000-0000-0000-110070010000',              -- uniqueidentifier
                     @ParentFullCode = N'System/Announcement',					-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Announcement:Query',						        -- nvarchar(50)
                     @Name = N'公告查询',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--System => Announcement => Announcement:Manage
EXEC dbo.usp_AddAuth @ID = '11007001-1000-0000-0000-110070011000',              -- uniqueidentifier
                     @ParentFullCode = N'System/Announcement',					-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Announcement:Manage',						    -- nvarchar(50)
                     @Name = N'公告管理',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 11,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)


--System => Mail
EXEC dbo.usp_AddAuth @ID = '11008000-0000-0000-0000-110080000000',              -- uniqueidentifier
                     @ParentFullCode = N'System',						        -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Mail',						                    -- nvarchar(50)
                     @Name = N'邮件',					                        -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 80,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--System => Mail => Mail:Query
EXEC dbo.usp_AddAuth @ID = '11008001-0000-0000-0000-110080010000',              -- uniqueidentifier
                     @ParentFullCode = N'System/Mail',						    -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Mail:Query',						                -- nvarchar(50)
                     @Name = N'邮件查询',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--System => Mail => Mail:Manage
EXEC dbo.usp_AddAuth @ID = '11008001-1000-0000-0000-110080011000',              -- uniqueidentifier
                     @ParentFullCode = N'System/Mail',						    -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Mail:Manage',						            -- nvarchar(50)
                     @Name = N'邮件管理',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 11,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)


--System => Log
EXEC dbo.usp_AddAuth @ID = '11009000-0000-0000-0000-110090000000',              -- uniqueidentifier
                     @ParentFullCode = N'System',						        -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Log',						                    -- nvarchar(50)
                     @Name = N'日志',					                        -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 90,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--System => Log => Log:Query
EXEC dbo.usp_AddAuth @ID = '11009001-0000-0000-0000-110090010000',              -- uniqueidentifier
                     @ParentFullCode = N'System/Log',				            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Log:Query',						                -- nvarchar(50)
                     @Name = N'日志查询',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 90,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)


--Authorize
EXEC dbo.usp_AddAuth @ID = '12000000-0000-0000-0000-120000000000',              -- uniqueidentifier
                     @ParentFullCode = N'',						                -- nvarchar(50)
                     @EnumProgramFlags = 0,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Authorize',						                -- nvarchar(50)
                     @Name = N'权限管理',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 120,							                -- int
                     @Remark = N''							                    -- nvarchar(500)


--Authorize => Employee
EXEC dbo.usp_AddAuth @ID = '12001000-0000-0000-0000-120010000000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize',						    -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Employee',						                -- nvarchar(50)
                     @Name = N'员工',					                        -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Authorize => Employee => Employee:Query
EXEC dbo.usp_AddAuth @ID = '12001001-0000-0000-0000-120010010000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Employee',					-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Employee:Query',				                    -- nvarchar(50)
                     @Name = N'员工查询',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Authorize => Employee => Employee:Manage
EXEC dbo.usp_AddAuth @ID = '12001001-1000-0000-0000-120010011000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Employee',		            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Employee:Manage',			                    -- nvarchar(50)
                     @Name = N'员工管理',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 11,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Authorize => Employee => Employee:Manage:Add
EXEC dbo.usp_AddAuth @ID = '12001001-2000-0000-0000-120010012000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Employee',		            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Employee:Manage:Add',		                    -- nvarchar(50)
                     @Name = N'员工管理:新增',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 12,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Authorize => Employee => Employee:Manage:Edit
EXEC dbo.usp_AddAuth @ID = '12001001-3000-0000-0000-120010013000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Employee',		            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Employee:Manage:Edit',		                    -- nvarchar(50)
                     @Name = N'员工管理:编辑',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 13,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Authorize => Employee => Employee:Manage:Delete
EXEC dbo.usp_AddAuth @ID = '12001001-4000-0000-0000-120010014000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Employee',		            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Employee:Manage:Delete',		                    -- nvarchar(50)
                     @Name = N'员工管理:删除',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 14,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Authorize => Employee => Employee:Manage:ResetPwd
EXEC dbo.usp_AddAuth @ID = '12001001-5000-0000-0000-120010015000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Employee',		            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Employee:Manage:ResetPwd',			            -- nvarchar(50)
                     @Name = N'员工管理:重置密码',					            -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 15,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)


--Authorize => Role
EXEC dbo.usp_AddAuth @ID = '12002000-0000-0000-0000-120020000000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize',						    -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Role',						                    -- nvarchar(50)
                     @Name = N'角色',					                        -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 20,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Authorize => Role => Role:Query
EXEC dbo.usp_AddAuth @ID = '12002001-0000-0000-0000-120020010000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Role',					    -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Role:Query',					                    -- nvarchar(50)
                     @Name = N'角色查询',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Authorize => Role => Role:Manage
EXEC dbo.usp_AddAuth @ID = '12002001-1000-0000-0000-120020011000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Role',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Role:Manage',				                    -- nvarchar(50)
                     @Name = N'角色管理',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 11,							                    -- int
                     @Remark = N'包含新增/编辑/删除/授权/成员维护等功能'		-- nvarchar(500)

--Authorize => Role => Role:Manage:Add
EXEC dbo.usp_AddAuth @ID = '12002001-2000-0000-0000-120020012000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Role',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Role:Manage:Add',			                    -- nvarchar(50)
                     @Name = N'角色管理:新增',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 12,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Authorize => Role => Role:Manage:Edit
EXEC dbo.usp_AddAuth @ID = '12002001-3000-0000-0000-120020013000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Role',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Role:Manage:Edit',			                    -- nvarchar(50)
                     @Name = N'角色管理:编辑',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 13,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Authorize => Role => Role:Manage:Delete
EXEC dbo.usp_AddAuth @ID = '12002001-4000-0000-0000-120020014000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Role',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Role:Manage:Delete',			                    -- nvarchar(50)
                     @Name = N'员工管理:删除',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 14,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Authorize => Role => Role:Manage:Auth
EXEC dbo.usp_AddAuth @ID = '12002001-5000-0000-0000-120020015000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Role',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Role:Manage:Auth',			                    -- nvarchar(50)
                     @Name = N'员工管理:授权',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 15,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--Authorize => Role => Role:Manage:Member
EXEC dbo.usp_AddAuth @ID = '12002001-6000-0000-0000-120020016000',              -- uniqueidentifier
                     @ParentFullCode = N'Authorize/Role',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Role:Manage:Member',			                    -- nvarchar(50)
                     @Name = N'员工管理:成员维护',			                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 16,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)






--MasterData
EXEC dbo.usp_AddAuth @ID = '21000000-0000-0000-0000-210000000000',              -- uniqueidentifier
                     @ParentFullCode = N'',						                -- nvarchar(50)
                     @EnumProgramFlags = 0,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'MasterData',					                    -- nvarchar(50)
                     @Name = N'主数据维护',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 210,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic
EXEC dbo.usp_AddAuth @ID = '21001000-0000-0000-0000-210010000000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData',				            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Basic',						                    -- nvarchar(50)
                     @Name = N'基础主数据',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Organization:Query
EXEC dbo.usp_AddAuth @ID = '21001001-0000-0000-0000-210010010000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Query',			                    -- nvarchar(50)
                     @Name = N'组织架构查询',				                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,						                        -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Organization:Manage
EXEC dbo.usp_AddAuth @ID = '21001001-1000-0000-0000-210010011000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage',		                    -- nvarchar(50)
                     @Name = N'组织架构管理',				                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 11,						                        -- int
                     @Remark = N'包含部门管理、岗位管理等全部管理功能'			-- nvarchar(500)


--MasterData => Basic => Organization:Manage:Department
EXEC dbo.usp_AddAuth @ID = '21001002-1000-0000-0000-210010021000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Department',					-- nvarchar(50)
                     @Name = N'组织架构管理:部门管理',					        -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 21,						                        -- int
                     @Remark = N'包含新增、编辑、删除、移动、关联成本中心等全部管理功能'-- nvarchar(500)

--MasterData => Basic => Organization:Manage:Department:Add
EXEC dbo.usp_AddAuth @ID = '21001002-2000-0000-0000-210010022000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Department:Add',	            -- nvarchar(50)
                     @Name = N'组织架构管理:部门管理:新增',			            -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 22,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Organization:Manage:Department:Edit
EXEC dbo.usp_AddAuth @ID = '21001002-3000-0000-0000-210010023000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Department:Edit',			-- nvarchar(50)
                     @Name = N'组织架构管理:部门管理:编辑',					    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 23,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Organization:Manage:Department:Delete
EXEC dbo.usp_AddAuth @ID = '21001002-4000-0000-0000-210010024000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Department:Delete',			-- nvarchar(50)
                     @Name = N'组织架构管理:部门管理:删除',					    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 24,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Organization:Manage:Department:Move
EXEC dbo.usp_AddAuth @ID = '21001002-5000-0000-0000-210010025000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Department:Move',            -- nvarchar(50)
                     @Name = N'组织架构管理:部门管理:移动',					    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 25,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Organization:Manage:Department:CostCenter
EXEC dbo.usp_AddAuth @ID = '21001002-6000-0000-0000-210010026000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Department:CostCenter',		-- nvarchar(50)
                     @Name = N'组织架构管理:部门管理:关联成本中心',				-- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 26,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Organization:Manage:Department:Valid
EXEC dbo.usp_AddAuth @ID = '21001002-9000-0000-0000-210010029000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Department:Valid',			-- nvarchar(50)
                     @Name = N'组织架构管理:部门管理:激活/停用',				-- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 29,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Organization:Manage:Station
EXEC dbo.usp_AddAuth @ID = '21001003-1000-0000-0000-210010031000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Station',					-- nvarchar(50)
                     @Name = N'组织架构管理:岗位管理',					        -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 31,							                    -- int
                     @Remark = N'包含新增、编辑、删除、人岗维护等全部管理功能'	-- nvarchar(500)

--MasterData => Basic => Organization:Manage:Station:Add
EXEC dbo.usp_AddAuth @ID = '21001003-2000-0000-0000-210010032000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',				        -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Station:Add',		        -- nvarchar(50)
                     @Name = N'组织架构管理:岗位管理:新增',				        -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 32,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Organization:Manage:Station:Edit
EXEC dbo.usp_AddAuth @ID = '21001003-3000-0000-0000-210010033000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Station:Edit',				-- nvarchar(50)
                     @Name = N'组织架构管理:岗位管理:编辑',					    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 33,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Organization:Manage:Station:Delete
EXEC dbo.usp_AddAuth @ID = '21001003-4000-0000-0000-210010034000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Station:Delete',				-- nvarchar(50)
                     @Name = N'组织架构管理:岗位管理:删除',					    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 34,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Organization:Manage:Station:Employee
EXEC dbo.usp_AddAuth @ID = '21001003-5000-0000-0000-210010035000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Station:Employee',			-- nvarchar(50)
                     @Name = N'组织架构管理:岗位管理:人岗维护',					-- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 35,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Organization:Manage:Station:Valid
EXEC dbo.usp_AddAuth @ID = '21001003-9000-0000-0000-210010039000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Organization:Manage:Station:Valid',				-- nvarchar(50)
                     @Name = N'组织架构管理:岗位管理:激活/停用',				-- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 39,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)



--MasterData => Basic => SubCompany:Query
EXEC dbo.usp_AddAuth @ID = '21001011-0000-0000-0000-210010110000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'SubCompany:Query',			                    -- nvarchar(50)
                     @Name = N'分公司查询',				                        -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 110,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => SubCompany:Manage
EXEC dbo.usp_AddAuth @ID = '21001011-1000-0000-0000-210010111000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'SubCompany:Manage',			                    -- nvarchar(50)
                     @Name = N'分公司管理',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 111,						                    -- int
                     @Remark = N'包含新增/编辑/删除等功能'						-- nvarchar(500)

--MasterData => Basic => SubCompany:Manage:Add
EXEC dbo.usp_AddAuth @ID = '21001011-2000-0000-0000-210010112000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'SubCompany:Manage:Add',						    -- nvarchar(50)
                     @Name = N'分公司管理:新增',					            -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 112,							                -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => SubCompany:Manage:Edit
EXEC dbo.usp_AddAuth @ID = '21001011-3000-0000-0000-210010113000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',                     -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'SubCompany:Manage:Edit',                         -- nvarchar(50)
                     @Name = N'分公司管理:编辑',                                -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 113,							                -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => SubCompany:Manage:Delete
EXEC dbo.usp_AddAuth @ID = '21001011-4000-0000-0000-210010114000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'SubCompany:Manage:Delete',						-- nvarchar(50)
                     @Name = N'分公司管理:删除',					            -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 114,							                -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => SubCompany:Manage:Valid
EXEC dbo.usp_AddAuth @ID = '21001011-9000-0000-0000-210010119000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'SubCompany:Manage:Valid',	                    -- nvarchar(50)
                     @Name = N'分公司管理:激活/停用',		                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 119,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Department:Query
EXEC dbo.usp_AddAuth @ID = '21001012-0000-0000-0000-210010120000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Department:Query',			                    -- nvarchar(50)
                     @Name = N'部门管理查询',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 120,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Department:Manage
EXEC dbo.usp_AddAuth @ID = '21001012-1000-0000-0000-210010121000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Department:Manage',			                    -- nvarchar(50)
                     @Name = N'部门管理',					                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 121,							                -- int
                     @Remark = N'包含新增/编辑/删除等功能'						-- nvarchar(500)

--MasterData => Basic => Department:Manage:Add
EXEC dbo.usp_AddAuth @ID = '21001012-2000-0000-0000-210010122000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Department:Manage:Add',						    -- nvarchar(50)
                     @Name = N'部门管理:新增',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 122,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Department:Manage:Edit
EXEC dbo.usp_AddAuth @ID = '21001012-3000-0000-0000-210010123000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Department:Manage:Edit',						    -- nvarchar(50)
                     @Name = N'部门管理:新增',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 123,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Department:Manage:Delete
EXEC dbo.usp_AddAuth @ID = '21001012-4000-0000-0000-210010124000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Department:Manage:Delete',						-- nvarchar(50)
                     @Name = N'部门管理:删除',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 124,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Department:Manage:Move
EXEC dbo.usp_AddAuth @ID = '21001012-5000-0000-0000-210010125000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Department:Manage:Move',						    -- nvarchar(50)
                     @Name = N'部门管理:移动',					                -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 125,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Department:Manage:CostCenter
EXEC dbo.usp_AddAuth @ID = '21001012-6000-0000-0000-210010126000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Department:Manage:CostCenter',		            -- nvarchar(50)
                     @Name = N'部门管理:关联成本中心',				            -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 126,							                -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Department:Manage:Valid
EXEC dbo.usp_AddAuth @ID = '21001012-9000-0000-0000-210010129000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Department:Manage:Valid',						-- nvarchar(50)
                     @Name = N'部门管理:激活/停用',				                -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 129,							                -- int
                     @Remark = N''							                    -- nvarchar(500)


--MasterData => Basic => CostCenter:Query
EXEC dbo.usp_AddAuth @ID = '21001013-0000-0000-0000-210010130000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'CostCenter:Query',			                    -- nvarchar(50)
                     @Name = N'成本中心查询',				                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 130,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => CostCenter:Manage
EXEC dbo.usp_AddAuth @ID = '21001013-1000-0000-0000-210010131000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'CostCenter:Manage',			                    -- nvarchar(50)
                     @Name = N'成本中心管理',				                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 131,						                    -- int
                     @Remark = N'包含新增/编辑/删除等功能'						-- nvarchar(500)

--MasterData => Basic => CostCenter:Manage:Add
EXEC dbo.usp_AddAuth @ID = '21001013-2000-0000-0000-210010132000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'CostCenter:Manage:Add',						    -- nvarchar(50)
                     @Name = N'成本中心管理:新增',					            -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 132,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => CostCenter:Manage:Edit
EXEC dbo.usp_AddAuth @ID = '21001013-3000-0000-0000-210010133000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'CostCenter:Manage:Edit',						    -- nvarchar(50)
                     @Name = N'成本中心管理:编辑',					            -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 133,							                -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => CostCenter:Manage:Delete
EXEC dbo.usp_AddAuth @ID = '21001013-4000-0000-0000-210010134000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'CostCenter:Manage:Delete',	                    -- nvarchar(50)
                     @Name = N'成本中心管理:删除',			                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 134,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => CostCenter:Manage:Valid
EXEC dbo.usp_AddAuth @ID = '21001013-9000-0000-0000-210010139000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'CostCenter:Manage:Valid',						-- nvarchar(50)
                     @Name = N'成本中心管理:激活/停用',					        -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 139,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Position:Query
EXEC dbo.usp_AddAuth @ID = '21001014-0000-0000-0000-210010140000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',			            -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Position:Query',				                    -- nvarchar(50)
                     @Name = N'职位查询',				                        -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 140,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Position:Manage
EXEC dbo.usp_AddAuth @ID = '21001014-1000-0000-0000-210010141000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Position:Manage',			                    -- nvarchar(50)
                     @Name = N'职位管理',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 141,						                    -- int
                     @Remark = N'包含新增/编辑/删除等功能'						-- nvarchar(500)

--MasterData => Basic => Position:Manage:Add
EXEC dbo.usp_AddAuth @ID = '21001014-2000-0000-0000-210010142000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Position:Manage:Add',						    -- nvarchar(50)
                     @Name = N'职位管理:新增',					                -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 142,							                -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Position:Manage:Edit
EXEC dbo.usp_AddAuth @ID = '21001014-3000-0000-0000-210010143000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Position:Manage:Edit',						    -- nvarchar(50)
                     @Name = N'职位管理:编辑',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 143,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Position:Manage:Delete
EXEC dbo.usp_AddAuth @ID = '21001014-4000-0000-0000-210010144000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Position:Manage:Delete',						    -- nvarchar(50)
                     @Name = N'职位管理:删除',					                -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 144,							                -- int
                     @Remark = N''							                    -- nvarchar(500)


--MasterData => Basic => Station:Query
EXEC dbo.usp_AddAuth @ID = '21001015-0000-0000-0000-210010150000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Station:Query',				                    -- nvarchar(50)
                     @Name = N'岗位查询',				                        -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 150,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Station:Manage
EXEC dbo.usp_AddAuth @ID = '21001015-1000-0000-0000-210010151000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Station:Manage',				                    -- nvarchar(50)
                     @Name = N'岗位管理',					                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 151,						                    -- int
                     @Remark = N'包含新增/编辑/删除等功能'						-- nvarchar(500)

--MasterData => Basic => Station:Manage:Add
EXEC dbo.usp_AddAuth @ID = '21001015-2000-0000-0000-210010152000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Station:Manage:Add',						        -- nvarchar(50)
                     @Name = N'岗位管理:新增',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 152,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Station:Manage:Edit
EXEC dbo.usp_AddAuth @ID = '21001015-3000-0000-0000-210010153000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Station:Manage:Edit',						    -- nvarchar(50)
                     @Name = N'岗位管理:编辑',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 153,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Station:Manage:Delete
EXEC dbo.usp_AddAuth @ID = '21001015-4000-0000-0000-210010154000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Station:Manage:Delete',						    -- nvarchar(50)
                     @Name = N'岗位管理:删除',				                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 154,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Basic => Station:Manage:Employee
EXEC dbo.usp_AddAuth @ID = '21001015-5000-0000-0000-210010155000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Station:Manage:Employee',						-- nvarchar(50)
                     @Name = N'岗位管理:人岗维护',					            -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 155,							                -- int
                     @Remark = N''	                                            -- nvarchar(500)

--MasterData => Basic => Station:Manage:Valid
EXEC dbo.usp_AddAuth @ID = '21001015-9000-0000-0000-210010159000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Basic',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Station:Manage:Valid',						    -- nvarchar(50)
                     @Name = N'岗位管理:停用/激活',					            -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 159,							                -- int
                     @Remark = N''	                                            -- nvarchar(500)


--MasterData => Business
EXEC dbo.usp_AddAuth @ID = '21005000-0000-0000-0000-210050000000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData',						    -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Business',					                    -- nvarchar(50)
                     @Name = N'业务主数据',					                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 50,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Business => BizDict:Query
EXEC dbo.usp_AddAuth @ID = '21005001-0000-0000-0000-210050010000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Business',					-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'BizDict:Query',				                    -- nvarchar(50)
                     @Name = N'业务字典查询',				                    -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Business => BizDict:Manage
EXEC dbo.usp_AddAuth @ID = '21005001-1000-0000-0000-210050011000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Business',					-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'BizDict:Manage',						            -- nvarchar(50)
                     @Name = N'业务字典管理',					                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 11,							                    -- int
                     @Remark = N'包含新增/编辑/删除等功能'			            -- nvarchar(500)

--MasterData => Business => BizDict:Manage:Add
EXEC dbo.usp_AddAuth @ID = '21005001-2000-0000-0000-210050012000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Business',					-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'BizDict:Manage:Add',						        -- nvarchar(50)
                     @Name = N'业务字典管理:新增',					            -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 12,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Business => BizDict:Manage:Edit
EXEC dbo.usp_AddAuth @ID = '21005001-3000-0000-0000-210050013000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Business',					-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'BizDict:Manage:Edit',						    -- nvarchar(50)
                     @Name = N'业务字典管理:编辑',			                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 13,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)

--MasterData => Business => BizDict:Manage:Delete
EXEC dbo.usp_AddAuth @ID = '21005001-4000-0000-0000-210050014000',              -- uniqueidentifier
                     @ParentFullCode = N'MasterData/Business',					-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'BizDict:Manage:Delete',						    -- nvarchar(50)
                     @Name = N'业务字典管理:删除',			                    -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 14,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)


--Report
EXEC dbo.usp_AddAuth @ID = '32000000-0000-0000-0000-320000000000',              -- uniqueidentifier
                     @ParentFullCode = N'',						                -- nvarchar(50)
                     @EnumProgramFlags = 0,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Report',						                    -- nvarchar(50)
                     @Name = N'报表',					                        -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 320,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)


--Report => PC
EXEC dbo.usp_AddAuth @ID = '32001000-0000-0000-0000-320010000000',              -- uniqueidentifier
                     @ParentFullCode = N'Report',						        -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'PC',						                        -- nvarchar(50)
                     @Name = N'桌面端',					                        -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)


--Report => PC => PC#Report:View 【AuthTagType: Report】
EXEC dbo.usp_AddAuth @ID = '32001001-0000-0000-0000-320010010000',              -- uniqueidentifier
                     @ParentFullCode = N'Report/PC',						    -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 2,					                    -- int
                     @Code = N'PC#Report:View',				                    -- nvarchar(50)
                     @Name = N'【桌面端】报表查看',				                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 20,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)


--Report => Mobile
EXEC dbo.usp_AddAuth @ID = '32002000-0000-0000-0000-320020000000',              -- uniqueidentifier
                     @ParentFullCode = N'Report',						        -- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Mobile',						                    -- nvarchar(50)
                     @Name = N'移动端',					                        -- nvarchar(200)
                     @Valid = 0,                                                -- bit
                     @Ordinal = 20,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)


--Report => Mobile => Mobile#Report:View 【AuthTagType: App】
EXEC dbo.usp_AddAuth @ID = '32002001-0000-0000-0000-320020010000',              -- uniqueidentifier
                     @ParentFullCode = N'Report/Mobile',						-- nvarchar(50)
                     @EnumProgramFlags = 1,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 2,					                    -- int
                     @Code = N'Mobile#Report:View',				                -- nvarchar(50)
                     @Name = N'【移动端】报表查看',				                -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 10,							                    -- int
                     @Remark = N''							                    -- nvarchar(500)






--Business
EXEC dbo.usp_AddAuth @ID = '51000000-0000-0000-0000-510000000000',              -- uniqueidentifier
                     @ParentFullCode = N'',						                -- nvarchar(50)
                     @EnumProgramFlags = 0,					                    -- int
                     @EnumFlags = 0,						                    -- int
                     @EnumAuthTagType = 0,					                    -- int
                     @Code = N'Business',						                -- nvarchar(50)
                     @Name = N'业务',					                        -- nvarchar(200)
                     @Valid = 1,                                                -- bit
                     @Ordinal = 510,						                    -- int
                     @Remark = N''							                    -- nvarchar(500)
