﻿--角色权限标签
CREATE TABLE [dbo].[RoleAuthTag]
(
    [ID]                            UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_RoleAuthTag_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                     UNIQUEIDENTIFIER            NOT NULL,
    [RoleId]                        UNIQUEIDENTIFIER            NOT NULL,
    [AuthId]                        UNIQUEIDENTIFIER            NOT NULL,
    [AuthTagId]                     UNIQUEIDENTIFIER            NOT NULL,
    CONSTRAINT [PK_RoleAuthTag] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_RoleAuthTag_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_RoleAuthTag_Role_00_Role_RoleAuthTags] FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Role] ([ID]),
    CONSTRAINT [FK_RoleAuthTag_Auth_00_Auth] FOREIGN KEY ([AuthId]) REFERENCES [dbo].[Auth] ([ID]),
    CONSTRAINT [FK_RoleAuthTag_AuthTag_00_AuthTag] FOREIGN KEY ([AuthTagId]) REFERENCES [dbo].[AuthTag] ([ID]),
);

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'角色权限标签',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RoleAuthTag',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'角色ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RoleAuthTag',
    @level2type = N'COLUMN',
    @level2name = N'RoleId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'权限ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RoleAuthTag',
    @level2type = N'COLUMN',
    @level2name = N'AuthId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'权限标签ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RoleAuthTag',
    @level2type = N'COLUMN',
    @level2name = N'AuthTagId'
