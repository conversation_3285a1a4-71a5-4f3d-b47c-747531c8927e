﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Shinsoft.DDI.Entities
{
    public static class AuthExtender
    {
        public static bool IsInIds(this Auth entity, IEnumerable<Guid> ids)
        {
            return entity.AnyChildren(p => ids.Contains(p.ID));
        }

        public static bool AnyChildren(this Auth entity, Func<Auth, bool> validate)
        {
            if (validate(entity))
            {
                return true;
            }
            else
            {
                if (entity.Children?.Count > 0)
                {
                    foreach (var child in entity.Children)
                    {
                        if (child.AnyChildren(validate))
                        {
                            return true;
                        }
                    }
                }

                if (entity.InvisibleChilren?.Count > 0)
                {
                    foreach (var child in entity.InvisibleChilren)
                    {
                        if (child.AnyChildren(validate))
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        public static bool AllChildren(this Auth entity, Func<Auth, bool> validate)
        {
            if (!validate(entity))
            {
                return false;
            }
            else
            {
                if (entity.Children?.Count > 0)
                {
                    foreach (var child in entity.Children)
                    {
                        if (!child.AllChildren(validate))
                        {
                            return false;
                        }
                    }
                }

                if (entity.InvisibleChilren?.Count > 0)
                {
                    foreach (var child in entity.InvisibleChilren)
                    {
                        if (!child.AllChildren(validate))
                        {
                            return false;
                        }
                    }
                }
            }

            return true;
        }
    }
}
