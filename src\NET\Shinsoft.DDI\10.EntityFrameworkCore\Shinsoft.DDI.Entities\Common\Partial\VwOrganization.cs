﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public partial class VwOrganization : IParent<VwOrganization>
    {
        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent

        #region Const

        public static partial class Foreigns
        {
            /// <summary>
            /// 公司
            /// </summary>
            public const string Company = nameof(VwOrganization.Company);

            /// <summary>
            /// 上级组织
            /// </summary>
            public const string Parent = nameof(VwOrganization.Parent);

            /// <summary>
            /// 分公司
            /// </summary>
            public const string SubCompany = nameof(VwOrganization.SubCompany);

            /// <summary>
            /// 部门
            /// </summary>
            public const string Department = nameof(VwOrganization.Department);

            /// <summary>
            /// 岗位
            /// </summary>
            public const string Station = nameof(VwOrganization.Station);

            /// <summary>
            /// 员工
            /// </summary>
            public const string Employee = nameof(VwOrganization.Employee);
        }

        public static partial class Inverse
        {
            /// <summary>
            /// 子组织
            /// </summary>
            public const string Children = nameof(VwOrganization.Children);
        }

        #endregion Const

        #region Foreign

#pragma warning disable CS8603 // 可能返回 null 引用。

        #region Company

        [NotMapped, XmlIgnore, JsonIgnore]
        private Company? _Company;

        [ForeignKey(Columns.CompanyId)]
        public virtual Company Company
        {
            get => this.LazyLoad(ref _Company);
            set => _Company = value;
        }

        #endregion Company

        #region Parent 上级组织

        [NotMapped, XmlIgnore, JsonIgnore]
        private VwOrganization? _Parent;

        /// <summary>
        /// 上级组织
        /// </summary>
        [ForeignKey(Columns.ParentId)]
        public virtual VwOrganization? Parent
        {
            get => this.LazyLoad(ref _Parent);
            set => _Parent = value;
        }

        #endregion Parent 上级组织

        #region SubCompany

        [NotMapped, XmlIgnore, JsonIgnore]
        private SubCompany? _SubCompany;

        [ForeignKey(Columns.SubCompanyId)]
        public virtual SubCompany SubCompany
        {
            get => this.LazyLoad(ref _SubCompany);
            set => _SubCompany = value;
        }

        #endregion SubCompany

        #region Department

        [NotMapped, XmlIgnore, JsonIgnore]
        private Department? _Department;

        [ForeignKey(Columns.DepartmentId)]
        public virtual Department? Department
        {
            get => this.LazyLoad(ref _Department);
            set => _Department = value;
        }

        #endregion Department

        #region Station

        [NotMapped, XmlIgnore, JsonIgnore]
        private Station? _Station;

        [ForeignKey(Columns.StationId)]
        public virtual Station? Station
        {
            get => this.LazyLoad(ref _Station);
            set => _Station = value;
        }

        #endregion Station

        #region Employee

        [NotMapped, XmlIgnore, JsonIgnore]
        private Employee? _Employee;

        [ForeignKey(Columns.StationId)]
        public virtual Employee? Employee
        {
            get => this.LazyLoad(ref _Employee);
            set => _Employee = value;
        }

        #endregion Employee

#pragma warning restore CS8603 // 可能返回 null 引用。

        #endregion Foreign

        #region Inverse

#pragma warning disable CS8603 // 可能返回 null 引用。

        #region Children 子组织

        [NotMapped, XmlIgnore, JsonIgnore]
        private List<VwOrganization>? _Children;

        /// <summary>
        /// 子部门
        /// </summary>
        [InverseProperty("Parent")]
        public virtual List<VwOrganization> Children
        {
            get => this.LazyLoad(ref _Children);
            set => _Children = value;
        }

        #endregion Children 子组织

#pragma warning restore CS8603 // 可能返回 null 引用。

        #endregion Inverse
    }
}
