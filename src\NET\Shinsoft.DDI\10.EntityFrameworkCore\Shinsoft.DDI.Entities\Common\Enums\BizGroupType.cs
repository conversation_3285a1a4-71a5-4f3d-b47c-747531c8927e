﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public enum BizGroupType
    {
        None = 0,

        /// <summary>
        /// 分公司
        /// </summary>
        [Description("分公司")]
        SubCompany = 1,

        /// <summary>
        /// 部门
        /// </summary>
        [Description("部门")]
        Department = 2,

        /// <summary>
        /// 成本中心
        /// </summary>
        [Description("成本中心")]
        CostCenter = 3,

        /// <summary>
        /// 项目
        /// </summary>
        [Description("项目")]
        Project = 10,

        ///// <summary>
        ///// 费用类型
        ///// </summary>
        //[Description("费用类型")]
        //CostType = 11,

        /// <summary>
        /// 合同类型
        /// </summary>
        [Description("合同类型")]
        ContractType = 12,
    }
}
