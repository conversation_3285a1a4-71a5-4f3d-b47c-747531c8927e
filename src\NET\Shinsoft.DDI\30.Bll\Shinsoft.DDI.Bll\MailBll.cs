﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Shinsoft.Core.Mail;

namespace Shinsoft.DDI.Bll
{
    public class MailBll : BaseMailBll
    {
        #region Constructs

        public MailBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public MailBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public MailBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public MailBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region internal

        internal BizResult<Mail> CreateMail(string mailTemplateCode, Dictionary<string, string> datas, List<Attachment>? attachments = null)
        {
            var result = new BizResult<Mail>();

            var mailTemplate = this.GetEntity<MailTemplate>(p => p.Code == mailTemplateCode);

            if (mailTemplate == null)
            {
                result.Error($"没有找到【{mailTemplateCode}】对应的邮件模板");
            }
            else
            {
                var sbSubject = new StringBuilder(mailTemplate.Subject);
                var sbContent = new StringBuilder(mailTemplate.Content);

                foreach (var data in datas)
                {
                    var key = data.Key.Trim();
                    var value = data.Value;

                    if (!key.StartsWith("{#"))
                    {
                        key = "{#" + key;
                    }

                    if (!key.EndsWith("#}"))
                    {
                        key += "#}";
                    }
                    sbSubject.Replace(key, value);
                    sbContent.Replace(key, value);
                }

                var mail = new Mail
                {
                    ID = CombGuid.NewGuid(),
                    Subject = sbSubject.ToString(),
                    Content = sbContent.ToString(),
                };

                if (attachments?.Any() == true)
                {
                    mail.MailAttachments = new List<MailAttachment>();

                    var dicts = new Dictionary<string, int>();
                    foreach (var attachment in attachments)
                    {
                        var mailAttachment = attachment.ToMailAttachment();
                        mailAttachment.ID = CombGuid.NewGuid();
                        mailAttachment.MailId = mail.ID;

                        if (mail.MailAttachments.Any(p => p.FileName == attachment.FileName))
                        {
                            if (mail.MailAttachments.All(p => p.FileIndexId != attachment.FileIndexId))
                            {
                                int index = mailAttachment.FileName.LastIndexOf('.');
                                var ext = index == -1 ? "" : mailAttachment.FileName.Substring(index);

                                if (dicts.ContainsKey(mailAttachment.FileName))
                                {
                                    var dict = dicts[mailAttachment.FileName];
                                    dicts[mailAttachment.FileName] = dict + 1;

                                    mailAttachment.FileName = mailAttachment.FileName.Substring(0, mailAttachment.FileName.Length - (ext ?? "").Length) + "(" + (dict + 1).ToString() + ")" + ext;
                                }
                                else
                                {
                                    dicts.Add(mailAttachment.FileName, 1);
                                    mailAttachment.FileName = mailAttachment.FileName.Substring(0, mailAttachment.FileName.Length - (ext ?? "").Length) + "(1)" + ext;
                                }

                                mail.MailAttachments.Add(mailAttachment);
                            }
                        }
                        else
                        {
                            mail.MailAttachments.Add(mailAttachment);
                        }
                    }
                }

                result.Data = mail;
            }

            return result;
        }

        internal BizResult<List<Mail>> AddMail(MailTrigger trigger, Guid objectId)
        {
            var result = new BizResult<List<Mail>>();

            switch (trigger)
            {
                default:
                    result.Error($"没有对应{trigger.GetDesc()}的邮件构造方法");
                    break;
            }

            if (result.Success)
            {
                var mails = result.Data.Value();

                if (mails.Any())
                {
                    foreach (var mail in mails)
                    {
                        mail.EnumTrigger = trigger;
                        mail.ObjectId = objectId.ToString();

                        mail.EnumStatus = MailStatus.Ready;

                        this.AddMail(mail);
                    }
                }
            }

            return result;
        }

        #endregion internal

        #region AddMail

        public BizResult<Mail> AddMail(Mail mail)
        {
            var result = new BizResult<Mail>();

            if (mail.ID.IsEmpty())
            {
                mail.ID = CombGuid.NewGuid();
            }

            var mailAttachments = new List<MailAttachment>();

            if (mail.MailAttachments?.Any() == true)
            {
                var fileIndexIds = mail.MailAttachments.Select(p => p.FileIndexId).ToList();
                var fileindexs = this.FileRepo.GetEntities<FileIndex>(p => fileIndexIds.Contains(p.ID));
                if (fileindexs.Any(p => !p.BaseFolder.IsEmpty() && !p.SubPath.IsEmpty()))
                {
                    mail.AttachmentPaths = String.Join(",", fileindexs.Where(p => !p.BaseFolder.IsEmpty() && !p.SubPath.IsEmpty()).Select(p => p.BaseFolder + p.SubPath));
                }

                foreach (var mailAttachment in mail.MailAttachments)
                {
                    if (mailAttachment.ID.IsEmpty())
                    {
                        mailAttachment.ID = CombGuid.NewGuid();
                    }
                    mailAttachment.MailId = mail.ID;

                    mailAttachments.Add(this.Add(mailAttachment, false));
                }
            }

            mail = this.Add(mail, false);

            this.SaveChanges();

            return result;
        }

        #endregion AddMail

        #region SendMail

        protected BizResult SendMail(Mail mail, bool save)
        {
            return this.SendMail(mail, null, null, save);
        }

        protected BizResult SendMail(Mail mail, List<MailAttachment>? mailAttachments, MailServer? mailServer, bool save)
        {
            var result = new BizResult();

            try
            {
                mailAttachments ??= this.GetEntities<MailAttachment>(p => p.MailId == mail.ID);

                if (mailAttachments?.Any() == true)
                {
                    var fileIndexIds = mailAttachments.Select(p => p.FileIndexId);

                    var fileIndexs = this.FileRepo.GetEntities<FileIndex>(FileIndex.Inverses.FileContent, p => fileIndexIds.Contains(p.ID));

                    foreach (var mailAttachment in mailAttachments)
                    {
                        var fileIndex = fileIndexs.FirstOrDefault(p => p.ID == mailAttachment.FileIndexId);

                        if (fileIndex == null)
                        {
                            result.Error($"找不到邮件附件[{mailAttachment.FileName}]");
                        }
                        else
                        {
                            mailAttachment.FileStream = this.FileBll.GetFileStream(fileIndex);
                        }
                    }

                    mail.MailAttachments = mailAttachments;
                }

                if (this.CompanyCache.MailSendType == MailSendType.SMTP)
                {
                    if (Config.Mail.Send)
                    {
                        mailServer ??= this.GetEntity<MailServer>(MailServer.Inverses.SmtpServer, p => p.Valid);

                        if (mailServer == null)
                        {
                            result.Error("没有可用的邮件服务器");
                        }
                        else
                        {
                            if (!mailServer.SendMail(mail))
                            {
                                result.Error(mail.SendMessage);
                            }

                            this.Update(mail, save);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Error(ex.Message);

                mail.SendTime = DateTime.Now;
                mail.SendCount += 1;
                mail.SendMessage = ex.Message;

                if (this.CompanyCache.MailSendType == MailSendType.SMTP)
                {
                    mail.EnumStatus = MailStatus.Resend;
                }

                this.Update(mail, save);
            }

            return result;
        }

        public BizResult SendMail(Guid id)
        {
            var result = new BizResult();

            var mail = this.Get<Mail>(id);

            if (mail == null)
            {
                result.Error("邮件不存在");
            }
            else
            {
                var sendResult = this.SendMail(mail, true);

                result.Merge(sendResult);
            }

            return result;
        }

        private static readonly List<MailStatus> _toSendmailStatuses = new()
        {
            MailStatus.Ready,
            MailStatus.Resend
        };

        public BizResult SendMails()
        {
            var result = new BizResult();

            MailServer? mailServer = null;

            if (this.CompanyCache.MailSendType == MailSendType.SMTP)
            {
                mailServer = this.GetEntity<MailServer>(MailServer.Inverses.SmtpServer, p => p.Valid);

                if (mailServer == null)
                {
                    result.Error("没有可用的邮件服务器");
                }
            }

            if (result.Success)
            {
                var mails = this.GetEntities<Mail>(p => _toSendmailStatuses.Contains(p.EnumStatus));

                var allMailAttachments = this.GetEntities<MailAttachment>(p => _toSendmailStatuses.Contains(p.Mail.EnumStatus));

                foreach (var mail in mails)
                {
                    // mailServer.SendMail
                    var mailAttachments = allMailAttachments.Where(p => p.MailId == mail.ID).ToList();

                    var sendResult = this.SendMail(mail, mailAttachments, mailServer, true);

                    result.Merge(sendResult);

                    if (mailServer?.SendInterval > 0)
                    {
                        Thread.Sleep(mailServer.SendInterval);
                    }
                }
            }

            return result;
        }

        public BizResult SendMails(List<Expression<Func<Mail, bool>>> exps, int? maxSendCount)
        {
            var result = new BizResult();

            MailServer? mailServer = null;

            var seedCount = maxSendCount;

            if (this.CompanyCache.MailSendType == MailSendType.SMTP)
            {
                mailServer = this.GetEntity<MailServer>(MailServer.Inverses.SmtpServer, p => p.Valid);

                if (mailServer == null)
                {
                    result.Error("没有可用的邮件服务器");
                }
                else
                {
                    seedCount = seedCount ?? mailServer.MaxRetry;
                }
            }

            if (result.Success)
            {
                exps.Add(x => x.SendCount < seedCount);

                var mails = this.GetEntities<Mail>(exps);
                var mailIds = mails.Select(p => p.ID).ToList();

                var allMailAttachments = this.GetEntities<MailAttachment>(p => mailIds.Contains(p.MailId));

                foreach (var mail in mails)
                {
                    // mailServer.SendMail
                    var mailAttachments = allMailAttachments.Where(p => p.MailId == mail.ID).ToList();

                    var sendResult = this.SendMail(mail, mailAttachments, mailServer, true);

                    result.Merge(sendResult);

                    if (mailServer?.SendInterval > 0)
                    {
                        Thread.Sleep(mailServer.SendInterval);
                    }
                }
            }

            return result;
        }

        #endregion SendMail
    }
}