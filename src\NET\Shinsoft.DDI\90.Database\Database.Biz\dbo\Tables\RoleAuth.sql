﻿--角色权限
CREATE TABLE [dbo].[RoleAuth]
(
    [ID]                            UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_RoleAuth_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                     UNIQUEIDENTIFIER            NOT NULL,
    [RoleId]                        UNIQUEIDENTIFIER            NOT NULL,
    [AuthId]                        UNIQUEIDENTIFIER            NOT NULL,
    [AllowAllTags]                  BIT                         NULL,
    CONSTRAINT [PK_RoleAuth] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_RoleAuth_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_RoleAuth_Role_00_Role_RoleAuths] FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Role] ([ID]),
    CONSTRAINT [FK_RoleAuth_Auth_00_Auth] FOREIGN KEY ([AuthId]) REFERENCES [dbo].[Auth] ([ID]),
);

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'角色权限',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RoleAuth',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'权限ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RoleAuth',
    @level2type = N'COLUMN',
    @level2name = N'AuthId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'角色ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RoleAuth',
    @level2type = N'COLUMN',
    @level2name = N'RoleId'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'允许全部权限标签',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'RoleAuth',
    @level2type = N'COLUMN',
    @level2name = N'AllowAllTags'