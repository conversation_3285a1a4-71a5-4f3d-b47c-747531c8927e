﻿using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    /// <summary>
    /// 基础数据维护
    /// </summary>
    [ApiExplorerSettings(GroupName = "数据维护")]
    public class BasicMasterDataController : BaseApiController<BasicMasterDataBll>
    {
        #region Organization

        /// <summary>
        /// 获取组织架构树
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.Organization_Query)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Department)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Department_Add)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Department_Edit)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Department_Delete)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Department_CostCenter)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Department_Valid)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station_Add)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station_Edit)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station_Delete)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station_Employee)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station_Valid)]
        [LogApi(ApiType.Query, Operate = "获取组织架构树")]
        public BizResult<List<VwOrganizationTree>> GetOrganizationTree()
        {
            var cc = this.CompanyCache;

            var roots = cc.Organizations.Where(p => p.Parent == null).ToList();

            var models = roots.Maps<VwOrganizationTree>();

            return this.BizResult(models);
        }

        #endregion Organization

        #region SubCompany

        /// <summary>
        /// 查询分公司
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Query)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage_Add)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage_Edit)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage_Delete)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage_Valid)]
        [LogApi(ApiType.Query, Operate = "查询分公司")]
        public QueryResult<SubCompanyQuery> QuerySubCompany([FromQuery] SubCompanyFilter filter)
        {
            var exps = this.NewExps<SubCompany>();

            return this.Repo.GetDynamicQuery<SubCompany, SubCompanyQuery>(filter, exps);
        }

        /// <summary>
        /// 获取分公司
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Query)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage_Add)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage_Edit)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage_Delete)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage_Valid)]
        [LogApi(ApiType.Query, Operate = "获取分公司")]
        public BizResult<SubCompanyModel> GetSubCompany([FromQuery, Required] Guid id)
        {
            var result = new BizResult<SubCompanyModel>();

            var entity = this.Repo.Get<SubCompany>(id);

            if (entity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist,CostCenter.I18ns._Entity);
            }
            else
            {
                var model = entity.Map<SubCompanyModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增分公司
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增分公司")]
        public BizResult<SubCompanyModel> AddSubCompany(SubCompanyModel model)
        {
            var entity = model.Map<SubCompany>();

            var result = this.Repo.AddSubCompany(entity);

            return result.Map<SubCompanyModel>();
        }

        /// <summary>
        /// 编辑分公司
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑分公司")]
        public BizResult<SubCompanyModel> UpdateSubCompany(SubCompanyModel model)
        {
            var entity = model.Map<SubCompany>();

            var result = this.Repo.UpdateSubCompany(entity);

            return result.Map<SubCompanyModel>();
        }

        /// <summary>
        /// 删除分公司
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage)]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除分公司")]
        public BizResult DeleteSubCompany(SubCompanyModel model)
        {
            return this.Repo.DeleteSubCompany(model.ID);
        }

        /// <summary>
        /// 设置分公司有效性
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.SubCompany_Manage_Valid)]
        [LogApi(ApiType.Save, Operate = "设置分公司有效性")]
        public BizResult<SubCompanyModel> SetSubCompanyValid(SubCompanyModel model)
        {
            var entity = model.Map<SubCompany>();

            var result = this.Repo.SetSubCompanyValid(entity);

            return result.Map<SubCompanyModel>();
        }

        #endregion SubCompany

        #region Department

        /// <summary>
        /// 查询部门
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Query)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Add)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Edit)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Delete)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_CostCenter)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Valid)]
        [LogApi(ApiType.Query, Operate = "查询部门")]
        public QueryResult<DepartmentQuery> QueryDepartment([FromQuery] DepartmentFilter filter)
        {
            var exps = this.NewExps<Department>();

            if (!filter.DepartmentId.IsEmpty() && filter.DepartmentId.HasValue)
            {
                List<Guid> departmentIds = new List<Guid>();
                var checkDepartment = this.Repo.Get<Department>(filter.DepartmentId);
                if (checkDepartment != null)
                {
                    departmentIds = this.Repo.GetEntities<Department>(a => a.UidPath.Contains($"|{checkDepartment.Uid}|")).Select(g => g.ID).ToList();
                }

                exps.And(a => departmentIds.Contains(a.ID));
            }

            return this.Repo.GetDynamicQuery<Department, DepartmentQuery>(filter, exps);
        }

        /// <summary>
        /// 获取部门
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Query)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Add)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Edit)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Delete)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_CostCenter)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Valid)]
        [LogApi(ApiType.Query, Operate = "获取部门")]
        public BizResult<DepartmentModel> GetDepartment([FromQuery, Required] Guid id)
        {
            var result = new BizResult<DepartmentModel>();

            var entity = this.Repo.Get<Department>(id);

            if (entity == null)
            {
                result.Error("部门不存在");
            }
            else
            {
                var model = entity.Map<DepartmentModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增部门
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增部门")]
        public BizResult<DepartmentModel> AddDepartment(DepartmentModel model)
        {
            var entity = model.Map<Department>();

            if (!model.ParentId.HasValue && !model.ParentCode.IsEmpty())
            {
                var parent = this.Repo.GetEntity<Department>(p => p.Code == model.ParentCode);

                entity.ParentId = parent?.ID;
            }

            var result = this.Repo.AddDepartment(entity);

            return result.Map<DepartmentModel>();
        }

        /// <summary>
        /// 编辑部门
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑部门")]
        public BizResult<DepartmentModel> UpdateDepartment(DepartmentModel model)
        {
            var entity = model.Map<Department>();

            if (!model.ParentId.HasValue && !model.ParentCode.IsEmpty())
            {
                var parent = this.Repo.GetEntity<Department>(p => p.Code == model.ParentCode);

                entity.ParentId = parent?.ID;
            }

            var result = this.Repo.UpdateDepartment(entity);

            return result.Map<DepartmentModel>();
        }

        /// <summary>
        /// 删除部门
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除部门")]
        public BizResult DeleteDepartment(DepartmentModel model)
        {
            return this.Repo.DeleteDepartment(model.ID);
        }

        /// <summary>
        /// 设置部门有效性
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Valid)]
        [LogApi(ApiType.Save, Operate = "设置部门有效性")]
        public BizResult<DepartmentModel> SetDepartmentValid(DepartmentModel model)
        {
            var entity = model.Map<Department>();

            var result = this.Repo.SetDepartmentValid(entity);

            return result.Map<DepartmentModel>();
        }

        #endregion Department

        #region CostCenter

        /// <summary>
        /// 查询成本中心
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Query)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage_Add)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage_Edit)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage_Delete)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage_Valid)]
        [LogApi(ApiType.Query, Operate = "查询成本中心")]
        public QueryResult<CostCenterQuery> QueryCostCenter([FromQuery] CostCenterFilter filter)
        {
            var exps = this.NewExps<CostCenter>();

            return this.Repo.GetDynamicQuery<CostCenter, CostCenterQuery>(filter, exps);
        }

        /// <summary>
        /// 获取成本中心
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Query)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage_Add)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage_Edit)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage_Delete)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage_Valid)]
        [LogApi(ApiType.Query, Operate = "获取成本中心")]
        public BizResult<CostCenterModel> GetCostCenter([FromQuery, Required] Guid id)
        {
            var result = new BizResult<CostCenterModel>();

            var entity = this.Repo.Get<CostCenter>(id);

            if (entity == null)
            {
                result.Error("成本中心不存在");
            }
            else
            {
                var model = entity.Map<CostCenterModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增成本中心
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增成本中心")]
        public BizResult<CostCenterModel> AddCostCenter(CostCenterModel model)
        {
            var entity = model.Map<CostCenter>();

            if (!model.ParentId.HasValue && !model.ParentCode.IsEmpty())
            {
                var parent = this.Repo.GetEntity<CostCenter>(p => p.Code == model.ParentCode);

                entity.ParentId = parent?.ID;
            }

            var result = this.Repo.AddCostCenter(entity);

            return result.Map<CostCenterModel>();
        }

        /// <summary>
        /// 编辑成本中心
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑成本中心")]
        public BizResult<CostCenterModel> UpdateCostCenter(CostCenterModel model)
        {
            var entity = model.Map<CostCenter>();

            //原有代码
            //if (!model.ParentId.HasValue && !model.ParentCode.IsEmpty())
            if (model.ParentId.HasValue)
            {
                //原有代码
                //var parent = this.Repo.GetEntity<CostCenter>(p => p.Code == model.ParentCode);
                var parent = this.Repo.GetEntity<CostCenter>(p => p.ID == model.ParentId);

                entity.ParentId = parent?.ID;
            }

            var result = this.Repo.UpdateCostCenter(entity);

            return result.Map<CostCenterModel>();
        }

        /// <summary>
        /// 删除成本中心
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除成本中心")]
        public BizResult DeleteCostCenter(CostCenterModel model)
        {
            return this.Repo.DeleteCostCenter(model.ID);
        }

        /// <summary>
        /// 设置成本中心有效性
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage)]
        [Auth(AuthCodes.MasterData.Basic.CostCenter_Manage_Valid)]
        [LogApi(ApiType.Save, Operate = "设置成本中心有效性")]
        public BizResult<CostCenterModel> SetCostCenterValid(CostCenterModel model)
        {
            var entity = model.Map<CostCenter>();

            var result = this.Repo.SetCostCenterValid(entity);

            return result.Map<CostCenterModel>();
        }

        #endregion CostCenter

        #region DepartmentCostCenter

        /// <summary>
        /// 查询部门成本中心
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_CostCenter)]
        [Auth(AuthCodes.MasterData.Basic.Department_Query)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Add)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Edit)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Delete)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_CostCenter)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_Valid)]
        [LogApi(ApiType.Query, Operate = "查询部门成本中心")]
        public QueryResult<DepartmentCostCenterQuery> QueryDepartmentCostCenter([FromQuery] DepartmentCostCenterFilter filter)
        {
            var exps = this.NewExps<DepartmentCostCenter>();

            if (!filter.DepartmentId.IsEmpty() && filter.DepartmentId.HasValue)
            {
                List<Guid> departmentIds = new List<Guid>();
                var checkDepartment = this.Repo.Get<Department>(filter.DepartmentId);
                if (checkDepartment != null)
                {
                    departmentIds = this.Repo.GetEntities<Department>(a => a.UidPath.Contains($"|{checkDepartment.Uid}|")).Select(g => g.ID).ToList();
                }

                exps.And(a => departmentIds.Contains(a.DepartmentId));
            }

            return this.Repo.GetDynamicQuery<DepartmentCostCenter, DepartmentCostCenterQuery>(filter, exps);
        }

        /// <summary>
        /// 新增部门成本中心
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_CostCenter)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_CostCenter)]
        [LogApi(ApiType.Save, Operate = "新增部门成本中心")]
        public BizResult<DepartmentCostCenterModel> AddDepartmentCostCenter(DepartmentCostCenterModel model)
        {
            var entity = model.Map<DepartmentCostCenter>();

            if (model.DepartmentId.IsEmpty() && !model.DepartmentCode.IsEmpty())
            {
                var department = this.Repo.GetEntity<Department>(p => p.Code == model.DepartmentCode);

                entity.DepartmentId = department?.ID ?? Guid.Empty;
            }

            if (model.CostCenterId.IsEmpty() && !model.CostCenterCode.IsEmpty())
            {
                var costCenter = this.Repo.GetEntity<CostCenter>(p => p.Code == model.CostCenterCode);

                entity.CostCenterId = costCenter?.ID ?? Guid.Empty;
            }

            var result = this.Repo.AddDepartmentCostCenter(entity);

            return result.Map<DepartmentCostCenterModel>();
        }

        /// <summary>
        /// 批量新增部门成本中心
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_CostCenter)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_CostCenter)]
        [LogApi(ApiType.Save, Operate = "新增部门成本中心")]
        public BizResult<List<DepartmentCostCenterModel>> AddDepartmentCostCenters(List<DepartmentCostCenterModel> models)
        {
            var entities = models.Maps<DepartmentCostCenter>();

            var result = this.Repo.AddDepartmentCostCenters(entities);

            return result.Maps<DepartmentCostCenterModel>();
        }

        /// <summary>
        /// 移除部门成本中心
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_CostCenter)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_CostCenter)]
        [LogApi(ApiType.Save, Operate = "移除部门成本中心")]
        public BizResult RemoveDepartmentCostCenter(DepartmentCostCenterModel model)
        {
            return this.Repo.RemoveDepartmentCostCenter(model.ID);
        }

        /// <summary>
        /// 设置部门默认成本中心
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_CostCenter)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Department_Manage_CostCenter)]
        [LogApi(ApiType.Save, Operate = "设置部门默认成本中心")]
        public BizResult<DepartmentCostCenterModel> SetDepartmentCostCenterDefault(DepartmentCostCenterModel model)
        {
            var entity = model.Map<DepartmentCostCenter>();

            var result = this.Repo.SetDepartmentCostCenterDefault(entity);

            return result.Map<DepartmentCostCenterModel>();
        }

        #endregion DepartmentCostCenter

        #region Position

        /// <summary>
        /// 查询职位
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.Position_Query)]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage_Add)]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage_Edit)]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage_Delete)]
        [LogApi(ApiType.Query, Operate = "查询职位")]
        public QueryResult<PositionQuery> QueryPosition([FromQuery] PositionFilter filter)
        {
            var exps = this.NewExps<Position>();

            return this.Repo.GetDynamicQuery<Position, PositionQuery>(filter, exps);
        }

        /// <summary>
        /// 获取职位
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.Position_Query)]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage_Add)]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage_Edit)]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage_Delete)]
        [LogApi(ApiType.Query, Operate = "获取职位")]
        public BizResult<PositionModel> GetPosition([FromQuery, Required] Guid id)
        {
            var result = new BizResult<PositionModel>();

            var entity = this.Repo.Get<Position>(id);

            if (entity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, Position.I18ns._Entity);
            }
            else
            {
                var model = entity.Map<PositionModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增职位
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增职位")]
        public BizResult<PositionModel> AddPosition(PositionModel model)
        {
            var entity = model.Map<Position>();

            var result = this.Repo.AddPosition(entity);

            return result.Map<PositionModel>();
        }

        /// <summary>
        /// 编辑职位
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑职位")]
        public BizResult<PositionModel> UpdatePosition(PositionModel model)
        {
            var entity = model.Map<Position>();

            var result = this.Repo.UpdatePosition(entity);

            return result.Map<PositionModel>();
        }

        /// <summary>
        /// 删除职位
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Position_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除职位")]
        public BizResult DeletePosition(PositionModel model)
        {
            return this.Repo.DeletePosition(model.ID);
        }

        #endregion Position

        #region Station

        /// <summary>
        /// 查询挂岗信息
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station)]
        [Auth(AuthCodes.MasterData.Basic.Station_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Station_Query)]
        [LogApi(ApiType.Query, Operate = "查询挂岗信息")]
        public QueryResult<StationQuery> QueryStation([FromQuery] StationFilter filter)
        {
            var exps = this.NewExps<Station>();

            if (!filter.DepartmentId.IsEmpty() && filter.DepartmentId.HasValue)
            {
                List<Guid> departmentIds = new List<Guid>();
                var checkDepartment = this.Repo.Get<Department>(filter.DepartmentId);
                if (checkDepartment != null)
                {
                    departmentIds = this.Repo.GetEntities<Department>(a => a.UidPath.Contains($"|{checkDepartment.Uid}|")).Select(g => g.ID).ToList();
                }

                exps.And(a => departmentIds.Contains(a.DepartmentId));
            }

            return this.Repo.GetDynamicQuery<Station, StationQuery>(filter, exps);
        }

        /// <summary>
        /// 获取岗位
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Station_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Station_Query)]
        [Auth(AuthCodes.MasterData.Basic.Station_Manage_Add)]
        [Auth(AuthCodes.MasterData.Basic.Station_Manage_Edit)]
        [Auth(AuthCodes.MasterData.Basic.Station_Manage_Delete)]
        [LogApi(ApiType.Query, Operate = "获取岗位")]
        public BizResult<StationModel> GetStation([FromQuery, Required] Guid id)
        {
            var result = new BizResult<StationModel>();

            var entity = this.Repo.Get<Station>(id);

            if (entity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, Station.I18ns._Entity);
            }
            else
            {
                var model = entity.Map<StationModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增岗位
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Station_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Station_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增岗位")]
        public BizResult<StationModel> AddStation(StationModel model)
        {
            var entity = model.Map<Station>();

            var result = this.Repo.AddStation(entity);

            return result.Map<StationModel>();
        }

        /// <summary>
        /// 编辑岗位
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Station_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Station_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑岗位")]
        public BizResult<StationModel> UpdateStation(StationModel model)
        {
            var entity = model.Map<Station>();

            var result = this.Repo.UpdateStation(entity);

            return result.Map<StationModel>();
        }

        /// <summary>
        /// 删除岗位
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Station_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Station_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除岗位")]
        public BizResult DeleteStation(StationModel model)
        {
            return this.Repo.DeleteStation(model.ID);
        }

        #endregion

        #region EmployeeStation

        /// <summary>
        /// 查询人员挂岗信息
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station_Employee)]
        [LogApi(ApiType.Query, Operate = "查询人员挂岗信息")]
        public QueryResult<EmployeeStationQuery> QueryEmployeeStation([FromQuery] EmployeeStationFilter filter)
        {
            var exps = this.NewExps<EmployeeStation>();

            if (!filter.DepartmentId.IsEmpty() && filter.DepartmentId.HasValue)
            {
                List<Guid> departmentIds = new List<Guid>();
                var checkDepartment = this.Repo.Get<Department>(filter.DepartmentId);
                if (checkDepartment != null)
                {
                    departmentIds = this.Repo.GetEntities<Department>(a => a.UidPath.Contains($"|{checkDepartment.Uid}|")).Select(g => g.ID).ToList();
                }

                exps.And(a=> departmentIds.Contains(a.Station.DepartmentId));
            }

            return this.Repo.GetDynamicQuery<EmployeeStation, EmployeeStationQuery>(filter, exps);
        }

        /// <summary>
        /// 获取人员挂岗
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station_Employee)]
        [LogApi(ApiType.Query, Operate = "获取人员挂岗")]
        public BizResult<EmployeeStationModel> GetEmployeeStation([FromQuery, Required] Guid id)
        {
            var result = new BizResult<EmployeeStationModel>();

            var entity = this.Repo.Get<EmployeeStation>(id);

            if (entity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, Station.I18ns._Entity);
            }
            else
            {
                var model = entity.Map<EmployeeStationModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 删除人员挂岗信息
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station_Employee)]
        [LogApi(ApiType.Save, Operate = "删除人员挂岗信息")]
        public BizResult DeleteEmployeeStation(EmployeeStationModel model)
        {
            return this.Repo.DeleteEmployeeStation(model.ID);
        }

        /// <summary>
        /// 新增人员挂岗信息
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station_Employee)]
        [LogApi(ApiType.Save, Operate = "新增人员挂岗信息")]
        public BizResult AddEmployeeStation(List<EmployeeStationModel> models)
        {
            var entities = models.Maps<EmployeeStation>();

            return this.Repo.AddEmployeeStation(entities);
        }

        /// <summary>
        /// 编辑岗位
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station)]
        [Auth(AuthCodes.MasterData.Basic.Organization_Manage_Station_Employee)]
        [LogApi(ApiType.Save, Operate = "编辑岗位")]
        public BizResult<EmployeeStationModel> UpdateEmployeeStation(EmployeeStationModel model)
        {
            var entity = model.Map<EmployeeStation>();

            var result = this.Repo.UpdateEmployeeStation(entity);

            return result.Map<EmployeeStationModel>();
        }
        #endregion
    }
}
