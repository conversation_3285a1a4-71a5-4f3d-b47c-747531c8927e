<script setup lang="ts">
import { authorizeApi } from "@/api/authorize";

defineOptions({
  name: "employee:view:info"
});

/**
 * 定义属性
 */
const props = defineProps({
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {}
  },

  // loading：控制变量
  loading: {
    form: false
  }
});

const model = defineModel<Record<string, any>>("modelValue", {
  required: true,
  default: {}
});
</script>

<template>
  <div>
    <el-form
      ref="formRef"
      v-loading="cfg.loading.form"
      :model="model"
      label-position="right"
      :label-width="formLabelWidth"
      class="el-dialog-form"
    >
      <el-row :gutter="formGutter">
        <el-col :span="formColSpan">
          <el-form-item prop="loginName" label="登录名">
            <span>{{ model.loginName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="formColSpan">
          <el-form-item prop="displayName" label="姓名">
            <span>{{ model.displayName }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="formGutter">
        <el-col :span="formColSpan">
          <el-form-item prop="jobNo" label="工号">
            <span>{{ model.jobNo }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="formColSpan">
          <el-form-item prop="email" label="邮箱">
            <span>{{ model.email }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="formColSpan">
          <el-form-item prop="enumGender" label="性别">
            <span>{{ model.enumGenderDesc }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="formColSpan">
          <el-form-item prop="enumStatus" label="状态">
            <span>{{ model.enumStatusDesc }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="formColSpan">
          <el-form-item prop="mobile" label="手机">
            <span>{{ model.mobile }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="formColSpan">
          <el-form-item prop="tel" label="电话">
            <span>{{ model.tel }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="formColSpan">
          <el-form-item prop="title" label="职称">
            <span>{{ model.title }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="formColSpan">
          <el-form-item prop="position" label="职务">
            <span>{{ model.position }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="formGutter">
        <el-col :span="24">
          <el-form-item prop="remark" label="备注">
            <span>{{ model.remark }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
