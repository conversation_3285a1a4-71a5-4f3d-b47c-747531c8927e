﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class VwLogFilter
    {
        /// <summary>
        /// 平台
        /// </summary>
        [DynamicQueryColumn(typeof(VwLog), VwLog.Columns.Platform, Operation = Operation.Equal)]
        public string? Platform { get; set; }

        /// <summary>
        /// 系统
        /// </summary>
        [DynamicQueryColumn(typeof(VwLog), VwLog.Columns.Program, Operation = Operation.Equal)]
        public string? Program { get; set; }

        /// <summary>
        /// 操作
        /// </summary>
        [DynamicQueryColumn(typeof(VwLog), VwLog.Columns.Operate, Operation = Operation.Equal)]
        public string? Operate { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        [DynamicQueryColumn(typeof(VwLog), VwLog.Columns.UserDisplayName, Operation = Operation.Equal)]
        public string? UserDisplayName { get; set; }

        /// <summary>
        /// 时间
        /// </summary>
        public List<DateTime?>? LogTime { get; set; }

        /// <summary>
        ///类别
        /// </summary>
        [DynamicQueryColumn(typeof(VwLog), VwLog.Columns.Level, Operation = Operation.Equal)]
        public string? Level { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        [DynamicQueryColumn(typeof(VwLog), VwLog.Columns.Message, Operation = Operation.StringIntelligence)]
        public string? Message { get; set; }

        /// <summary>
        /// 类别
        /// </summary>
        [DynamicQueryColumn(typeof(VwLog), VwLog.Columns.Category, Operation = Operation.Equal)]
        public string? Category { get; set; }
    }
}
