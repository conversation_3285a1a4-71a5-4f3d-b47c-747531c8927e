﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Org.BouncyCastle.Crypto;
using Shinsoft.Core.Caching;
using Shinsoft.Core.Caching.Memory;
using Shinsoft.Core.Caching.Redis;
using Shinsoft.Core.Hosting;
using Shinsoft.Core.I18n;

namespace Shinsoft.DDI.Bll.Caching
{
    public class CompanyCache : BaseCompanyEntityCacheClient<CompanyBll, Guid>, II18nTranslator
    {
        public static CompanyCache GetCompanyCache(Guid companyId)
        {
            var pool = MvcHttpContext.GetService<CompanyCachePool>() ?? HostContext.GetRequiredService<CompanyCachePool>();

            return pool.GetCompanyCache(companyId);
        }

        public object? GetKey(string key)
        {
            return this.CurrentCache.Get<object>(key);
        }

        public CompanyCache(Company company)
        {
            this.Company = company;

            this.InitCompany(company);
        }

        public Company Company { get; private set; }

        public MailSendType MailSendType => this.Company.Cfg?.EnumMailSendType ?? MailSendType.SMTP;

        private Company InitCompany(Company company)
        {
            if (!((IEntity)company).IsNew)
            {
                var cfg = company.Cfg?.Clone();
                company = company.Clone();
                company.Cfg = cfg;

                this.Company = company;
            }

            if (company.Cfg == null)
            {
                var cfg = this.Repo.Get<CompanyCfg>(company.ID);

                if (cfg != null)
                {
                    company.Cfg = cfg.Clone();
                }
            }

            return company;
        }

        public void FlushCompany()
        {
            var company = this.Repo.Get<Company>(this.CompanyId).Value();

            this.Company = this.InitCompany(company);
        }

        #region override

        protected override Guid CompanyId => this.Company.ID;

        protected override CacheType CurrentType => (this.Company.Cfg?.Redis).IsEmpty() ? CacheType.Memory : CacheType.Redis;

        private MemoryCacheClient? _memoryCache;

        protected override MemoryCacheClient MemoryCache => _memoryCache ??= new MemoryCacheClient(this.Company.Code);

        protected override void FlushMemoryCache()
        {
            _memoryCache?.Flush();
            _memoryCache = null;
        }

        private RedisClient? _redisCache;

        protected override RedisClient RedisCache
        {
            get
            {
                if (_redisCache == null)
                {
                    if (this.CurrentType == CacheType.Redis)
                    {
                        _redisCache = new RedisClient((this.Company.Cfg?.Redis).Value());
                    }
                    else
                    {
                        throw new InvalidOperationException(
                            $"{nameof(CompanyCache)}:【{this.Company.Name}】该公司未配置Redis链接"
                        );
                    }
                }

                return _redisCache;
            }
        }

        protected override void FlushRedisCache()
        {
            _redisCache?.Flush();
            _redisCache = null;
        }

        protected override void DisposeRedisClinet()
        {
            _redisCache?.Dispose();
        }

        #endregion override

        #region GetData

        protected List<CultureText> GetClientCultureTexts()
        {
            var datas = new List<CultureText>();

            var cultures = this.CompanyCultures;

            foreach (var culture in cultures)
            {
                var data = this.GetClientCultureText(culture.Culture);

                if (data != null)
                {
                    datas.Add(data);
                }
            }

            return datas;
        }

        protected CultureText? GetClientCultureText(string culture)
        {
            CultureText? data = null;

            if (this.CompanyCultures.Any(c => c.Culture == culture))
            {
                data = new CultureText
                {
                    Culture = culture,
                };

                var i18ns = this.I18ns
                    .Where(p => p.EnumType == I18nType.Culture)
                    .Where(p => p.EnumFlags.HasFlag(I18nFlag.Client))
                    .Where(p => p.CultureText.ContainsKey(culture))
                    .ToList();

                var allTexts = new List<I18nText>();

                if (i18ns.Count > 0)
                {
                    foreach (var i18n in i18ns)
                    {
                        InitI18nText(i18n, allTexts, data);
                    }

                    if (data.Texts.Count > 1)
                    {
                        data.Texts = [.. data.Texts.OrderBy(p => p.Key)];

                        foreach (var text in data.Texts)
                        {
                            OrderI18nText(text);
                        }
                    }
                }
            }

            return data;
        }

        protected static I18nText InitI18nText(I18n i18n, List<I18nText> allTexts, CultureText data)
        {
            var text = allTexts.FirstOrDefault(p => p.Group == i18n.Group && p.Key == i18n.Key);

            var isNew = false;

            if (text == null)
            {
                text = new I18nText
                {
                    Group = i18n.Group,
                    Key = i18n.Key
                };

                allTexts.Add(text);
                isNew = true;
            }

            if (i18n.CultureText.TryGetValue(data.Culture, out string? value))
            {
                text.Text = value;
            }

            if (i18n.Parent == null)
            {
                if (isNew)
                {
                    data.Texts.Add(text);
                }
            }
            else
            {
                var parent = InitI18nText(i18n.Parent, allTexts, data);

                parent.Children ??= [];

                parent.Children.Add(text);
            }

            return text;
        }

        protected static void OrderI18nText(I18nText text)
        {
            if (text.Children?.Count > 0)
            {
                if (text.Children.Count > 1)
                {
                    text.Children = [.. text.Children.OrderBy(p => p.Key)];
                }

                foreach (var child in text.Children)
                {
                    OrderI18nText(child);
                }
            }
        }

        #endregion GetData

        #region GetAll

        protected List<Auth> GetAllAuth()
        {
            var sc = HostContext.GetRequiredService<SysCache>();

            var companyAuths = this.Repo.GetEntities<CompanyAuth>(p => p.Valid);

            var authIds = companyAuths.Select(p => p.AuthId);

            var entities = sc.Auths.Where(p => p.IsInIds(authIds)).ToList();

            return entities;
        }

        protected List<CompanyCulture> GetAllCompanyCulture()
        {
            var entities = this.Repo.GetEntities<CompanyCulture>(p => p.CompanyId == this.CompanyId);

            if (entities.Count == 0)
            {
                var sc = HostContext.GetRequiredService<SysCache>();

                entities = sc.SysCultures.Select(p => new CompanyCulture { Culture = p.Culture, Name = p.Name }).ToList();
            }

            entities = entities
                .OrderBy(p => p.Culture == this.Company.Cfg!.Culture ? 0 : 1)
                .ThenBy(p => p.Culture)
                .ToList();

            return entities;
        }

        protected List<I18n> GetAllI18n()
        {
            var all = this.Repo.GetEntities<I18n>(p => p.Valid && (!p.CompanyId.HasValue || p.CompanyId == this.CompanyId));

            var entities = new List<I18n>();

            var roots = all.Where(p => !p.ParentId.HasValue).ToList();

            foreach (var entity in roots)
            {
                InitValidI18ns(entity, entities, all);
            }

            return entities;
        }

        private static void InitValidI18ns(I18n entity, List<I18n> valids, List<I18n> all)
        {
            if (entity.Valid
                && (entity.CompanyId.HasValue
                    || !all.Any(p => p.ParentId == entity.ParentId && p.Valid
                        && p.CompanyId.HasValue && p.Key == entity.Key))
            )
            {
                valids.Add(entity);

                var children = all.Where(p => p.ParentId == entity.ID).ToList();

                foreach (var child in children)
                {
                    InitValidI18ns(child, valids, all);
                }
            }
        }

        protected List<VueRoute> GetAllVueRoute()
        {
            var all = this.Repo.GetEntities<VueRoute>(p => p.Valid);

            var entities = new List<VueRoute>();

            var roots = all.Where(p => !p.ParentId.HasValue).ToList();

            foreach (var entity in roots)
            {
                InitValidVueRoutes(entity, entities, all);
            }

            return entities;
        }

        private static void InitValidVueRoutes(VueRoute entity, List<VueRoute> valids, List<VueRoute> all)
        {
            if (entity.Valid)
            {
                valids.Add(entity);

                var children = all.Where(p => p.ParentId == entity.ID).ToList();

                foreach (var child in children)
                {
                    InitValidVueRoutes(child, valids, all);
                }
            }
        }

        protected List<VwOrganization> GetAllVwOrganization()
        {
            var types = new List<OrganizationType>
            {
                OrganizationType.HeadOffice,
                OrganizationType.Branch,
                OrganizationType.Department,
                OrganizationType.Station
            };

            var order = $"{VwOrganization.Columns.EnumType} ASC, {VwOrganization.Columns.Grade} DESC, {VwOrganization.Columns.Name} ASC";

            return this.Repo.GetEntities<VwOrganization>(p => types.Contains(p.EnumType), order);
        }

        protected List<SubCompany> GetAllSubCompany()
        {
            return this.Repo.GetEntities<SubCompany>(p => p.Valid);
        }

        protected List<Department> GetAllDepartment()
        {
            return this.Repo.GetEntities<Department>(p => p.Valid);
        }

        protected List<CostCenter> GetAllCostCenter()
        {
            return this.Repo.GetEntities<CostCenter>(p => p.Valid);
        }

        #endregion GetAll

        #region InitClones

        protected virtual List<Auth> InitClones(List<Auth> clones)
        {
            foreach (var clone in clones)
            {
                var children = clones
                    .Where(p => p.ParentId == clone.ID && !p.EnumFlags.HasFlag(AuthFlag.Invisible))
                    .ToList();
                var invisibleChildren = clones
                    .Where(p => p.ParentId == clone.ID && p.EnumFlags.HasFlag(AuthFlag.Invisible))
                    .ToList();

                clone.Children = children;
                clone.InvisibleChilren = invisibleChildren;
            }

            return clones;
        }

        protected virtual List<I18n> InitClones(List<I18n> clones)
        {
            var cultures = this.Repo.GetEntities<I18nCulture>(p => !p.CompanyId.HasValue || p.CompanyId == this.CompanyId);

            var roots = clones.Where(p => !p.ParentId.HasValue).ToList();

            foreach (var root in roots)
            {
                this.InitI18n(root, clones, cultures);
            }

            return clones;
        }

        private void InitI18n(I18n clone, List<I18n> clones, List<I18nCulture> cultures)
        {
            if (clone.Parent != null && clone.EnumFlags == I18nFlag.None)
            {
                clone.EnumFlags |= clone.Parent.EnumFlags;
            }

            if (clone.EnumType == I18nType.Culture)
            {
                clone.CultureText.Clear();

                if (!clone.Text.IsEmpty())
                {
                    clone.CultureText.TryAdd(this.Company.Cfg!.Culture, clone.Text);
                }

                var currCultures = cultures
                    .Where(p => p.I18nId == clone.ID)
                    .OrderBy(p => p.CompanyId.HasValue ? 1 : 0)
                    .ToList();

                foreach (var culture in currCultures)
                {
                    if (clone.CultureText.TryGetValue(culture.Culture, out string? text))
                    {
                        clone.CultureText[culture.Culture] = culture.Text;
                    }
                    else
                    {
                        clone.CultureText.TryAdd(culture.Culture, culture.Text);
                    }
                }
            }

            var children = clones.Where(p => p.ParentId == clone.ID).ToList();

            foreach (var child in children)
            {
                InitI18n(child, clones, cultures);
            }
        }

        protected virtual List<VueRoute> InitClones(List<VueRoute> clones)
        {
            var metas = this.Repo.GetEntities<VueRouteMeta>(p => p.Route.Valid);

            foreach (var clone in clones)
            {
                var meta = metas.FirstOrDefault(p => p.ID == clone.ID);

                clone.Meta = meta?.Clone();
            }

            return clones;
        }

        protected virtual List<VwOrganization> InitClones(List<VwOrganization> clones)
        {
            foreach (var clone in clones)
            {
                clone.SubCompany = this.GetSubCompany(clone.SubCompanyId)!;

                if (clone.DepartmentId.HasValue)
                {
                    clone.Department = this.GetDepartment(clone.DepartmentId.Value);
                }

                if (clone.StationId.HasValue)
                {
                    clone.Station = this.GetStation(clone.StationId.Value);
                }
            }

            return clones;
        }

        protected virtual List<Role> InitClones(List<Role> clones)
        {
            var allRoleAuths = this.Repo.GetEntities<RoleAuth>(p =>
                !p.Role.Deleted && !p.Auth.Deleted && p.Auth.EnumType == AuthType.Permission
            );
            var allRoleAuthTags = this.Repo.GetEntities<RoleAuthTag>(p =>
                !p.Role.Deleted && !p.AuthTag.Deleted
            );

            foreach (var clone in clones)
            {
                var roleAuths = allRoleAuths.Where(p => p.RoleId == clone.ID).ToList();
                var roleAuthTags = allRoleAuthTags.Where(p => p.RoleId == clone.ID).ToList();

                foreach (var roleAuth in roleAuths)
                {
                    var auth = this.GetAuth(roleAuth.AuthId);
                    if (auth != null)
                    {
                        var userAuth = clone.Auths.FirstOrDefault(p => p.AuthCode == auth.Code);

                        if (userAuth == null)
                        {
                            userAuth = UserAuth.Create(auth.Code);
                            clone.Auths.Add(userAuth);
                        }

                        if (auth.EnumAuthTagType == AuthTagType.None)
                        {
                            userAuth.AllowAllTags = null;
                            userAuth.AuthTagCodes = null;
                        }
                        else if (roleAuth.AllowAllTags == true)
                        {
                            userAuth.AllowAllTags = true;
                        }
                        else
                        {
                            userAuth.AllowAllTags = false;

                            var authTagIds = roleAuthTags
                                .Where(p => p.AuthId == auth.ID)
                                .Select(p => p.AuthTagId)
                                .ToList();
                            var authTags = this
                                .AuthTags.Where(p =>
                                    (p.EnumProgramFlags & auth.EnumProgramFlags) != ProgramFlag.None
                                    && authTagIds.Contains(p.ID)
                                )
                                .ToList();

                            var authTagCodes = authTags.Select(p => p.Code).ToList();

                            if (userAuth.AuthTagCodes == null)
                            {
                                userAuth.AuthTagCodes = authTagCodes;
                            }
                            else
                            {
                                userAuth.AuthTagCodes = userAuth
                                    .AuthTagCodes.Union(authTagCodes)
                                    .Distinct()
                                    .ToList();
                            }
                        }
                    }
                }
            }

            return clones;
        }

        protected virtual List<Department> InitClones(List<Department> clones)
        {
            var departmentCostCenters = this.Repo.GetEntities<DepartmentCostCenter>();

            foreach (var clone in clones)
            {
                var costCenterIds = departmentCostCenters
                    .Where(p => p.DepartmentId == clone.ID)
                    .Select(p => p.CostCenterId)
                    .ToList();

                clone.CostCenters = this
                    .CostCenters.Where(p => costCenterIds.Contains(p.ID))
                    .ToList();
            }

            return clones;
        }

        #endregion InitClones

        #region 登录相关缓存，存放于内存中

        #region Auth

        public List<Auth> Auths => this.GetMemoryCaches<Auth>();

        public Auth? GetAuth(Guid id)
        {
            return this.Auths.FirstOrDefault(p => p.ID == id);
        }

        public Auth? GetAuth(string code)
        {
            return this.Auths.FirstOrDefault(p => p.Code == code);
        }

        public List<Auth> GetAuths(List<Guid> ids)
        {
            return this.Auths.Where(p => ids.Contains(p.ID)).ToList();
        }

        public List<Auth> GetAuthsByParent(Guid? parentId = null, bool includeInvisible = false)
        {
            var query = this.Auths.Where(p => p.ParentId == parentId);

            if (!includeInvisible)
            {
                query = query.Where(p => !p.EnumFlags.HasFlag(AuthFlag.Invisible));
            }

            return query.ToList();
        }

        public List<Auth> GetAuthsByParent(string parentCode)
        {
            return this.Auths.Where(p => p.Parent?.Code == parentCode).ToList();
        }

        public Auth? GetAuth(AuthTagType tagType)
        {
            var query = this.Auths.Where(p => p.EnumType == AuthType.Permission);

            query = query.Where(p => p.EnumAuthTagType == tagType);

            var permission = query.FirstOrDefault();

            return permission;
        }

        #endregion Auth

        #region AuthTag

        public List<AuthTag> AuthTags => this.GetMemoryCaches<AuthTag>();

        public AuthTag GetAuthTag(Guid id)
        {
            var entity =
                this.AuthTags.FirstOrDefault(p => p.ID == id)
                ?? throw new InvalidOperationException($"{nameof(SysCache)}:未找到ID为【{id}】的标签");

            return entity;
        }

        public AuthTag? GetAuthTag(AuthTagType type, string code)
        {
            return this.AuthTags.FirstOrDefault(p => p.EnumType == type && p.Code == code);
        }

        public List<AuthTag> GetAuthTags(List<Guid> ids)
        {
            return this.AuthTags.Where(p => ids.Contains(p.ID)).ToList();
        }

        public List<AuthTag> GetAuthTags(List<string> codes)
        {
            return this.AuthTags.Where(p => codes.Contains(p.Code)).ToList();
        }

        public List<AuthTag> GetAuthTags(AuthTagType type)
        {
            return this.AuthTags.Where(p => p.EnumType == type).ToList();
        }

        #endregion AuthTag

        #region Role

        public List<Role> Roles => this.GetMemoryCaches<Role>();

        public Role? GetRole(Guid id)
        {
            return this.Roles.FirstOrDefault(p => p.ID == id);
        }

        public Role? GetRole(string code)
        {
            return this.Roles.FirstOrDefault(p => p.Code == code);
        }

        #endregion Role

        #endregion 登录相关缓存，存放于内存中

        #region CompanySetting

        public List<CompanySetting> CompanySettings => this.GetHashCaches<CompanySetting>();

        public CompanySetting? GetCompanySetting(string key)
        {
            return this.GetHashCache(
                key,
                (field) => this.Repo.GetEntity<CompanySetting>(p => p.Key == field)
            );
        }

        public TValue? GetCompanySetting<TValue>(string key, TValue? defaultValue = default)
        {
            var entity = this.GetCompanySetting(key);

            if (entity == null)
            {
                return defaultValue;
            }
            else
            {
                return entity.Value.As(defaultValue);
            }
        }

        public string GetCompanySettingValue(string key, string defaultValue = "")
        {
            var setting = this.GetCompanySetting(key);

            return setting?.Value ?? defaultValue;
        }

        #endregion CompanySetting

        #region CompanyCurrency

        public List<CompanyCurrency> CompanyCurrencies => this.GetHashCaches<CompanyCurrency>();

        public CompanyCurrency StdCurrency => this.GetStdCurrency();

        public CompanyCurrency? GetCompanyCurrency(string currency)
        {
            return this.GetHashCache(
                currency,
                (field) => this.Repo.GetEntity<CompanyCurrency>(p => p.Currency == field)
            );
        }

        protected CompanyCurrency GetStdCurrency()
        {
            var std = this.GetCompanyCurrency("CNY");

            if (std?.IsStandard != true)
            {
                std = this.CompanyCurrencies.FirstOrDefault(p => p.IsStandard);
            }

            if (std == null)
            {
                throw new InvalidOperationException("没有找到本币币种");
            }

            return std;
        }

        #endregion CompanyCurrency

        #region CompanyCulture

        public List<CompanyCulture> CompanyCultures => this.GetCaches<CompanyCulture>();

        public CompanyCulture? GetCompanyCulture(string culture)
        {
            return this.CompanyCultures.FirstOrDefault(p => p.Culture == culture);
        }

        #endregion CompanyCulture

        #region I18n

        public List<I18n> I18ns => this.GetCaches<I18n>();

        private List<I18n>? _i18nRoots = null;

        public List<I18n> I18nRoots => _i18nRoots ??= this.I18ns.Where(p => !p.ParentId.HasValue).ToList();

        public string Translate(string key, string? culture = null)
        {
            var lastDot = key.LastIndexOf('.');

            string group;

            if (lastDot < 0)
            {
                group = string.Empty;
            }
            else
            {
                group = key[..lastDot];
                key = key[(lastDot + 1)..];
            }

            return this.Translate(group, key, culture);
        }

        public string Translate(string group, string key, string? culture = null)
        {
            if (culture.IsEmpty())
            {
                culture = this.Company.Cfg!.Culture;
            }

            var entity = this.I18ns
                 .Where(p => p.EnumType == I18nType.Culture && p.Group == group && p.Key == key)
                 .FirstOrDefault();

            string? text = null;

            if (entity != null && !entity.CultureText.TryGetValue(culture!, out text))
            {
                text = entity.Text;
            }

            return text ?? (group.IsEmpty() ? key : $"{group}.{key}");
        }

        #endregion I18n

        #region VueRoute

        public List<VueRoute> VueRoutes => this.GetCaches<VueRoute>();

        #endregion VueRoute

        #region ClientCultures

        private const string ClientCultureKey = "ClientCulture";

        public List<CultureText> ClientCultures => this.GetHashCaches<CultureText>(this.GetClientCultureTexts, ClientCultureKey);

        public CultureText? GetClientCulture(string culture)
        {
            return this.GetHashCache<CultureText>(culture, this.GetClientCultureText, this.GetClientCultureTexts, ClientCultureKey);
        }

        public List<I18nText> GetClientTexts(string culture)
        {
            var clientCulture = this.GetClientCulture(culture);

            return clientCulture?.Texts ?? [];
        }

        public void RemoveI18nCache()
        {
            this.RemoveCache<I18n>();
            this.RemoveKey(ClientCultureKey);
        }

        #endregion ClientCultures

        #region Calendar

        public List<Calendar> Calendars => this.GetCaches<Calendar>();

        protected void InitCalendar(DateTime date)
        {
            var bll = this.GetRepo<BasicMasterDataBll>();

            bll.InitCalendar(date);

            this.RemoveCache<Calendar>();
        }

        public Calendar GetCalendar(DateTime date)
        {
            date = date.Date;

            var calendar = this.Calendars.FirstOrDefault(p => p.Date == date);

            if (calendar == null)
            {
                this.InitCalendar(date);

                calendar = this.GetCalendar(date);
            }

            return calendar;
        }

        #endregion Calendar

        #region Dict

        public List<Dict> Dicts => this.GetCaches<Dict>();

        public Dict? GetDict(Guid id)
        {
            return this.Dicts.FirstOrDefault(p => p.ID == id);
        }

        public Dict? GetDict(string code)
        {
            return this.Dicts.FirstOrDefault(p => p.Code == code);
        }

        public Dict? GetDict(Guid? parentId, string code)
        {
            return this.Dicts.FirstOrDefault(p => p.ParentId == parentId && p.Code == code);
        }

        public Dict? GetDictByName(Guid? parentId, string name)
        {
            return this.Dicts.FirstOrDefault(p => p.ParentId == parentId && p.Name == name);
        }

        public Dict? GetDict(string parentCode, string code)
        {
            return this.Dicts.FirstOrDefault(p => p.Parent?.Code == parentCode && p.Code == code);
        }

        public Dict? GetDictByName(string parentCode, string name)
        {
            return this.Dicts.FirstOrDefault(p => p.Parent?.Code == parentCode && p.Name == name);
        }

        public List<Dict> GetDictsByParent(Guid parentId)
        {
            return this.Dicts.Where(p => p.ParentId == parentId).ToList();
        }

        public List<Dict> GetDictsByParent(string parentCode)
        {
            return this.Dicts.Where(p => p.Parent?.Code == parentCode).ToList();
        }

        #endregion Dict

        #region Organization

        public List<VwOrganization> Organizations => this.GetCaches<VwOrganization>();

        public VwOrganization? GetOrganization(string id)
        {
            return this.Organizations.FirstOrDefault(p => p.ID == id);
        }

        #endregion Organization

        #region SubCompany

        public List<SubCompany> SubCompanies => this.GetCaches<SubCompany>();

        public SubCompany? GetSubCompany(Guid id)
        {
            return this.SubCompanies.FirstOrDefault(p => p.ID == id);
        }

        public SubCompany? GetSubCompany(string code)
        {
            return this.SubCompanies.FirstOrDefault(p => p.Code == code);
        }

        #endregion SubCompany

        #region Department

        public List<Department> Departments => this.GetCaches<Department>();

        public Department? GetDepartment(Guid id)
        {
            return this.Departments.FirstOrDefault(p => p.ID == id);
        }

        public Department? GetDepartment(string code)
        {
            return this.Departments.FirstOrDefault(p => p.Code == code);
        }

        #endregion Department

        #region Station

        public List<Station> Stations => this.GetCaches<Station>();

        public Station? GetStation(Guid id)
        {
            return this.Stations.FirstOrDefault(p => p.ID == id);
        }

        #endregion Station

        #region CostCenter

        public List<CostCenter> CostCenters => this.GetCaches<CostCenter>();

        public CostCenter? GetCostCenter(Guid id)
        {
            return this.CostCenters.FirstOrDefault(p => p.ID == id);
        }

        public CostCenter? GetCostCenter(string code)
        {
            return this.CostCenters.FirstOrDefault(p => p.Code == code);
        }

        #endregion CostCenter
    }
}