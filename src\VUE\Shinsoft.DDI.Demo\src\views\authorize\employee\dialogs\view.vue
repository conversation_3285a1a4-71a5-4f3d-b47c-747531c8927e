<script setup lang="ts">
import { authorizeApi } from "@/api/authorize";
import { useUserStoreHook } from "@/store/modules/user";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElMessage, ElMessageBox } from "element-plus";
import EmployeeInfo from "../components/employeeInfo.vue";
import EmployeeRoles from "../components/employeeRoles.vue";
import EmployeeStations from "../components/employeeStations.vue";

defineOptions({
  name: "employee:view"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // 标题：查看
  viewTitle: {
    type: String,
    default: "查看员工"
  },
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.model?.displayName
    ? `${props.viewTitle} - ${state.model.displayName}`
    : props.viewTitle;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      enumStatus: 1
    },
    tabsIndex: "employeeInfo"
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    tabs: false,
    btn: false
  },
  // 按钮权限
  btn: {
    delete: computed(() => {
      return (
        state.model.id &&
        state.model.id != userStore.info?.employeeId &&
        userStore.hasAnyAuth(["Employee:Manage", "Employee:Manage:Delete"])
      );
    })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  model: cfg.default.model as Record<string, any>,
  tabsIndex: cfg.default.tabsIndex
});

/**
 * 当前用户存储
 */
const userStore = useUserStoreHook();

/**
 * 当前组件ref
 */
const employeeInfoRef = ref();
const employeeRolesRef = ref();
const employeeStationsRef = ref();

/**
 * 初始化组件（异步）
 */
const init = () => {
  // 防止之前打开的数据残留，因此初始化时赋予state默认值
  initState();

  get();
};

/**
 * 设置model数据
 */
const setModel = (data: any) => {
  state.model = data;
  employeeRolesRef.value.init();
  employeeStationsRef.value.init();
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 初始化state数据
 */
const initState = () => {
  state.model = cfg.default.model;
  state.tabsIndex = cfg.default.tabsIndex;
};

/**
 * 清空mstate数据
 */
const clearState = () => {
  if (state.id) {
    employeeRolesRef.value.clear();
    employeeStationsRef.value.clear();
  }

  initState();
};

/**
 * 获取model数据
 */
const get = () => {
  if (state.id) {
    cfg.loading.tabs = true;
    authorizeApi
      .GetEmployee(state.id)
      .then(res => {
        if (res.success) {
          setModel(res.data);
        } else {
          close();
        }
      })
      .finally(() => {
        cfg.loading.tabs = false;
      });
  }
};

/**
 * 按钮事件：【删除】
 */
const del = (data?: any) => {
  // 内部调用时，data为空
  // 外部调用时，需要传入{id:""}的对象
  data = data ?? getModel();

  if (data && data.id) {
    ElMessageBox.confirm(
      `请确认是否要删除员工【${data.displayName}】?<br />提示：删除后将不可恢复!`,
      "删除确认",
      {
        showClose: false,
        closeOnClickModal: false,
        draggable: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "error"
      }
    )
      .then(() => {
        cfg.loading.btn = true;
        authorizeApi
          .DeleteEmployee(data)
          .then(res => {
            // 仅提示
            if (res.success) {
              refresh("删除成功", "success");
            }
          })
          .finally(() => {
            cfg.loading.btn = false;
          });
      })
      .catch(() => {
        close();
      });
  }
};

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string) => {
  cfg.dialog.visible = true;
  state.id = id;
  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();

  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }

  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open,
  del
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      v-model:title="title"
      append-to-body
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-tabs v-model="cfg.tabsIndex" v-loading="cfg.loading.tabs" tab-position="left">
        <el-tab-pane label="基本信息">
          <employee-info ref="employeeInfoRef" v-model="state.model" />
        </el-tab-pane>
        <el-tab-pane label="角色">
          <employee-roles ref="employeeRolesRef" v-model:employee-id="state.id" />
        </el-tab-pane>
        <el-tab-pane label="岗位">
          <employee-stations ref="employeeStationsRef" v-model:employee-id="state.id" />
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.delete"
            class="delete"
            style="float: left"
            :round="btnRound"
            :disabled="cfg.loading.tabs"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnDeleteIcon)"
            @click="del()"
          >
            删除
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
