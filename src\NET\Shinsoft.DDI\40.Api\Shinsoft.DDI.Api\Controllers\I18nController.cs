﻿using Shinsoft.Core.I18n;

namespace Shinsoft.DDI.Api
{
    /// <summary>
    /// 多语言
    /// </summary>
    [ApiExplorerSettings(GroupName = "多语言")]
    public class I18nController : BaseApiController<SysSetupBll>
    {
        /// <summary>
        /// 初始化多语言
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        public void InitI18n()
        {
            this.Repo.InitSysI18ns();
        }

        /// <summary>
        /// 获取多语言JSON
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取多语言JSON")]
        public List<I18nText> GetI18n([FromQuery, Required] string culture)
        {
            var texts = this.CompanyCache.GetClientTexts(culture);

            return texts;
        }

        /// <summary>
        /// 翻译多语言
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "翻译多语言")]
        public string Translate([FromQuery, Required] string key, string? culture = null)
        {
            var cc = this.CompanyCache;

            return cc.Translate(key, culture);
        }

        /// <summary>
        /// 翻译系统多语言
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        [LogApi(ApiType.Query, Operate = "翻译系统多语言")]
        public string SysTranslate([FromQuery, Required] string key, string? culture = null)
        {
            var sc = this.SysCache;

            return sc.Translate(key, culture);
        }
    }
}