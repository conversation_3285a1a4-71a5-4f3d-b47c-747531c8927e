﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class EmployeeMeta
    {
        [MapFromProperty(typeof(Employee), Employee.Foreigns.MajorSubCompany, SubCompany.Columns.Name)]
        public string? MajorSubCompanyName { get; set; }

        [MapFromProperty(typeof(Employee), Employee.Foreigns.MajorDepartment, Department.Columns.Name)]
        public string? MajorDepartmentName { get; set; }

        [MapFromProperty(typeof(Employee), Employee.Foreigns.MajorCostCenter, CostCenter.Columns.Name)]
        public string? MajorCostCenterName { get; set; }

        [MapFromProperty(typeof(Employee), Employee.Foreigns.MajorStation, Station.Columns.Name)]
        public string? MajorStationName { get; set; }
    }
}