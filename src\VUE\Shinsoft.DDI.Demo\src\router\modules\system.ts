export default {
  path: "/system",
  meta: {
    icon: "ep:setting",
    title: "系统管理",
    alwaysShow: true,
    rank: 900
  },
  children: [
    {
      path: "/system/announcement",
      name: "system-announcement",
      meta: {
        title: "公告",
        alwaysShow: false
      },
      children: [
        {
          path: "/system/announcement/query",
          name: "announcement:query",
          component: () => import("@/views/system/announcement/query.vue"),
          meta: {
            title: "公告管理",
            auths: ["Announcement:Query", "Announcement:Manage"]
          }
        }
      ]
    },
    {
      path: "/system/setup",
      name: "system-setup",
      meta: {
        title: "设置",
        alwaysShow: false
      },
      children: [
        {
          path: "/system/setup/companySetting",
          name: "CompanySetting",
          component: () => import("@/views/system/setup/queryCompanySetting.vue"),
          meta: {
            title: "扩展配置",
            auths: "CompanySetting:Maintain"
          }
        }
      ]
    },
    {
      path: "/system/mail",
      name: "Mail",
      component: () => import("@/views/system/mail/queryMail.vue"),
      meta: {
        title: "邮件管理",
        auths: ["Mail:Query", "Mail:Manage"]
      }
    },
    {
      path: "/system/log",
      name: "Log",
      component: () => import("@/views/system/log/queryLog.vue"),
      meta: {
        title: "日志查询",
        auths: "Log:Query"
      }
    }
  ]
} satisfies RouteConfigsTable;
