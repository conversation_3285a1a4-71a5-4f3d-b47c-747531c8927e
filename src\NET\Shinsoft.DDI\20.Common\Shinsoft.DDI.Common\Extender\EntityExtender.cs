using Shinsoft.Core.EntityFrameworkCore;
using System.Data;
using System.Reflection;

namespace Shinsoft.DDI.Common
{

    public static class EntityExtender
    {
        public static Dictionary<string, List<PropertyInfo>> GetPropertyInfo<T>(this IList<T> lst, IDictionary<string, string> columns)
            where T : class
        {
            var properties = new Dictionary<string, List<PropertyInfo>>();
            foreach (var kvp in columns)
            {
                var propertyInfo = GetPropertyInfo<T>(kvp.Value);
                if (propertyInfo.Any())
                {
                    properties.Add(kvp.Key, propertyInfo);
                }
            }

            return properties;
        }

        public static List<PropertyInfo> GetPropertyInfo<T>(string properties)
        where T : class
        {
            var result = new List<PropertyInfo>();
            var type = typeof(T);

            GetPropertyInfo(type, properties, result);

            return result;
        }

        private static void GetPropertyInfo(Type type, string properties, ICollection<PropertyInfo> parents)
        {
            var property = properties;
            var subProperties = string.Empty;
            if (property.IndexOf(".", StringComparison.Ordinal) > 0)
            {
                property = properties.Substring(0, properties.IndexOf(".", StringComparison.Ordinal));
                subProperties = properties.Substring(properties.IndexOf(".", StringComparison.Ordinal) + 1);
            }

            var prop = type.GetProperty(property);
            if (prop != null)
            {
                parents.Add(prop);

                if (!subProperties.IsEmpty())
                {
                    GetPropertyInfo(prop.PropertyType, subProperties, parents);
                }
            }
        }

        public static object? GetValue<T>(this T item, List<PropertyInfo> props)
            where T : class
        {
            object? result = item;

            foreach (var prop in props.Where(prop => result != null))
            {
                result = prop.GetValue(result);
            }

            return result;
        }

        public static Dictionary<String, Object> ToMap<T>(this T item)
        {
            Dictionary<String, Object> map = new Dictionary<string, object>();

            Type t = item?.GetType() ?? throw new ArgumentNullException(nameof(item));

            PropertyInfo[] pi = t.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (PropertyInfo p in pi)
            {
                MethodInfo? mi = p.GetGetMethod();

                if (mi != null && mi.IsPublic)
                {
                    var value = mi.Invoke(item, new Object[] { }) ?? DBNull.Value;
                    map.Add(p.Name, value);
                }
            }

            return map;

        }

        /// <summary>
        /// 反射得到实体类的字段名称和类型
        /// var dict = GetProperties(model);
        /// </summary>
        /// <typeparam name="T">实体类</typeparam>
        /// <param name="t">实例化</param>
        /// <returns></returns>
        public static Dictionary<string, Type>? GetProperties(this IEntity t)
        {
            var ret = new Dictionary<string, Type>();
            if (t == null) { return null; }
            PropertyInfo[] properties = t.GetType().GetProperties().Where(o=>o.CustomAttributes.Any(a=>a.AttributeType == typeof(Shinsoft.Core.EntityFrameworkCore.ColumnAttribute))).ToArray(); ;
            if (properties.Length <= 0) { return null; }
            foreach (PropertyInfo item in properties)
            {             

                var propertyType = GetTypeByPropertyInfo(item);
                if (propertyType != null)
                {
                    ret.Add(item.Name, propertyType);
                }
            }
            return ret;
        }

        private static Type? GetTypeByPropertyInfo(PropertyInfo property)
        {
            if (property.PropertyType == typeof(Nullable<int>))
                return typeof(int);
 
            if (property.PropertyType == typeof(Nullable<Guid>))
                return typeof(Guid);

            if (property.PropertyType == typeof(Nullable<DateTime>))
                return typeof(DateTime);

            if (property.PropertyType.IsEnum)
                return typeof(int);

            return property.PropertyType;
        }

        /// <summary>
        /// 转换DataTable
        /// </summary>
        /// <typeparam name="IEntity"></typeparam>
        /// <param name="converter"></param>
        /// <returns></returns>
        public static DataTable EntityToDataTable<TEntity>(this IEnumerable<TEntity> converter)
        {
            var entities = converter.ToArray();

            var t = typeof(TEntity);

            var properties = t.GetProperties().ToArray();
            var table = new DataTable();

            foreach (var property in properties)
            {
                var propertyType = property.PropertyType;
                if (propertyType.IsGenericType && propertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
                {
                    propertyType = Nullable.GetUnderlyingType(propertyType);
                }

                if (propertyType != null)
                {
                    table.Columns.Add(new DataColumn(property.Name, propertyType));
                }
            }

            foreach (var entity in entities)
            {
                table.Rows.Add(properties.Select(p => GetPropertyValue(p.GetValue(entity, null) ?? DBNull.Value)).ToArray());
            }

            return table;
        }


        private static object GetPropertyValue(object? obj)
        {
            return obj ?? DBNull.Value;
        }
    }
}