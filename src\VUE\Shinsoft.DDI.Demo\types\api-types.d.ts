/**
 * 框架改造
 * 创建 httpApi.ts所需的ltypes
 */
import type { Method, AxiosError, AxiosResponse, AxiosRequestConfig } from "axios";

declare global {
  /**
   * API返回结果
   */
  interface ApiResult {
    success?: boolean;
    type?: number;
    messages?: Array<string>;
  }

  interface BizResult extends ApiResult {
    data?: any;
  }

  interface QueryResult extends ApiResult {
    datas?: Array;
    total: number;
    pageIndex?: number;
    pageSize?: number;
    lastPageIndex?: number;
    isLastPage?: number;
  }

  interface QueryFilter extends Record<string, any> {
    order?: string;
  }

  interface PagingFilter extends QueryFilter {
    pageIndex?: number;
    pageSize?: number;
    requireTotal?: boolean;
  }

  type RequestMethods = Extract<
    Method,
    "get" | "post" | "put" | "delete" | "patch" | "option" | "head"
  >;

  interface ApiError extends AxiosError {
    isCancelRequest?: boolean;
  }

  interface ApiResponse extends AxiosResponse {
    config: ApiRequestConfig;
  }

  interface ApiRequestConfig extends AxiosRequestConfig {
    beforeRequestCallback?: (request: ApiRequestConfig) => void;
    beforeResponseCallback?: (response: ApiResponse) => void;
    messageResult?: boolean;
    messageBoxResult?: boolean;
  }
}
