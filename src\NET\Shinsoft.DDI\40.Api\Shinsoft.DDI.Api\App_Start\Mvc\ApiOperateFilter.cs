﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Shinsoft.Core.Mvc.Filters;
using Shinsoft.DDI.Api.App_Start.Interfaces;

namespace Shinsoft.DDI.Api
{
    public class ApiOperateFilter : BaseApiOperateFilter
    {
        protected override void WriteSpecialLog(string specialCode, ApiLogEvent logEvent, ActionExecutedContext context)
        {
        }

        protected override void WriteLog(ApiLogEvent logEvent, ActionExecutedContext context)
        {
            if (logEvent.UserId.IsEmpty()
                && logEvent.UserUniqueName.IsEmpty()
                && logEvent.UserDisplayName.IsEmpty()
                && context.Controller is IReceiverController receiverController)
            {
                logEvent.UserUniqueName = receiverController.SdrCode;

                if (receiverController.RawReceiver != null)
                {
                    logEvent.UserDisplayName = receiverController.RawReceiver.Name;
                }
            }

            base.WriteLog(logEvent, context);
        }
    }
}