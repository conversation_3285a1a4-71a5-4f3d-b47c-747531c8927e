﻿CREATE TABLE [log].[LogException] (
    [ID]                        UNIQUEIDENTIFIER        NOT NULL,
    [CompanyId]                 NVARCHAR (50)           NULL,
    [Exception]                 NVARCHAR (MAX)          NULL,
    [StackTrace]                NVARCHAR (MAX)          NOT NULL,
    CONSTRAINT [PK_LogException] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_LogException_Log] FOREIGN KEY ([ID]) REFERENCES [log].[Log] ([ID])
);
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'异常日志',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogException';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'异常',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogException',
    @level2type = N'COLUMN',
    @level2name = N'Exception';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'堆栈跟踪',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogException',
    @level2type = N'COLUMN',
    @level2name = N'StackTrace';

