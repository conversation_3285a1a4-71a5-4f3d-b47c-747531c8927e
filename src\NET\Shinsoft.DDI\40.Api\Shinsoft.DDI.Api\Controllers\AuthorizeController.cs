﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Shinsoft.DDI.Api.Models;
using Shinsoft.Core.AutoMapper;
using Shinsoft.Core.Mvc;

namespace Shinsoft.DDI.Api
{
    public class AuthorizeController : BaseApiController<AuthorizeBll>
    {
        #region 员工

        /// <summary>
        /// 查询员工
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Authorize.Employee.Employee_Query)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Add)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Edit)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Delete)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_ResetPwd)]
        [LogApi(ApiType.Query, Operate = "查询员工")]
        public QueryResult<EmployeeQuery> QueryEmployee([FromQuery] EmployeeFilter filter)
        {
            var today = DateTime.Now;   //SysDateTime.Today;  --离岗当天用日期查会查出已离岗；

            var exps = this.NewExps<Employee>();

            if (!filter.DepartmentId.IsEmpty() && filter.DepartmentId.HasValue)
            {
                List<Guid> departmentIds = new List<Guid>();
                var checkDepartment = this.Repo.Get<Department>(filter.DepartmentId);
                if (checkDepartment != null)
                {
                    departmentIds = this.Repo.GetEntities<Department>(a => a.UidPath.Contains($"|{checkDepartment.Uid}|")).Select(g => g.ID).ToList();
                }

                exps.And(p =>
                    (p.MajorDepartmentId.HasValue && departmentIds.Contains(p.MajorDepartmentId.Value))
                    || p.EmployeeStation.Any(p1 =>
                        departmentIds.Contains(p1.Station.DepartmentId)
                        && (!p1.StartDate.HasValue || p1.StartDate <= today)
                        && (!p1.EndDate.HasValue || p1.EndDate >= today)
                        && !p1.Station.Deleted
                        && p1.Station.Valid
                        && !p1.Station.Position.Deleted
                        && (!p1.Station.StartDate.HasValue || p1.Station.StartDate <= today)
                        && (!p1.Station.EndDate.HasValue || p1.Station.EndDate >= today)
                    )
                );
            }

            //if (!filter.RoleId.HasValue)
            //{
            //    var roleMembers = this.Repo.GetEntities<RoleMember>(p => p.RoleId == filter.RoleId);

            //    var employeeIds = roleMembers.Where(p => p.EnumType == RoleMemberType.Employee).Select(p => p.MemberId);
            //    var departmentIds = roleMembers.Where(p => p.EnumType == RoleMemberType.Department).Select(p => p.MemberId);
            //    var stationsIds = roleMembers.Where(p => p.EnumType == RoleMemberType.Station).Select(p => p.MemberId);
            //    var positionIds = roleMembers.Where(p => p.EnumType == RoleMemberType.Station).Select(p => p.MemberId);

            //    var stations = this.Repo.GetEntities<Station>(p => p.Valid          // 岗位有效
            //        && !p.Department.Deleted && p.Department.Valid                  // 部门有效
            //        && !p.Position.Deleted                                          // 职位有效
            //        && (!p.StartDate.HasValue || p.StartDate <= today)              // 岗位时间有效
            //        && (!p.EndDate.HasValue || p.EndDate >= today)
            //        && (stationsIds.Contains

            //        p.EmployeeStation.Any(p1 => p1.EmployeeId == identity.ID     // 入岗时间有效
            //            && (!p1.StartDate.HasValue || p1.StartDate <= today)
            //            && (!p1.EndDate.HasValue || p1.EndDate >= today)
            //        )
            //    );

            //    var stationIds = stations.Select(p => p.ID).ToList();
            //    var positionIds = stations.Select(p => p.PositionId).Distinct().ToList();
            //    var deptUids = stations.SelectMany(p => p.Department.UidPath.Split('|'))
            //        .Where(p => !p.IsEmpty())
            //        .Distinct()
            //        .Select(p => p.As<int>())
            //        .ToList();
            //    var depts = this.Repo.GetEntities<Department>(p => deptUids.Contains(p.Uid));
            //    var deptIds = depts.Select(p => p.ID);

            //    exps.And(p => p.RoleMembers
            //        .Any(p1 => (p1.EnumType == RoleMemberType.Employee && p1.MemberId == employeeId)
            //            || (p1.EnumType == RoleMemberType.Station && stationIds.Contains(p1.MemberId))
            //            || (p1.EnumType == RoleMemberType.Position && positionIds.Contains(p1.MemberId))
            //            || (p1.EnumType == RoleMemberType.Department && deptIds.Contains(p1.MemberId))
            //        )
            //    );
            //}

            return this.Repo.GetDynamicQuery<Employee, EmployeeQuery>(filter, exps);
        }

        /// <summary>
        /// 获取员工
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Authorize.Employee.Employee_Query)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Add)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Edit)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Delete)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_ResetPwd)]
        [LogApi(ApiType.Query, Operate = "获取员工")]
        public BizResult<EmployeeModel> GetEmployee([FromQuery, Required] Guid id)
        {
            var result = new BizResult<EmployeeModel>();

            var entity = this.Repo.Get<Employee>(id);

            if (entity == null)
            {
                result.Error(I18ns.Rule.Employee.NotExist);
            }
            else
            {
                var model = entity.Map<EmployeeModel>();
                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 获取员工角色
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Authorize.Employee.Employee_Query)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Add)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Edit)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Delete)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_ResetPwd)]
        [LogApi(ApiType.Query, Operate = "获取员工角色")]
        public BizResult<List<RoleQuery>> GetEmployeeRoles([FromQuery, Required] Guid id)
        {
            var result = new BizResult<List<RoleQuery>>();

            var dbEmployee = this.Repo.Get<Employee>(id);

            if (dbEmployee == null)
            {
                result.Error(I18ns.Rule.Employee.NotExist);
            }
            else
            {
                var today = SysDateTime.Today;

                var exp = this.NewExp<RoleMember>(p =>
                    p.EnumType == RoleMemberType.Employee && p.MemberId == dbEmployee.ID
                );

                if (dbEmployee.UserId.HasValue)
                {
                    exp = exp.Or(p =>
                        p.EnumType == RoleMemberType.User && p.MemberId == dbEmployee.UserId
                    );
                }

                if (dbEmployee.MajorDepartmentId.HasValue)
                {
                    // 对于不适用岗位相关概念的情况，员工只属于一个部门，存储在Employee的MajorDepartmentId中

                    var dept = this.Repo.Get<Department>(dbEmployee.MajorDepartmentId);

                    if (dept?.Valid == true)
                    {
                        exp = exp.Or(p =>
                            p.EnumType == RoleMemberType.Department && p.MemberId == dept.ID
                        );
                    }
                }

                //岗位相关角色
                var stations = this.Repo.GetEntities<Station>(p =>
                    p.Valid // 岗位有效
                    && !p.Department.Deleted
                    && p.Department.Valid // 部门有效
                    && !p.Position.Deleted // 职位有效
                    && (!p.StartDate.HasValue || p.StartDate <= today) // 岗位时间有效
                    && (!p.EndDate.HasValue || p.EndDate >= today)
                    && p.EmployeeStation.Any(p1 =>
                        p1.EmployeeId == dbEmployee.ID // 入岗时间有效
                        && (!p1.StartDate.HasValue || p1.StartDate <= today)
                        && (!p1.EndDate.HasValue || p1.EndDate >= today)
                    )
                );

                if (stations.Count != 0)
                {
                    var departmentIds = stations.Select(p => p.DepartmentId);
                    var stationsIds = stations.Select(p => p.ID);
                    var positionIds = stations.Select(p => p.PositionId).Distinct();

                    exp = exp.Or(p =>
                        p.EnumType == RoleMemberType.Department
                        && departmentIds.Contains(p.MemberId)
                    );
                    exp = exp.Or(p =>
                        p.EnumType == RoleMemberType.Station && stationsIds.Contains(p.MemberId)
                    );
                    exp = exp.Or(p =>
                        p.EnumType == RoleMemberType.Position && positionIds.Contains(p.MemberId)
                    );
                }

                var roleMembers = this.Repo.GetEntities(exp);

                var roleIds = roleMembers.Select(p => p.RoleId).Distinct();

                var roles = this
                    .CompanyCache.Roles.Where(p => roleIds.Contains(p.ID))
                    .OrderBy(p => p.Name)
                    .ToList();

                result.Data = roles.Maps<RoleQuery>();
            }

            return result;
        }

        /// <summary>
        /// 获取员工岗位
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Authorize.Employee.Employee_Query)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Add)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Edit)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Delete)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_ResetPwd)]
        [LogApi(ApiType.Query, Operate = "获取员工岗位")]
        public BizResult<List<StationQuery>> GetEmployeeStations([FromQuery, Required] Guid id)
        {
            var result = new BizResult<List<StationQuery>>();

            var dbEmployee = this.Repo.Get<Employee>(id);

            if (dbEmployee == null)
            {
                result.Error(I18ns.Rule.Employee.NotExist);
            }
            else
            {
                var today = SysDateTime.Today;

                var order = new StringBuilder();

                order.Append($"{Station.Foreigns.Position}.{Position.Columns.Grade} DESC");
                order.Append($",{Station.Foreigns.Department}.{Department.Columns.Name} ASC");
                order.Append($",{Station.Columns.Name} ASC");

                //岗位相关角色
                var stations = this.Repo.GetEntities<Station>(
                    p =>
                        p.Valid // 岗位有效
                        && !p.Department.Deleted
                        && p.Department.Valid // 部门有效
                        && !p.Position.Deleted // 职位有效
                        && (!p.StartDate.HasValue || p.StartDate <= today) // 岗位时间有效
                        && (!p.EndDate.HasValue || p.EndDate >= today)
                        && p.EmployeeStation.Any(p1 =>
                            p1.EmployeeId == dbEmployee.ID // 入岗时间有效
                            && (!p1.StartDate.HasValue || p1.StartDate <= today)
                            && (!p1.EndDate.HasValue || p1.EndDate >= today)
                        ),
                    order.ToString()
                );

                var stationQuerys = stations.Maps<StationQuery>();

                //是否主岗
                foreach (var item in stationQuerys)
                {
                    if (item.ID == dbEmployee.MajorStationId)
                    {
                        item.IsMajorStation = "是";
                    }
                    else
                    {
                        item.IsMajorStation = "否";
                    }
                }

                result.Data = stationQuerys;
            }

            return result;
        }

        /// <summary>
        /// 获取员工配置
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Add)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Edit)]
        [LogApi(ApiType.Query, Operate = "获取员工配置")]
        public BizResult<EmployeeCfg> GetEmployeeCfg()
        {
            var cfg = this.CompanyCache.Company.Cfg!.Map<EmployeeCfg>();

            return this.BizResult(cfg);
        }

        /// <summary>
        /// 新增员工
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增员工")]
        public BizResult<EmployeeModel> AddEmployee(EmployeeModel model)
        {
            var entity = model.Map<Employee>();

            var result = this.Repo.AddEmployee(entity);

            return result.Map<EmployeeModel>();
        }

        /// <summary>
        /// 编辑员工
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑员工")]
        public BizResult<EmployeeModel> UpdateEmployee(EmployeeModel model)
        {
            var entity = model.Map<Employee>();

            var result = this.Repo.UpdateEmployee(entity);

            return result.Map<EmployeeModel>();
        }

        /// <summary>
        /// 删除员工
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除员工")]
        public BizResult DeleteEmployee(EmployeeModel model)
        {
            return this.Repo.DeleteEmployee(model.ID);
        }

        /// <summary>
        /// 重置员工密码
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_ResetPwd)]
        [LogApi(ApiType.Save, Operate = "重置员工密码")]
        public BizResult<EmployeeModel> ResetEmployeePwd(EmployeeModel model)
        {
            var entity = model.Map<Employee>();

            var result = this.Repo.ResetEmployeePwd(entity.ID);

            return result.Map<EmployeeModel>();
        }

        /// <summary>
        /// 设置主岗
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage)]
        [Auth(AuthCodes.Authorize.Employee.Employee_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "设置主岗")]
        public BizResult<EmployeeModel> SetEmployeeMajor(EmployeeModel model)
        {
            var entity = model.Map<Employee>();

            var result = this.Repo.SetEmployeeMajor(entity);

            return result.Map<EmployeeModel>();
        }
        #endregion 员工

        #region Role

        /// <summary>
        /// 查询角色
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Authorize.Role.Role_Query)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Add)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Edit)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Delete)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Auth)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Member)]
        [LogApi(ApiType.Query, Operate = "查询角色")]
        public QueryResult<RoleQuery> QueryRole([FromQuery] RoleFilter filter)
        {
            var today = SysDateTime.Today;

            var exps = this.NewExps<Role>();

            if (filter.UserId.HasValue)
            {
                var userId = filter.UserId.Value;

                exps.And(p =>
                    p.RoleMembers.Any(p1 =>
                        (p1.EnumType == RoleMemberType.User && p1.MemberId == userId)
                    )
                );
            }

            if (filter.EmployeeId.HasValue)
            {
                var employeeId = filter.EmployeeId.Value;
                var employee = this.Repo.Get<Employee>(employeeId);

                if (employee != null)
                {
                    var stations = this.Repo.GetEntities<Station>(
                        Station.Foreigns.Department,
                        p =>
                            p.Valid
                            && (!p.StartDate.HasValue || p.StartDate <= today)
                            && (!p.EndDate.HasValue || p.EndDate >= today)
                            && p.EmployeeStation.Any(p1 =>
                                p1.EmployeeId == employeeId
                                && (!p1.StartDate.HasValue || p1.StartDate <= today)
                                && (!p1.EndDate.HasValue || p1.EndDate >= today)
                            )
                    );

                    var stationIds = stations.Select(p => p.ID).ToList();
                    var positionIds = stations.Select(p => p.PositionId).Distinct().ToList();
                    var deptUids = stations
                        .SelectMany(p => p.Department.UidPath.Split('|'))
                        .Where(p => !p.IsEmpty())
                        .Distinct()
                        .Select(p => p.As<int>())
                        .ToList();
                    var depts = this.Repo.GetEntities<Department>(p => deptUids.Contains(p.Uid));
                    var deptIds = depts.Select(p => p.ID).ToList();

                    if (
                        employee.MajorDepartmentId.HasValue
                        && !deptIds.Contains(employee.MajorDepartmentId.Value)
                    )
                    {
                        deptIds.Add(employee.MajorDepartmentId.Value);
                    }

                    exps.And(p =>
                        p.RoleMembers.Any(p1 =>
                            (p1.EnumType == RoleMemberType.Employee && p1.MemberId == employeeId)
                            || (
                                p1.EnumType == RoleMemberType.Station
                                && stationIds.Contains(p1.MemberId)
                            )
                            || (
                                p1.EnumType == RoleMemberType.Position
                                && positionIds.Contains(p1.MemberId)
                            )
                            || (
                                p1.EnumType == RoleMemberType.Department
                                && deptIds.Contains(p1.MemberId)
                            )
                        )
                    );
                }
            }

            if (filter.Flags != RoleFlag.None)
            {
                exps.And(p => (p.EnumFlags & filter.Flags) != RoleFlag.None);
            }

            return this.Repo.GetDynamicQuery<Role, RoleQuery>(filter, exps);
        }

        /// <summary>
        /// 获取角色
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Authorize.Role.Role_Query)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Add)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Edit)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Delete)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Auth)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Member)]
        [LogApi(ApiType.Query, Operate = "获取角色")]
        public BizResult<RoleModel> GetRole([FromQuery, Required] Guid id)
        {
            var result = new BizResult<RoleModel>();

            var entity = this.Repo.Get<Role>(id);

            if (entity == null)
            {
                result.Error(I18ns.Rule.Role.NotExist);
            }
            else
            {
                result.Data = entity.Map<RoleModel>();
            }

            return result;
        }

        /// <summary>
        /// 新增角色
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Authorize.Role.Role_Manage)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增角色")]
        public BizResult<RoleModel> AddRole(RoleModel model)
        {
            var entity = model.Map<Role>();

            var result = this.Repo.AddRole(entity);

            return result.Map<RoleModel>();
        }

        /// <summary>
        /// 编辑角色
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Authorize.Role.Role_Manage)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑角色")]
        public BizResult<RoleModel> UpdateRole(RoleModel model)
        {
            var entity = model.Map<Role>();

            var result = this.Repo.UpdateRole(entity);

            return result.Map<RoleModel>();
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Authorize.Role.Role_Manage)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除角色")]
        public BizResult DeleteRole(RoleModel model)
        {
            return this.Repo.DeleteRole(model.ID);
        }

        #endregion Role

        #region RoleAuth

        private static AuthTree ToAuthTree(Auth auth, List<Auth> all)
        {
            var tree = auth.Map<AuthTree>();

            var children = all.Where(p => p.ParentId == auth.ID).ToList();

            if (children.Count > 0)
            {
                tree.Children = children.Select(p => ToAuthTree(p, all)).ToList();
            }

            return tree;
        }

        /// <summary>
        /// 获取角色权限
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Authorize.Role.Role_Query)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Add)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Edit)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Delete)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Auth)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Member)]
        [LogApi(ApiType.Query, Operate = "获取角色权限")]
        public BizResult<RoleAuthsModel> GetRoleAuths(
            [FromQuery] Guid roleId,
            [FromQuery] bool isEdit = false
        )
        {
            var result = new BizResult<RoleAuthsModel>();

            var entity = this.Repo.Get<Role>(roleId);

            if (entity == null)
            {
                result.Error(I18ns.Rule.Role.NotExist);
            }
            else
            {
                var cc = this.CompanyCache;

                var model = entity.Map<RoleAuthsModel>();

                var dbRoleAuths = this.Repo.GetEntities<RoleAuth>(p => p.RoleId == roleId);
                var dbRoleAuthTags = this.Repo.GetEntities<RoleAuthTag>(p => p.RoleId == roleId);

                var authIds = dbRoleAuths.Select(p => p.AuthId).ToList();

                var auths = cc.Auths
                    .Where(p => p.EnumType == AuthType.Permission && authIds.Contains(p.ID))
                    .ToList();

                var query = cc.Auths.Where(p => !p.EnumFlags.HasFlag(AuthFlag.Invisible) && !p.EnumFlags.HasFlag(AuthFlag.AgentOnly));

                #region Tree

                var treeAuthIds = auths
                    .Where(p => p.EnumAuthTagType == AuthTagType.None)
                    .Select(p => p.ID)
                    .ToList();

                var treeAuthQuery = query.Where(p => p.AllChildren(p1 => p1.EnumAuthTagType == AuthTagType.None));

                if (!isEdit)
                {
                    treeAuthQuery = treeAuthQuery.Where(p => p.AnyChildren(p1 => treeAuthIds.Contains(p1.ID)));
                }

                var treeAuths = treeAuthQuery.OrderBy(p => p.Ordinal).ThenBy(p => p.Name).ToList();

                model.Auths = treeAuths
                    .Where(auth => !auth.ParentId.HasValue)
                    .Select(auth => ToAuthTree(auth, treeAuths))
                    .ToList();

                if (isEdit)
                {
                    model.AuthedIds = treeAuths
                        .Where(p => p.EnumType == AuthType.Permission && treeAuthIds.Contains(p.ID))
                        .Select(p => p.ID)
                        .ToList();
                }

                #endregion Tree

                var tagAuthIds = auths
                    .Where(p => p.EnumAuthTagType != AuthTagType.None)
                    .Select(p => p.ID)
                    .ToList();

                var tagQuery = query.Where(p => p.EnumType == AuthType.Permission && p.EnumAuthTagType != AuthTagType.None);

                if (!isEdit)
                {
                    tagQuery = tagQuery.Where(p => p.AnyChildren(p1 => tagAuthIds.Contains(p1.ID)));
                }

                var tagAuths = tagQuery
                    .OrderBy(p => p.Ordinal)
                    .ThenBy(p => p.Name)
                    .ToList();

                var tagRoleAuthModels = new List<RoleAuthModel>();

                foreach (var tagAuth in tagAuths)
                {
                    var add = isEdit;

                    RoleAuthModel roleAuthModel;

                    var dbRoleAuth = dbRoleAuths.SingleOrDefault(p => p.AuthId == tagAuth.ID);

                    roleAuthModel = dbRoleAuth == null
                        ? tagAuth.Map<RoleAuthModel>()
                        : dbRoleAuth.Map<RoleAuthModel>();

                    roleAuthModel.AllowAllTags ??= false;

                    if (dbRoleAuth?.AllowAllTags == true)
                    {
                        add = true;
                    }

                    var authTagQuery = cc.AuthTags.Where(p => p.EnumType == tagAuth.EnumAuthTagType
                            && (p.EnumProgramFlags == ProgramFlag.None || (p.EnumProgramFlags & tagAuth.EnumProgramFlags) != ProgramFlag.None)
                        );

                    var authedTagIds = dbRoleAuthTags
                        .Where(p => p.AuthId == tagAuth.ID)
                        .Select(p => p.AuthTagId)
                        .ToList();

                    if (!isEdit)
                    {
                        authTagQuery = authTagQuery.Where(p => authedTagIds.Contains(p.ID));
                    }

                    var authTags = authTagQuery.OrderBy(p => p.Ordinal)
                        .ThenBy(p => p.Name)
                        .ToList();

                    if (authTags.Count > 0)
                    {
                        add = true;

                        var authTagModels = authTags.Maps<AuthTagQuery>();

                        if (isEdit && authedTagIds.Count > 0)
                        {
                            foreach (var authTagModel in authTagModels)
                            {
                                authTagModel.Checked = authedTagIds.Contains(authTagModel.ID);
                            }
                        }

                        roleAuthModel.AuthTags = authTagModels;
                    }

                    if (add)
                    {
                        tagRoleAuthModels.Add(roleAuthModel);
                    }
                }

                model.TagAuths = tagRoleAuthModels;

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 设置角色权限
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Authorize.Role.Role_Manage)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Auth)]
        [LogApi(ApiType.Save, Operate = "设置角色权限")]
        public BizResult SetRoleAuths([FromBody] RoleAuthsModel request)
        {
            var roleId = request.ID;
            var authIds = request.AuthedIds ?? [];
            var tagRoleAuths = new List<RoleAuth>();

            if (request.TagAuths?.Count > 0)
            {
                foreach (var tagAuth in request.TagAuths)
                {
                    var tagRoleAuth = tagAuth.Map<RoleAuth>();

                    tagRoleAuth.AuthTags = tagAuth.AuthTags?.Where(p => p.Checked).Maps<AuthTag>() ?? [];

                    tagRoleAuths.Add(tagRoleAuth);
                }
            }

            var result = this.Repo.SetRoleAuths(roleId, authIds, tagRoleAuths);

            return result;
        }

        #endregion RoleAuth

        #region RoleMember

        /// <summary>
        /// 查询角色成员
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Authorize.Role.Role_Manage)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Member)]
        [LogApi(ApiType.Query, Operate = "查询角色成员")]
        public QueryResult<VwRoleMemberQuery> QueryRoleMember([FromQuery] VwRoleMemberFilter filter)
        {
            var exps = this.NewExps<VwRoleMember>();

            return this.Repo.GetDynamicQuery<VwRoleMember, VwRoleMemberQuery>(filter, exps);
        }

        /// <summary>
        /// 新增角色成员
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增角色成员")]
        public BizResult AddRoleMembers([FromBody] RoleMemberRequest request)
        {
            var roleId = request.RoleId;
            var memberType = request.MemberType;
            var memberIds = request.MemberIds ?? [];

            var result = this.Repo.AddRoleMembers(roleId, memberType, memberIds);

            return result;
        }

        /// <summary>
        /// 删除角色成员
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除角色成员")]
        public BizResult DeleteRoleMember([FromBody] RoleMemberModel model)
        {
            var result = this.Repo.DeleteRoleMember(model.ID);

            return result;
        }

        #endregion RoleMember

        #region RoleEmployee

        /// <summary>
        /// 查询角色所属员工
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Authorize.Role.Role_Query)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Add)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Edit)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Delete)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Auth)]
        [Auth(AuthCodes.Authorize.Role.Role_Manage_Member)]
        [LogApi(ApiType.Query, Operate = "查询角色所属员工")]
        public QueryResult<EmployeeQuery> QueryRoleEmployee([FromQuery] RoleEmployeeFilter filter)
        {
            var exps = this.NewExps<Employee>();

            if (!filter.RoleId.IsEmpty())
            {
                var roleId = filter.RoleId;

                var roleMembers = this.Repo.GetEntities<RoleMember>(p => p.EnumType != RoleMemberType.User && p.RoleId == roleId);

                var today = SysDateTime.Today;

                var roleExp = this.NewExp<Employee>(p => false);

                var employeeIds = roleMembers
                    .Where(p => p.EnumType == RoleMemberType.Employee)
                    .Select(p => p.MemberId);

                if (employeeIds.Any())
                {
                    var exp = this.NewExp<Employee>(p => employeeIds.Contains(p.ID));
                    roleExp = roleExp.Or(exp);
                }

                var stationIds = roleMembers
                    .Where(p => p.EnumType == RoleMemberType.Station)
                    .Select(p => p.MemberId);

                if (stationIds.Any())
                {
                    var exp = this.NewExp<Employee>(p =>
                        p.EmployeeStation.Any(p1 =>
                            !p1.Station.Deleted
                            && p1.Station.Valid
                            && (!p1.StartDate.HasValue || p1.StartDate <= today)
                            && (!p1.EndDate.HasValue || p1.EndDate >= today)
                            && stationIds.Contains(p1.StationId)
                        )
                    );
                    roleExp = roleExp.Or(exp);
                }

                var positionIds = roleMembers
                    .Where(p => p.EnumType == RoleMemberType.Position)
                    .Select(p => p.MemberId);

                if (positionIds.Any())
                {
                    var exp = this.NewExp<Employee>(p =>
                        p.EmployeeStation.Any(p1 =>
                            !p1.Station.Deleted
                            && p1.Station.Valid
                            && (!p1.StartDate.HasValue || p1.StartDate <= today)
                            && (!p1.EndDate.HasValue || p1.EndDate >= today)
                            && positionIds.Contains(p1.Station.PositionId)
                            && !p1.Station.Position.Deleted
                        )
                    );
                    roleExp = roleExp.Or(exp);
                }

                var departmentIds = roleMembers
                    .Where(p => p.EnumType == RoleMemberType.Department)
                    .Select(p => p.MemberId)
                    .ToList();

                if (departmentIds.Count != 0)
                {
                    var exp = this.NewExp<Employee>(p =>
                        (
                            p.MajorDepartmentId.HasValue
                            && departmentIds.Contains(p.MajorDepartmentId.Value)
                            && !p.MajorDepartment!.Deleted
                            && p.MajorDepartment.Valid
                        )
                        || (
                            p.EmployeeStation.Any(p1 =>
                                !p1.Station.Deleted
                                && p1.Station.Valid
                                && (!p1.StartDate.HasValue || p1.StartDate <= today)
                                && (!p1.EndDate.HasValue || p1.EndDate >= today)
                                && departmentIds.Contains(p1.Station.DepartmentId)
                                && !p1.Station.Department.Deleted
                                && !p1.Station.Department.Valid
                            )
                        )
                    );
                    roleExp = roleExp.Or(exp);
                }

                exps.And(roleExp);
            }

            return this.Repo.GetDynamicQuery<Employee, EmployeeQuery>(filter, exps);
        }

        #endregion RoleEmployee
    }
}
