﻿CREATE TABLE [dbo].[CompanyCurrency]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_CompanyCurrency_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [Currency]                  NVARCHAR(10)                NOT NULL,
    [Name]                      NVARCHAR(50)                NOT NULL,
    [Symbol]                    NVARCHAR(5)                 NULL,
    [Valid]                     BIT                         NOT NULL,
    [IsStandard]                BIT                         NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_CompanyCurrency] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_CompanyCurrency_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
)
