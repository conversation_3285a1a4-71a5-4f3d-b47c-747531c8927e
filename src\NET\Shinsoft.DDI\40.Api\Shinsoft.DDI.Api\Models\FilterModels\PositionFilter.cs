﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class PositionFilter
    {
        /// <summary>
        /// 关键词（编码,名称）
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(Position), Position.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Position), Position.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }

        [DynamicQueryColumn(typeof(Position), Position.Columns.ParentId, Operation = Operation.Equal)]
        public Guid? ParentId { get; set; }

        /// <summary>
        /// 编码
        /// </summary>
        [Description("编码")]
        [DynamicQueryColumn(typeof(Position), Position.Columns.Code, Operation = Operation.StringIntelligence)]
        public string? Code { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        [DynamicQueryColumn(typeof(Position), Position.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Name { get; set; }
    }
}