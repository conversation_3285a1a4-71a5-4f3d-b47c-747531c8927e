﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    /// <summary>
    /// 父级字典及其子项数据传输类
    /// </summary>
    public class ParentDictWithChildrenModel
    {
        /// <summary>
        /// 父级字典信息
        /// </summary>
        public Dict ParentDict { get; set; } = new Dict();

        /// <summary>
        /// 子项列表
        /// </summary>
        public List<Dict>? Children { get; set; }
    }

    public class BizMasterDataBll : BaseCompanyBll
    {
        #region Constructs

        public BizMasterDataBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        public BizMasterDataBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        public BizMasterDataBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        public BizMasterDataBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs

        #region BizDict 业务字典

        public BizResult<Dict> AddBizDict(Dict entity)
        {
            var cc = this.CompanyCache;

            var result = new BizResult<Dict>();

            if (!entity.ParentId.HasValue)
            {
                result.Error("请选择所属业务字典项");
            }
            else
            {
                var parent = cc.GetDict(entity.ParentId.Value);

                if (parent == null)
                {
                    result.Error("所属业务字典项不存在");
                }
                else if (!parent.EnumFlags.HasFlag(DictFlag.Biz))
                {
                    result.Error("只允许维护业务字典项");
                }
                else if (!parent.EnumEditFlags.HasFlag(EditFlag.Children))
                {
                    result.Error($"不允许维护业务字典【{parent.Name}】的子项");
                }
                else
                {
                    entity.EnumFlags = parent.EnumFlags;
                    entity.EnumEditFlags = parent.EnumEditFlags_Child;

                    if (parent.EnumEditFlags_Child.HasFlag(EditFlag.Code))
                    {
                        if (entity.Code.IsEmpty())
                        {
                            result.Error("请输入字典编码");
                        }
                        else
                        {
                            var exist = cc.GetDict(entity.ParentId, entity.Code);

                            if (exist != null)
                            {
                                result.Error("已存在相同编码的字典项");
                            }
                        }
                    }

                    if (entity.Name.IsEmpty())
                    {
                        result.Error("请输入字典名称");
                    }
                    else
                    {
                        var exist = cc.GetDictByName(entity.ParentId, entity.Name);

                        if (exist != null)
                        {
                            result.Error("已存在相同名称的字典项");
                        }
                    }
                }
            }

            if (result.Success)
            {
                entity = this.Add(entity, false);

                this.SaveChanges();

                cc.RemoveCache<Dict>();

                result.Data = entity;
            }

            return result;
        }

        public BizResult<Dict> UpdateBizDict(Dict entity)
        {
            var cc = this.CompanyCache;

            var result = new BizResult<Dict>();

            var dbEntity = this.Get<Dict>(entity.ID);

            if (dbEntity == null)
            {
                result.Error("业务字典项不存在");
            }
            else if (!dbEntity.EnumFlags.HasFlag(DictFlag.Biz))
            {
                result.Error("只允许维护业务字典项");
            }
            else if (!dbEntity.ParentId.HasValue)
            {
                result.Error("只允许编辑业务字典项的子项");
            }
            else
            {
                var parent = cc.GetDict(dbEntity.ParentId.Value);

                if (parent == null)
                {
                    result.Error("所属业务字典项不存在");
                }
                else if (!parent.EnumFlags.HasFlag(DictFlag.Biz))
                {
                    result.Error("只允许维护业务字典项");
                }
                else if (!parent.EnumEditFlags.HasFlag(EditFlag.Children))
                {
                    result.Error($"不允许维护业务字典【{parent.Name}】的子项");
                }
                else
                {
                    if (dbEntity.EnumEditFlags.HasFlag(EditFlag.Code))
                    {
                        if (entity.Code.IsEmpty())
                        {
                            result.Error("请输入字典编码");
                        }
                        else
                        {
                            var exist = cc.GetDict(dbEntity.ParentId, entity.Code);

                            if (exist != null && exist.ID != dbEntity.ID)
                            {
                                result.Error("已存在相同编码的字典项");
                            }
                        }
                    }
                    else
                    {
                        entity.RemoveChangedColumn(Dict.Columns.Code);
                    }

                    if (dbEntity.EnumEditFlags.HasFlag(EditFlag.Name))
                    {
                        if (entity.Name.IsEmpty())
                        {
                            result.Error("请输入字典名称");
                        }
                        else
                        {
                            var exist = cc.GetDictByName(entity.ParentId, entity.Name);

                            if (exist != null && exist.ID != dbEntity.ID)
                            {
                                result.Error("已存在相同名称的字典项");
                            }
                        }
                    }
                    else
                    {
                        entity.RemoveChangedColumn(Dict.Columns.Name);
                    }
                }
            }

            if (result.Success)
            {
                // 以下字段不可以编辑
                entity.RemoveChangedColumn(Dict.Columns.ParentId);
                entity.RemoveChangedColumn(Dict.Columns.EnumFlags);
                entity.RemoveChangedColumn(Dict.Columns.EnumEditFlags);
                entity.RemoveChangedColumn(Dict.Columns.EnumEditFlags_Child);
                entity.RemoveChangedColumn(Dict.Columns.Deleted);

                entity = this.Update(entity, false);

                this.SaveChanges();

                cc.RemoveCache<Dict>();

                result.Data = entity;
            }

            return result;
        }

        public BizResult DeleteBizDict(Guid id)
        {
            var cc = this.CompanyCache;

            var result = new BizResult<Dict>();

            var dbEntity = this.Get<Dict>(id);

            if (dbEntity == null)
            {
                result.Error("业务字典项不存在");
            }
            else if (!dbEntity.EnumFlags.HasFlag(DictFlag.Biz))
            {
                result.Error("只允许维护业务字典项");
            }
            else if (!dbEntity.EnumEditFlags.HasFlag(EditFlag.Delete))
            {
                result.Error("当前字典项的不允许删除");
            }
            else if (!dbEntity.ParentId.HasValue)
            {
                result.Error("只允许编辑业务字典项的子项");
            }
            else
            {
                var parent = cc.GetDict(dbEntity.ParentId.Value);

                if (parent == null)
                {
                    result.Error("所属业务字典项不存在");
                }
                else if (!parent.EnumFlags.HasFlag(DictFlag.Biz))
                {
                    result.Error("只允许维护业务字典项");
                }
            }

            if (result.Success)
            {
                dbEntity = dbEntity.Value();

                this.Delete(dbEntity, false);

                this.SaveChanges();

                cc.RemoveCache<Dict>();
            }

            return result;
        }

        /// <summary>
        /// 新增业务字典及其子项
        /// </summary>
        /// <param name="model">父级字典及其子项数据传输对象</param>
        /// <returns>创建结果</returns>
        public BizResult<Dict> AddDictWithChildren(ParentDictWithChildrenModel model)
        {
            var cc = this.CompanyCache;
            var result = new BizResult<Dict>();

            // 验证父级字典信息
            var parentDict = model.ParentDict;

            if (parentDict.Code.IsEmpty())
            {
                result.Error("请输入父级字典编码");
            }
            else
            {
                var existParent = cc.GetDict(parentDict.Code);
                if (existParent != null)
                {
                    result.Error("已存在相同编码的父级字典");
                }
            }

            if (parentDict.Name.IsEmpty())
            {
                result.Error("请输入父级字典名称");
            }
            else
            {
                var existParent = cc.GetDictByName(parentDict.Name, string.Empty);
                if (existParent != null)
                {
                    result.Error("已存在相同名称的父级字典");
                }
            }

            // 验证子项信息
            if (model.Children != null && model.Children.Count > 0)
            {
                var childCodes = new HashSet<string>();
                var childNames = new HashSet<string>();

                foreach (var childDict in model.Children)
                {
                    if (childDict.Code.IsEmpty())
                    {
                        result.Error($"子项【{childDict.Name}】的编码不能为空");
                        break;
                    }

                    if (childCodes.Contains(childDict.Code))
                    {
                        result.Error($"子项编码【{childDict.Code}】重复");
                        break;
                    }
                    childCodes.Add(childDict.Code);

                    if (childDict.Name.IsEmpty())
                    {
                        result.Error($"子项【{childDict.Code}】的名称不能为空");
                        break;
                    }

                    if (childNames.Contains(childDict.Name))
                    {
                        result.Error($"子项名称【{childDict.Name}】重复");
                        break;
                    }
                    childNames.Add(childDict.Name);
                }
            }

            if (result.Success)
            {
                // 设置父级字典属性
                parentDict.ID = CombGuid.NewGuid();
                parentDict.ParentId = null; // 父级字典没有上级
                parentDict.EnumFlags = DictFlag.Biz; // 设置为业务字典
                parentDict.EnumEditFlags = EditFlag.Children | EditFlag.Delete; // 允许编辑子项和删除
                parentDict.EnumEditFlags_Child = EditFlag.Name | EditFlag.Delete; // 子项允许编辑名称、编码和删除
                parentDict.Ordinal = 0; // TODO：默认排序

                // 添加父级字典
                parentDict = this.Add(parentDict, false);

                // 添加子项
                if (model.Children != null && model.Children.Count > 0)
                {
                    for (int i = 0; i < model.Children.Count; i++)
                    {
                        var childDict = model.Children[i];

                        childDict.ID = CombGuid.NewGuid();
                        childDict.ParentId = parentDict.ID; // 设置父级ID
                        childDict.EnumFlags = parentDict.EnumFlags; // 继承父级标志
                        childDict.EnumEditFlags = parentDict.EnumEditFlags_Child; // 使用父级定义的子项编辑权限
                        childDict.Ordinal = i + 1; // 设置排序

                        this.Add(childDict, false);
                    }
                }

                // 保存所有更改
                this.SaveChanges();

                // 清除缓存
                cc.RemoveCache<Dict>();

                // 返回创建的父级字典
                result.Data = parentDict;
            }

            return result;
        }

        /// <summary>
        /// 编辑业务字典及其子项
        /// </summary>
        /// <param name="model">父级字典及其子项数据传输对象</param>
        /// <returns>编辑结果</returns>
        public BizResult<Dict> UpdateDictWithChildren(ParentDictWithChildrenModel model)
        {
            var cc = this.CompanyCache;
            var result = new BizResult<Dict>();

            // 验证父级字典信息
            var parentDict = model.ParentDict;

            if (parentDict.ID == Guid.Empty)
            {
                result.Error("父级字典ID不能为空");
                return result;
            }

            // 获取现有的父级字典
            var existingParent = this.Get<Dict>(parentDict.ID);
            if (existingParent == null)
            {
                result.Error("父级字典不存在");
                return result;
            }

            if (!existingParent.EnumFlags.HasFlag(DictFlag.Biz))
            {
                result.Error("只允许编辑业务字典");
                return result;
            }

            if (existingParent.ParentId.HasValue)
            {
                result.Error("只允许编辑父级字典");
                return result;
            }

            // 验证父级字典名称
            if (parentDict.Name.IsEmpty())
            {
                result.Error("请输入父级字典名称");
                return result;
            }

            // 检查名称是否与其他父级字典重复（排除自己）
            var existParentByName = cc.GetDictByName(parentDict.Name, string.Empty);
            if (existParentByName != null && existParentByName.ID != parentDict.ID)
            {
                result.Error("已存在相同名称的父级字典");
                return result;
            }

            // 验证子项信息
            if (model.Children != null && model.Children.Count > 0)
            {
                var childCodes = new HashSet<string>();
                var childNames = new HashSet<string>();

                foreach (var childDict in model.Children)
                {
                    if (childDict.Code.IsEmpty())
                    {
                        result.Error($"子项【{childDict.Name}】的编码不能为空");
                        return result;
                    }

                    if (childCodes.Contains(childDict.Code))
                    {
                        result.Error($"子项编码【{childDict.Code}】重复");
                        return result;
                    }
                    childCodes.Add(childDict.Code);

                    if (childDict.Name.IsEmpty())
                    {
                        result.Error($"子项【{childDict.Code}】的名称不能为空");
                        return result;
                    }

                    if (childNames.Contains(childDict.Name))
                    {
                        result.Error($"子项名称【{childDict.Name}】重复");
                        return result;
                    }
                    childNames.Add(childDict.Name);
                }
            }

            if (result.Success)
            {
                // 更新父级字典（编码不允许修改）
                existingParent.Name = parentDict.Name;
                this.Update(existingParent, false);

                // 获取现有的子项
                var existingChildren = this.GetEntities<Dict>(p => p.ParentId == existingParent.ID).ToList();

                // 删除不在新列表中的子项
                var newChildIds = model.Children?.Where(c => c.ID != Guid.Empty).Select(c => c.ID).ToHashSet() ?? new HashSet<Guid>();
                var childrenToDelete = existingChildren.Where(c => !newChildIds.Contains(c.ID)).ToList();

                foreach (Dict childToDelete in childrenToDelete)
                {
                    this.Delete<Dict>(childToDelete, false);
                }

                // 处理新的子项列表
                if (model.Children != null && model.Children.Count > 0)
                {
                    for (int i = 0; i < model.Children.Count; i++)
                    {
                        var childDict = model.Children[i];

                        // 查找现有的子项（只有当ID不为空时才查找）
                        var existingChild = childDict.ID != Guid.Empty ?
                            existingChildren.FirstOrDefault(c => c.ID == childDict.ID) : null;

                        if (existingChild != null)
                        {
                            // 更新现有子项（编码不允许修改）
                            existingChild.Name = childDict.Name;
                            existingChild.Ordinal = i + 1;
                            this.Update(existingChild, false);
                        }
                        else
                        {
                            // 添加新子项
                            childDict.ID = CombGuid.NewGuid();
                            childDict.ParentId = existingParent.ID;
                            childDict.EnumFlags = existingParent.EnumFlags;
                            childDict.EnumEditFlags = existingParent.EnumEditFlags_Child;
                            childDict.Ordinal = i + 1;
                            this.Add(childDict, false);
                        }
                    }
                }

                // 保存所有更改
                this.SaveChanges();

                // 清除缓存
                cc.RemoveCache<Dict>();

                result.Data = existingParent;
            }

            return result;
        }

        /// <summary>
        /// 删除字典项并重新排序
        /// </summary>
        /// <param name="id">要删除的字典项ID</param>
        /// <returns>删除结果</returns>
        public BizResult DeleteDictItem(Guid id)
        {
            var cc = this.CompanyCache;
            var result = new BizResult();

            var dbEntity = this.Get<Dict>(id);

            if (dbEntity == null)
            {
                result.Error("业务字典项不存在");
            }
            else if (!dbEntity.EnumFlags.HasFlag(DictFlag.Biz))
            {
                result.Error("只允许维护业务字典项");
            }
            else if (!dbEntity.EnumEditFlags.HasFlag(EditFlag.Delete))
            {
                result.Error("当前字典项不允许删除");
            }
            else if (!dbEntity.ParentId.HasValue)
            {
                result.Error("只允许删除字典项，不能删除父级字典");
            }
            else
            {
                var parent = cc.GetDict(dbEntity.ParentId.Value);

                if (parent == null)
                {
                    result.Error("所属业务字典项不存在");
                }
                else if (!parent.EnumFlags.HasFlag(DictFlag.Biz))
                {
                    result.Error("只允许维护业务字典项");
                }
                else
                {
                    // 删除字典项
                    this.Delete<Dict>(ref dbEntity, false);

                    // 获取同级的其他字典项并重新排序
                    var siblings = this.GetEntities<Dict>(p => p.ParentId == dbEntity.ParentId)
                                      .OrderBy(p => p.Ordinal)
                                      .ToList();

                    // 重新设置排序号
                    for (int i = 0; i < siblings.Count; i++)
                    {
                        siblings[i].Ordinal = i + 1;
                        this.Update(siblings[i], false);
                    }

                    // 保存更改
                    this.SaveChanges();

                    // 清除缓存
                    cc.RemoveCache<Dict>();
                }
            }

            return result;
        }

        #endregion BizDict 业务字典
    }
}
