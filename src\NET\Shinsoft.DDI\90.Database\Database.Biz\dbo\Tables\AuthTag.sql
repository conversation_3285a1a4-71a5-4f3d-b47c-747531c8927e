CREATE TABLE [dbo].[AuthTag]
(
    [ID]                            UNIQUEIDENTIFIER            NOT NULL  CONSTRAINT [DF_AuthTag_ID]  DEFAULT NEWSEQUENTIALID(),
    [CompanyId]                     UNIQUEIDENTIFIER            NOT NULL,
    [EnumType]                      INT                         NOT NULL,
    [EnumProgramFlags]              INT                         NOT NULL,
    [Code]                          NVARCHAR(50)                NOT NULL,
    [Name]                          NVARCHAR(200)               NOT NULL,
    [Ordinal]                       INT                         NOT NULL,
    [Remark]                        NVARCHAR(500)               NOT NULL,
    [Deleted]                       BIT                         NOT NULL,
    [Creator]				        NVARCHAR(50)		        NULL, 
    [CreateTime]			        DATETIME			        NULL, 
    [LastEditor]			        NVARCHAR(50)		        NULL, 
    [LastEditTime]			        DATETIME			        NULL, 
    CONSTRAINT [PK_AuthTag] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_AuthTag_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
)

