﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    [MapFromType(typeof(User), Reverse = false)]
    public class IdentityUser : IIdentityUser, IModel
    {
        public ProgramFlag Program { get; set; }

        #region IIdentity

        public string Culture { get; set; } = string.Empty;

        public string UserId { get; set; } = string.Empty;

        public string? EmployeeId { get; set; }

        public string? AgentId { get; set; }

        public string? RoleId { get; set; }

        [JsonIgnore]
        public int IdentitySeed { get; set; }

        [JsonIgnore, MapFromIgnore]
        public IIdentityKey IdentityKey => new ApiIdentityKey
        {
            Program = this.Program,
            Culture = this.Culture,
            UserId = this.UserId,
            EmployeeId = this.EmployeeId,
            AgentId = this.AgentId,
            RoleId = this.RoleId,
        };

        #endregion IIdentity

        #region IIdentityUser

        [MapFromProperty(typeof(User), nameof(User.EmployeeName), Reverse = false)]
        public string DisplayName { get; set; } = string.Empty;

        [MapFromIgnore]
        public string AuthToken { get; set; } = string.Empty;

        [MapFromIgnore]
        public string? RefreshToken { get; set; }

        [MapFromIgnore]
        public DateTime? RefreshExpiry { get; set; }

        [MapFromIgnore]
        public string? LoginToken { get; set; }

        [MapFromIgnore]
        public DateTime? LoginExpiry { get; set; }

        #endregion IIdentityUser

        [JsonIgnore]
        [MapFromProperty(typeof(User), User.Columns.LoginName, Reverse = false)]
        public string LoginName { get; set; } = string.Empty;

        [MapFromProperty(typeof(User), User.Columns.WeChatAvatar, Reverse = false)]
        public string? Avatar { get; set; }

        [MapFromProperty(typeof(User), nameof(User.Employee), Employee.Columns.Title)]
        public string? Title { get; set; }

        [MapFromProperty(typeof(User), nameof(User.EmployeeEmail), Reverse = false)]
        public string? Email { get; set; }

        [MapFromProperty(typeof(User), nameof(User.EmployeeMobile), Reverse = false)]
        public string? Mobile { get; set; }

        public bool IsAgent => !this.AgentId.IsEmpty();

        #region Company

        public Guid? CurrentCompanyId { get; set; }

        public string? CurrentCompanyName { get; set; }

        #endregion Company

        #region MyCompanies

        [MapFromIgnore]
        public Dictionary<Guid, IdentityCompany> MyCompanies { get; set; } = [];

        //public int MyIdentitiesToDoCount => this.MyIdentities.Sum(p => p.Identities.Sum(p1 => p1.ToDoCount));

        #endregion MyCompanies

        #region LineManager

        public bool HasLM => this.LM_ID.HasValue;

        [JsonIgnore]
        [MapFromProperty(typeof(User), nameof(User.LineManager), Employee.Columns.ID, Reverse = false)]
        public Guid? LM_ID { get; set; }

        [MapFromProperty(typeof(User), nameof(User.LineManager), Employee.Columns.JobNo, Reverse = false)]
        public string? LM_JobNo { get; set; }

        [MapFromProperty(typeof(User), nameof(User.LineManager), Employee.Columns.Title, Reverse = false)]
        public string? LM_Title { get; set; }

        [MapFromProperty(typeof(User), nameof(User.LineManager), Employee.Columns.DisplayName, Reverse = false)]
        public string? LM_DisplayName { get; set; }

        #endregion LineManager

        [MapFromProperty(typeof(User), nameof(User.Auths), Reverse = false)]
        public List<UserAuth>? Auths { get; set; }

        public string? RedirectUri { get; set; }
    }
}
