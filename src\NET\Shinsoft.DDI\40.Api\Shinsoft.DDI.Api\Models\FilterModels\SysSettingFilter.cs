﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class SysSettingFilter
    {
        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        [DynamicQueryColumn(typeof(SysSetting), SysSetting.Columns.Name, Operation = Operation.StringIntelligence)]
        public virtual string? Name { get; set; }

        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [DynamicQueryColumn(typeof(SysSetting), SysSetting.Columns.Key, Operation = Operation.StringIntelligence)]
        public virtual string? Key { get; set; }

        /// <summary>
        /// 值
        /// </summary>
        [Description("值")]
        [DynamicQueryColumn(typeof(SysSetting), SysSetting.Columns.Value, Operation = Operation.StringIntelligence)]
        public virtual string? Value { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        [DynamicQueryColumn(typeof(SysSetting), SysSetting.Columns.Remark, Operation = Operation.StringIntelligence)]
        public virtual string? Remark { get; set; }
    }
}