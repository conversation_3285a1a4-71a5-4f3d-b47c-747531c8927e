using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace Shinsoft.DDI.Api.Models
{
    /// <summary>
    /// 产品规格查询结果模型
    /// 用于返回产品规格查询结果的数据模型，包含关联的产品和药企信息
    /// </summary>
    public partial class ProductSpecQuery
    {
        /// <summary>
        /// 产品中文名称
        /// </summary>
        [MapFromProperty(typeof(ProductSpec), nameof(ProductSpec.Product), Product.Columns.NameCn)]
        public string? ProductName { get; set; }

        /// <summary>
        /// 产品英文名称
        /// </summary>
        [MapFromProperty(typeof(ProductSpec), nameof(ProductSpec.Product), Product.Columns.NameEn)]
        public string? ProductNameEn { get; set; }

        /// <summary>
        /// 通用名称
        /// </summary>
        [MapFromProperty(typeof(ProductSpec), nameof(ProductSpec.Product), Product.Columns.CommonName)]
        public string? CommonName { get; set; }

        /// <summary>
        /// 药企名称
        /// </summary>
        [MapFromProperty(typeof(ProductSpec), nameof(ProductSpec.Product), nameof(Product.Manufacturer), Manufacturer.Columns.Name)]
        public string? ManufacturerName { get; set; }
    }
}
