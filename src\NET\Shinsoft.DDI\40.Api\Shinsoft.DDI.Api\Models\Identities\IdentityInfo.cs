﻿//namespace Shinsoft.DDI.Api.Models
//{
//    [MapFromType(typeof(User), Reverse = false)]
//    public class IdentityInfo : IIdentityInfo, IModel
//    {
//        [MapFromProperty(typeof(User), nameof(User.EmployeeName), Reverse = false)]
//        public string DisplayName { get; set; } = string.Empty;

//        [JsonIgnore]
//        [MapFromProperty(typeof(User), User.Columns.LoginName, Reverse = false)]
//        public string LoginName { get; set; } = string.Empty;

//        [MapFromProperty(typeof(User), User.Columns.WeChatAvatar, Reverse = false)]
//        public string? Avatar { get; set; }

//        [MapFromProperty(typeof(User), nameof(User.Employee), Employee.Columns.Title)]
//        public string? Title { get; set; }

//        [MapFromProperty(typeof(User), nameof(User.EmployeeEmail), Reverse = false)]
//        public string? Email { get; set; }

//        [MapFromProperty(typeof(User), nameof(User.EmployeeMobile), Reverse = false)]
//        public string? Mobile { get; set; }
//    }
//}
