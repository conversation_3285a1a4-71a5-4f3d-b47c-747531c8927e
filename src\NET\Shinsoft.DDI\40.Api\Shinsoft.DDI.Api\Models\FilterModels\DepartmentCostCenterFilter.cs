﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class DepartmentCostCenterFilter
    {
        /// <summary>
        /// 分公司ID
        /// </summary>
        [Description("分公司ID")]
        [DynamicQueryColumn(typeof(Department), Department.Columns.SubCompanyId, Operation = Operation.Equal)]
        public Guid? SubCompanyId { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public Guid? DepartmentId { get; set; }

        /// <summary>
        /// 关键词（成本中心编码、名称）
        /// </summary>
        [DynamicQueryColumn(typeof(DepartmentCostCenter), DepartmentCostCenter.Foreigns.CostCenter, CostCenter.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(DepartmentCostCenter), DepartmentCostCenter.Foreigns.CostCenter, CostCenter.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }
    }
}
