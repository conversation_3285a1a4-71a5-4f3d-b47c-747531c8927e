import { $t } from "@/plugins/i18n";
import iconAuth from "@iconify-icons/material-symbols/passkey-outline";

export default {
  path: "/authorize",
  name: "authorize",
  meta: {
    icon: iconAuth,
    title: $t("menus.authorize._group"),
    alwaysShow: true,
    rank: 700
  },
  children: [
    {
      path: "/authorize/employee",
      name: "employee:query",
      component: () => import("@/views/authorize/employee/query.vue"),
      meta: {
        title: $t("menus.authorize.employee"),
        auths: [
          "Employee:Query",
          "Employee:Manage",
          "Employee:Manage:Add",
          "Employee:Manage:Edit",
          "Employee:Manage:Delete",
          "Employee:Manage:ResetPwd"
        ]
      }
    },
    {
      path: "/authorize/role",
      name: "role:query",
      component: () => import("@/views/authorize/role/query.vue"),
      meta: {
        title: $t("menus.authorize.role"),
        auths: [
          "Role:Query",
          "Role:Manage",
          "Role:Manage:Add",
          "Role:Manage:Edit",
          "Role:Manage:Delete",
          "Role:Manage:Auth",
          "Role:Manage:Member"
        ]
      }
    }
  ]
} satisfies RouteConfigsTable;
