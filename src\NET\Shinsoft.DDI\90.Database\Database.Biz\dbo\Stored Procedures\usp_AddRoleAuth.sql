﻿CREATE PROCEDURE [dbo].[usp_AddRoleAuth]
	@RoleCode NVARCHAR(50),
	@AuthCode NVARCHAR(50),
	@CompanyId UNIQUEIDENTIFIER = NULL
AS
	
INSERT INTO dbo.RoleAuth
(
    CompanyId,
    RoleId,
    AuthId
) 
SELECT
    r.CompanyId,    -- CompanyId - uniqueidentifier
    r.ID,    -- RoleId - uniqueidentifier
    a.ID     -- PermissionId - uniqueidentifier
	--,p.[Group],p.Name
FROM dbo.Role AS r
CROSS JOIN dbo.Auth AS a
INNER JOIN dbo.CompanyAuth AS ca ON ca.CompanyId = r.CompanyId AND ca.AuthId = a.ID
WHERE r.Deleted = 0
AND a.Deleted = 0
AND ca.Valid = 1
AND a.EnumType = 1
AND NOT EXISTS(SELECT 1 FROM dbo.vRoleAuth AS ra WHERE ra.RoleId = r.ID AND ra.AuthId = a.ID)
AND r.Code = @RoleCode
AND a.Code = @AuthCode
AND (@CompanyId IS NULL OR r.CompanyId = @CompanyId)

SELECT * FROM dbo.vRoleAuth AS ra
WHERE ra.RoleCode = @RoleCode
AND (@CompanyId IS NULL OR ra.CompanyId = @CompanyId)


RETURN 0
