﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class MailFilter
    {
        /// <summary>
        /// 邮件ID
        /// </summary>
        [DynamicQueryColumn(typeof(Mail), Mail.Columns.ID, Operation = Operation.Equal)]
        public Guid? ID { get; set; }

        /// <summary>
        /// 邮件触发器
        /// </summary>
        [DynamicQueryColumn(typeof(Mail), Mail.Columns.Trigger, Operation = Operation.Equal)]
        public string? Trigger { get; set; }

        /// <summary>
        /// 触发对象名称
        /// </summary>
        [DynamicQueryColumn(typeof(Mail), Mail.Columns.ObjectName, Operation = Operation.StringIntelligence)]
        public string? ObjectName { get; set; }

        /// <summary>
        /// 邮件标题
        /// </summary>
        [DynamicQueryColumn(typeof(Mail), Mail.Columns.Subject, Operation = Operation.StringIntelligence)]
        public string? Subject { get; set; }

        /// <summary>
        /// To
        /// </summary>
        [DynamicQueryColumn(typeof(Mail), Mail.Columns.To, Operation = Operation.StringIntelligence)]
        public string? To { get; set; }

        /// <summary>
        /// 状态（传入状态为空则只发送状态为发送失败邮件）
        /// </summary>
        [DynamicQueryColumn(typeof(Mail), Mail.Columns.EnumStatus, Operation = Operation.In)]
        public List<MailStatus>? Statuses { get; set; }

        /// <summary>
        /// 邮件创建时间范围（必须同时传入 开始、截止时间）
        /// </summary>

        [DynamicQueryColumn(typeof(Mail), Mail.Columns.CreateTime, Operation = Operation.BetweenWithMinAndMax)]
        public List<DateTime>? Times { get; set; } = new List<DateTime>();

        /// <summary>
        /// 最大发送次数
        /// </summary>
        [DynamicQueryColumn(typeof(Mail), Mail.Columns.SendCount, Operation = Operation.LessThan)]
        public int? MaxSendCount { get; set; }
    }
}