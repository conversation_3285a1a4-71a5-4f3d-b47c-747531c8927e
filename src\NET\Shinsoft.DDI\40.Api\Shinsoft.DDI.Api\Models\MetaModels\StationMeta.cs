﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class StationMeta
    {
        [JsonDateTime(false)]
        public override DateTime? StartDate { get; set; }

        [JsonDateTime(false)]
        public override DateTime? EndDate { get; set; }

        /// <summary>
        /// 上级岗位
        /// </summary>
        [MapFromProperty(typeof(Station), Station.Foreigns.Parent, Station.Columns.Name)]
        public virtual string? ParentName { get; set; }

        /// <summary>
        /// 部门编码
        /// </summary>
        [MapFromProperty(typeof(Station), Station.Foreigns.Department, Department.Columns.Code)]
        public virtual string? DepartmenCode { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        [MapFromProperty(typeof(Station), Station.Foreigns.Department, Department.Columns.Name)]
        public virtual string? DepartmentName { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [MapFromProperty(typeof(Station), Station.Foreigns.Department, nameof(Department.Text))]
        public virtual string? DepartmentText { get; set; }

        /// <summary>
        /// 职位编码
        /// </summary>
        [MapFromProperty(typeof(Station), Station.Foreigns.Position, Position.Columns.Code)]
        public virtual string? PositionCode { get; set; }

        /// <summary>
        /// 职位名称
        /// </summary>
        [MapFromProperty(typeof(Station), Station.Foreigns.Position, Position.Columns.Name)]
        public virtual string? PositionName { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        [MapFromProperty(typeof(Station), Station.Foreigns.Position, nameof(Position.Text))]
        public virtual string? PositionText { get; set; }

        /// <summary>
        /// 职级
        /// </summary>
        [MapFromProperty(typeof(Station), Station.Foreigns.Position, Position.Columns.Grade)]
        public virtual int? PositionGrade { get; set; }

        /// <summary>
        /// 是否主岗
        /// </summary>
        public string? IsMajorStation { get; set; }
    }
}
