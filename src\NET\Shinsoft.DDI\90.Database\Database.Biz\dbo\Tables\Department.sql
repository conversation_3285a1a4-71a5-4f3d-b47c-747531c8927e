--部门
CREATE TABLE [dbo].[Department] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_Department_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [ParentId]                  UNIQUEIDENTIFIER            NULL,
	[SubCompanyId]				UNIQUEIDENTIFIER	        NOT NULL,			--FK,分公司ID
    [Uid]                       INT                         NOT NULL    IDENTITY (1, 1) ,
    [UidPath]                   VARCHAR (500)               NOT NULL,
	[DefaultCostCenterId]		UNIQUEIDENTIFIER	        NULL,			--FK,默认成本中心ID
    [EnumFlags]                 INT                         NOT NULL,
    [Rank]                      INT                         NOT NULL,
    [Code]                      NVARCHAR(50)                NOT NULL,
    [Name]                      NVARCHAR(200)               NOT NULL,
    [ShortName]                 NVARCHAR(200)               NOT NULL,
    [Remark]                    NVARCHAR(500)               NOT NULL,
    [Valid]                     BIT                         NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_Department] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Department_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_Department_Department_00_Parent_Children] FOREIGN KEY ([ParentId]) REFERENCES [dbo].[Department] ([ID]),
    CONSTRAINT [FK_Department_SubCompany_00_SubCompany] FOREIGN KEY ([SubCompanyId]) REFERENCES [dbo].[SubCompany] ([ID]),
    CONSTRAINT [FK_Department_CostCenter_00_DefaultCostCenter] FOREIGN KEY ([DefaultCostCenterId]) REFERENCES [dbo].[CostCenter] ([ID]),
);
GO

CREATE UNIQUE INDEX [IX_Department_Uid] ON [dbo].[Department]
(
	[Uid] ASC
);
GO

CREATE INDEX [IX_Department_UidPath] ON [dbo].[Department]
(
	[UidPath] ASC
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'部门',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'上级部门ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = N'COLUMN',
    @level2name = N'ParentId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'唯一码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = N'COLUMN',
    @level2name = N'Uid'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'级别',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = N'COLUMN',
    @level2name = N'Rank'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'编码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = N'COLUMN',
    @level2name = N'Code'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = N'COLUMN',
    @level2name = N'Name'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'简称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = N'COLUMN',
    @level2name = N'ShortName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = N'COLUMN',
    @level2name = N'Remark'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'唯一码路径',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = N'COLUMN',
    @level2name = N'UidPath'
GO


EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'分公司ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = N'COLUMN',
    @level2name = N'SubCompanyId'

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'默认成本中心ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = N'COLUMN',
    @level2name = N'DefaultCostCenterId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'标签',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = N'COLUMN',
    @level2name = N'EnumFlags'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'有效性',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Department',
    @level2type = N'COLUMN',
    @level2name = N'Valid'
