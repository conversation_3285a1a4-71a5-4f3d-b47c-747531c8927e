CREATE TABLE [dbo].[Receiver<PERSON>lias] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [Default_ReceiverAlias_ID] DEFAULT (NEWSEQUENTIALID()),
    [ReceiverId]                UNIQUEIDENTIFIER            NOT NULL,
    [DistributorId]             UNIQUEIDENTIFIER            NOT NULL,
    [ReceiverAliasName]         NVARCHAR(200)               NOT NULL,
    [Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL,
    [CreateTime]                DATETIME                    NULL,
    [LastEditor]                NVARCHAR(50)                NULL,
    [LastEditTime]              DATETIME                    NULL,
    CONSTRAINT [PK_ReceiverAlias] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ReceiverAlias_Receiver_00_Receiver] FOREIGN KEY ([ReceiverId]) REFERENCES [dbo].[Receiver] ([ID]),
    CONSTRAINT [FK_ReceiverAlias_Receiver_01_Distributor] FOREIGN KEY ([DistributorId]) REFERENCES [dbo].[Receiver] ([ID])
) 
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'收货方别名',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverAlias',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverAlias',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'收货方ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverAlias',
    @level2type = N'COLUMN',
    @level2name = N'ReceiverId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'经销商ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverAlias',
    @level2type = N'COLUMN',
    @level2name = N'DistributorId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'收货方别名名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverAlias',
    @level2type = N'COLUMN',
    @level2name = N'ReceiverAliasName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverAlias',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverAlias',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverAlias',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverAlias',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverAlias',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'
