﻿namespace Shinsoft.DDI.Api.Models
{
    public class RoleAuthsModel : RoleMeta
    {
        /// <summary>
        /// 权限树（不包含有TagType的权限）
        /// </summary>
        public List<AuthTree>? Auths { get; set; }

        /// <summary>
        /// 选中的权限Id
        /// </summary>
        public List<Guid>? AuthedIds { get; set; }

        /// <summary>
        /// TagType 权限
        /// </summary>
        public List<RoleAuthModel>? TagAuths { get; set; }
    }
}
