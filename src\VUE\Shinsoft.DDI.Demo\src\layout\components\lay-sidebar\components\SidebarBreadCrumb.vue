<script setup lang="ts">
import { isEqual } from "@pureadmin/utils";
import { transformI18n } from "@/plugins/i18n";
import { useRoute, useRouter } from "vue-router";
import { ref, watch, onMounted, toRaw } from "vue";
import { getParentPaths, findRouteByPath } from "@/router/utils";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { getConfig } from "@/config";

const route = useRoute();
const levelList = ref([]);
const router = useRouter();
const routes: any = router.options.routes;
const multiTags: any = useMultiTagsStoreHook().multiTags;

const getBreadcrumb = (): void => {
  // 当前路由信息
  let currentRoute;
  let setTitle = false;

  if (Object.keys(route.query).length > 0) {
    multiTags.forEach(item => {
      if (isEqual(route.query, item?.query)) {
        currentRoute = toRaw(item);
        setTitle = true;
      }
    });
  } else if (Object.keys(route.params).length > 0) {
    multiTags.forEach(item => {
      if (isEqual(route.params, item?.params)) {
        currentRoute = toRaw(item);
        setTitle = true;
      }
    });
  } else {
    currentRoute = findRouteByPath(router.currentRoute.value.path, routes);
  }

  if (setTitle && currentRoute) {
    // 框架改造 如果是带参数的标签页 重新设置浏览器title
    const Title = getConfig().Title;
    if (Title) document.title = `${transformI18n(currentRoute.meta.title)}  |  ${Title}`;
  }

  // 当前路由的父级路径组成的数组
  const parentRoutes = getParentPaths(router.currentRoute.value.name as string, routes, "name");
  // 存放组成面包屑的数组
  const matched = [];

  // 获取每个父级路径对应的路由信息
  parentRoutes.forEach(path => {
    if (path !== "/") matched.push(findRouteByPath(path, routes));
  });

  matched.push(currentRoute);

  matched.forEach((item, index) => {
    if (currentRoute?.query || currentRoute?.params) return;
    if (item?.children) {
      item.children.forEach(v => {
        if (v?.meta?.title === item?.meta?.title) {
          matched.splice(index, 1);
        }
      });
    }
  });

  levelList.value = matched.filter(item => item?.meta && item?.meta.title !== false);
};

const handleLink = item => {
  const { redirect, name, path, meta } = item;

  if (meta?.isBreadcrumbLink == null || meta.isBreadcrumbLink) {
    if (redirect) {
      router.push(redirect as any);
    } else {
      if (name) {
        if (item.query) {
          router.push({
            name,
            query: item.query
          });
        } else if (item.params) {
          router.push({
            name,
            params: item.params
          });
        } else {
          router.push({ name });
        }
      } else {
        router.push({ path });
      }
    }
  }
};

onMounted(() => {
  getBreadcrumb();
});

watch(
  () => route.path,
  () => {
    getBreadcrumb();
  },
  {
    deep: true
  }
);

// 框架改造, 监听标签页变量，适配业务组件中增加的标签页内容
watch(
  () => multiTags,
  () => {
    getBreadcrumb();
  },
  {
    deep: true
  }
);
</script>

<template>
  <el-breadcrumb class="!leading-[50px] select-none" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="item in levelList" :key="item.path" class="!inline !items-stretch">
        <a
          v-if="item.meta?.isBreadcrumbLink == null || item.meta.isBreadcrumbLink"
          @click.prevent="handleLink(item)"
        >
          {{ transformI18n(item.meta.title) }}
        </a>
        <span v-else>
          {{ transformI18n(item.meta.title) }}
        </span>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>
