CREATE TABLE [dbo].[I18n] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_I18n_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NULL,
    [ParentId]                  UNIQUEIDENTIFIER            NULL,
    [EnumCategory]              INT                         NOT NULL,
    [EnumType]                  INT                         NOT NULL,
    [EnumFlags]                 INT                         NOT NULL,
    [Rank]                      INT                         NOT NULL,
    [Group]                     NVARCHAR(500)               NOT NULL,
    [Key]                       NVARCHAR(200)               NOT NULL,
    [Name]                      NVARCHAR(200)               NOT NULL,
    [Text]                      NVARCHAR(500)               NOT NULL,
    [Remark]                    NVARCHAR(500)               NOT NULL,
    [Valid]				        BIT					        NOT NULL,
    [IsSys]                     BIT                         NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_I18n] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_I18n_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_I18n_I18n_00_Parent_Children] FOREIGN KEY ([ParentId]) REFERENCES [dbo].[I18n] ([ID])
)
GO



CREATE UNIQUE INDEX [IX_I18n] ON [dbo].[I18n]
(
	[CompanyId] ASC,
	[Group] ASC,
	[Key] ASC
);
GO
