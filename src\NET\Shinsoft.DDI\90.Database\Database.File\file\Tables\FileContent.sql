﻿CREATE TABLE [file].[FileContent] (
    [ID]                        UNIQ<PERSON><PERSON>ENTIFIER    NOT NULL    CONSTRAINT [DF_FileContent_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER    NOT NULL,
    [Content]                   VARBINARY (MAX)     NOT NULL,
    CONSTRAINT [PK_FileContent] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_FileContent_FileIndex] FOREIGN KEY ([ID]) REFERENCES [file].[FileIndex] ([ID])
);


GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'文件内容',
    @level0type = N'SCHEMA',
    @level0name = N'file',
    @level1type = N'TABLE',
    @level1name = N'FileContent',
    @level2type = N'COLUMN',
    @level2name = N'Content'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'文件内容',
    @level0type = N'SCHEMA',
    @level0name = N'file',
    @level1type = N'TABLE',
    @level1name = N'FileContent',
    @level2type = NULL,
    @level2name = NULL