﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NLog;

namespace Shinsoft.DDI.Bll
{
    public class ScheduleBll : BaseCompanyBll
    {
        #region Constructs

        public ScheduleBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public ScheduleBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public ScheduleBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public ScheduleBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        public BizResult SendMails(bool manual = false)
        {
            var result = new BizResult();

            JobLogEvent? logEvent = null;

            try
            {
                var mailBll = this.GetRepo<MailBll>();
                var syncResult = mailBll.SendMails();

                if (!syncResult.Success)
                {
                    result.Merge(syncResult);

                    var msg = string.Join(';', result.Messages);
                    logEvent = NLogHelper.GetLogEvent<JobLogEvent>("发送邮件", msg, level: LogLevel.Error);
                }
            }
            catch (Exception ex)
            {
                result.Error(ex.Message);

                if (manual)
                {
                    logEvent = NLogHelper.GetLogEvent<JobLogEvent>(ex, "发送邮件", ex.Message);
                }
                else
                {
                    logEvent = NLogHelper.GetLogEvent<JobLogEvent>(ex, "定时发送邮件", ex.Message);
                }
            }

            if (logEvent != null)
            {
                logEvent.CompanyId = this.CurrentCompany.ID.ToString();
                logEvent.CompanyCode = this.CurrentCompany.Code;
                logEvent.CompanyName = this.CurrentCompany.Name;

                NLogHelper.Log(logEvent);
            }

            return result;
        }

        public BizResult SendMails(List<Expression<Func<Mail, bool>>> exps, int? maxSendCount, bool manual = false)
        {
            var result = new BizResult();

            JobLogEvent? logEvent = null;

            try
            {
                var mailBll = this.GetRepo<MailBll>();
                var syncResult = mailBll.SendMails(exps, maxSendCount);

                if (!syncResult.Success)
                {
                    result.Merge(syncResult);

                    var msg = string.Join(';', result.Messages);
                    logEvent = NLogHelper.GetLogEvent<JobLogEvent>("发送邮件", msg, level: LogLevel.Error);
                }
            }
            catch (Exception ex)
            {
                result.Error(ex.Message);

                if (manual)
                {
                    logEvent = NLogHelper.GetLogEvent<JobLogEvent>(ex, "发送邮件", ex.Message);
                }
                else
                {
                    logEvent = NLogHelper.GetLogEvent<JobLogEvent>(ex, "定时发送邮件", ex.Message);
                }
            }

            if (logEvent != null)
            {
                logEvent.CompanyId = this.CurrentCompany.ID.ToString();
                logEvent.CompanyCode = this.CurrentCompany.Code;
                logEvent.CompanyName = this.CurrentCompany.Name;

                NLogHelper.Log(logEvent);
            }

            return result;
        }
    }
}