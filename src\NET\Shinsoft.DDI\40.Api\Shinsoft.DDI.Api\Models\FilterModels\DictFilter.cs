﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class DictFilter
    {
        /// <summary>
        /// 关键词（编码,名称）
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(Dict), Dict.Columns.Remark, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Dict), Dict.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }


        /// <summary>
        /// 父级ID
        /// </summary>
        [DynamicQueryColumn(typeof(Dict), Dict.Columns.ParentId, Operation = Operation.Equal)]
        public Guid? ParentId { get; set; }

        /// <summary>
        /// 编码
        /// </summary>
        [DynamicQueryColumn(typeof(Dict), Dict.Columns.Code, Operation = Operation.StringIntelligence)]
        public string? Code { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [DynamicQueryColumn(typeof(Dict), Dict.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Name { get; set; }
    }
}
