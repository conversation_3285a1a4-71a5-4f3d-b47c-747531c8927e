﻿CREATE TABLE [log].[LogTargetType] (
    [ID]                INT                 NOT NULL IDENTITY (1, 1) ,
    [TargetType]        NVARCHAR (200)      NULL,
    CONSTRAINT [PK_LogTargetType] PRIMARY KEY CLUSTERED ([ID] ASC)
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'目标类型',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogTargetType'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'目标类型',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogTargetType',
    @level2type = N'COLUMN',
    @level2name = N'TargetType'

