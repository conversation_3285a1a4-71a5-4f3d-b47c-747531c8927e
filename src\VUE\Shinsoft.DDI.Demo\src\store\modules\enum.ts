/**
 * 框架改造
 * 重写了整个用户信息的存储、读写逻辑
 * todo: refreshToken 和 autoLogin
 */
import { defineStore } from "pinia";
import { store } from "../utils";
import { selectorApi } from "@/api/selector";

export const useEnumStore = defineStore({
  id: "pure-enum",
  state: () => ({
    // 枚举定义 {"枚举名称": {"组(grouo)": [ enumInfo1, enumInfo2 ] } }
    enums: {} as Record<string, Record<string, Array<EnumInfo>>>
  }),
  actions: {
    /** 存储枚举信息 */
    SET_ENUM_INFO(enumType: string, group: string, enumInfos: Array<EnumInfo>) {
      if (!this.enums.hasOwnProperty(enumType)) {
        this.enums[enumType] = {} as Record<string, Array<EnumInfo>>;
      }

      this.enums[enumType][group] = enumInfos;
    },
    /** 获取枚举信息 */
    async getEnumInfos(enumType: string, group?: string) {
      return new Promise<Array<EnumInfo>>((resolve, reject) => {
        group ??= "";

        if (this.enums.hasOwnProperty(enumType) && this.enums[enumType].hasOwnProperty(group)) {
          const enumInfos = this.enums[enumType][group];

          resolve(enumInfos);
        } else {
          const data: any = {
            enumType
          };

          if (group) {
            data.group = group;
          }

          selectorApi
            .GetEnumInfos(data)
            .then(res => {
              if (res.success) {
                const { data } = res;
                this.SET_ENUM_INFO(enumType, group, data);

                resolve(data);
              } else {
                resolve(null);
              }
            })
            .catch(error => {
              reject(error);
            });
        }
      });
    },
    clear() {
      this.enums = {};
    }
  }
});

export function useEnumStoreHook() {
  return useEnumStore(store);
}
