﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class DepartmentMeta
    {
        [MapFromProperty(typeof(Department), nameof(Department.Text))]
        public string? Text { get; set; }

        [MapFromProperty(typeof(Department), Department.Columns.Rank, Reverse = false)]
        public override int Rank { get; set; }

        [MapFromProperty(typeof(Department), Department.Foreigns.SubCompany, SubCompany.Columns.Code)]
        public string? SubCompanyCode { get; set; }

        [MapFromProperty(typeof(Department), Department.Foreigns.SubCompany, SubCompany.Columns.Name)]
        public string? SubCompanyName { get; set; }

        [MapFromProperty(typeof(Department), Department.Foreigns.Parent, Department.Columns.Code)]
        public string? ParentCode { get; set; }

        [MapFromProperty(typeof(Department), Department.Foreigns.Parent, Department.Columns.Name)]
        public string? ParentName { get; set; }

        [MapFromProperty(typeof(Department), Department.Foreigns.Parent, nameof(Department.Text))]
        public string? ParentText { get; set; }
    }
}
