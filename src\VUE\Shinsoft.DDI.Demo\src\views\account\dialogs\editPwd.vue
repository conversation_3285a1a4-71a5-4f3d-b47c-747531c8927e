<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { useEnumStoreHook } from "@/store/modules/enum";
import { ElMessage, FormInstance } from "element-plus";
import { allowEdit } from "@/utils/auth";
import { personApi } from "@/api/person";

defineOptions({
  name: "account:editPwd"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "50%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "120px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 24
  }
});

/**
 * dialog：标题
 */
const title = "修改密码";

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {}
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  }
});

/**
 * 数据变量
 */
const state = reactive({
  model: cfg.default.model as Record<string, any>
});

/**
 * 验证规则
 */
const rules = {
  form: {
    oldPwd: [
      { required: true, message: "请输入旧密码", trigger: "blur" },
      { max: 50, message: "编码长度不可以超过50个字符", trigger: "blur" }
    ],
    newPwd: [
      { required: true, message: "请输入新密码", trigger: "blur" },
      { max: 50, message: "姓名长度不可以超过50个字符", trigger: "blur" }
    ],
    newPwd2: [
      { required: true, message: "请输重复密码", trigger: "blur" },
      { max: 50, message: "备注长度不可以超过50个字符", trigger: "blur" }
    ]
  }
};
/**
 * 密码的验证
 */
const messages = (msg: any) => {
  if (msg) {
    ElMessage({
      message: msg,
      dangerouslyUseHTMLString: true,
      showClose: true,
      type: "error"
    });
  }
};
/**
 * 存储
 */
const userStore = useUserStoreHook();

/**
 * 当前组件ref
 */
const formRef = ref<FormInstance>();

/**
 * 初始化组件
 */
const init = () => {
  // 防止之前打开的数据残留，因此初始化时state
  initState();
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 初始化state数据
 */
const initState = () => {
  state.model = cfg.default.model;
};

/**
 * 清除state数据
 */
const clearState = () => {
  initState();
  formRef.value.clearValidate();
  formRef.value.resetFields();
};

/**
 * 更新数据
 */
const edit = () => {
  cfg.loading.btn = true;

  const data = getModel();
  personApi
    .ChangeMyPwd(data, { messageBoxResult: true })
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh("保存成功", "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 按钮事件：【保存】
 */
const save = async () => {
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      var regex = new RegExp("(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,30}");
      if (state.model.oldPwd.trim() === state.model.newPwd.trim()) {
        messages("原密码不能与新密码相同");
      } else if (state.model.newPwd.trim() !== state.model.newPwd2.trim()) {
        messages("重复新密码必须与新密码一致");
      } else if (!regex.test(state.model.newPwd.trim())) {
        messages(
          "您的新密码复杂度太低(密码中必须包含字母、数字、特殊字符，至少8个字符，最多30个字符)，请修改新密码！"
        );
      } else {
        edit();
      }
    }
  });
};

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string) => {
  cfg.dialog.visible = true;
  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        v-loading="cfg.loading.form"
        :rules="rules.form"
        :model="state.model"
        label-position="right"
        :label-width="formLabelWidth"
        class="el-dialog-form"
      >
        <el-row :gutter="formGutter">
          <el-col :span="formColSpan">
            <el-form-item prop="oldPwd" label="原密码">
              <el-input
                v-model="state.model.oldPwd"
                clearable
                type="password"
                show-password
                how-word-limit
                placeholder="原密码"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="newPwd" label="新密码">
              <el-input
                v-model="state.model.newPwd"
                clearable
                type="password"
                show-password
                show-word-limit
                placeholder="新密码"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="newPwd2" label="重复密码">
              <el-input
                v-model="state.model.newPwd2"
                clearable
                type="password"
                show-password
                show-word-limit
                placeholder="重复密码"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div>
          <el-button
            v-if="userStore.isAgent === false"
            class="save"
            :round="btnRound"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnSaveIcon)"
            @click="save()"
          >
            保存
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
