﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using Microsoft.EntityFrameworkCore;
using Shinsoft.Core.EntityFrameworkCore;
using Shinsoft.DDI.Entities;

namespace Shinsoft.DDI.Dal
{
    public partial class BizDbContext : BaseDbContext
    {
	    public BizDbContext(DbContextOptions options)
			: base(options)
        { 
		}

        /// <summary>
        /// 公告
        /// </summary>
		public DbSet<Announcement> Announcement { get; set; }

        /// <summary>
        /// 公告内容
        /// </summary>
		public DbSet<AnnouncementContent> AnnouncementContent { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
		public DbSet<Attachment> Attachment { get; set; }

        /// <summary>
        /// 权限
        /// </summary>
		public DbSet<Auth> Auth { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<AuthTag> AuthTag { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<BizGroup> BizGroup { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<BizGroupMember> BizGroupMember { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<BizMail> BizMail { get; set; }

        /// <summary>
        /// 日历
        /// </summary>
		public DbSet<Calendar> Calendar { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
		public DbSet<City> City { get; set; }

        /// <summary>
        /// 列映射
        /// </summary>
		public DbSet<ColumnMapping> ColumnMapping { get; set; }

        /// <summary>
        /// 列映射模版
        /// </summary>
		public DbSet<ColumnMappingTemplate> ColumnMappingTemplate { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
		public DbSet<Company> Company { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CompanyAuth> CompanyAuth { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CompanyCfg> CompanyCfg { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CompanyCulture> CompanyCulture { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<CompanyCurrency> CompanyCurrency { get; set; }

        /// <summary>
        /// 扩展配置
        /// </summary>
		public DbSet<CompanySetting> CompanySetting { get; set; }

        /// <summary>
        /// 成本中心
        /// </summary>
		public DbSet<CostCenter> CostCenter { get; set; }

        /// <summary>
        /// 区县
        /// </summary>
		public DbSet<County> County { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
		public DbSet<Department> Department { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<DepartmentCostCenter> DepartmentCostCenter { get; set; }

        /// <summary>
        /// 字典
        /// </summary>
		public DbSet<Dict> Dict { get; set; }

        /// <summary>
        /// 月库存
        /// </summary>
		public DbSet<DistributorInventoryDaily> DistributorInventoryDaily { get; set; }

        /// <summary>
        /// 日库存
        /// </summary>
		public DbSet<DistributorPurchaseDaily> DistributorPurchaseDaily { get; set; }

        /// <summary>
        /// 月采购
        /// </summary>
		public DbSet<DistributorPurchaseMonthly> DistributorPurchaseMonthly { get; set; }

        /// <summary>
        /// 日库存
        /// </summary>
		public DbSet<DistributorSalesFlowDaily> DistributorSalesFlowDaily { get; set; }

        /// <summary>
        /// 月库存
        /// </summary>
		public DbSet<DistributorSalesFlowMonthly> DistributorSalesFlowMonthly { get; set; }

        /// <summary>
        /// 员工
        /// </summary>
		public DbSet<Employee> Employee { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<EmployeeDelegate> EmployeeDelegate { get; set; }

        /// <summary>
        /// 员工代理权限
        /// </summary>
		public DbSet<EmployeeDelegateAuth> EmployeeDelegateAuth { get; set; }

        /// <summary>
        /// 员工代理权限标签
        /// </summary>
		public DbSet<EmployeeDelegateAuthTag> EmployeeDelegateAuthTag { get; set; }

        /// <summary>
        /// 员工岗位
        /// </summary>
		public DbSet<EmployeeStation> EmployeeStation { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<I18n> I18n { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<I18nCulture> I18nCulture { get; set; }

        /// <summary>
        /// 导入日志
        /// </summary>
		public DbSet<ImportDataLog> ImportDataLog { get; set; }

        /// <summary>
        /// 销售流向导入临时表
        /// </summary>
		public DbSet<ImportInventoryTemp> ImportInventoryTemp { get; set; }

        /// <summary>
        /// 销售流向导入临时表
        /// </summary>
		public DbSet<ImportPurchaseTemp> ImportPurchaseTemp { get; set; }

        /// <summary>
        /// 销售流向导入临时表
        /// </summary>
		public DbSet<ImportSalesFlowTemp> ImportSalesFlowTemp { get; set; }

        /// <summary>
        /// 药企
        /// </summary>
		public DbSet<Manufacturer> Manufacturer { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
		public DbSet<Position> Position { get; set; }

        /// <summary>
        /// 产品
        /// </summary>
		public DbSet<Product> Product { get; set; }

        /// <summary>
        /// 产品别名
        /// </summary>
		public DbSet<ProductAlias> ProductAlias { get; set; }

        /// <summary>
        /// 产品规格
        /// </summary>
		public DbSet<ProductSpec> ProductSpec { get; set; }

        /// <summary>
        /// 省份
        /// </summary>
		public DbSet<Province> Province { get; set; }

        /// <summary>
        /// 经销商/收货方
        /// </summary>
		public DbSet<Receiver> Receiver { get; set; }

        /// <summary>
        /// 收货方别名
        /// </summary>
		public DbSet<ReceiverAlias> ReceiverAlias { get; set; }

        /// <summary>
        /// 收货方配置
        /// </summary>
		public DbSet<ReceiverClient> ReceiverClient { get; set; }

        /// <summary>
        /// 客户端日志
        /// </summary>
		public DbSet<ReceiverClientLog> ReceiverClientLog { get; set; }

        /// <summary>
        /// 收货方类型
        /// </summary>
		public DbSet<ReceiverType> ReceiverType { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ReviewAuditor> ReviewAuditor { get; set; }

        /// <summary>
        /// 数据审核内容
        /// </summary>
		public DbSet<ReviewData> ReviewData { get; set; }

        /// <summary>
        /// 数据审核扩展信息
        /// </summary>
		public DbSet<ReviewExtInfo> ReviewExtInfo { get; set; }

        /// <summary>
        /// 数据审核目录
        /// </summary>
		public DbSet<ReviewIndex> ReviewIndex { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<ReviewTask> ReviewTask { get; set; }

        /// <summary>
        /// 角色
        /// </summary>
		public DbSet<Role> Role { get; set; }

        /// <summary>
        /// 角色权限
        /// </summary>
		public DbSet<RoleAuth> RoleAuth { get; set; }

        /// <summary>
        /// 角色权限标签
        /// </summary>
		public DbSet<RoleAuthTag> RoleAuthTag { get; set; }

        /// <summary>
        /// 角色成员
        /// </summary>
		public DbSet<RoleMember> RoleMember { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SerialNumber> SerialNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SerialSeed> SerialSeed { get; set; }

        /// <summary>
        /// 发货方
        /// </summary>
		public DbSet<Shipper> Shipper { get; set; }

        /// <summary>
        /// 货主产品配置表
        /// </summary>
		public DbSet<ShipperProductSpec> ShipperProductSpec { get; set; }

        /// <summary>
        /// 货主经销商配置
        /// </summary>
		public DbSet<ShipperReceiver> ShipperReceiver { get; set; }

        /// <summary>
        /// 岗位
        /// </summary>
		public DbSet<Station> Station { get; set; }

        /// <summary>
        /// 分公司
        /// </summary>
		public DbSet<SubCompany> SubCompany { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SyncTask> SyncTask { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<SysCulture> SysCulture { get; set; }

        /// <summary>
        /// 系统设置
        /// </summary>
		public DbSet<SysSetting> SysSetting { get; set; }

        /// <summary>
        /// 用户
        /// </summary>
		public DbSet<User> User { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VueRoute> VueRoute { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VueRouteMeta> VueRouteMeta { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwEmployeeStation> VwEmployeeStation { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwOrganization> VwOrganization { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwRoleMember> VwRoleMember { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwStation> VwStation { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public DbSet<VwStationLeader> VwStationLeader { get; set; }

	}
}