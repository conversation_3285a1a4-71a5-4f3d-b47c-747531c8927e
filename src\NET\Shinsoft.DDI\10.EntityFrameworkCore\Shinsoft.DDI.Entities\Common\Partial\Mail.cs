﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Shinsoft.Core.Mail;

namespace Shinsoft.DDI.Entities
{
    public partial class Mail : IMail
    {
        #region IMail

        [NotMapped, XmlIgnore, JsonIgnore]
        object IMail.ID => this.ID;

        [NotMapped, XmlIgnore, JsonIgnore]
        List<IMailAttachment>? IMail.MailAttachments => this.MailAttachments?.Cast<IMailAttachment>().ToList();

        [NotMapped, XmlIgnore, JsonIgnore]
        public List<MailAttachment>? MailAttachments { get; set; }

        #endregion IMail

        [NotMapped, XmlIgnore, JsonIgnore]
        public MailTrigger EnumTrigger
        {
            get => this.Trigger.ToEnum<MailTrigger>();
            set => this.Trigger = value == MailTrigger.None
                ? string.Empty
                : value.ToString();
        }
    }
}