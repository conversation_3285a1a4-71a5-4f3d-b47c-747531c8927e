﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Common
{
    public static class DictionaryExtender
    {
        public static bool ContainsKeyIgnoreCase<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, TKey checkKey)
            where TKey : notnull
        {
            if (typeof(TKey) == typeof(string))
            {
                return dictionary.Keys.Any(key => string.Equals(key.AsString(), checkKey.AsString(), StringComparison.CurrentCultureIgnoreCase));
            }

            return dictionary.ContainsKey(checkKey);
        }

        public static TValue? GetValueIgnoreCase<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, TKey checkKey)
            where TKey : notnull
        {
            if (typeof(TKey) == typeof(string))
            {
                foreach (var item in dictionary.Keys.Where(item => string.Equals(item.AsString(), checkKey.AsString(), StringComparison.CurrentCultureIgnoreCase)))
                {
                    return dictionary[item];
                }
                return default;
            }

            return dictionary.ContainsKey(checkKey) ? dictionary[checkKey] : default;
        }
    }
}