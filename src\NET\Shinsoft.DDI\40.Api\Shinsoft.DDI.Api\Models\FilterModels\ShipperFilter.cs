﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using static Org.BouncyCastle.Crypto.Engines.SM2Engine;

namespace Shinsoft.DDI.Api.Models
{
    public partial class ShipperFilter
    {
      
        /// <summary>
        /// 关键词
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(Shipper), Shipper.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Shipper), Shipper.Columns.Name, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Shipper), Shipper.Columns.ShortName, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Shipper), Shipper.Columns.Address, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Shipper), Shipper.Columns.Telephone, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Shipper), Shipper.Columns.EMail, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Shipper), Shipper.Columns.ContactPerson, Operation = Operation.StringIntelligence)]       
        public string? Keywords { get; set; }

        /// <summary>
        /// Code
        /// </summary>
        [Description("Code")]
        [DynamicQueryColumn(typeof(Shipper), Shipper.Columns.Code, Operation = Operation.StringIntelligence)]
        public string? Code { get; set; }


        /// <summary>
        /// 货主名称
        /// </summary>
        [Description("货主名称")]
        [DynamicQueryColumn(typeof(Shipper), Shipper.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Name { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [Description("联系人")]
        [DynamicQueryColumn(typeof(Shipper), Shipper.Columns.ContactPerson, Operation = Operation.StringIntelligence)]
        public string? ContactPerson { get; set; }
        

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        [DynamicQueryColumn(typeof(Shipper), Shipper.Columns.EnumStatus, Operation = Operation.In)]
        public List<ShipperStatus>? Status { get; set; }

      
    }
}
