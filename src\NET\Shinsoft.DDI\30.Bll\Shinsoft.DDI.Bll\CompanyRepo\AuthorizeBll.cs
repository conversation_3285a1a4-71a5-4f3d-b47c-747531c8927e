﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class AuthorizeBll : BaseCompanyBll
    {
        #region Constructs

        public AuthorizeBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        public AuthorizeBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        public AuthorizeBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        public AuthorizeBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs

        #region Employee

        /// <summary>
        /// 生成用户默认密码
        /// </summary>
        protected string GenUserDefaultPwdText()
        {
            //请在此方法内改动默认密码规则
            var format = this.CompanyCache.Company.Cfg!.DefaultPwdFormat;

            if (format.IsEmpty())
            {
                format = Config.DefaultPwdFormat;
            }

            return SysDateTime.Now.ToString(format);
        }

        protected BizResult<Employee> CheckEmployee(Employee employee, BizResult<Employee>? result = null)
        {
            result ??= new BizResult<Employee>();

            var loginNameType = this.CurrentCompany.Cfg!.EnumLoginNameType;

            if (employee.DisplayName.IsEmpty())
            {
                result.Error(I18ns.Rule.Employee.DisplayName_Empty);
            }

            if (loginNameType == LoginNameType.JobNo && employee.JobNo.IsEmpty())
            {
                result.Error(I18ns.Rule.Employee.JobNo_Empty);
            }

            if (employee.Email.IsEmpty())
            {
                result.Error(I18ns.Rule.Employee.Email_Empty);
            }
            else if (!Config.EmailRegex.IsEmpty() && !Regex.IsMatch(employee.Email, Config.EmailRegex))
            {
                result.Error(I18ns.Rule.Employee.Email_FormateError);
            }
            else
            {
                var json = this.CompanyCache.Company.Cfg!.EmailSuffix;

                if (json.IsEmpty())
                {
                    var emailSuffixes = json.DeserializeJson<List<string>>();

                    if (emailSuffixes?.Count > 0 && !emailSuffixes.Any(p => employee.Email.EndsWith(p)))
                    {
                        //result.Error($"只允许使用公司指定的邮箱:{}");
                        result.Error(I18ns.Rule.Employee.Format.CompanyEmail, string.Join(',', emailSuffixes));
                    }
                }
            }

            if (!employee.Mobile.IsEmpty() && !Config.MobileRegex.IsEmpty() && !Regex.IsMatch(employee.Mobile, Config.MobileRegex))
            {
                result.Error(I18ns.Rule.Employee.Mobile_FormateError);
            }

            if (employee.EnumStatus == EmployeeStatus.None)
            {
                result.Error(I18ns.Rule.Employee.Choose_EmployeeState);
            }

            if (!employee.JobNo.IsEmpty() || !employee.Email.IsEmpty())
            {
                var exps = this.NewExps<Employee>();

                if (!employee.ID.IsEmpty())
                {
                    exps.And(p => p.ID != employee.ID);
                }

                var exp = this.NewExp<Employee>(p => false);

                if (!employee.JobNo.IsEmpty())
                {
                    exp = exp.Or(p => p.JobNo == employee.JobNo);
                }

                if (!employee.Email.IsEmpty())
                {
                    exp = exp.Or(p => p.Email == employee.Email);
                }

                exps.And(exp);

                var exist = this.GetEntities(exps);

                if (!employee.JobNo.IsEmpty() && exist.Any(p => p.JobNo == employee.JobNo))
                {
                    result.Error(I18ns.Rule.Employee.JobNo_Repeat);
                }

                if (!employee.Email.IsEmpty() && exist.Any(p => p.Email == employee.Email))
                {
                    result.Error(I18ns.Rule.Employee.Email_Repeat);
                }
            }

            return result;
        }

        /// <summary>
        /// 检查员工对应用户是否符合规则
        /// </summary>
        protected User? CheckEmployeeUser(Employee employee, BizResult<Employee> result)
        {
            User? user = null;

            var loginNameType = this.CurrentCompany.Cfg!.EnumLoginNameType;

            var loginName = loginNameType switch
            {
                LoginNameType.Email => employee.Email,
                LoginNameType.JobNo => employee.JobNo,
                LoginNameType.EmailPrefix => employee.Email.Split('@').First(),
                _ => employee.LoginName
            };

            if (result.Success)
            {
                if (employee.UserId.HasValue)
                {
                    user = this.Get<User>(employee.UserId.Value);

                    if (user == null)
                    {
                        result.Error(I18ns.Rule.Employee.ChooseEmployee_NotExist);
                    }
                    else if (!loginName.IsEmpty() && !loginName.Equals(user.LoginName, StringComparison.InvariantCultureIgnoreCase))
                    {
                        var existUserCount = this.GetCount<User>(p => p.LoginName == loginName);

                        if (existUserCount == 0)
                        {
                            user.LoginName = loginName;
                        }
                        else
                        {
                            loginName = user.LoginName;
                        }
                    }
                }

                if (!loginName.IsEmpty())
                {
                    employee.LoginName = loginName;

                    user ??= this.GetEntity<User>(p => p.LoginName == loginName);

                    if (user != null && user.GroupCompanyId != this.CurrentCompany.GroupId)
                    {
                        result.Error(I18ns.Rule.Employee.CanNotChoose_OtherCompanyEmployee);
                    }
                }
            }

            return user;
        }

        /// <summary>
        /// 根据员工添加用户
        /// </summary>
        protected User NewEmployeeUser(Employee employee, bool save)
        {
            var user = new User
            {
                ID = employee.ID,
                GroupCompanyId = this.CurrentCompany.GroupId,
                EnumType = UserType.Employee,
                LoginName = employee.LoginName,
                EnumPwdType = PwdType.None,
                PwdText = this.GenUserDefaultPwdText(),
                DefaultCompanyId = this.CurrentCompanyId,
                DefaultEmployeeId = employee.ID,
            };

            user.CopyFrom(employee);

            if (Config.AllowLoginEmployeeStatus.Contains(employee.EnumStatus))
            {
                user.EnumStatus = UserStatus.Valid;
            }
            else
            {
                user.EnumStatus = UserStatus.Invalid;
            }

            this.Add(user, save);

            return user;
        }

        /// <summary>
        /// 检查员工信息是否符合规则
        /// 新增/更新时调用
        /// </summary>

        protected void CalcEmployeeUserStatus(Employee employee, User user, bool save)
        {
            var employees = this.SysRepo.GetEntities<Employee>(p => p.UserId == user.ID);

            if (!employees.Any(p => p.ID == employee.ID))
            {
                employees.Add(employee);
            }

            employees = employees.Where(p => !p.Deleted).ToList();

            user.EnumStatus = employees.Any(p => Config.AllowLoginEmployeeStatus.Contains(p.EnumStatus)) ? UserStatus.Valid : UserStatus.Invalid;

            if (employees.Count == 1)
            {
                user.DefaultEmployeeId = employees.First().ID;

                this.Update(user, false);
            }

            this.SaveChanges(save);
        }

        /// <summary>
        /// 新增员工
        /// </summary>
        public BizResult<Employee> AddEmployee(Employee entity)
        {
            var result = this.CheckEmployee(entity);

            User? user = this.CheckEmployeeUser(entity, result);

            if (result.Success)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                var newUser = user == null && !entity.LoginName.IsEmpty();

                if (newUser)
                {
                    user = this.NewEmployeeUser(entity, false);

                    //result.Info($"已创建用户，默认密码：{user.PwdText}");
                    result.Info(I18ns.Message.Entity.Format.CreatedEmployee_DefaultPwd, user.PwdText);
                }

                entity.UserId = user?.ID;

                entity = this.Add(entity, false);

                if (user != null && !newUser)
                {
                    this.CalcEmployeeUserStatus(entity, user, false);
                }

                this.SaveChanges();

                result.Data = entity;
            }

            return result;
        }

        /// <summary>
        /// 更新员工
        /// </summary>
        public BizResult<Employee> UpdateEmployee(Employee entity)
        {
            var result = new BizResult<Employee>();

            var id = entity.ID;
            var dbEntity = this.Get<Employee>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Rule.Employee.NotExist);
            }
            else
            {
                var user = dbEntity.User;

                if (user != null)
                {
                    entity.UserId = user.ID;

                    entity.RemoveChangedColumn(Employee.Columns.UserId);
                    entity.RemoveChangedColumn(Employee.Columns.LoginName);
                }

                if (this.Update(ref entity, false))
                {
                    result = this.CheckEmployee(entity, result);

                    user ??= this.CheckEmployeeUser(entity, result);

                    if (result.Success)
                    {
                        var newUser = user == null && !entity.LoginName.IsEmpty();

                        if (newUser)
                        {
                            user = this.NewEmployeeUser(entity, false);

                            //result.Info($"已创建用户，默认密码：{user.PwdText}");
                            result.Info(I18ns.Message.Entity.Format.CreatedEmployee_DefaultPwd, user.PwdText);
                        }

                        entity.UserId = user?.ID;

                        if (user != null && !newUser)
                        {
                            this.CalcEmployeeUserStatus(entity, user, false);
                        }
                    }
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.RollbackChanges();
                }
            }

            return result;
        }

        /// <summary>
        /// 删除员工
        /// </summary>
        public BizResult DeleteEmployee(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<Employee>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Rule.Employee.NotExist);
            }
            else if (dbEntity.ID == this.CurrentEmployeeId)
            {
                result.Error(I18ns.Rule.Employee.CanNot_RemoveSelf);
            }
            else
            {
                this.Delete(dbEntity, false);

                var user = dbEntity.User;

                if (user != null)
                {
                    if (user.DefaultEmployeeId == dbEntity.ID)
                    {
                        user.DefaultEmployeeId = null;

                        this.CalcEmployeeUserStatus(dbEntity, user, false);
                    }
                }

                this.SaveChanges();
            }

            return result;
        }

        public BizResult ResetEmployeePwd(Guid employeeId, string? newPwdText = null)
        {
            var result = new BizResult();

            var dbEntity = this.Get<Employee>(employeeId);

            if (dbEntity == null)
            {
                result.Error(I18ns.Rule.Employee.NotExist);
            }
            else if (dbEntity.EnumStatus != EmployeeStatus.InService)
            {
                result.Error(I18ns.Rule.Employee.EmployeeState_Error);
            }
            else if (!dbEntity.UserId.HasValue)
            {
                result.Error(I18ns.Rule.Employee.Employee_NoInit);
            }
            else if (dbEntity.User?.EnumStatus != UserStatus.Valid)
            {
                //result.Error("员工用户不可用");
                result.Error(I18ns.Rule.Employee.Employee_Invaild);
            }
            else
            {
                if (newPwdText.IsEmpty())
                {
                    newPwdText = this.GenUserDefaultPwdText();
                }

                var user = new User
                {
                    ID = dbEntity.UserId.Value,
                    EnumPwdType = PwdType.None,
                    PwdText = newPwdText!
                };

                this.Update(ref user, false);

                user.IncreaseSeed();

                this.SaveChanges();

                //result.Info($"密码已重置为：{newPwdText}");
                result.Info(I18ns.Message.Entity.Format.Pwd_Reset, newPwdText!);
            }

            return result;
        }

        /// <summary>
        /// 设置主岗
        /// </summary>
        public BizResult<Employee> SetEmployeeMajor(Employee entity)
        {
            var result = new BizResult<Employee>();

            var id = entity.ID;
            var dbEntity = this.Get<Employee>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Rule.Employee.NotExist);
            }
            else
            {
                var dbStation = this.Get<Station>(entity.MajorStationId);
                if (dbStation == null)
                {
                    result.Error(I18ns.Rule.EmployeeStation.NotExist);
                }
                else
                {
                    dbEntity.MajorDepartmentId = dbStation.DepartmentId;
                    dbEntity.MajorSubCompanyId = dbStation.CompanyId;
                    dbEntity.MajorCostCenterId = dbStation.Department.DefaultCostCenterId;
                    dbEntity.MajorStationId = entity.MajorStationId;

                    var resultEntity = this.Update(dbEntity);
                    result.Data = resultEntity;
                }
            }

            return result;
        }

        #endregion Employee

        #region Role

        protected BizResult<Role> CheckRole(Role entity, BizResult<Role>? result = null)
        {
            result ??= new BizResult<Role>();

            if (entity.Name.IsEmpty())
            {
                result.Error(I18ns.Rule.Role.Name_Required);
            }

            if (!entity.Code.IsEmpty() || !entity.Name.IsEmpty())
            {
                var exps = this.NewExps<Role>();

                if (!entity.ID.IsEmpty())
                {
                    exps.And(p => p.ID != entity.ID);
                }

                var exp = this.NewExp<Role>(p => false);

                if (!entity.Code.IsEmpty())
                {
                    exp = exp.Or(p => p.Code == entity.Code);
                }

                if (!entity.Name.IsEmpty())
                {
                    exp = exp.Or(p => p.Name == entity.Name);
                }

                exps.And(exp);

                var exist = this.GetEntities(exps);

                if (!entity.Code.IsEmpty() && exist.Any(p => p.Code == entity.Code))
                {
                    result.Error(I18ns.Rule.Role.Code_Repeat);
                }

                if (!entity.Name.IsEmpty() && exist.Any(p => p.Name == entity.Name))
                {
                    result.Error(I18ns.Rule.Role.Name_Repeat);
                }
            }

            return result;
        }

        public BizResult<Role> AddRole(Role entity)
        {
            var result = this.CheckRole(entity);

            if (result.Success)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity.EnumEditFlags = EditFlag.Unlimited;

                entity = this.Add(entity);

                result.Data = entity;
            }

            return result;
        }

        public BizResult<Role> UpdateRole(Role entity)
        {
            var result = new BizResult<Role>();

            var id = entity.ID;
            var dbEntity = this.Get<Role>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Rule.Role.NotExist);
            }
            else
            {
                entity.RemoveChangedColumn(Role.Columns.EnumEditFlags);

                if (!dbEntity.AllowEdit(EditFlag.Code))
                {
                    entity.Code = dbEntity.Code;
                    entity.RemoveChangedColumn(Role.Columns.Code);
                }

                if (!dbEntity.AllowEdit(EditFlag.Name))
                {
                    entity.Name = dbEntity.Name;
                    entity.RemoveChangedColumn(Role.Columns.Name);
                }

                if (!dbEntity.AllowEdit(EditFlag.Flag))
                {
                    entity.EnumFlags = dbEntity.EnumFlags;
                    entity.RemoveChangedColumn(Role.Columns.EnumFlags);
                }

                if (!dbEntity.AllowEdit(EditFlag.Remark))
                {
                    entity.Remark = dbEntity.Remark;
                    entity.RemoveChangedColumn(Role.Columns.Remark);
                }

                var editFlags = typeof(RoleFlag).GetEnumInfos("Edit").Where(p => !p.Disabled).Select(p => p.Value.ToEnum<RoleFlag>());

                var dbExtFlags = dbEntity.EnumFlags;

                foreach (var flag in editFlags)
                {
                    dbExtFlags &= ~flag;
                }

                entity.EnumFlags |= dbExtFlags;

                if (this.Update(ref entity, false))
                {
                    result = this.CheckRole(entity, result);
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.RollbackChanges();
                }
            }

            return result;
        }

        public BizResult DeleteRole(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<Role>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Rule.Role.NotExist);
            }
            else if (!dbEntity.AllowEdit(EditFlag.Delete))
            {
                //result.Error($"不允许删除角色【{dbEntity.Name}】");
                result.Error(I18ns.Rule.Role.Format.CanNot_Remove, dbEntity.Name);
            }
            else
            {
                this.Delete(dbEntity);
            }

            return result;
        }

        #endregion Role

        #region RoleAuth

        public BizResult SetRoleAuths(Guid roleId, List<Guid> authIds, List<RoleAuth> tagRoleAuths)
        {
            var result = new BizResult();
            var cc = this.CompanyCache;

            Role? role = null;

            var query = cc.Auths.Where(p => p.EnumType == AuthType.Permission && !p.EnumFlags.HasFlag(AuthFlag.Invisible));

            var auths = query.Where(p => p.EnumAuthTagType == AuthTagType.None && authIds.Contains(p.ID)).ToList();

            var tagAuthIds = tagRoleAuths
                .Where(p => p.AllowAllTags.GetValueOrDefault() || p.AuthTags?.Count > 0)
                .Select(p => p.AuthId)
                .ToList();

            var tagAuths = query.Where(p => p.EnumAuthTagType != AuthTagType.None && tagAuthIds.Contains(p.ID)).ToList();

            if (roleId.IsEmpty())
            {
                result.Error(I18ns.Rule.Role.Choose_Role);
            }
            else
            {
                role = this.Get<Role>(roleId);

                if (role == null)
                {
                    result.Error(I18ns.Rule.Role.NotExist);
                }

                if (authIds.Any(id => !auths.Any(auth => auth.ID == id)))
                {
                    result.Error(I18ns.Rule.Role.Auth_InVaild);
                }

                if (tagAuthIds.Any(id => !tagAuths.Any(auth => auth.ID == id)))
                {
                    result.Error(I18ns.Rule.Role.AuthContent_InVaild);
                }
            }

            if (result.Success)
            {
                #region Auth

                var dbAllRoleAuths = this.GetEntities<RoleAuth>(nameof(Auth), p => p.RoleId == roleId && !p.Auth.EnumFlags.HasFlag(AuthFlag.Invisible));

                var dbRoleAuths = dbAllRoleAuths
                    .Where(p => p.Auth.EnumAuthTagType == AuthTagType.None)
                    .ToList();

                var delRoleAuths = dbRoleAuths
                    .Where(p => !auths.Any(auth => auth.ID == p.AuthId))
                    .ToList();

                var addRoleAuths = auths
                    .Where(auth => !dbRoleAuths.Any(p => p.AuthId == auth.ID))
                    .ToList();

                foreach (var del in delRoleAuths)
                {
                    this.Remove(del, false);
                }

                foreach (var add in addRoleAuths)
                {
                    var roleAuth = new RoleAuth
                    {
                        ID = CombGuid.NewGuid(),
                        RoleId = roleId,
                        AuthId = add.ID,
                        AllowAllTags = null
                    };

                    this.Add(roleAuth, false);
                }

                #endregion Auth

                #region Tag Auth

                var dbRoleAuthTags = this.GetEntities<RoleAuthTag>(p => p.RoleId == roleId);

                var dbTagRoleAuths = dbAllRoleAuths
                    .Where(p => p.Auth.EnumAuthTagType != AuthTagType.None)
                    .ToList();

                var delTagRoleAuths = dbTagRoleAuths
                    .Where(p => !tagAuths.Any(auth => auth.ID == p.AuthId))
                    .ToList();

                foreach (var del in delTagRoleAuths)
                {
                    var delTags = dbRoleAuthTags.Where(p => p.AuthId == del.AuthId).ToList();

                    foreach (var tag in delTags)
                    {
                        this.Remove(tag, false);
                    }

                    this.Remove(del, false);
                }

                foreach (var tagAuth in tagAuths)
                {
                    var tagRoleAuth = tagRoleAuths.First(p => p.AuthId == tagAuth.ID);
                    var dbTagRoleAuth = dbTagRoleAuths.FirstOrDefault(p => p.AuthId == tagAuth.ID);

                    var tags = tagRoleAuth.AuthTags;

                    // 读取 Auth Tag ，只读取合法的tag
                    var authTags = cc.AuthTags
                        .Where(p => p.EnumType == tagAuth.EnumAuthTagType
                            && (p.EnumProgramFlags == ProgramFlag.None || (p.EnumProgramFlags & tagAuth.EnumProgramFlags) != ProgramFlag.None))
                        .Where(p => tags?.Any(p1 => p1.ID == p.ID) == true)
                        .ToList();

                    if (dbTagRoleAuth == null)
                    {
                        tagRoleAuth.ID = CombGuid.NewGuid();
                        tagRoleAuth.RoleId = roleId;

                        tagRoleAuth.AllowAllTags ??= false;

                        this.Add(tagRoleAuth, false);
                    }
                    else
                    {
                        dbTagRoleAuth.AllowAllTags = tagRoleAuth.AllowAllTags ?? false;
                    }

                    var dbTags = dbRoleAuthTags.Where(p => p.AuthId == tagAuth.ID).ToList();

                    var delQuery = dbTags.AsQueryable();
                    IEnumerable<AuthTag>? addQuery = null;

                    if (tagRoleAuth.AllowAllTags == true)
                    {
                        // 允许所有标签
                        //删除所有tag，且无需添加新tag
                    }
                    else
                    {
                        delQuery = delQuery.Where(p => !authTags.Any(p1 => p1.ID == p.AuthTagId));
                        addQuery = authTags.Where(p => !dbTags.Any(p1 => p1.AuthTagId == p.ID));
                    }

                    // 删除Tag
                    var delTags = delQuery.ToList();

                    foreach (var delTag in delTags)
                    {
                        this.Remove(delTag, false);
                    }

                    if (addQuery != null)
                    {
                        //需要新增tag
                        var addAuthTags = addQuery.ToList();

                        foreach (var addAuthTag in addAuthTags)
                        {
                            var roleAuthTag = new RoleAuthTag
                            {
                                ID = CombGuid.NewGuid(),
                                RoleId = roleId,
                                AuthId = tagAuth.ID,
                                AuthTagId = addAuthTag.ID,
                            };

                            this.Add(roleAuthTag, false);
                        }
                    }
                }

                #endregion Tag Auth

                this.SaveChanges();

                cc.RemoveCache<Role>();
            }

            return result;
        }

        #endregion RoleAuth

        #region RoleMember

        public BizResult AddRoleMembers(Guid roleId, RoleMemberType memberType, List<Guid> memberIds)
        {
            var result = new BizResult();

            Role? role = null;

            if (roleId.IsEmpty())
            {
                result.Error(I18ns.Rule.Role.Choose_Role);
            }
            else
            {
                role = this.Get<Role>(roleId);

                if (role == null)
                {
                    result.Error(I18ns.Rule.Role.NotExist);
                }
            }

            if (memberType == RoleMemberType.None)
            {
                result.Error(I18ns.Rule.Role.Choose_EmployeeType);
            }

            List<IPrimaryKey<Guid>>? members = null;

            if (memberIds.Count > 0)
            {
                switch (memberType)
                {
                    case RoleMemberType.User:
                        members = this.GetEntities<User>(p => memberIds.Contains(p.ID)).Cast<IPrimaryKey<Guid>>().ToList();
                        break;

                    case RoleMemberType.Employee:
                        members = this.GetEntities<Employee>(p => memberIds.Contains(p.ID)).Cast<IPrimaryKey<Guid>>().ToList();
                        break;

                    case RoleMemberType.Station:
                        members = this.GetEntities<Station>(p => memberIds.Contains(p.ID)).Cast<IPrimaryKey<Guid>>().ToList();
                        break;

                    case RoleMemberType.Position:
                        members = this.GetEntities<Position>(p => memberIds.Contains(p.ID)).Cast<IPrimaryKey<Guid>>().ToList();
                        break;

                    case RoleMemberType.Department:
                        members = this.GetEntities<Department>(p => memberIds.Contains(p.ID)).Cast<IPrimaryKey<Guid>>().ToList();
                        break;
                }

                if (members?.Count > 0)
                {
                    //存在成员
                }
                else
                {
                    result.Error(I18ns.Rule.Role.Employee_InVaild);
                }
            }
            else
            {
                result.Error(I18ns.Rule.Role.Choose_Employee);
            }

            if (result.Success)
            {
                memberIds = members!.Select(p => p.ID).ToList();

                var dbMembers = this.GetEntities<RoleMember>(p => p.EnumType == memberType && memberIds.Contains(p.ID));

                var newIds = memberIds.Where(p => !dbMembers.Any(p1 => p1.ID == p)).ToList();

                if (newIds.Count > 0)
                {
                    foreach (var memberId in newIds)
                    {
                        var roleMember = new RoleMember
                        {
                            ID = CombGuid.NewGuid(),
                            RoleId = roleId,
                            EnumType = memberType,
                            MemberId = memberId
                        };

                        this.Add(roleMember, false);
                    }

                    this.SaveChanges();
                }
            }
            return result;
        }

        public BizResult DeleteRoleMember(Guid id)
        {
            var result = new BizResult();

            var entity = new RoleMember
            {
                ID = id
            };

            this.Remove(entity);

            return result;
        }

        #endregion RoleMember
    }
}
