﻿CREATE TABLE [dbo].[ShipperProductSpec]
(
	[ID]  UNIQUEIDENTIFIER            NOT NULL  CONSTRAINT [Default_ShipperProductSpec_ID] DEFAULT (NEWSEQUENTIALID()), 
	[ShipperId] UNIQUEIDENTIFIER NOT NULL,
    [ProductSpecId] UNIQUEIDENTIFIER NOT NULL,
	[Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL, 
    [CreateTime]                DATETIME                    NULL, 
    [LastEditor]                NVARCHAR(50)                NULL, 
    [LastEditTime]              DATETIME                    NULL, 
	CONSTRAINT [PK_ShipperProductSpec] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ShipperProductSpec_Manufacturer] FOREIGN KEY ([ShipperId]) REFERENCES [dbo].[Shipper] ([ID]),
	CONSTRAINT [FK_ShipperProductSpec_ProductSpec] FOREIGN KEY ([ProductSpecId]) REFERENCES [dbo].[ProductSpec] ([ID]),
)
GO


EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'货主产品配置表',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperProductSpec',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
 
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'货主Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'ShipperId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'规格Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'ProductSpecId'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperProductSpec',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'
