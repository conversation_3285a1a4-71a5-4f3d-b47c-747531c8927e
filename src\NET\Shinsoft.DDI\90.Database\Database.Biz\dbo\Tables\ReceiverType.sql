CREATE TABLE [dbo].[ReceiverType] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL,
    ParentReceiverTypeId        UNIQUEIDENTIFIER            NULL,
    [Code]                      NVARCHAR(50)                NOT NULL,
    [Name]                      NVARCHAR(50)                NOT NULL,
    [ShortName]                 NVARCHAR(50)                NOT NULL,
    [EnumStatus]                INT                         NOT NULL,
    [Remark]                    NVARCHAR(500)               NULL,
    [Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL,
    [CreateTime]                DATETIME                    NULL,
    [LastEditor]                NVARCHAR(50)                NULL,
    [LastEditTime]              DATETIME                    NULL,
    CONSTRAINT [PK_ReceiverType] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ReceiverType_ReceiverType_00_ParentReceiverType_SubReceiverType] FOREIGN KEY (ParentReceiverTypeId) REFERENCES [dbo].[ReceiverType] ([ID])
) ON [PRIMARY];
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'收货方类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'父级收货方类型ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = N'COLUMN',
    @level2name = N'ParentReceiverTypeId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'类型编码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = N'COLUMN',
    @level2name = N'Code'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'类型名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = N'COLUMN',
    @level2name = N'Name'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'类型简称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = N'COLUMN',
    @level2name = N'ShortName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'状态',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = N'COLUMN',
    @level2name = N'EnumStatus'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = N'COLUMN',
    @level2name = N'Remark'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverType',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'
