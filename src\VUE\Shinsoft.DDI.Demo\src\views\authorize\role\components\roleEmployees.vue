<script setup lang="ts">
import { authorizeApi } from "@/api/authorize";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getColumnOrder, getDefaultOrder, resetFilter } from "@/utils/table";
import { useEnumStoreHook } from "@/store/modules/enum";
import { useI18n } from "vue-i18n";
import { Sort } from "element-plus";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "role:view:employee"
});

const roleId = defineModel<string>("roleId");

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    datas: [],
    total: 0,
    filter: {
      roleId: "",
      pageSize: 10
    }
  },
  enums: {
    gender: [],
    employeeStatus: []
  },
  filter: {
    gutter: 3,
    span: 6
  },
  list: {
    height: 450,
    defaultSort: { prop: "displayName", order: "ascending" } as Sort
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  roleName: "",
  datas: cfg.default.datas,
  total: 0
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({});

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 存储
 */
const enumStore = useEnumStoreHook();

/**
 * 当前组件ref
 */
const listRef = ref();

/**
 * 初始化组件(created时调用)
 */
const init = (init?: boolean) => {
  init ??= true;
  if (init) {
    // 防止之前打开的数据残留，因此初始化时赋予state默认值
    initState();
    initFilter().then(() => {
      query();
    });
  }
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  resetFilter(filter, cfg.default.filter);
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  cfg.loading.filter = true;
  const initGender = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("Gender").then(enumInfos => {
        cfg.enums.gender = enumInfos;
        resolve();
      });
    });
  };

  cfg.loading.filter = true;
  const initEmployeeStatus = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("EmployeeStatus").then(enumInfos => {
        cfg.enums.employeeStatus = enumInfos;
        resolve();
      });
    });
  };

  const allInits = [initGender(), initEmployeeStatus()];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.filter = false;
      resolve();
    });
  });
};

/**
 * 设置model数据
 */
const setModel = (res: QueryResult) => {
  state.datas = res.datas;
  state.total = res.total;
  filter.pageIndex = res.pageIndex;
};

/**
 * 初始化state
 */
const initState = () => {
  state.datas = cfg.default.datas;
  state.total = cfg.default.total;
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  if (roleId.value) {
    cfg.loading.list = true;
    filter.roleId = roleId.value;
    authorizeApi
      .QueryRoleEmployee(filter)
      .then(res => {
        setModel(res);
      })
      .finally(() => {
        cfg.loading.list = false;
      });
  }
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  init
});
</script>

<template>
  <div>
    <div class="filter-container">
      <el-form
        ref="filterRef"
        v-loading="cfg.loading.filter"
        class="filter-form"
        :rules="rules.filter"
        :model="filter"
      >
        <el-row :gutter="cfg.filter.gutter" type="flex">
          <el-col :span="cfg.filter.span">
            <el-form-item>
              <el-input
                v-model="filter.keywords"
                clearable
                :placeholder="t('filter.keywords')"
                class="filter-item"
                @keyup.enter="query"
              />
            </el-form-item>
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-form-item>
              <el-select
                v-model="filter.genders"
                multiple
                clearable
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="2"
                :placeholder="tt('Entity.Employee.EnumGender')"
              >
                <el-option
                  v-for="item in cfg.enums.gender"
                  :key="item.value"
                  :label="item.text"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-form-item>
              <el-select
                v-model="filter.statuses"
                multiple
                clearable
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="3"
                :placeholder="tt('Entity.Employee.EnumStatus')"
              >
                <el-option
                  v-for="item in cfg.enums.employeeStatus"
                  :key="item.value"
                  :label="item.text"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-button
              class="query"
              :loading="cfg.loading.list"
              :icon="useRenderIcon('ep:search')"
              @click="query"
            >
              {{ t("operate.query") }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="list-container">
      <el-table
        ref="listRef"
        v-loading="cfg.loading.list"
        :data="state.datas"
        row-key="id"
        stripe
        border
        class-name="list"
        style="width: 100%"
        :height="cfg.list.height"
        :default-sort="cfg.list.defaultSort"
        @sort-change="sortChange"
      >
        <template #empty>
          <NoDatas />
        </template>
        <el-table-column
          fixed
          :label="t('list.no')"
          :show-overflow-tooltip="true"
          width="60"
          align="center"
        >
          <template v-slot="scope">
            <span>{{ (filter.pageIndex - 1) * filter.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed
          sortable="custom"
          prop="loginName"
          :label="tt('Entity.User.LoginName')"
          width="150"
        />
        <el-table-column
          fixed
          sortable="custom"
          prop="displayName"
          :label="tt('Entity.Employee.DisplayName')"
          width="180"
        />
        <el-table-column
          sortable="custom"
          prop="jobNo"
          :label="tt('Entity.Employee.JobNo')"
          width="100"
        />
        <el-table-column
          sortable="custom"
          prop="email"
          :label="tt('Entity.Employee.Email')"
          min-width="200"
        />
        <el-table-column
          sortable="custom"
          prop="Title"
          :label="tt('Entity.Employee.Title')"
          width="150"
        />
        <el-table-column
          sortable="custom"
          prop="position"
          :label="tt('Entity.Employee.Position')"
          width="150"
        />
        <el-table-column
          sortable="custom"
          sort-by="enumGender"
          prop="enumGenderDesc"
          :label="tt('Entity.Employee.EnumGender')"
          width="120"
          align="center"
        />
        <el-table-column
          sortable="custom"
          sort-by="enumStatus"
          prop="enumStatusDesc"
          :label="tt('Entity.Employee.EnumStatus')"
          width="120"
          align="center"
        />
      </el-table>
      <pagination v-model:filter="filter" :total="state.total" @change="getList" />
    </div>
  </div>
</template>
