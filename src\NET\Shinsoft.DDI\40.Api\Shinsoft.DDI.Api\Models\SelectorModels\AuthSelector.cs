﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class AuthSelector
    {
        [MapFromProperty(typeof(Auth), Auth.Foreigns.Parent, Auth.Columns.Code)]
        public string? ParentCode { get; set; }

        [MapFromProperty(typeof(Auth), Auth.Foreigns.Parent, Auth.Columns.Name)]
        public string? ParentName { get; set; }
    }
}