<script setup lang="ts">
import { useUserStoreHook } from "@/store/modules/user";
import { useEnumStoreHook } from "@/store/modules/enum";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { announcementApi } from "@/api/announcement";
import { useResizeObserver } from "@vueuse/core";
import Edit from "./dialogs/edit.vue";
import View from "./dialogs/view.vue";
import { getColumnOrder, getDefaultOrder } from "@/utils/table";
import Epearch from "@iconify-icons/ep/search";
import EpAdd from "@iconify-icons/ep/document-add";
import EpDocument from "@iconify-icons/ep/document";
import EpEdit from "@iconify-icons/ep/edit";
import EpDelete from "@iconify-icons/ep/delete";

import { useI18n } from "vue-i18n";
import { array } from "vue-types";
const { t } = useI18n();
const tt = t;

defineOptions({
  name: "announcement:query"
});

/**
 * 基本配置定义
 */
const userStore = useUserStoreHook();
const enumStore = useEnumStoreHook();

const cfg = reactive({
  filter: {
    gutter: 3,
    span: 4
  },
  list: {
    height: 0,
    defaultSort: { prop: "publishTime", order: "ascending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.edit ? 45 : 0))
    }
  },
  btn: {
    edit: computed(() => {
      return userStore.hasAnyAuth(["BizDict:Manage", "BizDict:Manage:Edit"]);
    })
  },
  enums: {
    importantList: []
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  }
});

onMounted(() => {
  //计算列表高度
  useResizeObserver(listContainerRef.value, entries => {
    const entry = entries[0];
    const { width, height } = entry.contentRect;
    cfg.list.height = height - 190;
  });
});

/**
 * 当前组件ref
 */
const editRef = ref();
const addRef = ref();
const viewRef = ref();
const filterRef = ref();
const listRef = ref();
const listContainerRef = ref();

/**
 * 初始化组件(created时调用)
 */
const init = () => {
  initFilter();
  initImportant();
  query();
};

const initImportant = async () => {
  return new Promise<void>(resolve => {
    enumStore.getEnumInfos("Important").then(enumInfos => {
      cfg.enums.importantList = enumInfos;
      resolve();
    });
  });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  //cfg.loading.filter = true;
};
/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  announcementApi
    .QueryAnnouncement(filter)
    .then(res => {
      state.datas = res.datas;
      state.total = res.total;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  keywords: "",
  dates: []
});

/**
 * 查看：点击列表中【查看】按钮事件
 */
const viewRow = (row: any) => {
  viewRef.value?.open(row.id);
};

/**
 * 编辑：点击列表中【编辑】按钮事件
 */
const showDialog = (row: any) => {
  editRef.value?.open(row.id);
};

/**
 * 删除：点击列表中【删除】按钮事件
 */
const deleteRow = (row: any) => {
  viewRef.value?.del(row);
};

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

/**
 * 序号
 */
const indexMethod = index => {
  return (filter.pageIndex - 1) * filter.pageSize + index + 1;
};

/**
 * 执行init,created时调用
 */
init();
</script>

<template>
  <div>
    <div class="filter-container">
      <el-form
        ref="filterRef"
        v-loading="cfg.loading.filter"
        class="filter-form"
        :rules="rules.filter"
        :model="filter"
      >
        <el-row :gutter="cfg.filter.gutter" type="flex" justify="start">
          <el-col :span="cfg.filter.span">
            <el-input
              v-model="filter.keywords"
              clearable
              :placeholder="tt('Entity.Announcement.Subject')"
              class="filter-item"
              @keyup.enter="query"
            />
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="filter.dates"
              style="width: 100%"
              type="daterange"
              range-separator=" - "
              :start-placeholder="tt('Entity.Announcement.StartTime')"
              :end-placeholder="tt('Entity.Announcement.EndTime')"
              @keyup.enter="query"
            />
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-form-item>
              <el-select
                v-model="filter.enumImportants"
                multiple
                clearable
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="3"
                :placeholder="tt('Entity.Announcement.EnumImportant')"
              >
                <el-option
                  v-for="item in cfg.enums.importantList"
                  :key="item.value"
                  :label="item.text"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button
              class="query"
              :loading="cfg.loading.list"
              :icon="useRenderIcon(Epearch)"
              @click="query"
            >
              {{ t("operate.query") }}
            </el-button>
          </el-col>
        </el-row>
        <el-row :gutter="cfg.filter.gutter" type="flex" justify="end">
          <el-col :span="24" class="buttonbar">
            <el-button class="new" :icon="useRenderIcon(EpAdd)" @click="showDialog">
              {{ t("operate.add") }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div ref="listContainerRef" class="list-container">
      <el-table
        ref="listRef"
        v-loading="cfg.loading.list"
        :data="state.datas"
        row-key="id"
        stripe
        border
        class-name="list"
        style="width: 100%"
        :height="cfg.list.height"
        :default-sort="cfg.list.defaultSort"
        @sort-change="sortChange"
      >
        <template #empty>
          <NoDatas />
        </template>
        <el-table-column
          fixed
          :label="t('list.no')"
          :index="indexMethod"
          type="index"
          width="70"
          align="center"
        />
        <el-table-column
          fixed
          sortable="custom"
          prop="subject"
          :label="tt('Entity.Announcement.Subject')"
          min-width="250"
        />
        <el-table-column
          sortable="custom"
          prop="startTime"
          :label="tt('Entity.Announcement.StartTime')"
        >
          <template #default="scope">
            <span>{{ scope.row.startTime }}</span>
          </template>
        </el-table-column>

        <el-table-column
          sortable="custom"
          prop="endTime"
          :label="tt('Entity.Announcement.EndTime')"
        >
          <template #default="scope">
            <span>{{ scope.row.endTime }}</span>
          </template>
        </el-table-column>
        <el-table-column sortable="custom" prop="isShow" :label="tt('Entity.Announcement.IsShow')">
          <template #default="scope">
            <span>{{ scope.row.isShow ? t("operate.yes") : t("operate.no") }}</span>
          </template>
        </el-table-column>

        <el-table-column
          fixed="right"
          :label="t('list.operate')"
          class-name="operate"
          align="center"
        >
          <template #default="{ row }">
            <el-button
              class="view"
              size="small"
              :circle="true"
              :title="t('operate.view')"
              :icon="useRenderIcon(EpDocument)"
              @click="viewRow(row)"
            />
            <el-button
              v-if="cfg.btn.edit"
              class="edit"
              size="small"
              :circle="true"
              :title="t('operate.edit')"
              :icon="useRenderIcon(EpEdit)"
              @click="showDialog(row)"
            />
            <el-button
              class="delete"
              size="small"
              :circle="true"
              :title="t('operate.delete')"
              :icon="useRenderIcon(EpDelete)"
              @click="deleteRow(row)"
            />
          </template>
        </el-table-column>
      </el-table>
      <Pagination v-model:filter="filter" :total="state.total" @change="getList" />
    </div>
    <Edit ref="editRef" @refresh="getList" />
    <View ref="viewRef" @refresh="getList" />
  </div>
</template>
