﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public enum ReviewType
    {
        /// <summary>
        /// 自动
        /// </summary>
        [Description("自动")]
        [EnumGroup("", Visible = true)]
        Auto = 0,

        /// <summary>
        /// 新增
        /// </summary>
        [Description("新增")]
        Add = 1,

        /// <summary>
        /// 更新
        /// </summary>
        [Description("更新")]
        Update = 2,

        /// <summary>
        /// 删除
        /// </summary>
        [Description("删除")]
        Delete = 3,

        /// <summary>
        /// 批量操作
        /// </summary>
        [Description("批量操作")]
        Batch = 10,
    }
}