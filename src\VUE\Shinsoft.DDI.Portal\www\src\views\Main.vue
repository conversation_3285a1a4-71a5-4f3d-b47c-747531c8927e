<template>
  <div class="layout">
    <div class="layout-container">
      <header class="layout-header">
        <div class="header">
          <div class="header_Up">
            <div class="header_Up_Box">
              <div class="header_Up_img">
                <img :src="logoSrc" alt />
              </div>
              <div class="header_Up_ul">
                <ul>
                  <!-- 首页 -->
                  <li :class="{ cur: num == 20 }" @click="onTab(20)">
                    <router-link
                      to="/home/<USER>"
                      style="color: #494949; font-size: 12px; width: 100%; height: 100%; display: block; text-decoration: none;"
                      >{{ $t("system.home") }}</router-link
                    >
                  </li>
                  <li
                    v-for="(item, index) in data"
                    :class="{ cur: index == num }"
                    @click="onTab(index)"
                    :key="index"
                  >
                    <span v-if="param == 0">{{ item.Name }}</span>
                    <span v-if="param == 1">{{ item.NameEn }}</span>
                  </li>
                </ul>
              </div>
            </div>
            <div class="user currentVersionNumberCss">
              <el-row>
                <el-col :span="24">
                  <el-dropdown class="user_Style" trigger="hover">
                    <div class="user-info-container">
                      <el-avatar
                        class="user-avatar"
                        :size="32"
                        :src="userAvatar"
                      >
                        <el-icon><User /></el-icon>
                      </el-avatar>
                      <div class="user-text-info">
                        <div class="user-name">{{ this.name }}</div>
                        <div class="shipper-name">{{ this.currentShipperName }}</div>
                      </div>
                      <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
                    </div>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="showChangeShipperDialog">
                          <el-icon><Switch /></el-icon>
                          变更货主
                        </el-dropdown-item>
                        <el-dropdown-item @click="updatePassword">
                          <el-icon><Setting /></el-icon>
                          {{ $t("main.updatePassword") }}
                        </el-dropdown-item>
                        <el-dropdown-item divided @click="onExitSystem">
                          <el-icon><SwitchButton /></el-icon>
                          {{ $t("main.mainExitSystem") }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="header_Down_Box" v-if="isShow">
            <div
              v-for="(itemCon, index) in data"
              class="header_Down"
              v-show="index == num"
              v-bind:key=index
            >
              <ul>
                <li v-for="TwoItemCon in itemCon.ChildrenModules"  v-bind:key="TwoItemCon.ID">
                  <router-link
                    :to="TwoItemCon.Route == null ? '' : TwoItemCon.Route"
                  >
                    <span v-if="param == 0">{{ TwoItemCon.Name }}</span>
                    <span v-if="param == 1">{{ TwoItemCon.NameEn }}</span>
                  </router-link>
                  <dl class="nav">
                    <dt v-for="threeitem in TwoItemCon.ChildrenModules" v-bind:key="threeitem.ID">
                      <router-link :to="threeitem.Route">
                        <span v-if="param == 0">{{ threeitem.Name }}</span>
                        <span v-if="param == 1">{{ threeitem.NameEn }}</span>
                      </router-link>
                    </dt>
                  </dl>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="header_Down_Border" v-if="!isShow"></div>
      </header>
      <main class="layout-content ivu-row-Bottom-30">
        <div>
          <router-view :key="activeDate"></router-view>
        </div>
      </main>
    <el-dialog
      v-model="showUpdatePasswordModal"
      :title="$t('userlist.updatePassword')"
      width="500px"
      :close-on-click-modal="false"
      @close="handleCancelUpdatePassword"
    >
      <div style="padding: 20px;">
        <el-form
          ref="updatePasswordForm"
          :model="employeeModel"
          :rules="ruleValidate"
          label-width="120px"
        >
          <el-form-item :label="$t('userlist.oldPassword')" prop="OldPassword">
            <el-input
              type="password"
              v-model="employeeModel.OldPassword"
              clearable
              show-password
            />
          </el-form-item>

          <el-form-item :label="$t('userlist.newPassword')" prop="NewPassword">
            <el-input
              type="password"
              v-model="employeeModel.NewPassword"
              clearable
              show-password
            />
          </el-form-item>

          <el-form-item :label="$t('userlist.pwdLevel')">
            <div class="password-strength">
              <div class="input_span">
                <span ref="refOne" class="one"></span>
                <span ref="refTwo" class="two"></span>
                <span ref="refThree" class="three"></span>
              </div>
              <div id="font">
                <span>弱</span>
                <span>中</span>
                <span>强</span>
              </div>
            </div>
          </el-form-item>

          <el-form-item :label="$t('userlist.confirmPassword')" prop="ConfirmPassword">
            <el-input
              type="password"
              v-model="employeeModel.ConfirmPassword"
              clearable
              show-password
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelUpdatePassword()">
            {{ $t("system.cancel") }}
          </el-button>
          <el-button
            type="primary"
            :disabled="!(this.pwdLevel>=3)"
            @click="handleUpdatePassword()"
          >
            {{ $t("system.save") }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 变更货主对话框 -->
    <el-dialog
      v-model="showChangeShipperModal"
      title="变更货主"
      width="500px"
      :close-on-click-modal="false"
      @close="handleCancelChangeShipper"
    >
      <div>
        <el-form :model="changeShipperModel" label-width="120px">
          <el-form-item label="当前货主：">
            <span>{{ currentShipperName }}</span>
          </el-form-item>
          <el-form-item label="变更货主：">
            <el-select
              v-model="changeShipperModel.newShipperId"
              placeholder="请选择货主"
              filterable
              remote
              :remote-method="searchShippers"
              :loading="shipperSearchLoading"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="shipper in shipperOptions"
                :key="shipper.id"
                :label="shipper.name"
                :value="shipper.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelChangeShipper">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="handleConfirmChangeShipper"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
  </div>
</template>
<script>
    import moment from 'moment';
    import logo_Prod from '../assets/cdms_logo.png';
    import logo_Test from '../assets/cdms_logo_test.png';
    import { ArrowDown, Setting, Switch, SwitchButton, User } from '@element-plus/icons-vue';
    export default {
        components: {
            ArrowDown,
            Setting,
            Switch,
            SwitchButton,
            User
        },
        data () {
            const validatePassword = (rule, value, callback) => {
                if (value !== this.employeeModel.NewPassword) {
                    callback(new Error(this.$t('userEdit.validPasswordCompare')))
                } else {
                    callback()
                }
            }
            return {
                languages: [
                    {
                        value: 'mergeZH',
                        label: '中'
                    },
                    {
                        value: 'mergeEN',
                        label: 'EN'
                    }
                ],
                account: '', // 登录账号
                name: '', // 登录人
                currentShipperName: '曙方医药', // 当前货主名称
                param: 0, // 语言切换条件
                userAvatar: '', // 用户头像URL
                activeDate: null, // 刷新路由
                num: 20,
                isShow: false,
                data: [],
                modalEditionShow: false,
                modalEditionList: [],
                filter: {
                    Page: 1,
                    Per: 3
                },
                // 总行数
                totalCount: 0,
                currentVersionNumber: '',
                logoSrc: logo_Test,
                employeeModel: {},
                showUpdatePasswordModal: false,
                pwdLevel: 0,
                ruleValidate: {
                    OldPassword: [
                        {
                            required: true,
                            type: 'string',
                            message: this.$t('userEdit.validOldPassword'),
                            trigger: 'blur,change'
                        }
                    ],
                    NewPassword: [
                        {
                            required: true,
                            type: 'string',
                            message: this.$t('userEdit.validNewPassword'),
                            trigger: 'blur,change'
                        }
                    ],
                    ConfirmPassword: [
                        {
                            required: true,
                            type: 'string',
                            message: this.$t('userEdit.validConfirmPassword'),
                            trigger: 'blur,change'
                        },
                        {validator: validatePassword}
                    ]
                },
                // 变更货主相关数据
                showChangeShipperModal: false,
                changeShipperModel: {
                    newShipperId: ''
                },
                shipperOptions: [],
                shipperSearchLoading: false,
                allShippers: [
                    // 模拟货主数据
                    { id: 1, name: 'Sperogenix Therapeutics Limited', shortName: '曙方医药', code: 'S001' },
                    { id: 2, name: '华润医药', shortName: '华润', code: 'S002' },
                    { id: 3, name: '国药控股', shortName: '国药', code: 'S003' },
                    { id: 4, name: '上海医药', shortName: '上药', code: 'S004' },
                    { id: 5, name: '九州通医药', shortName: '九州通', code: 'S005' },
                    { id: 6, name: '华东医药', shortName: '华东', code: 'S006' }
                ]
            };
        },

        created () {
            // set logo
            this.getLogo();
            // 初始化语言
            this.initLanguage();
            // 语言切换
            this.languageSwitch();
            this.account = localStorage.getItem('account');
            this.name = localStorage.getItem('name');
            // 导航栏样式记录-切换
            if (sessionStorage.sessionStorageNum == null) {
                sessionStorage.sessionStorageNum = 20;
            } else {
                this.num = sessionStorage.sessionStorageNum;
            }

            if (sessionStorage.getItem('sessionStorageNum') == 20) {
                this.isShow = false;
            } else {
                this.isShow = true;
            }
            this.initModule();
        },
        methods: {
            getLogo () {
                if (
                    // TODO
                    window.location.href.indexOf('cdms.sperogenix.com:') >= 0 || window.location.href.indexOf('cdms.sperogenix.com') === -1
                ) { this.logoSrc = logo_Test; } else this.logoSrc = logo_Prod;
            },
            initModule () {
                // 检查是否有模块数据，如果没有则使用模拟数据
                if (!localStorage.ModulesData) {
                    // 模拟模块数据 - 添加系统管理菜单
                    const mockModulesData = [
                        {
                            ID: 1,
                            Name: '主数据管理',
                            NameEn: 'Master Data Management',
                            Route: '/master',
                            ChildrenModules: [
                                {
                                    ID: 11,
                                    Name: '货主管理',
                                    NameEn: 'Shipper Management',
                                    Route: '/master/shipper',
                                    ChildrenModules: []
                                },
                                {
                                    ID: 12,
                                    Name: '收货方管理',
                                    NameEn: 'Distributor Management',
                                    Route: '/master/distributor',
                                    ChildrenModules: []
                                },
                                {
                                    ID: 13,
                                    Name: '产品管理',
                                    NameEn: 'Product Management',
                                    Route: '/master/product',
                                    ChildrenModules: []
                                }
                            ]
                        },
                        {
                            ID: 2,
                            Name: 'DDI配置监控',
                            NameEn: 'DDI Configuration & Monitoring',
                            Route: '/ddi-config',
                            ChildrenModules: [
                                {
                                    ID: 21,
                                    Name: 'DDI配置',
                                    NameEn: 'DDI Configuration',
                                    Route: null,
                                    ChildrenModules: [
                                        {
                                            ID: 211,
                                            Name: '经销商配置',
                                            NameEn: 'Distributor Configuration',
                                            Route: '/ddi-config/config/distributor',
                                            ChildrenModules: []
                                        },
                                        {
                                            ID: 212,
                                            Name: '产品配置',
                                            NameEn: 'Product Configuration',
                                            Route: '/ddi-config/config/product',
                                            ChildrenModules: []
                                        },
                                        {
                                            ID: 213,
                                            Name: '客户端配置',
                                            NameEn: 'Client Configuration',
                                            Route: '/ddi-config/config/client',
                                            ChildrenModules: []
                                        }
                                    ]
                                },
                                {
                                    ID: 22,
                                    Name: 'DDI监控',
                                    NameEn: 'DDI Monitoring',
                                    Route: '/ddi-config/monitor',
                                    ChildrenModules: []
                                }
                            ]
                        },
                        {
                            ID: 3,
                            Name: '数据清洗',
                            NameEn: 'Flow Cleaning',
                            Route: '/salesflow-cleaning',
                            ChildrenModules: [
                                {
                                    ID: 31,
                                    Name: '别名管理',
                                    NameEn: 'Alias Management',
                                    Route: null,
                                    ChildrenModules: [
                                        {
                                            ID: 311,
                                            Name: '产品别名',
                                            NameEn: 'Product Alias',
                                            Route: '/salesflow-cleaning/alias/product',
                                            ChildrenModules: []
                                        },
                                        {
                                            ID: 312,
                                            Name: '收货方别名',
                                            NameEn: 'Distributor Alias',
                                            Route: '/salesflow-cleaning/alias/distributor',
                                            ChildrenModules: []
                                        }
                                    ]
                                },
                                {
                                    ID: 32,
                                    Name: '流向导入日志',
                                    NameEn: 'Import Log',
                                    Route: '/salesflow-cleaning/import',
                                    ChildrenModules: []
                                }
                            ]
                        },
                        {
                            ID: 4,
                            Name: '数据查询',
                            NameEn: 'Flow Query',
                            Route: '/salesflow',
                            ChildrenModules: [
                                {
                                    ID: 41,
                                    Name: '日数据查询',
                                    NameEn: 'Daily Data Query',
                                    Route: null,
                                    ChildrenModules: [
                                        {
                                            ID: 411,
                                            Name: '日销售流向查询',
                                            NameEn: 'Daily Sales Flow Query',
                                            Route: '/salesflow/daily/salesflow',
                                            ChildrenModules: []
                                        },
                                        {
                                            ID: 412,
                                            Name: '日采购查询',
                                            NameEn: 'Daily Purchase Query',
                                            Route: '/salesflow/daily/purchase',
                                            ChildrenModules: []
                                        },
                                        {
                                            ID: 413,
                                            Name: '日库存查询',
                                            NameEn: 'Daily Inventory Query',
                                            Route: '/salesflow/daily/inventory',
                                            ChildrenModules: []
                                        }
                                    ]
                                },
                                {
                                    ID: 42,
                                    Name: '月数据查询',
                                    NameEn: 'Monthly Data Query',
                                    Route: null,
                                    ChildrenModules: [
                                        {
                                            ID: 421,
                                            Name: '月销售流向查询',
                                            NameEn: 'Monthly Sales Flow Query',
                                            Route: '/salesflow/monthly/salesflow',
                                            ChildrenModules: []
                                        },
                                        {
                                            ID: 422,
                                            Name: '月采购查询',
                                            NameEn: 'Monthly Purchase Query',
                                            Route: '/salesflow/monthly/purchase',
                                            ChildrenModules: []
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            ID: 5,
                            Name: '系统管理',
                            NameEn: 'System Management',
                            Route: '/system',
                            ChildrenModules: [
                                {
                                    ID: 51,
                                    Name: '用户管理',
                                    NameEn: 'User Management',
                                    Route: '/system/employee/userlist',
                                    ChildrenModules: []
                                },
                                {
                                    ID: 52,
                                    Name: '字典管理',
                                    NameEn: 'Dictionary Management',
                                    Route: '/system/dictionary/dictionarylist',
                                    ChildrenModules: []
                                },
                                {
                                    ID: 53,
                                    Name: '日志管理',
                                    NameEn: 'Log Management',
                                    Route: null,
                                    ChildrenModules: [
                                        {
                                            ID: 531,
                                            Name: '操作日志查询',
                                            NameEn: 'Operation Log Query',
                                            Route: '/system/log/operationlog',
                                            ChildrenModules: []
                                        },
                                        {
                                            ID: 532,
                                            Name: '异常日志查询',
                                            NameEn: 'Exception Log Query',
                                            Route: '/system/log/exceptionlog',
                                            ChildrenModules: []
                                        },
                                        {
                                            ID: 533,
                                            Name: '客户端日志查询',
                                            NameEn: 'Client Log Query',
                                            Route: '/system/log/clientlog',
                                            ChildrenModules: []
                                        }
                                    ]
                                }
                            ]
                        }
                    ];

                    localStorage.ModulesData = JSON.stringify(mockModulesData);
                }

                this.data = JSON.parse(localStorage.ModulesData);
            },
            // 初始化语言
            initLanguage () {
                this.$i18n.locale =
                    localStorage.getItem('currentLanguage') === null
                        ? 'mergeZH'
                        : localStorage.getItem('currentLanguage');
            },
            changePage (value) {
                this.filter.Page = value;
            },
            changePageSize (value) {
                this.filter.Per = value;
                this.filter.Page = 1;
            },
            onChangeLanguage (value) {
                if (value === undefined) return;
                this.$i18n.locale = value;
                localStorage.setItem('currentLanguage', value);
                // 刷新路由
                this.activeDate = new Date().toLocaleTimeString();
                this.languageSwitch(); // 语言切换
            },
            // 语言切换
            languageSwitch () {
                if (
                    this.$t('system.language') === '中' ||
                    this.$t('system.language') === 'mergeZH'
                ) {
                    this.param = 0;
                } else if (
                    this.$t('system.language') === 'EN' ||
                    this.$t('system.language') === 'mergeEN'
                ) {
                    this.param = 1;
                }
            },
            onLanguage () {
                if (this.$t('system.language') === '中') {
                    this.onChangeLanguage('mergeEN');
                } else if (this.$t('system.language') === 'EN') {
                    this.onChangeLanguage('mergeZH');
                }
            },
            // tab图片切换
            onTab (index) {
                this.num = index;
                if (this.num === 20) {
                    this.isShow = false;
                } else {
                    this.isShow = true;
                }
                sessionStorage.setItem('sessionStorageNum', index);
            },
            updatePassword () {
                this.showUpdatePasswordModal = true
            },
            // 退出系统
            onExitSystem () {
                this.$http
                    .get('/Sys/UserLogout')
                    .then((response) => {
                        // 检查响应结果
                        if (response.data && response.data.success) {
                            this.$message.success('退出登录成功');
                        } else {
                            console.log('后端登出响应:', response.data);
                        }
                        // 无论后台返回什么，都清除本地数据并跳转
                        this.clearUserData();
                        this.logout();
                    })
                    .catch((error) => {
                        console.error('退出登录失败:', error);
                        // 即使接口调用失败，也要清除本地数据并跳转
                        this.clearUserData();
                        this.logout();
                    });
            },
            // 清除用户数据
            clearUserData () {
                // 清除token和用户信息
                localStorage.removeItem('token');
                localStorage.removeItem('tokenStorage');
                localStorage.removeItem('userId');
                localStorage.removeItem('account');
                localStorage.removeItem('name');
                localStorage.removeItem('userInfo');

                // 清除其他可能的用户相关数据
                sessionStorage.clear();
            },
            logout () {
                window.location.href = window.location.protocol + '//' + window.location.host + '/#/login';
            },
            handleCancelUpdatePassword () {
                this.showUpdatePasswordModal = false;
                this.pwdLevel = 0;
                // Element Plus的表单重置方法
                if (this.$refs.updatePasswordForm) {
                    this.$refs.updatePasswordForm.resetFields();
                }
                // 重置密码强度指示器
                this.resetPasswordStrength();
            },
            handleUpdatePassword () {
                this.$refs.updatePasswordForm.validate((valid) => {
                    if (valid) {
                        this.employeeModel.LoginName = this.account
                        this.$http.post('/Employee/UpdatePassword', this.employeeModel).then(response => {
                            if (response.data.Message) {
                                this.$alert(response.data.Message, this.$t('system.alter'), {
                                    dangerouslyUseHTMLString: true
                                });
                            } else {
                                this.$message.success(this.$t('system.updateSuccess'));
                                this.showUpdatePasswordModal = false;
                                this.pwdLevel = 0;
                                this.$refs.updatePasswordForm.resetFields();
                                this.resetPasswordStrength();
                            }
                        }).catch(error => {
                            this.$message.error(this.$t('system.updateFail'));
                        })
                    }
                })
            },
            // 重置密码强度指示器
            resetPasswordStrength() {
                if (this.$refs.refOne) this.$refs.refOne.style.background = '#eee';
                if (this.$refs.refTwo) this.$refs.refTwo.style.background = '#eee';
                if (this.$refs.refThree) this.$refs.refThree.style.background = '#eee';
            },
            // 显示变更货主对话框
            showChangeShipperDialog() {
                this.showChangeShipperModal = true;
                // 初始化货主选项
                this.shipperOptions = this.allShippers.slice(0, 5);
            },
            // 搜索货主
            searchShippers(query) {
                if (query !== '') {
                    this.shipperSearchLoading = true;
                    // 模拟API调用延迟
                    setTimeout(() => {
                        this.shipperOptions = this.allShippers.filter(shipper =>
                            shipper.name.toLowerCase().includes(query.toLowerCase()) ||
                            shipper.code.toLowerCase().includes(query.toLowerCase())
                        );
                        this.shipperSearchLoading = false;
                    }, 200);
                } else {
                    this.shipperOptions = this.allShippers.slice(0, 5);
                }
            },
            // 取消变更货主
            handleCancelChangeShipper() {
                this.showChangeShipperModal = false;
                this.changeShipperModel.newShipperId = '';
                this.shipperOptions = [];
            },
            // 确认变更货主
            handleConfirmChangeShipper() {
                if (!this.changeShipperModel.newShipperId) {
                    this.$message.warning('请选择要变更的货主');
                    return;
                }

                const selectedShipper = this.allShippers.find(s => s.id === this.changeShipperModel.newShipperId);
                if (selectedShipper) {
                    this.currentShipperName = selectedShipper.name;
                    this.$message.success(`已成功变更为货主：${selectedShipper.name}`);
                    this.handleCancelChangeShipper();
                }
            },
            // 更新密码强度指示器
            updatePasswordStrength() {
                if (!this.$refs.refOne || !this.$refs.refTwo || !this.$refs.refThree) return;

                // 重置所有指示器
                this.resetPasswordStrength();

                // 根据强度级别设置颜色
                if (this.pwdLevel >= 1) {
                    this.$refs.refOne.style.background = 'red';
                }
                if (this.pwdLevel >= 2) {
                    this.$refs.refTwo.style.background = 'red';
                }
                if (this.pwdLevel >= 3) {
                    this.$refs.refTwo.style.background = 'orange';
                }
                if (this.pwdLevel >= 4) {
                    this.$refs.refThree.style.background = '#00D1B2';
                }
            },
            checkStrong (sValue) {
                var modes = 0;
                // 正则表达式验证符合要求的
                if (sValue.length < 1) return modes;
                if (/\d/.test(sValue)) modes++; // 数字
                if (/[a-z]/.test(sValue)) modes++; // 小写
                if (/[A-Z]/.test(sValue)) modes++; // 大写
                if (/\W/.test(sValue)) modes++; // 特殊字符

                // 逻辑处理
                switch (modes) {
                case 1:
                    return 1;
                case 2:
                    return 2;
                case 3:
                    return sValue.length < 8 ? 2 : 3;
                case 4:
                    return sValue.length < 8 ? 2 : 4;
                }
                return modes;
            }
        },
        watch: {
            'employeeModel.NewPassword': {
                handler (newname, oldname) {
                    // 使用nextTick确保DOM已更新
                    this.$nextTick(() => {
                        if (newname === null || newname === undefined || newname === '') {
                            this.resetPasswordStrength();
                            this.pwdLevel = 0;
                        } else {
                            this.pwdLevel = this.checkStrong(newname);
                            this.updatePasswordStrength();
                        }
                    });
                }
            }
        },

        filters: {
            formatDate: function (val) {
                if (val == null || val === undefined) {
                    return '';
                }
                return moment(val).format('YYYY-MM-DD');
            }
        }
    };
</script>
<style scoped>
/* 强制移除Content区域的滚动条，解决双滚动条问题 */
.ivu-layout-content {
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
}

/* 移除Content内部div的滚动条 */
.ivu-layout-content > div {
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
}

.updateContent {
  height: 200px;
  overflow: auto;
}
.updateContent::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
/*header_Up*/
.ivu-layout-header {
  padding: 0 !important;
  height: auto !important;
  line-height: 50px;
}

/*头部*/
.header {
  width: 100%;
  min-width: 1200px;
}

.header > .header_Up {
  width: 100%;
  padding: 0 15px;
  height: 50px;
  margin: 0 auto;
  position: relative;
  background: #fff;
}
.header > .header_Up > .header_Up_Box {
  display: inline-block;
  width: 78%;
  height: 50px;
}
.header > .header_Up > .header_Up_Box .header_Up_img {
  display: inline-block;
  width: 20%;
  height: 50px;
}
.header > .header_Up > .header_Up_Box .header_Up_img > img {
  padding: 5px 0;
  display: block;
  width: auto;
  height: 100%;
}
.header_Up_ul {
  display: inline-block;
  width: 80%;
  float: right;
}

.header > .header_Up > .header_Up_Box > .header_Up_ul > ul {
  display: inline-block;
  display: flex;
  justify-content: space-left;
  width: 100%;
  height: 100%;
}

.header > .header_Up .header_Up_Box > .header_Up_ul > ul > li {
  width: 90px;
  text-align: center;
  position: relative;
  line-height: 50px;
  cursor: pointer;
  color: #494949;
  text-decoration: none; /* 去掉下划线 */
  transition: color 0.3s ease; /* 添加颜色变化动画 */
  font-size: 12px;
}

.header > .header_Up .header_Up_Box > .header_Up_ul > ul > li a {
  text-decoration: none; /* 去掉链接下划线 */
  color: inherit;
  transition: color 0.3s ease; /* 添加颜色变化动画 */
  font-size: 12px;
}

.header > .header_Up > .header_Up_Box > .header_Up_ul ul > li:hover {
  color: #fd8900;
}
.header > .header_Up > .header_Up_Box > .header_Up_ul ul > li.cur:before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%; /* 与菜单宽度一致 */
  height: 5px;
  background: #fd9e00;
  transition: all 0.3s ease; /* 添加动画效果 */
}
/*有内容样式*/
.header_Down_Box {
  width: 100%;
  padding: 0 40px;
  height: 40px;
  background: #fd9e00;
  font-size: 12px;
}
.header_Down ul {
  height: 40px;
  display: flex;
  justify-content: flex-start;
  width: 100%;
}

.header_Down ul li {
  margin: 0 10px;
  position: relative;
  color: #fff;
  padding: 0px 5px;
  line-height: 20px;
  cursor: pointer;
}

.header_Down ul li:hover {
  background: #fd9e00;
}

.header_Down ul li:hover .nav {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.header_Down ul li a {
  color: #fff;
  display: block;
  height: 40px;
  line-height: 40px;
  text-decoration: none; /* 去掉二级菜单下划线 */
  transition: color 0.3s ease; /* 添加颜色变化动画 */
}
.header_Down ul li .nav {
  min-width: 160px;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  top: 100%;
  left: 0;
  background: #fd9e00;
  z-index: 1000;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.header_Down ul li .nav dt {
  display: block;
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
}
.header_Down ul li .nav dt a {
  color: #fff;
  text-decoration: none; /* 去掉三级菜单下划线 */
  transition: color 0.3s ease; /* 添加颜色变化动画 */
}
.header_Down ul li .nav dt:hover {
  background: #fd9e00;
}
.header_Down_Hind {
  display: none;
}
/*无内容样式*/
.header_Down_Border {
  height: 3px;
  width: 100%;
  background: #fd9e00;
}
.header .user {
  position: absolute;
  top: 0;
  left: 80%;
  width: 20%;
  height: 50px;
}
.user_Style {
  float: right;
}
/* 用户信息区域样式 */
.user-info-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 20px;
  transition: background-color 0.3s ease;
}

.user-info-container:hover {
  background-color: rgba(253, 158, 0, 0.1);
}

.user-avatar {
  margin-right: 8px;
  border: 2px solid #fd9e00;
  background-color: #f5f5f5;
}

.user-avatar .el-icon {
  color: #fd9e00;
  font-size: 18px;
}

.user-text-info {
  display: flex;
  flex-direction: column;
  margin-right: 8px;
}

.user-name {
  color: #494949;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 2px;
}

.shipper-name {
  color: #999;
  font-size: 12px;
  line-height: 1.2;
}

.dropdown-icon {
  color: #494949;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.user-info-container:hover .dropdown-icon {
  transform: rotate(180deg);
}

.user_Style {
  float: right;
}

.user_Style a {
  color: #494949;
  text-decoration: none; /* 去掉用户菜单下划线 */
}

/* 下拉菜单样式 */
.el-dropdown-menu__item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  font-size: 12px
}

.el-dropdown-menu__item .el-icon {
  margin-right: 8px;
  color: #fd9e00;
}

.el-dropdown-menu__item:hover {
  background-color: rgba(253, 158, 0, 0.1);
  color: #fd9e00;
}

/* 密码强度指示器样式 */
.password-strength {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.ivu-row {
  height: 50px;
}
.input_span span {
display: inline-block;
width: 85px;
height: 10px;
background: #eee;
line-height: 20px;
}

.one {
border-top-left-radius: 5px;
border-bottom-left-radius: 5px;
border-right: 0px solid;
margin-left: 20px;
margin-right: 3px;
}

.two {
border-left: 0px solid;
border-right: 0px solid;
margin-left: -5px;
margin-right: 3px;
}

.three {
border-top-right-radius: 5px;
border-bottom-right-radius: 5px;
border-left: 0px solid;
margin-left: -5px;
}
#font span:nth-child(1){
color:red;
margin-left: 80px;
}
#font span:nth-child(2){
    color:orange;
    margin: 0 60px;
  }
#font span:nth-child(3){
     color:#00D1B2;
  }
  p.UpdateContentClass > ul {
  margin-left: 20px !important;
}

.currentVersionNumberCss .ivu-tooltip-inner {
  max-width: 324px !important;
}


</style>

<!-- 全局公共样式 - 不使用scoped，供所有子组件使用 -->
<style>
/* ========== 公共页面样式 ========== */
/* 面包屑导航样式 */
.page-header {
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #dddee1;
}

.page-header .el-breadcrumb {
  font-size: 14px;
}

.page-header .el-breadcrumb-item:not(:last-child) {
  color: #999 !important;
  font-size: 14px !important;
}

.page-header .el-breadcrumb-item:not(:last-child) a {
  color: #999 !important;
  font-size: 14px !important;
}

/* 导航栏最后一级样式 - 深色加粗 */
.page-header .el-breadcrumb-item:last-child {
  font-size: 14px !important;
  font-weight: 700 !important;
  color: #495060 !important;
}

.page-header .el-breadcrumb-item:last-child .el-breadcrumb-item__inner {
  font-size: 14px !important;
  font-weight: 700 !important;
  color: #495060 !important;
}

.page-header .el-breadcrumb-item:last-child span {
  font-size: 14px !important;
  font-weight: 700 !important;
  color: #495060 !important;
}

/* 面包屑内部元素统一样式 */
.page-header .el-breadcrumb__inner {
  font-size: 14px !important;
  color: #999 !important;
}

/* 最后一级面包屑内部元素特殊样式 */
.page-header .el-breadcrumb-item:last-child .el-breadcrumb__inner {
  font-size: 14px !important;
  font-weight: 700 !important;
  color: #495060 !important;
}

/* 管理页面特有的页面头部样式 */
.page-header.management-style {
  padding: 16px 20px 0 20px;
  background: #f5f5f5;
  border-bottom: none;
}

/* 搜索容器样式 */
.search-container {
  padding: 20px 20px 0 20px;
  background: #f5f5f5 !important;
  margin-bottom: 0;
}



/* 查询区域输入框行间距 */
.search-container .el-col {
  margin-bottom: 16px;
}

/* 操作按钮容器样式 */
.action-container {
  padding: 0 20px 16px 20px;
  background: #f5f5f5;
  text-align: right;
}

.action-row {
  margin-bottom: 16px;
}

.action-row:last-child {
  margin-bottom: 0;
}

/* 表格容器样式 */
.table-container {
  background: #f5f5f5;
}

/* 分页容器样式 */
.pagination-container {
  padding-right: 20px;
  padding-bottom: 5px;
  background: #f5f5f5;
  display: flex;
  justify-content: flex-end;
}

/* 表单控件样式 */
.el-input,
.el-select,
.el-date-picker,
.el-cascader {
  width: 100%;
}

/* 统一查询框字号 */
.search-container .el-input input {
  font-size: 12px;
}

.search-container .el-cascader .el-input input {
  font-size: 12px;
}

/* 统一下拉框字号 */
.search-container .el-select .el-input input {
  font-size: 12px;
}

.search-container .el-select .el-select__placeholder {
  font-size: 12px;
}

.search-container .el-select .el-select__selected-item {
  font-size: 12px;
}

/* 下拉选项字号 */
.el-select-dropdown .el-select-dropdown__item {
  font-size: 12px;
}



/* 表格样式 */
.el-table {
  border: 1px solid #dddee1;
}

.el-table th,
.el-table td {
  border-color: #dddee1;
}

.el-table thead th {
  background-color: #f8f8f9 !important;
  font-weight: 600;
}

.el-table th.el-table__cell {
  background-color: #f8f8f9 !important;
  font-weight: 600;
}

.el-table tbody tr:hover {
  background-color: rgba(253, 158, 0, 0.1) !important;
}

.el-table td {
  font-size: 12px;
}

/* 表格中操作按钮样式 - 统一橙色风格 */
.el-table .el-button--small.is-circle {
  background-color: transparent !important;
  border-color: #dcdfe6 !important;
  color: #606266 !important;
}

.el-table .el-button--small.is-circle:hover {
  background-color: rgba(253, 158, 0, 0.1) !important;
  border-color: #fd9e00 !important;
  color: #fd9e00 !important;
}

/* 表格中所有操作按钮悬停样式 - 橙色风格 */
.el-table .el-button:hover {
  background-color: rgba(253, 158, 0, 0.1) !important;
  border-color: #fd9e00 !important;
  color: #fd9e00 !important;
}

/* 表格中文字按钮悬停样式 */
.el-table .el-button--text:hover {
  background-color: rgba(253, 158, 0, 0.1) !important;
  color: #fd9e00 !important;
}

/* 表格中链接按钮悬停样式 */
.el-table .el-link:hover {
  color: #fd9e00 !important;
}

/* 日期选择器弹出框位置调整 - 全局样式 */
.date-picker-dropdown {
  margin-top: 2px !important;
}

.date-picker-dropdown .el-picker-panel {
  margin-top: 0 !important;
}

/* 查询框字体大小统一为12px */
.search-container .el-input input, .el-select
.search-container .el-select input,
.search-container .el-date-editor input {
  font-size: 12px !important;
}

/* ========== 弹出框公共样式 ========== */
/* 弹出框搜索容器样式 - 无下边距版本 */
.el-dialog .dialog-search-container {
  padding: 16px 20px 0 20px;
  background: #f5f5f5;
  margin: 0;
}

/* 弹出框搜索容器样式 - 保留原版本兼容性 */
.el-dialog .search-container {
  padding: 16px 20px;
  background: #f5f5f5;
  margin: 0;
}

/* 弹出框操作按钮容器样式 */
.el-dialog .dialog-action-container {
  padding: 0 20px 16px 20px;
  background: #f5f5f5;
  text-align: right;
  margin: 0;
}

/* 弹出框操作按钮组样式 */
.dialog-action-container .action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 弹出框表格容器样式 */
.el-dialog .table-container,
.el-dialog .error-table-container {
  background: #fff;
  padding: 0;
  margin: 0;
}

/* 弹出框分页容器样式 */
.el-dialog .pagination-container,
.el-dialog .error-pagination-container {
  background: #f5f5f5;
  padding: 16px 20px;
  display: flex;
  justify-content: flex-end;
  margin: 0;
}

/* 弹出框表单样式 */
.el-dialog .el-form-item {
  margin-bottom: 18px;
}

/* 弹出框操作按钮样式 */
.el-dialog .el-table .el-button--small {
  padding: 5px 8px;
  font-size: 12px;
}

/* 弹出框中表格操作按钮悬停样式 - 橙色风格 */
.el-dialog .el-table .el-button:hover {
  background-color: rgba(253, 158, 0, 0.1) !important;
  border-color: #fd9e00 !important;
  color: #fd9e00 !important;
}

.el-dialog .el-table .el-button--text:hover {
  background-color: rgba(253, 158, 0, 0.1) !important;
  color: #fd9e00 !important;
}

.el-dialog .el-table .el-link:hover {
  color: #fd9e00 !important;
}

/* 弹出框查询框字体大小统一为12px */
.el-dialog .search-container .el-input input,
.el-dialog .search-container .el-select input,
.el-dialog .dialog-search-container .el-input input,
.el-dialog .dialog-search-container .el-select input {
  font-size: 12px;
}

.el-switch.is-checked .el-switch__core {
    border-color:  #fd9e00; 
    background-color:  #fd9e00;
}

.section-title {
  margin: 0 0 20px 0;
  padding: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #fd9e00;
}

/* 全局操作列按钮悬停样式 - 橙色风格 */
/* 适用于所有列表的操作列按钮 */
.operation-column .el-button:hover,
.action-column .el-button:hover,
[class*="operation"] .el-button:hover,
[class*="action"] .el-button:hover {
  background-color: rgba(253, 158, 0, 0.1) !important;
  border-color: #fd9e00 !important;
  color: #fd9e00 !important;
}

/* 操作列文字按钮悬停样式 */
.operation-column .el-button--text:hover,
.action-column .el-button--text:hover,
[class*="operation"] .el-button--text:hover,
[class*="action"] .el-button--text:hover {
  background-color: rgba(253, 158, 0, 0.1) !important;
  color: #fd9e00 !important;
}

/* 操作列链接按钮悬停样式 */
.operation-column .el-link:hover,
.action-column .el-link:hover,
[class*="operation"] .el-link:hover,
[class*="action"] .el-link:hover {
  color: #fd9e00 !important;
}

/* 表格最后一列（通常是操作列）按钮悬停样式 */
.el-table__body tr td:last-child .el-button:hover {
  background-color: rgba(253, 158, 0, 0.1) !important;
  border-color: #fd9e00 !important;
  color: #fd9e00 !important;
}

.el-table__body tr td:last-child .el-button--text:hover {
  background-color: rgba(253, 158, 0, 0.1) !important;
  color: #fd9e00 !important;
}

.el-table__body tr td:last-child .el-link:hover {
  color: #fd9e00 !important;
}

/* 弹出对话框关闭按钮样式 */
/* 默认状态 - 黑色 */
.el-dialog__header .el-dialog__headerbtn {
  color: #999 !important;
}

.el-dialog__header .el-dialog__headerbtn .el-dialog__close {
  color: #999 !important;
}

/* 悬停状态 - 橙色 */
.el-dialog__header .el-dialog__headerbtn:hover {
  color: #fd9e00 !important;
}

.el-dialog__header .el-dialog__headerbtn:hover .el-dialog__close {
  color: #fd9e00 !important;
}

/* 确保图标本身也应用颜色 */
.el-dialog__header .el-icon-close:before {
  color: inherit !important;
}

.dialog-footer {
  padding-right: 20px;
  text-align: right;
}

.dialog-list-footer {  
  text-align: right;
}


.dialog-footer .el-button {
  margin-left: 10px;
}

.detail-tab-content{
  padding:0px;
}

/* Layout 样式 */
.layout-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.layout-header {
  flex-shrink: 0;
}

.layout-content {
  flex: 1;
  overflow: auto;
}

.el-message-box__headerbtn .el-message-box__close {
    color: #999 !important;
}

.el-form-item__label {
  font-size: 12px !important;
}

.el-select__wrapper {
  font-size: 12px !important;
}

</style>
