<script setup lang="ts">
import { logApi } from "@/api/log";
import { useUserStoreHook } from "@/store/modules/user";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;
defineOptions({
  name: "Log:view"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // 标题：查看
  viewTitle: {
    type: String,
    default: "查看日志"
  },
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "190px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.model?.displayName
    ? `${t("operate.view")} ${tt("Entity.Log._Entity")} - ${state.model.name}`
    : `${t("operate.view")} ${tt("Entity.Log._Entity")}`;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {}
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  }
});

/**
 * 当前用户存储
 */
const userStore = useUserStoreHook();
/**
 * 数据变量
 */
const state = reactive({
  id: "",
  model: cfg.default.model as Record<string, any>
});
/**
 * 初始化组件（异步）
 */
const init = () => {
  state.model = cfg.default.model;

  return new Promise<void>(resolve => {
    resolve();
  });
};

/**
 * 获取model数据
 */
const get = (id: string) => {
  if (id) {
    cfg.loading.form = true;
    logApi
      .GetLog(id)
      .then(res => {
        if (res.success) {
          state.model = res.data;
          emit("refresh", state.model);
        }
      })
      .finally(() => {
        cfg.loading.form = false;
      });
  }
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};
// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = async (id: string) => {
  cfg.dialog.visible = true;
  if (id) {
    state.model.id = id;
  }
  init().then(() => {
    if (id) {
      get(id);
    }
  });
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      v-model:title="title"
      append-to-body
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <div>
        <el-form
          ref="formRef"
          v-loading="cfg.loading.form"
          :model="state.model"
          label-position="right"
          :label-width="formLabelWidth"
          class="el-dialog-form"
        >
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="message" :label="tt('Entity.Log.Message')">
                <span>{{ state.model.message }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="category" :label="tt('Entity.Log.Category')">
                <span>{{ state.model.category }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="level" :label="tt('Entity.Log.Level')">
                <span>{{ state.model.level }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="platform" :label="tt('Entity.LogPlatform.Log')">
                <span>{{ state.model.platform }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="program" :label="tt('Entity.LogProgram.Log')">
                <span>{{ state.model.program }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="operate" :label="tt('Entity.LogOperate.Log')">
                <span>{{ state.model.operate }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="job" :label="tt('Entity.LogJob.Log')">
                <span>{{ state.model.job }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="duration" :label="tt('Entity.Log.Duration')">
                <span>{{ state.model.duration }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="userDisplayName" :label="tt('Entity.Log.UserDisplayName')">
                <span>{{ state.model.userDisplayName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="employeeName" :label="tt('Entity.Log.EmployeeName')">
                <span>{{ state.model.employeeName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="agentName" :label="tt('Entity.Log.AgentName')">
                <span>{{ state.model.agentName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="url" label="URL">
                <span>{{ state.model.url }}</span>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="formColSpan">
              <el-form-item prop="targetName" label="目标名称">
                <span>{{ state.model.targetName }}</span>
              </el-form-item>
            </el-col> -->
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="host" label="Host">
                <span>{{ state.model.host }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="ip" label="IP">
                <span>{{ state.model.ip }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="24">
              <el-form-item prop="headers" label="Header">
                <span style="width: 100%">{{ state.model.headers }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="apiType" label="API类别">
                <span>{{ state.model.apiType }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="success" label="调用成功">
                <span>{{ state.model.success }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="input" label="输入">
                <span>{{ state.model.input }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="output" label="输出">
                <span>{{ state.model.output }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="interfaceName" label="接口名称">
                <span>{{ state.model.interfaceName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="interfaceAddress" label="接口地址">
                <span>{{ state.model.interfaceAddress }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="exception" label="异常信息">
                <span>{{ state.model.exception }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="interfaceMethod" label="接口方式">
                <span>{{ state.model.interfaceMethod }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="interfaceRequest" label="接口请求">
                <span>{{ state.model.interfaceRequest }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="stackTrace" label="堆栈跟踪">
                <span>{{ state.model.stackTrace }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="24">
              <el-form-item prop="interfaceResponse" label="接口响应">
                <span>{{ state.model.interfaceResponse }}</span>
              </el-form-item>
            </el-col>
          </el-row> -->
        </el-form>
      </div>

      <template #footer>
        <div>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
