﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class StationFilter
    {
        /// <summary>
        /// 关键词
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(Station), Station.Columns.Name, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Department, Department.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Department, Department.Columns.Name, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Position, Position.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Position, Position.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }

        /// <summary>
        /// 分公司ID
        /// </summary>
        [Description("分公司ID")]
        [DynamicQueryColumn(typeof(Station), Station.Columns.CompanyId, Operation = Operation.Equal)]
        public Guid? SubCompanyId { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        [Description("部门ID")]
        public Guid? DepartmentId { get; set; }

        /// <summary>
        /// 岗位ID
        /// </summary>
        [Description("岗位ID")]
        [DynamicQueryColumn(typeof(Station), Station.Columns.ID, Operation = Operation.Equal)]
        public Guid? StationId { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        [Description("职位")]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Position, Position.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(Station), Station.Foreigns.Position, Position.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? PositionKeywords { get; set; }
    }
}
