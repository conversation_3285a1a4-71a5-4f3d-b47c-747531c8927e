--业务群
CREATE TABLE [dbo].[BizGroup]
(
    [ID]					        UNIQUEIDENTIFIER	        NOT NULL    CONSTRAINT [DF_BizGroup_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                     UNIQUEIDENTIFIER            NOT NULL,
	[EnumType]					    INT					        NOT NULL,		--枚举,CrowdType
	[Code]				            NVARCHAR(50)		        NOT NULL,		--编码
	[Name]				            NVARCHAR(50)		        NOT NULL,		--名称
	[Remark]				        NVARCHAR(500)		        NOT NULL ,
    [Deleted]				        BIT					        NOT NULL, 
    [Creator]				        NVARCHAR(50)		        NULL, 
    [CreateTime]			        DATETIME			        NULL, 
    [LastEditor]			        NVARCHAR(50)		        NULL, 
    [LastEditTime]			        DATETIME			        NULL, 
    CONSTRAINT [PK_BizGroup] PRIMARY KEY CLUSTERED ([ID]), 
    CONSTRAINT [FK_BizGroup_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
)
