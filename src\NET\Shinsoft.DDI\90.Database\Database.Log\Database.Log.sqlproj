﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>Database.Log</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{55667633-0f5a-411f-aade-3228178bbb19}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.Sql120DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>Database.Log</RootNamespace>
    <AssemblyName>Database.Log</AssemblyName>
    <ModelCollation>2052,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <DefaultCollation>Chinese_PRC_CI_AS</DefaultCollation>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="log" />
    <Folder Include="log\Stored Procedures" />
    <Folder Include="log\Tables" />
    <Folder Include="log\Tables\LogItem" />
    <Folder Include="log\Views" />
    <Folder Include="Security" />
    <Folder Include="Scripts" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="log\Stored Procedures\sp_ClearAllLogs.sql" />
    <Build Include="log\Stored Procedures\sp_ClearLogs.sql" />
    <Build Include="log\Stored Procedures\sp_ResetLogs.sql" />
    <Build Include="log\Stored Procedures\sp_WriteExceptionLog.sql" />
    <Build Include="log\Stored Procedures\sp_WriteLog.sql" />
    <Build Include="log\Tables\LogItem\LogCategory.sql" />
    <Build Include="log\Tables\LogItem\LogJob.sql" />
    <Build Include="log\Tables\LogItem\LogOperate.sql" />
    <Build Include="log\Tables\LogItem\LogPlatform.sql" />
    <Build Include="log\Tables\LogItem\LogProgram.sql" />
    <Build Include="log\Tables\Log.sql" />
    <Build Include="log\Tables\LogApi.sql" />
    <Build Include="log\Tables\LogException.sql" />
    <Build Include="log\Tables\LogInterface.sql" />
    <Build Include="log\Tables\LogWeb.sql" />
    <Build Include="Security\log.sql" />
    <Build Include="log\Tables\LogItem\LogTargetType.sql" />
    <Build Include="log\Views\VwLog.sql" />
    <Build Include="log\Views\VwLogApi.sql" />
    <Build Include="log\Views\VwLogBiz.sql" />
    <Build Include="log\Views\VwLogException.sql" />
    <Build Include="log\Views\VwLogInterface.sql" />
    <Build Include="log\Views\VwLogJob.sql" />
    <Build Include="log\Tables\LogTarget.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="xl.dev.publish.xml" />
  </ItemGroup>
</Project>