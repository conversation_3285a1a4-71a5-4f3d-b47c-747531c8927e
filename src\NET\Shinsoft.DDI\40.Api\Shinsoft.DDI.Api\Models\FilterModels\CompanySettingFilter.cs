﻿namespace Shinsoft.DDI.Api.Models.FilterModels
{
    public class CompanySettingFilter
    {
        /// <summary>
        /// 关键词（名称,Key值）
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(CompanySetting), CompanySetting.Columns.Name, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(CompanySetting), CompanySetting.Columns.Key, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }
    }
}
