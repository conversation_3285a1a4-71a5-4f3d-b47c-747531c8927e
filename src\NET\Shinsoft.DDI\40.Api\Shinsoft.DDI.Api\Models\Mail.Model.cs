﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码是从模板生成的.
//
//     手动更改此文件可能会导致应用程序出现意外行为。
//     如果重新生成代码，将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.ComponentModel;
using System.Text.Json.Serialization;
using Shinsoft.Core;
using Shinsoft.Core.AutoMapper;
using Shinsoft.Core.DynamicQuery;
using Shinsoft.Core.NLog;
using Shinsoft.DDI.Entities;
using Shinsoft.Core.Mail;

#pragma warning disable CS8669

namespace Shinsoft.DDI.Api.Models
{

	#region Mail

    /// <summary>
    /// 邮件
    /// </summary>
	[Description("邮件")]
    [MapFromType(typeof(Mail), Reverse = true)]
	public abstract class MailRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Mail);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid? MailServerId { get; set; }

        public virtual string Trigger { get; set; } = string.Empty;

        public virtual string ObjectType { get; set; } = string.Empty;

        public virtual string ObjectId { get; set; } = string.Empty;

        public virtual string ObjectName { get; set; } = string.Empty;

        public virtual bool IsHtmlBody { get; set; }

        public virtual string From { get; set; } = string.Empty;

        public virtual string RealFrom { get; set; } = string.Empty;

        public virtual string DebugTo { get; set; } = string.Empty;

        public virtual string To { get; set; } = string.Empty;

        public virtual string Cc { get; set; } = string.Empty;

        public virtual string Bcc { get; set; } = string.Empty;

        public virtual string Subject { get; set; } = string.Empty;

        public virtual string Content { get; set; } = string.Empty;

        public virtual string? AttachmentPaths { get; set; }

        public virtual MailStatus EnumStatus { get; set; }

		public virtual string EnumStatusDesc => this.EnumStatus.GetI18nDesc<Guid>(this.CompanyId);

        public virtual DateTime? PlanSendTime { get; set; }

        public virtual DateTime? SendTime { get; set; }

        public virtual int SendCount { get; set; }

        public virtual string SendMessage { get; set; } = string.Empty;

    }

    /// <summary>
    /// 邮件
    /// </summary>
	[Description("邮件")]
	public abstract partial class MailMeta : MailRaw
	{
	}

    /// <summary>
    /// 邮件
    /// </summary>
	[Description("邮件")]
	public partial class MailModel : MailMeta
	{
	}

    /// <summary>
    /// 邮件
    /// </summary>
	[Description("邮件")]
	public partial class MailQuery : MailMeta
	{
	}

    /// <summary>
    /// 邮件
    /// </summary>
	[Description("邮件")]
	public partial class MailSelector : MailMeta
	{
	}

    /// <summary>
    /// 邮件查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Mail))]
	public partial class MailFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 邮件选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Mail))]
	public partial class MailSelectorFilter : PagingFilterModel
	{
	}

	#endregion Mail


	#region MailAttachment

    [MapFromType(typeof(MailAttachment), Reverse = true)]
	public abstract class MailAttachmentRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(MailAttachment);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual Guid MailId { get; set; }

        public virtual Guid FileIndexId { get; set; }

        public virtual long FileSize { get; set; }

        public virtual string ContentType { get; set; } = string.Empty;

        public virtual string FileName { get; set; } = string.Empty;

        public virtual string FileExt { get; set; } = string.Empty;

    }

	public abstract partial class MailAttachmentMeta : MailAttachmentRaw
	{
	}

	public partial class MailAttachmentModel : MailAttachmentMeta
	{
	}

	public partial class MailAttachmentQuery : MailAttachmentMeta
	{
	}

	public partial class MailAttachmentSelector : MailAttachmentMeta
	{
	}

	[DynamicQueryEntity(typeof(MailAttachment))]
	public partial class MailAttachmentFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(MailAttachment))]
	public partial class MailAttachmentSelectorFilter : PagingFilterModel
	{
	}

	#endregion MailAttachment


	#region MailServer

    /// <summary>
    /// 邮件服务器
    /// </summary>
	[Description("邮件服务器")]
    [MapFromType(typeof(MailServer), Reverse = true)]
	public abstract class MailServerRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Code;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(MailServer);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 邮件服务器类型
        /// </summary>
		[Description("邮件服务器类型")]
        public virtual MailServerType EnumType { get; set; }

		/// <summary>
        /// 邮件服务器类型
        /// </summary>
		[Description("邮件服务器类型")]
		public virtual string EnumTypeDesc => this.EnumType.GetI18nDesc<Guid>(this.CompanyId);

		/// <summary>
        /// 邮件服务器编码
        /// </summary>
		[Description("邮件服务器编码")]
        public virtual string Code { get; set; } = string.Empty;

		/// <summary>
        /// 邮件发件人地址
        /// </summary>
		[Description("邮件发件人地址")]
        public virtual string From { get; set; } = string.Empty;

		/// <summary>
        /// Debug收件人地址
        /// </summary>
		[Description("Debug收件人地址")]
        public virtual string DebugTo { get; set; } = string.Empty;

        public virtual int SendInterval { get; set; }

        public virtual int RetryInterval { get; set; }

        public virtual int MaxRetry { get; set; }

        public virtual int MaxResend { get; set; }

        public virtual bool Valid { get; set; }

        public virtual string? Description { get; set; }

    }

    /// <summary>
    /// 邮件服务器
    /// </summary>
	[Description("邮件服务器")]
	public abstract partial class MailServerMeta : MailServerRaw
	{
	}

    /// <summary>
    /// 邮件服务器
    /// </summary>
	[Description("邮件服务器")]
	public partial class MailServerModel : MailServerMeta
	{
	}

    /// <summary>
    /// 邮件服务器
    /// </summary>
	[Description("邮件服务器")]
	public partial class MailServerQuery : MailServerMeta
	{
	}

    /// <summary>
    /// 邮件服务器
    /// </summary>
	[Description("邮件服务器")]
	public partial class MailServerSelector : MailServerMeta
	{
	}

    /// <summary>
    /// 邮件服务器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(MailServer))]
	public partial class MailServerFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 邮件服务器选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(MailServer))]
	public partial class MailServerSelectorFilter : PagingFilterModel
	{
	}

	#endregion MailServer


	#region MailTemplate

    /// <summary>
    /// 邮件模板
    /// </summary>
	[Description("邮件模板")]
    [MapFromType(typeof(MailTemplate), Reverse = true)]
	public abstract class MailTemplateRaw : CompanyOperateInfoModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Code.GetValueOrDefault();

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(MailTemplate);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? Code { get; set; }

        public virtual bool IsHtmlBody { get; set; }

        public virtual string Subject { get; set; } = string.Empty;

        public virtual string Content { get; set; } = string.Empty;

    }

    /// <summary>
    /// 邮件模板
    /// </summary>
	[Description("邮件模板")]
	public abstract partial class MailTemplateMeta : MailTemplateRaw
	{
	}

    /// <summary>
    /// 邮件模板
    /// </summary>
	[Description("邮件模板")]
	public partial class MailTemplateModel : MailTemplateMeta
	{
	}

    /// <summary>
    /// 邮件模板
    /// </summary>
	[Description("邮件模板")]
	public partial class MailTemplateQuery : MailTemplateMeta
	{
	}

    /// <summary>
    /// 邮件模板
    /// </summary>
	[Description("邮件模板")]
	public partial class MailTemplateSelector : MailTemplateMeta
	{
	}

    /// <summary>
    /// 邮件模板查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(MailTemplate))]
	public partial class MailTemplateFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 邮件模板选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(MailTemplate))]
	public partial class MailTemplateSelectorFilter : PagingFilterModel
	{
	}

	#endregion MailTemplate


	#region SmtpServer

    /// <summary>
    /// 'SMTP服务器
    /// </summary>
	[Description("'SMTP服务器")]
    [MapFromType(typeof(SmtpServer), Reverse = true)]
	public abstract class SmtpServerRaw : BaseCompanyModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(SmtpServer);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

		/// <summary>
        /// 地址
        /// </summary>
		[Description("地址")]
        public virtual string Host { get; set; } = string.Empty;

		/// <summary>
        /// 端口
        /// </summary>
		[Description("端口")]
        public virtual int Port { get; set; }

		/// <summary>
        /// 1:Ssl; 10:Tls; 11:Tls1; 12:Tls2; 13:Tls3
        /// </summary>
		[Description("1:Ssl; 10:Tls; 11:Tls1; 12:Tls2; 13:Tls3")]
        public virtual SmtpSecure EnumSmtpSecure { get; set; }

		/// <summary>
        /// 1:Ssl; 10:Tls; 11:Tls1; 12:Tls2; 13:Tls3
        /// </summary>
		[Description("1:Ssl; 10:Tls; 11:Tls1; 12:Tls2; 13:Tls3")]
		public virtual string EnumSmtpSecureDesc => this.EnumSmtpSecure.GetI18nDesc<Guid>(this.CompanyId);

		/// <summary>
        /// 用户名
        /// </summary>
		[Description("用户名")]
        public virtual string? Username { get; set; }

		/// <summary>
        /// 密码
        /// </summary>
		[Description("密码")]
        public virtual string? Password { get; set; }

        public virtual bool PwdEncrypted { get; set; }

    }

    /// <summary>
    /// 'SMTP服务器
    /// </summary>
	[Description("'SMTP服务器")]
	public abstract partial class SmtpServerMeta : SmtpServerRaw
	{
	}

    /// <summary>
    /// 'SMTP服务器
    /// </summary>
	[Description("'SMTP服务器")]
	public partial class SmtpServerModel : SmtpServerMeta
	{
	}

    /// <summary>
    /// 'SMTP服务器
    /// </summary>
	[Description("'SMTP服务器")]
	public partial class SmtpServerQuery : SmtpServerMeta
	{
	}

    /// <summary>
    /// 'SMTP服务器
    /// </summary>
	[Description("'SMTP服务器")]
	public partial class SmtpServerSelector : SmtpServerMeta
	{
	}

    /// <summary>
    /// 'SMTP服务器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(SmtpServer))]
	public partial class SmtpServerFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 'SMTP服务器选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(SmtpServer))]
	public partial class SmtpServerSelectorFilter : PagingFilterModel
	{
	}

	#endregion SmtpServer

}

#pragma warning restore CS8669
