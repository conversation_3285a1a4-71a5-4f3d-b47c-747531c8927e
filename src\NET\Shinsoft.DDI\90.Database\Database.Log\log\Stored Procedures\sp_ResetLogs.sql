﻿CREATE PROCEDURE [log].[sp_ResetLogs]
AS
	SET NOCOUNT ON;

	EXEC [log].[sp_ClearAllLogs];

	DELETE FROM [log].[LogPlatform];
	DBCC CHECKIDENT('[log].[LogPlatform]',RESEED,0);

	DELETE FROM [log].[LogProgram];
	DBCC CHECKIDENT('[log].[LogProgram]',RESEED,0);

	DELETE FROM [log].[LogOperate];
	DBCC CHECKIDENT('[log].[LogOperate]',RESEED,0);

	DELETE FROM [log].[LogJob];
	DBCC CHECKIDENT('[log].[LogJob]',RESEED,0);

	DELETE FROM [log].[LogTargetType];
	DBCC CHECKIDENT('[log].[LogTargetType]',RESEED,0);
GO

GRANT EXEC ON [log].[sp_ResetLogs] TO PUBLIC
GO