﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Shinsoft.DDI.Dal;
using Microsoft.Extensions.DependencyInjection;

namespace Shinsoft.DDI.Bll
{
    public static class ServiceExtender
    {
        public static IServiceCollection AddBllServics(this IServiceCollection services)
        {
            services.AddDbContextPool<BizDbContext>(Config.DecryptConnStr);

            services.AddDbContextPool<FileDbContext>(Config.DecryptConnStr);

            services.AddDbContextPool<MailDbContext>(Config.DecryptConnStr);

            services.AddDbContextPool<LogDbContext>(Config.DecryptConnStr);

            services.AddRepositories<IRepo>();

            services.AddSingleton<SysCache>();

            services.AddSingleton<CompanyCachePool>();

            return services;
        }
    }
}