CREATE VIEW [dbo].[vOrganization] AS

SELECT TOP (100) PERCENT
    o.ID,
    o.CompanyId,
    o.ParentId,
    CASE o.EnumType
	    WHEN 1 THEN '总公司'
	    WHEN 2 THEN '分公司'
	    WHEN 100 THEN '部门'
	    WHEN 200 THEN '岗位'
	    WHEN 300 THEN '员工'
	    ELSE ''
    END AS [Type],
    o.Name,
    o.EnumType,
    o.Valid,
    o.Level,
    o.Grade,
    o.SubCompanyId,
    o.DepartmentId,
    o.StationId,
    o.EmployeeId,
    s.PositionId

FROM dbo.VwOrganization AS o
LEFT JOIN dbo.SubCompany AS sc ON sc.ID = o.SubCompanyId
LEFT JOIN dbo.Department AS d ON d.ID = o.DepartmentId
LEFT JOIN dbo.Station AS s ON s.ID = o.StationId
LEFT JOIN dbo.Employee AS e ON e.ID = o.EmployeeId
ORDER BY o.EnumType ASC, o.Grade DESC, o.Name ASC





