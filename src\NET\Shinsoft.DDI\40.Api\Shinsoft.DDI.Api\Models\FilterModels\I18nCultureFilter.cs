﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class I18nCultureFilter
    {
        /// <summary>
        /// 主表ID
        /// </summary>
        [Description("主表ID")]
        public Guid? I18nId { get; set; }

        /// <summary>
        /// 关键词
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(I18nCulture), I18nCulture.Foreigns.I18n, I18n.Columns.Key, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(I18nCulture), I18nCulture.Foreigns.I18n, I18n.Columns.Name, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(I18nCulture), I18nCulture.Columns.Culture, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }
    }
}
