﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    [MapFromType(typeof(Company), Reverse = false)]
    public class IdentityCompany : IModel
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        [JsonIgnore]
        public Guid ID { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 可用身份
        /// </summary>
        public List<IdentityEmployee> Identities { get; set; } = [];

        /// <summary>
        /// 可用代理
        /// </summary>
        public List<IdentityEmployee> Delegates { get; set; } = [];
    }
}
