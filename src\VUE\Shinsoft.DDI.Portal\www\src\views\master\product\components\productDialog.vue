<!--
/**
 * 产品管理对话框组件
 * 支持新增和编辑功能
 * 根据传入的recordId判断是新增还是编辑模式
 */
-->
<template>
  <el-dialog
    :title="isEdit ? '编辑产品' : '新增产品'"
    v-model="dialogVisible"
    width="400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="productFormRef"
      :model="productForm"
      :rules="formRules"
      label-width="80px"
    >
      <!-- 基本信息 -->
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="药企" prop="manufacturerId">
            <el-select v-model="productForm.manufacturerId" placeholder="请选择药企" style="width: 100%">
              <el-option 
                v-for="manufacturer in manufacturerList" 
                :key="manufacturer.id" 
                :label="manufacturer.name" 
                :value="manufacturer.id" 
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16" v-if="isEdit">
        <el-col :span="24">
          <el-form-item label="产品编码" prop="code">
            <el-input v-model="productForm.code" placeholder="产品编码" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="中文名称" prop="nameCn">
            <el-input v-model="productForm.nameCn" placeholder="请输入中文名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16" v-if="!isEdit">
        <el-col :span="24">
          <el-form-item label="中文名称" prop="nameCn">
            <el-input v-model="productForm.nameCn" placeholder="请输入中文名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="英文名称" prop="nameEn">
            <el-input v-model="productForm.nameEn" placeholder="请输入英文名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="通用名" prop="commonName">
            <el-input v-model="productForm.commonName" placeholder="请输入通用名" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" >
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { productApi } from '@/api/productApi'
import { manufacturerApi } from '@/api/manufacturerApi'

export default {
  name: 'productDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    // 表单引用
    const productFormRef = ref(null)
    
    // 是否为编辑模式
    const isEdit = computed(() => !!props.recordId)
    
    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })
    
    // 表单数据
    const productForm = reactive({
      id: null,
      code: '',
      manufacturerId: '',
      nameCn: '',
      nameEn: '',
      commonName: ''
    })

    // 厂家列表
    const manufacturerList = ref([])

    // 表单验证规则
    const formRules = computed(() => {
      const rules = {
        manufacturerId: [
          { required: true, message: '请选择药企', trigger: 'change' }
        ],
        nameCn: [
          { required: true, message: '请输入中文名称', trigger: 'blur' },
          { max: 50, message: '中文名称长度不能超过50个字符', trigger: 'blur' }
        ],
        nameEn: [
          { max: 50, message: '英文名称长度不能超过50个字符', trigger: 'blur' }
        ],
        commonName: [
          { required: true, message: '请输入通用名', trigger: 'blur' },
          { max: 100, message: '通用名长度不能超过100个字符', trigger: 'blur' }
        ]
      }
      
 
      return rules
    })

    /**
     * 加载厂家列表
     */
    const loadManufacturerList = async () => {
      try {
        // 调用药企选择器API获取不翻页的药企列表
        const response = await manufacturerApi.getManufacturerSelector()

        if (response.data && response.data.success === true) {
          const selectorList = response.data.data || []

          // 映射为下拉选择需要的格式
          manufacturerList.value = selectorList.map(item => ({
            id: item.id,
            name: item.name
          }))
        } else {
          // 处理错误信息，显示messages数组中的信息
          let errorMessage = '获取药企列表失败'
          if (response.data?.messages && response.data.messages.length > 0) {
            errorMessage = response.data.messages.join('; ')
          }
          console.error(errorMessage)
          manufacturerList.value = []
        }
      } catch (error) {
        console.error('获取药企列表失败:', error)
        manufacturerList.value = []
      }
    }

    /**
     * 重置表单
     */
    const resetForm = () => {
      Object.assign(productForm, {
        id: null,
        code: '',
        manufacturerId: '',
        nameCn: '',
        nameEn: '',
        commonName: ''
      })

      if (productFormRef.value) {
        productFormRef.value.resetFields()
      }
    }

    /**
     * 加载记录数据（编辑模式）
     */
    const loadRecordData = async (recordId) => {
      try {
        const response = await productApi.getProduct(recordId)

        if (response.data && response.data.success === true) {          
          const data = response.data.data  
          Object.assign(productForm, data);
        } else {
          // 处理错误信息，显示messages数组中的信息
          let errorMessage = '加载数据失败'
          if (response.data?.messages && response.data.messages.length > 0) {
            errorMessage = response.data.messages.join('; ')
          }
          ElMessage.error(errorMessage)
        }
      } catch (error) {
        ElMessage.error('加载数据失败：' + (error.message || '网络错误'))
      }
    }

    /**
     * 保存产品信息
     */
    const handleSave = () => {
      productFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            // 编辑模式需要包含ID
            if (isEdit.value) {
              productForm.id = props.recordId
            }

            // 调用对应的API接口
            const response = isEdit.value
              ? await productApi.editProduct(productForm)
              : await productApi.addProduct(productForm)

            if (response.data && response.data.success === true) {
              ElMessage.success(isEdit.value ? '产品信息更新成功' : '产品添加成功')
              emit('success')
              handleClose()
            } else {
              // 处理错误信息，显示messages数组中的信息
              let errorMessage = '操作失败'
              if (response.data?.messages && response.data.messages.length > 0) {
                errorMessage = response.data.messages.join('; ')
              }
              ElMessage.error(errorMessage)
            }
          } catch (error) {
            ElMessage.error('操作失败：' + (error.message || '网络错误'))
          } 
        }
      })
    }

    /**
     * 关闭对话框
     */
    const handleClose = () => {
      resetForm()
      emit('update:visible', false)
    }

    // 监听对话框显示状态
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        if (isEdit.value && props.recordId) {
          // 编辑模式，加载数据
          loadRecordData(props.recordId)
        } else {
          // 新增模式，重置表单
          resetForm()
        }
      }
    })

    // 组件挂载时加载厂家列表
    onMounted(() => {
      loadManufacturerList()
    })

    return {
      productFormRef,      
      isEdit,
      dialogVisible,
      productForm,
      formRules,
      manufacturerList,
      handleSave,
      handleClose,
      resetForm
    }
  }
}
</script>
