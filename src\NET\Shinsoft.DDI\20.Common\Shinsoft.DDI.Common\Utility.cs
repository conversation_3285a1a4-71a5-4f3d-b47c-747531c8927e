﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Common
{
    public static class Utility
    {
        public static string FormatDateTime(this DateTime obj)
        {
            return obj.ToString("yyyy-MM-dd HH:mm:ss");
        }


        public static string FormatDate(this DateTime obj)
        {
            return obj.ToString("yyyy-MM-dd");
        }

        public static string FormatDateTime(this DateTimeOffset obj)
        {
            return obj.ToString("yyyy-MM-dd HH:mm:ss");
        }

        public static string FormatDate(this DateTimeOffset obj)
        {
            return obj.ToString("yyyy-MM-dd");
        }   
 
    }
}
