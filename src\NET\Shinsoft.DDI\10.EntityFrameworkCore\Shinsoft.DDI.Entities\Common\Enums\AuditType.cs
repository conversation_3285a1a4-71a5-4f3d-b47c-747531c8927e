﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace Shinsoft.DDI.Entities
{
    /// <summary>
    /// 审批类型枚举
    /// 用于定义工作流中不同的审批处理方式
    /// </summary>
    public enum AuditType
    {
        /// <summary>
        /// 无审批类型
        /// 默认值，表示不需要审批或未指定审批类型
        /// </summary>
        [Description("无")]
        None = 0,

        /// <summary>
        /// 顺序审批
        /// </summary>
        [Description("顺序审批")]
        Ordinal = 1,

        /// <summary>
        /// 竞争审批
        /// </summary>
        [Description("竞争审批")]
        Competitive = 2,

        /// <summary>
        /// 会签审批
        /// </summary>
        [Description("会签审批")]
        Countersign = 3,
    }
}