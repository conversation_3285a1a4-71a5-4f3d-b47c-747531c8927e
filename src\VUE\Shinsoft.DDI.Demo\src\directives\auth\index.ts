import { useUserStoreHook } from "@/store/modules/user";
import type { Directive, DirectiveBinding } from "vue";

export const auth: Directive = {
  mounted(
    el: HTMLElement,
    binding: DirectiveBinding<
      | string // auth = "权限"
      | Record<string, undefined | null | boolean | string | Array<string>>
    >
  ) {
    const { value } = binding;
    if (value) {
      !useUserStoreHook().hasAuth(value) && el.parentNode?.removeChild(el);
    } else {
      throw new Error("[Directive: auth]: need auths! Like v-auth=\"['btn.add','btn.edit']\"");
    }
  }
};

export const anyAuth: Directive = {
  mounted(
    el: HTMLElement,
    binding: DirectiveBinding<
      | string // auths = "权限"
      | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
      | Array<
          // auths = [auth]
          | string // auth = "权限"
          | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
        >
    >
  ) {
    const { value } = binding;
    if (value) {
      !useUserStoreHook().hasAnyAuth(value) && el.parentNode?.removeChild(el);
    } else {
      throw new Error("[Directive: auth]: need auths! Like v-auth=\"['btn.add','btn.edit']\"");
    }
  }
};

export const allAuth: Directive = {
  mounted(
    el: HTMLElement,
    binding: DirectiveBinding<
      | string // auths = "权限"
      | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
      | Array<
          // auths = [auth]
          | string // auth = "权限"
          | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
        >
    >
  ) {
    const { value } = binding;
    if (value) {
      !useUserStoreHook().hasAllAuth(value) && el.parentNode?.removeChild(el);
    } else {
      throw new Error("[Directive: auth]: need auths! Like v-auth=\"['btn.add','btn.edit']\"");
    }
  }
};
