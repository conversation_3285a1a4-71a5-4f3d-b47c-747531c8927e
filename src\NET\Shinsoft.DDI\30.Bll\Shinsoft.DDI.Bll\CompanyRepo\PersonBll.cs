﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class PersonBll : BaseCompanyBll
    {
        #region Constructs

        public PersonBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public PersonBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public PersonBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public PersonBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs

        #region Password

        public BizResult ChangeMyPassword(string oldPwd, string newPwd)
        {
            var result = this.BizResult();

            if (this.OperatorUser == null)
            {
                result.Error(I18ns.Rule.Employee.Please_Login);
            }
            else if (this.CurrentUser.IsAgent)
            {
                result.Error(I18ns.Rule.Employee.ProxyUserCanNotChangePassword);
            }
            else
            {
                var user = this.Get<User>(this.CurrentUserId);

                var sysPwdRegex = this.SysCache.GetSysSetting(SysSettingKeys.PwdRegex);
                var companyPwdRegex = this.CompanyCache.GetCompanySetting(CompanySettingKeys.PwdRegex);

                if (user == null)
                {
                    result.Error(I18ns.Rule.Employee.Employee_NotExist);
                }
                else if (user.EnumStatus == UserStatus.Invalid)
                {
                    result.Error(I18ns.Rule.Employee.Employee_Invaild);
                }
                else if (newPwd.IsEmpty())
                {
                    result.Error(I18ns.Rule.Employee.NewPasword_Empty);
                }
                else if (sysPwdRegex?.Value.IsEmpty() == false && !Regex.IsMatch(newPwd, sysPwdRegex.Value))
                {
                    if (sysPwdRegex.Remark.IsEmpty())
                    {
                        result.Error(I18ns.Rule.Employee.Pasword_SimpleForSystem);
                    }
                    else
                    {
                        result.Error(sysPwdRegex.Remark);
                    }
                }
                else if (companyPwdRegex?.Value.IsEmpty() == false && !Regex.IsMatch(newPwd, companyPwdRegex.Value))
                {
                    if (companyPwdRegex.Remark.IsEmpty())
                    {
                        result.Error(I18ns.Rule.Employee.Pasword_SimpleForCompany);
                    }
                    else
                    {
                        result.Error(companyPwdRegex.Remark);
                    }
                }
                else
                {
                    var dbPwd = user.EnumPwdType switch
                    {
                        PwdType.MD5 => oldPwd.ToMD5(),
                        PwdType.SHA1 => oldPwd.ToSHA1(),
                        PwdType.SHA256 => oldPwd.ToSHA256(),
                        _ => oldPwd,
                    };

                    if (dbPwd != user.PwdText)
                    {
                        result.Error(I18ns.Rule.Employee.OriginalPassword_error);
                    }
                    else
                    {
                        var newDbPwd = newPwd.ToSHA1();
                        user.EnumPwdType = PwdType.SHA1;
                        user.PwdText = newDbPwd;

                        user.IncreaseSeed();

                        this.Update(user);
                    }
                }
            }

            return result;
        }

        #endregion Password

        #region Delegate

        public BizResult<EmployeeDelegate> AddMyEmployeeDelegate(EmployeeDelegate entity)
        {
            var result = new BizResult<EmployeeDelegate>();

            if (this.OperatorUser?.Employee == null)
            {
                result.Error(I18ns.Rule.Employee.CanNotSetProxy);
            }
            else if (this.OperatorUser.IsAgent)
            {
                result.Error(I18ns.Rule.Employee.NoReightSetProxy);
            }
            else
            {
                if (entity.AgentId.IsEmpty())
                {
                    result.Error(I18ns.Rule.Employee.Select_Agent);
                }

                if (result.Success)
                {
                    entity.EmployeeId = this.OperatorUser.Employee.ID;

                    if (entity.ID.IsEmpty())
                    {
                        entity.ID = CombGuid.NewGuid();
                    }

                    entity = this.Add(entity);

                    result.Data = entity;
                }
            }

            return result;
        }

        public BizResult<EmployeeDelegate> UpdateMyEmployeeDelegate(EmployeeDelegate entity)
        {
            var result = new BizResult<EmployeeDelegate>();

            if (this.OperatorUser?.Employee == null)
            {
                result.Error(I18ns.Rule.Employee.CanNotSetProxy);
            }
            else if (this.OperatorUser.IsAgent)
            {
                result.Error(I18ns.Rule.Employee.NoReightSetProxy);
            }
            else
            {
                var id = entity.ID;

                var dbEntity = this.Get<EmployeeDelegate>(id);

                if (dbEntity == null)
                {
                    result.Error(I18ns.Rule.Employee.ProxyNotExist);
                }
                else if (dbEntity.EmployeeId != this.CurrentEmployeeId)
                {
                    result.Error(I18ns.Rule.Employee.NoRightUpdateProxy);
                }
                else
                {
                    entity.RemoveChangedColumn(EmployeeDelegate.Columns.EmployeeId);

                    if (this.Update(ref entity, false))
                    {
                        if (entity.AgentId.IsEmpty())
                        {
                            result.Error(I18ns.Rule.Employee.Select_Agent);
                        }
                    }

                    if (result.Success)
                    {
                        this.SaveChanges();

                        result.Data = entity;
                    }
                    else
                    {
                        this.RollbackChanges();
                    }
                }
            }
            return result;
        }

        public BizResult<EmployeeDelegate> ToggleMyEmployeeDelegate(Guid id)
        {
            var result = new BizResult<EmployeeDelegate>();

            if (this.OperatorUser?.Employee == null)
            {
                result.Error(I18ns.Rule.Employee.CanNotSetProxy);
            }
            else if (this.OperatorUser.IsAgent)
            {
                result.Error(I18ns.Rule.Employee.NoReightSetProxy);
            }
            else
            {
                var dbEntity = this.Get<EmployeeDelegate>(id);

                if (dbEntity == null)
                {
                    result.Error(I18ns.Rule.Employee.ProxyNotExist);
                }
                else if (dbEntity.EmployeeId != this.OperatorUser.Employee.ID)
                {
                    result.Error(I18ns.Rule.Employee.NoRightUpdateProxy);
                }
                else
                {
                    dbEntity.Valid = !dbEntity.Valid;

                    result.Data = this.Update(dbEntity);
                }
            }
            return result;
        }

        public BizResult DeleteMyEmployeeDelegate(Guid id)
        {
            var result = new BizResult();

            if (this.OperatorUser?.Employee == null)
            {
                result.Error(I18ns.Rule.Employee.CanNotSetProxy);
            }
            else if (this.OperatorUser.IsAgent)
            {
                result.Error(I18ns.Rule.Employee.NoReightSetProxy);
            }
            else
            {
                var dbEntity = this.Get<EmployeeDelegate>(id);

                if (dbEntity == null)
                {
                    result.Error(I18ns.Rule.Employee.ProxyNotExist);
                }
                else if (dbEntity.EmployeeId != this.OperatorUser.Employee.ID)
                {
                    result.Error(I18ns.Rule.Employee.NoRightUpdateProxy);
                }
                else
                {
                    this.Delete(dbEntity);
                }
            }
            return result;
        }

        #endregion Delegate
    }
}
