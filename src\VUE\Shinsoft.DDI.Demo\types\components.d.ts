/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    IconCarbonPassword: typeof import('~icons/carbon/password')['default']
    IconEpCircleCheck: typeof import('~icons/ep/circle-check')['default']
    IconEpCircleClose: typeof import('~icons/ep/circle-close')['default']
    IconEpCloseBold: typeof import('~icons/ep/close-bold')['default']
    IconEpSelect: typeof import('~icons/ep/select')['default']
    IconMaterialSymbolsManageAccounts: typeof import('~icons/material-symbols/manage-accounts')['default']
    IconMdiAccountSwitchOutline: typeof import('~icons/mdi/account-switch-outline')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
