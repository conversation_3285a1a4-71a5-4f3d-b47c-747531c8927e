﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class ManagementBll : BaseCompanyBll
    {
        #region Constructs

        public ManagementBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        public ManagementBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        public ManagementBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        public ManagementBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs

        #region Dict

        public BizResult<Dict> AddDict(Dict entity)
        {
            var result = new BizResult<Dict>();

            if (entity.Code.IsEmpty())
            {
                result.Error("请输入编码");
            }

            if (result.Success)
            {
                var exist = this.GetEntity<Dict>(p => p.Code == entity.Code);

                if (exist != null)
                {
                    result.Error("已存在相同编码的字典");
                }

                if (entity.ParentId.HasValue)
                {
                    var parent = this.Get<Dict>(entity.ParentId.Value);

                    if (parent == null)
                    {
                        result.Error("父字典不存在");
                    }
                    else if (!parent.EnumEditFlags.HasFlag(EditFlag.Children))
                    {
                        result.Error("父字典不允许维护子项");
                    }
                    else
                    {
                        entity.EnumEditFlags = parent.EnumEditFlags_Child;
                        entity.EnumEditFlags_Child = parent.EnumEditFlags_Child;
                    }
                }
            }

            if (result.Success)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                entity = this.Add(entity);

                result.Data = entity;
            }

            return result;
        }

        public BizResult<Dict> UpdateDict(Dict entity)
        {
            var result = new BizResult<Dict>();

            var id = entity.ID;
            var dbEntity = this.Get<Dict>(id);

            if (dbEntity == null)
            {
                result.Error("字典不存在");
            }
            else
            {
                var editFlags = dbEntity.EnumEditFlags;

                var code = dbEntity.Code;
                var name = dbEntity.Name;

                // 不更新上级ID,编辑标志
                entity.RemoveChangedColumn(Dict.Columns.ParentId);
                entity.RemoveChangedColumn(Dict.Columns.EnumEditFlags);

                if (this.Update(ref entity, false))
                {
                    if (entity.Code.IsEmpty())
                    {
                        result.Error("请输入编码");
                    }
                    else if (!editFlags.HasFlag(EditFlag.Code) && code != entity.Code)
                    {
                        result.Error("字典编码不可编辑");
                    }

                    if (entity.Name.IsEmpty())
                    {
                        result.Error("请输入名称");
                    }
                    else if (!editFlags.HasFlag(EditFlag.Name) && name != entity.Name)
                    {
                        result.Error("字典名称不可编辑");
                    }

                    var exist = this.GetEntity<Dict>(p => p.ID != id && p.Code == entity.Code);

                    if (exist != null)
                    {
                        result.Error("已存在相同编码的字典");
                    }
                }

                if (result.Success)
                {
                    this.SaveChanges();

                    result.Data = entity;
                }
                else
                {
                    this.Detach(entity);
                }
            }

            return result;
        }

        public BizResult DeleteDict(Dict entity)
        {
            var result = new BizResult<Dict>();

            var id = entity.ID;
            var dbEntity = this.Get<Dict>(id);

            if (dbEntity == null)
            {
                result.Error("字典不存在");
            }
            else if (!dbEntity.EnumEditFlags.HasFlag(EditFlag.Delete))
            {
                result.Error("字典不允许被删除");
            }
            else
            {
                this.Delete(entity);
            }

            return result;
        }

        #endregion Dict

        #region Announcement
        public BizResult<Announcement> AddAnnouncement(Announcement entity,AnnouncementContent content)
        {
            var result = new BizResult<Announcement>();

            if (string.IsNullOrWhiteSpace(entity.Subject))
            {
                result.Error(I18ns.Rule.Announcement.Subject_Required);
            }
            if(string.IsNullOrWhiteSpace( content.Content))
            {
                result.Error(I18ns.Rule.Announcement.AnnouncementContent_Required);
            }

            if(entity.StartTime.HasValue && entity.EndTime.HasValue && entity.EndTime > entity.StartTime)
            {
                result.Error(I18ns.Rule.Announcement.EndTimeEarlierStart);
            }

            if (result.Success)
            {
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                content.ID = entity.ID;

                entity = this.Add(entity, false);

                content = this.Add(content, false);

                this.SaveChanges();
                
                result.Data = entity;
            }



            return result;
        }


        public BizResult<Announcement> UpdateAnnouncement(Announcement entity, AnnouncementContent content)
        {
            var cc = this.CompanyCache;

            var result = new BizResult<Announcement>();

            var id = entity.ID;

            var dbEntity = this.Get<Announcement>(id);

            var dbContent = this.Get<AnnouncementContent>(id);

            if (string.IsNullOrWhiteSpace(entity.Subject))
            {
                result.Error(I18ns.Rule.Announcement.Subject_Required);
            }
            if (string.IsNullOrWhiteSpace(content.Content))
            {
                result.Error(I18ns.Rule.Announcement.AnnouncementContent_Required);
            }

            if (entity.StartTime.HasValue && entity.EndTime.HasValue && entity.EndTime > entity.StartTime)
            {
                result.Error(I18ns.Rule.Announcement.EndTimeEarlierStart);
            }

            if (result.Success)
            {
                entity = this.Update(entity, false);

                content = this.Update(content, false);

                this.SaveChanges();

                result.Data = entity;

                cc.RemoveCache<Dict>();
            }
            else
            {
                this.Detach(entity);
            }
            return result;
        }

        public BizResult DeleteAnnouncement(Guid id)
        {
            var cc = this.CompanyCache;

            var result = new BizResult<Announcement>();

            var dbEntity = this.Get<Announcement>(id);

            if (dbEntity == null)
            {
                result.Error(I18ns.Rule.Announcement.Not_Exist);
            }
            else
            {
                if (result.Success)
                {
                    this.Delete(dbEntity,false);  

                    this.SaveChanges(true);

                    cc.RemoveCache<Announcement>();
                }
            }
            return result;
        }
        #endregion Dict
    }
}
