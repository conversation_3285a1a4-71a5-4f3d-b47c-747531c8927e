﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class CompanyBll : BaseCompanyBll
    {
        #region Constructs

        public CompanyBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        public CompanyBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        public CompanyBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        public CompanyBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs
    }
}
