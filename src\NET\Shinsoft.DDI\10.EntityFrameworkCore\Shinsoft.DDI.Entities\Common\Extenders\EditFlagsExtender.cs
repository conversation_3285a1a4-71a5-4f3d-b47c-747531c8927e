﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public static partial class EditFlagsExtender
    {
        public static bool AllowEdit(this IEditFlags entity, params EditFlag[]? args)
        {
            var allow = false;
            if (entity != null)
            {
                if (entity.EnumEditFlags == EditFlag.Unlimited || args == null || args.Length == 0)
                {
                    allow = true;
                }
                else
                {
                    var editFlags = EditFlag.None;

                    foreach (var flag in args)
                    {
                        editFlags |= flag;
                    }

                    allow = (entity.EnumEditFlags & editFlags) != EditFlag.None;
                }
            }

            return allow;
        }

        public static bool AllowEditAll(this IEditFlags entity, params EditFlag[]? args)
        {
            var allow = false;
            if (entity != null)
            {
                if (entity.EnumEditFlags == EditFlag.Unlimited || args == null || args.Length == 0)
                {
                    allow = true;
                }
                else
                {
                    var editFlags = EditFlag.None;

                    args.Aggregate(editFlags, (flags, flag) => flags | flag);

                    allow = (entity.EnumEditFlags & editFlags) == editFlags;
                }
            }

            return allow;
        }
    }
}
