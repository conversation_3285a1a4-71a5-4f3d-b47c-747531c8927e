--扩展配置
CREATE TABLE [dbo].[CompanySetting]
(
    [ID]					        UNIQUEIDENTIFIER	        NOT NULL    CONSTRAINT [DF_CompanySetting_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                     UNIQUEIDENTIFIER            NOT NULL,
	[Key]					        NVARCHAR(50)		        NOT NULL ,
    [Name]                          NVARCHAR(50)                NOT NULL ,
	[Value]					        NVARCHAR(MAX)		        NOT NULL ,
	[Remark]				        NVARCHAR(500)		        NOT NULL ,
    [Deleted]				        BIT					        NOT NULL, 
    [Creator]				        NVARCHAR(50)		        NULL, 
    [CreateTime]			        DATETIME			        NULL, 
    [LastEditor]			        NVARCHAR(50)		        NULL, 
    [LastEditTime]			        DATETIME			        NULL, 
    CONSTRAINT [PK_CompanySetting] PRIMARY KEY CLUSTERED ([ID]), 
    CONSTRAINT [FK_CompanySetting_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
)

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'扩展配置',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CompanySetting',
    @level2type = NULL,
    @level2name = NULL

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'唯一标识',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CompanySetting',
    @level2type = N'COLUMN',
    @level2name = N'Key'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CompanySetting',
    @level2type = N'COLUMN',
    @level2name = N'Name'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'值',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CompanySetting',
    @level2type = N'COLUMN',
    @level2name = N'Value'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CompanySetting',
    @level2type = N'COLUMN',
    @level2name = N'Remark'
