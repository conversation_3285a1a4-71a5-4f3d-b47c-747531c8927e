﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    [Flags]
    public enum RoleFlag
    {
        None = 0,

        /// <summary>
        /// 隐藏
        /// </summary>
        [Description("隐藏")]
        Invisible = 1,

        /// <summary>
        /// 系统
        /// </summary>
        [EnumGroup("Query")]
        [EnumGroup("Edit", Disabled = true)]
        [Description("系统")]
        Sys = 2,

        /// <summary>
        /// 登录用户（登录时自动赋予所有用户）
        /// </summary>
        [EnumGroup("Query")]
        [EnumGroup("Edit", Disabled = true)]
        [Description("登录用户")]
        LoginUser = 4,

        /// <summary>
        /// 上级领导
        /// </summary>
        [EnumGroup("Query")]
        [EnumGroup("Edit", Disabled = true)]
        [Description("上级领导")]
        LineManager = 8,
    }
}