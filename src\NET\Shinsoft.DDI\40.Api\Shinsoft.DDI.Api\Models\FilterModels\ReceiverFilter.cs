using System;
using System.Collections.Generic;
using System.ComponentModel;
using Shinsoft.Core;
using Shinsoft.DDI.Entities;

namespace Shinsoft.DDI.Api.Models
{
    /// <summary>
    /// 经销商/收货方查询条件
    /// </summary>
    public partial class ReceiverFilter 
    {
        /// <summary>
        /// 编码
        /// </summary>
        [Description("编码")]
        [DynamicQueryColumn(typeof(Receiver), Receiver.Columns.Code, Operation = Operation.StringIntelligence)]
        public string? Code { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        [DynamicQueryColumn(typeof(Receiver), Receiver.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Name { get; set; }

        /// <summary>
        /// 省份ID
        /// </summary>
        [Description("省份ID")]
        [DynamicQueryColumn(typeof(Receiver), Receiver.Columns.ProvinceId, Operation = Operation.Equal)]
        public Guid? ProvinceId { get; set; }

        /// <summary>
        /// 城市ID
        /// </summary>
        [Description("城市ID")]
        [DynamicQueryColumn(typeof(Receiver), Receiver.Columns.CityId, Operation = Operation.Equal)]
        public Guid? CityId { get; set; }

        /// <summary>
        /// 区县ID
        /// </summary>
        [Description("区县ID")]
        [DynamicQueryColumn(typeof(Receiver), Receiver.Columns.CountyId, Operation = Operation.Equal)]
        public Guid? CountyId { get; set; }

        /// <summary>
        /// 收货方类型ID
        /// </summary>
        [Description("收货方类型ID")]
        [DynamicQueryColumn(typeof(Receiver), Receiver.Columns.ReceiverTypeId, Operation = Operation.Equal)]
        public Guid? ReceiverTypeId { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [Description("电话")]
        [DynamicQueryColumn(typeof(Receiver), Receiver.Columns.Telephone, Operation = Operation.StringIntelligence)]
        public string? Telephone { get; set; }

        /// <summary>
        /// 邮件
        /// </summary>
        [Description("邮件")]
        [DynamicQueryColumn(typeof(Receiver), Receiver.Columns.EMail, Operation = Operation.StringIntelligence)]
        public string? EMail { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        [DynamicQueryColumn(typeof(Receiver), Receiver.Columns.EnumStatus, Operation = Operation.In)]
        public List<ReceiverStatus>? Status { get; set; }

        /// <summary>
        /// 需要排除的ID
        /// </summary>
        public Guid? ExcludeId { get; set; }
    }
}