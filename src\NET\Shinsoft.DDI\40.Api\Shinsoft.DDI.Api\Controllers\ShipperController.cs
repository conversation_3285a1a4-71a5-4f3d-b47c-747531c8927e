using Microsoft.AspNetCore.Mvc;
using Shinsoft.Core.Mvc;
using Shinsoft.DDI.Bll;
using Shinsoft.DDI.Common;
using System;
using System.Collections.Generic;
using System.Linq;
 

namespace Shinsoft.DDI.Api.Controllers
{
    /// <summary>
    /// 货主管理控制器
    /// 提供货主的增删改查等API接口
    /// </summary>
    [ApiExplorerSettings(GroupName = "货主维护")]
    public class ShipperController : BaseApiController<ShipperBll>
    {
        /// <summary>
        /// 分页查询货主列表
        /// </summary>
        /// <param name="filter">查询条件</param> 
        /// <returns>分页货主列表</returns>
        [HttpGet]
        //[Auth(AuthCodes.MasterData.Business.Shipper_Query)]
        [LogApi(ApiType.Query, Operate = "分页查询货主列表")]
        public QueryResult<ShipperQuery> QueryShipper([FromQuery] ShipperFilter filter)
        {          
            return this.Repo.GetDynamicQuery<Shipper, ShipperQuery>(filter);
        }

        /// <summary>
        /// 获取货主详情
        /// </summary>
        /// <param name="id">货主ID</param>
        /// <returns>货主详情</returns>
        [HttpGet]
        public BizResult<ShipperModel> Get(Guid id)
        {
            var result = new BizResult<ShipperModel>();

            var entity = this.Repo.GetShipper(id);
            if (entity == null)
            {
                result.Error("货主不存在");
            }
            else
            {
                result.Data = this.Map<ShipperModel>(entity);
            }


            return result;
        }


        /// <summary>
        /// 新增货主
        /// </summary>
        /// <param name="model">货主模型</param>
        /// <returns>新增结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Shipper_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增货主")]
        public BizResult<ShipperModel> Add([FromBody] ShipperModel model)
        {
            var entity = this.Map<Shipper>(model);
            var result = this.Repo.AddShipper(entity);
            return result.Map<ShipperModel>();
        }

        /// <summary>
        /// 编辑货主
        /// </summary>
        /// <param name="model">货主模型</param>
        /// <returns>编辑结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Shipper_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑货主")]
        public BizResult<ShipperModel> Edit([FromBody] ShipperModel model)
        {
            var entity = this.Map<Shipper>(model);
            var result = this.Repo.UpdateShipper(entity);

            return result.Map<ShipperModel>();
        }

        /// <summary>
        /// 停用货主
        /// </summary>
        /// <param name="model">货主</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Shipper_Manage_Stop)]
        [LogApi(ApiType.Save, Operate = "停用货主")]
        public BizResult StopShipper([FromBody] Shipper model)
        {
            return this.Repo.StopShipper(model.ID);
        }

        /// <summary>
        /// 启用货主
        /// </summary>
        /// <param name="model">货主</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Shipper_Manage_Enable)]
        [LogApi(ApiType.Save, Operate = "启用货主")]
        public BizResult EnableShipper([FromBody] Shipper model)
        {
            return this.Repo.EnableShipper(model.ID);
        }
        
        /// <summary>
        /// 删除货主
        /// </summary>
        /// <param name="model">货主</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Shipper_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除货主")]
        public BizResult Delete([FromBody] Shipper model)
        { 
            return this.Repo.DeleteShipper(model.ID);
        }

        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Shipper_Manage_Export)]
        [LogApi(ApiType.Query, Operate = "导出货主信息")]
        public FileContentResult ExportShipper([FromBody] ShipperFilter filter)
        {
            var entities = this.Repo.GetEntities<Shipper>(filter);

            var models = entities.Maps<ShipperQuery>();

            //byte[] result = ExcelHelper.WriteToExcelBytes(models, ShipperDictionary.ExportShipperColumns, "货主信息");

            return File(new byte[0], ConstDefinition.Common.Export_ContentType, "货主信息.xlsx");
        }
    }

}