﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Quartz;
using Quartz.Impl;

namespace Shinsoft.DDI.Api
{
    public static class QuartzExtender
    {
        public static void UseQuartzAsync(this IServiceCollection services)
        {
            var schedulerFactory = new StdSchedulerFactory();

            services.AddSingleton<ISchedulerFactory>(schedulerFactory);

            var scheduler = schedulerFactory.GetScheduler().Result;
            /*
            Stopwatch stopwatch;

            #region CacheJob

            stopwatch = Stopwatch.StartNew();

            ITrigger dailyTrigger_3H = TriggerBuilder.Create()
                .WithIdentity("3:00:00", "Daily")
                .StartNow()
                .WithCronSchedule("0 0 3 * * ?")
                .Build();

            IJobDetail cacheJob = JobBuilder.Create<CacheJob>()
                .WithIdentity(nameof(CacheJob), "Daily")
                .Build();

            scheduler.ScheduleJob(cacheJob, dailyTrigger_3H).GetAwaiter();

            NLogHelper.Info("设置缓存任务", $"任务已设置：每天3:00运行一次", nameof(CacheJob), stopwatch.ElapsedMilliseconds);

            #endregion CacheJob

            */
        }
    }
}