﻿CREATE TABLE [dbo].[BizMail]
(
    [ID]					        UNIQUEIDENTIFIER        	NOT NULL    CONSTRAINT [DF_BizMail_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                     UNIQUEIDENTIFIER        	NOT NULL,
    [EnumMailTrigger]               INT                         NOT NULL,
    [EnumBizType]                   INT                         NOT NULL,
    [BizId]                         UNIQUEIDENTIFIER        	NULL,
    [LastGeneratedTime]             DATETIME                    NULL, 
    CONSTRAINT [PK_BizMail] PRIMARY KEY CLUSTERED ([ID]) , 
    CONSTRAINT [FK_BizMail_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),

)
GO
CREATE UNIQUE INDEX [IX_BizMail] ON [dbo].[BizMail]
(
    [CompanyId] ASC,
    [EnumMailTrigger] ASC,
    [EnumBizType] ASC,
    [BizId] ASC
);
GO
