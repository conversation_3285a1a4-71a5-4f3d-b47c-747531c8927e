﻿namespace Shinsoft.DDI.Api.Models
{
    [MapFromType(typeof(AnnouncementContent), Reverse = true)]
    public partial class AnnouncementModel
    {
        [MapFromProperty(typeof(Announcement),Announcement.Inverses.Content,AnnouncementContent.Columns.Content)]
        [MapFromProperty(typeof(AnnouncementContent),AnnouncementContent.Columns.Content)]
        public string? Content { get; set; }
    }
}
