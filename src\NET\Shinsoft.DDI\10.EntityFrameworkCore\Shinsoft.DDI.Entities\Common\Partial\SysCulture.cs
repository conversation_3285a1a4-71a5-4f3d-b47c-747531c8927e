﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public partial class SysCulture : IHashCache, IKeyValue<string, Dictionary<string, object?>>
    {
        #region IHashCache

        [NotMapped, XmlIgnore, JsonIgnore]
        public string Field => this.Culture;

        #endregion IHashCache

        #region IKeyValue

        [NotMapped, XmlIgnore, JsonIgnore]
        string IKeyValue<string, Dictionary<string, object?>>.Key
        {
            get => this.Culture;
            set => this.Culture = value;
        }

        [NotMapped, XmlIgnore, JsonIgnore]
        Dictionary<string, object?>? IKeyValue<string, Dictionary<string, object?>>.Value
        {
            get => this.I18nDict;
            set => this.I18nDict = value;
        }

        #endregion IKeyValue

        [NotMapped, XmlIgnore, JsonIgnore]
        public Dictionary<string, object?>? I18nDict { get; set; }
    }
}