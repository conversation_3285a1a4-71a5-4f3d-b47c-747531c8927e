﻿CREATE VIEW [dbo].[vStationLeader] AS

SELECT 
	sl.ID,
    sl.CompanyId,
	s.Name AS [StationName], 
	e.LoginName AS [EmployeeLoginName],
	e.DisplayName AS [Employee],
	lm.LoginName AS [LineManagerLoginName],
	lm.DisplayName AS [LineManager],
	l.LoginName AS [LeaderLoginName],
	l.<PERSON>ame AS [Leader],
	sl.StationId,
    sl.EmployeeId,
	sl.LineManagerStationId,
    sl.LineManagerId,
	sl.LeaderStationId,
	sl.LeaderId
FROM  dbo.VwStationLeader AS sl
INNER JOIN dbo.Station AS s ON s.ID = sl.StationId
LEFT JOIN dbo.Employee AS e ON e.ID = sl.EmployeeId
LEFT JOIN dbo.Employee AS lm ON lm.ID = sl.LineManagerId
LEFT JOIN dbo.Employee AS l ON l.ID = sl.LeaderId
