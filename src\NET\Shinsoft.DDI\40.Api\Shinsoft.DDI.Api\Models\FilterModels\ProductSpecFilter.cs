using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using Shinsoft.Core;
using Shinsoft.DDI.Entities;

namespace Shinsoft.DDI.Api.Models
{
    /// <summary>
    /// 产品规格查询条件
    /// 用于产品规格列表查询的过滤条件，支持药企\产品\规格级联筛选
    /// </summary>
    [Description("产品规格查询条件")]
    [DynamicQueryEntity(typeof(ProductSpec))]
    public partial class ProductSpecFilter
    {
        /// <summary>
        /// 药企ID（级联筛选 - 药企级别）
        /// </summary>
        [Description("药企ID")]
        [DynamicQueryColumn(typeof(ProductSpec), ProductSpec.Foreigns.Product, Product.Columns.ManufacturerId, Operation = Operation.Equal)]
        public Guid? ManufacturerId { get; set; }

        /// <summary>
        /// 产品ID（级联筛选 - 产品级别）
        /// </summary>
        [Description("产品ID")]
        [DynamicQueryColumn(typeof(ProductSpec), ProductSpec.Columns.ProductId, Operation = Operation.Equal)]
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 产品规格ID（级联筛选 - 规格级别）
        /// </summary>
        [Description("产品规格ID")]
        [DynamicQueryColumn(typeof(ProductSpec), ProductSpec.Columns.ID, Operation = Operation.Equal)]
        public Guid? ProductSpecId { get; set; }

        /// <summary>
        /// 通用名称（通过关联查询）
        /// </summary>
        [Description("通用名称")]
        [DynamicQueryColumn(typeof(ProductSpec), ProductSpec.Foreigns.Product, Product.Columns.CommonName, Operation = Operation.StringIntelligence)]
        public string? CommonName { get; set; }
    }
}
