-- =============================================
-- 枚举转字典数据初始化脚本
-- 将系统中的枚举类型转换为字典表数据
-- 创建时间: 2025-08-01
-- =============================================

-- 设置变量
DECLARE @CompanyId UNIQUEIDENTIFIER = '88888888-8888-8888-8888-888888888888';
DECLARE @CurrentTime DATETIME = GETDATE();
DECLARE @Creator NVARCHAR(50) = 'SysAdmin';
DECLARE @LastEditor NVARCHAR(50) = 'SysAdmin';

-- 临时变量用于存储父字典ID
DECLARE @ParentId UNIQUEIDENTIFIER;

-- =============================================
-- 1. DBConnectType 数据库连接方式
-- =============================================
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES (NEWID(), @CompanyId, NULL, 2, 1025, 5, 'DBConnectType', '数据库连接方式', '', 1, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

SET @ParentId = (SELECT TOP 1 [ID] FROM [dbo].[Dict] WHERE [Code] = 'DBConnectType' AND [CompanyId] = @CompanyId);

-- 子字典项 (跳过None)
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES 
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Ado32', 'ADO 32位连接', '', 1, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Odbc64', 'ODBC 64位连接', '', 2, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'OleDb64', 'OLE DB 64位连接', '', 3, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'OracleClient64', 'Oracle客户端 64位连接', '', 4, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'SqlClient64', 'SQL Server客户端 64位连接', '', 5, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

-- =============================================
-- 2. ScheduleType 调度类型
-- =============================================
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES (NEWID(), @CompanyId, NULL, 2, 1025, 5, 'ScheduleType', '调度类型', '', 2, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

SET @ParentId = (SELECT TOP 1 [ID] FROM [dbo].[Dict] WHERE [Code] = 'ScheduleType' AND [CompanyId] = @CompanyId);

-- 子字典项 (跳过None)
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES 
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Daily', '每日执行', '', 1, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Monthly', '每月执行', '', 2, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

-- =============================================
-- 3. SdrDbConnectType SDR数据库连接方式
-- =============================================
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES (NEWID(), @CompanyId, NULL, 2, 1025, 5, 'SdrDbConnectType', 'SDR数据库连接方式', '', 3, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

SET @ParentId = (SELECT TOP 1 [ID] FROM [dbo].[Dict] WHERE [Code] = 'SdrDbConnectType' AND [CompanyId] = @CompanyId);

-- 子字典项 (跳过None)
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES 
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Ado32', 'ADO 32位连接方式', '', 1, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Odbc64', 'ODBC 64位连接方式', '', 2, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'OleDb64', 'OLE DB 64位连接方式', '', 3, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'OracleClient64', 'Oracle客户端 64位连接方式', '', 4, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'SqlClient64', 'SQL Server客户端 64位连接方式', '', 5, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

-- =============================================
-- 4. SdrFileEncoding DDI文件编码类型
-- =============================================
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES (NEWID(), @CompanyId, NULL, 2, 1025, 5, 'SdrFileEncoding', 'DDI文件编码类型', '', 4, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

SET @ParentId = (SELECT TOP 1 [ID] FROM [dbo].[Dict] WHERE [Code] = 'SdrFileEncoding' AND [CompanyId] = @CompanyId);

-- 子字典项 (跳过None)
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES 
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Unicode', 'Unicode编码', '', 1, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Ascii', 'ASCII编码', '', 2, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

-- =============================================
-- 5. SdrFtpType FTP连接类型
-- =============================================
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES (NEWID(), @CompanyId, NULL, 2, 1025, 5, 'SdrFtpType', 'FTP连接类型', '', 5, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

SET @ParentId = (SELECT TOP 1 [ID] FROM [dbo].[Dict] WHERE [Code] = 'SdrFtpType' AND [CompanyId] = @CompanyId);

-- 子字典项 (跳过None)
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'PASV', '被动模式FTP连接', '', 1, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'PORT', '主动模式FTP连接', '', 2, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

-- =============================================
-- 6. SdrLogType DDI日志记录类型
-- =============================================
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES (NEWID(), @CompanyId, NULL, 2, 1025, 5, 'SdrLogType', 'DDI日志记录类型', '', 6, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

SET @ParentId = (SELECT TOP 1 [ID] FROM [dbo].[Dict] WHERE [Code] = 'SdrLogType' AND [CompanyId] = @CompanyId);

-- 子字典项 (跳过None)
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Local', '本地日志记录', '', 1, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Remote', '远程日志记录', '', 2, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'All', '全部日志记录', '', 3, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

-- =============================================
-- 7. SdrMode DDI运行模式
-- =============================================
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES (NEWID(), @CompanyId, NULL, 2, 1025, 5, 'SdrMode', 'DDI运行模式', '', 7, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

SET @ParentId = (SELECT TOP 1 [ID] FROM [dbo].[Dict] WHERE [Code] = 'SdrMode' AND [CompanyId] = @CompanyId);

-- 子字典项 (跳过None)
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Normal', '正常模式', '', 1, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Track', '跟踪模式', '', 2, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Debug', '调试模式', '', 3, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

-- =============================================
-- 8. SdrScheduleType DDI调度类型
-- =============================================
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES (NEWID(), @CompanyId, NULL, 2, 1025, 5, 'SdrScheduleType', 'DDI调度类型', '', 8, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

SET @ParentId = (SELECT TOP 1 [ID] FROM [dbo].[Dict] WHERE [Code] = 'SdrScheduleType' AND [CompanyId] = @CompanyId);

-- 子字典项 (跳过None)
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Daily', '每日调度', '', 1, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Monthly', '每月调度', '', 2, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

-- =============================================
-- 9. SdrSourceType DDI数据源类型
-- =============================================
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES (NEWID(), @CompanyId, NULL, 2, 1025, 5, 'SdrSourceType', 'DDI数据源类型', '', 9, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

SET @ParentId = (SELECT TOP 1 [ID] FROM [dbo].[Dict] WHERE [Code] = 'SdrSourceType' AND [CompanyId] = @CompanyId);

-- 子字典项 (跳过None)
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'DB', '数据库数据源', '', 1, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'FixPath', '固定路径数据源', '', 2, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'FixFile', '固定文件数据源', '', 3, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

-- =============================================
-- 10. SdrTargetType DDI目标上传类型
-- =============================================
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES (NEWID(), @CompanyId, NULL, 2, 1025, 5, 'SdrTargetType', 'DDI目标上传类型', '', 10, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

SET @ParentId = (SELECT TOP 1 [ID] FROM [dbo].[Dict] WHERE [Code] = 'SdrTargetType' AND [CompanyId] = @CompanyId);

-- 子字典项 (跳过None)
INSERT INTO [dbo].[Dict] ([ID], [CompanyId], [ParentId], [EnumFlags], [EnumEditFlags], [EnumEditFlags_Child], [Code], [Name], [ShortName], [Ordinal], [Remark], [Deleted], [Creator], [CreateTime], [LastEditor], [LastEditTime])
VALUES
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'HTTP', 'HTTP上传方式', '', 1, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'FTP', 'FTP上传方式', '', 2, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'WebService', 'Web服务上传方式', '', 3, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime),
    (NEWID(), @CompanyId, @ParentId, 2, 5, 0, 'Path', '路径上传方式', '', 4, '', 0, @Creator, @CurrentTime, @LastEditor, @CurrentTime);

-- =============================================
-- 脚本执行完成
-- =============================================
PRINT '枚举转字典数据初始化完成';
