﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    [ApiExplorerSettings(GroupName = "手动任务")]
    public class SyncController : BaseApiController<SyncBll>
    {
        /// <summary>
        /// 获取数据同步信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取数据同步信息")]
        public BizResult<SyncTaskModel> GetSyncTask([FromQuery, Required] string code)
        {
            var result = new BizResult<SyncTaskModel>();

            var entity = this.Repo.GetEntity<SyncTask>(p => p.Code == code);

            if (entity == null)
            {
                result.Info("尚未同步过该类数据");
            }
            else
            {
                result.Data = entity.Map<SyncTaskModel>();
            }

            return result;
        }
    }
}