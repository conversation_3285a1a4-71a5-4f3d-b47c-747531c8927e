using System.ComponentModel;
using Shinsoft.DDI.Entities;

namespace Shinsoft.DDI.Api.Models
{
    /// <summary>
    /// 经销商/收货方选择器模型
    /// </summary>
    public partial class ReceiverSelector 
    {
        /// <summary>
        /// 省份名称
        /// </summary>
        [Description("省份名称")]
        [MapFromProperty(typeof(Receiver), Receiver.Foreigns.Province, Province.Columns.Name)]
        public string? ProvinceName { get; set; }

        /// <summary>
        /// 城市名称
        /// </summary>
        [Description("城市名称")]
        [MapFromProperty(typeof(Receiver), Receiver.Foreigns.City, City.Columns.Name)]
        public string? CityName { get; set; }

        /// <summary>
        /// 区县名称
        /// </summary>
        [Description("区县名称")]
        [MapFromProperty(typeof(Receiver), Receiver.Foreigns.County, County.Columns.Name)]
        public string? Phone { get; set; }
    }
}
