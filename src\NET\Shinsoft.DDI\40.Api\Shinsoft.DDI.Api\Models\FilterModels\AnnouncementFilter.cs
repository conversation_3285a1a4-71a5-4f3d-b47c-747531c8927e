﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class AnnouncementFilter
    {
        /// <summary>
        /// 关键词（标题）
        /// </summary>
        [Description("标题")]
        [DynamicQueryColumn(typeof(Announcement), Announcement.Columns.Subject, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }

        /// <summary>
        /// 时间
        /// </summary>
        [Description("时间")]
        public List<DateTime>? Dates{get; set; }

        /// <summary>
        /// 重要性
        /// </summary>
        [Description("重要性")]
        [DynamicQueryColumn(typeof(Announcement), Announcement.Columns.EnumImportant, Operation = Operation.In)]
        public List<Important>? EnumImportants { get; set; }
    }
}
