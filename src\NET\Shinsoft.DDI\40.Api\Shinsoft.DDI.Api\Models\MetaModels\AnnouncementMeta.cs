﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class AnnouncementMeta
    {
        [JsonDateTime(true,false)]
        public override DateTime? PublishTime { get => base.PublishTime; set => base.PublishTime = value; }

        [JsonDateTime(true, false)]
        public override DateTime? StartTime { get => base.StartTime; set => base.StartTime = value; }

        [JsonDateTime(true, false)]
        public override DateTime? EndTime { get => base.EndTime; set => base.EndTime = value; }
    }
}
