<script setup lang="ts">
import { basicMasterDataApi } from "@/api/basicMasterData";
import AddDepartmentCostCenter from "../dialogs/addDepartmentCostCenter.vue";
import { getDefaultOrder, getColumnOrder } from "@/utils/table";
import { TrendCharts } from "@element-plus/icons-vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "organization:deptCostCenter"
});

/**
 * 当前组件ref
 */
const addDepartmentCostCenterRef = ref();

/**
 * 基本配置定义
 */
const cfg = reactive({
  filter: {
    gutter: 3,
    span: 4
  },
  list: {
    defaultSort: { prop: "costCenterId", order: "ascending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.edit ? 45 : 0) + (cfg.btn.delete ? 45 : 0))
    }
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  },
  showOperate: false
});

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  subCompanyId: null,
  departmentId: null
});

/**
 * 初始化组件(created时调用)
 */
const initPage = (subCompanyId, departmentId) => {
  filter.subCompanyId = subCompanyId;
  filter.departmentId = departmentId;

  if (departmentId) {
    cfg.showOperate = true;
  } else {
    cfg.showOperate = false;
  }

  initFilter().then(() => {
    query();
  });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  cfg.loading.filter = true;

  const allInits = [];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.filter = false;
      resolve();
    });
  });
};

/**
 * 获取列表数据
 */
const getList = () => {
  cfg.loading.list = true;
  basicMasterDataApi
    .QueryDepartmentCostCenter(filter)
    .then(res => {
      if (res.success) {
        state.datas = res.datas;
        state.total = res.total;
        filter.pageIndex = res.pageIndex;
      }
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

/**
 * 清空
 */
const clear = () => {
  state.datas = [];
};

/**
 * 添加成本中心
 */
const addCostCenter = (data: any) => {
  addDepartmentCostCenterRef.value.open(filter.subCompanyId, filter.departmentId);
};

/**
 * 设置默认成本中心
 */
const setDefaultCostCenter = (data: any) => {
  data.isDefault = true;

  basicMasterDataApi
    .SetDepartmentCostCenterDefault(data)
    .then(res => {
      if (res.success) {
        ElMessage({
          message: t("operate.message.success"),
          type: "success",
          duration: 3 * 1000
        });
        getList();
      }
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 删除成本中心
 */
const del = (data?: any) => {
  if (data && data.id) {
    ElMessageBox.confirm(
      `${t("operate.confirm.delete")}<br />${t("operate.confirm.deleteHint")}`,
      `${t("operate.title.deleteConfirm")} - ${data.name}`,
      {
        showClose: false,
        closeOnClickModal: false,
        draggable: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: t("operate.ok"),
        cancelButtonText: t("operate.cancel"),
        type: "warning"
      }
    )
      .then(() => {
        cfg.loading.btn = true;
        basicMasterDataApi
          .RemoveDepartmentCostCenter(data)
          .then(res => {
            // 仅提示
            if (res.success) {
              refresh(t("operate.message.success"), "success");
            }
          })
          .finally(() => {
            cfg.loading.btn = false;
          });
      })
      .catch(() => {
        close();
      });
  }
};

/**
 * 刷新table数据
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  query();
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  initPage,
  clear
});
</script>

<template>
  <div>
    <el-row :gutter="10" class="stationInfo">
      <el-col class="station-title" :span="20">
        <el-icon size="15" span="24"><TrendCharts /></el-icon>{{ tt("Entity.CostCenter._Entity") }}
      </el-col>
      <el-col class="buttonbar" :span="4">
        <el-button
          v-if="cfg.showOperate"
          class="new"
          :icon="useRenderIcon('ep:document-add')"
          size="small"
          @click="addCostCenter"
        >
          {{ t("operate.append") }}{{ tt("Entity.CostCenter._Entity") }}
        </el-button>
      </el-col>
      <el-col>
        <el-row :gutter="cfg.filter.gutter" type="flex">
          <el-col :span="cfg.filter.span">
            <el-form-item>
              <el-input
                v-model="filter.keywords"
                clearable
                :placeholder="t('filter.keywords')"
                class="filter-item"
                @keyup.enter="query"
              />
            </el-form-item>
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-button class="query" :loading="cfg.loading.list" @click="query">{{
              t("operate.query")
            }}</el-button>
          </el-col>
        </el-row>
      </el-col>
      <el-col class="station-table">
        <el-table
          v-loading="cfg.loading.list"
          :data="state.datas"
          :height="cfg.list.height"
          :default-sort="cfg.list.defaultSort"
          row-key="id"
          stripe
          border
          class-name="list"
          style="width: 100%; font-size: 12px"
        >
          <template #empty>
            <NoDatas />
          </template>
          <el-table-column
            fixed
            type="index"
            :label="t('list.no')"
            show-overflow-tooltip
            width="60"
            align="center"
          />
          <el-table-column
            fixed
            sortable
            prop="costCenterName"
            :label="tt('Entity.CostCenter._Entity')"
            min-width="120"
          />
          <el-table-column
            sortable
            prop="departmentName"
            :label="tt('Entity.Department._Entity')"
            min-width="100"
          />
          <el-table-column
            :label="tt('Entity.Department.DefaultCostCenter')"
            width="120"
            align="center"
          >
            <template #default="{ row }">
              {{ row.isDefault ? t("operate.yes") : t("operate.no") }}
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            :label="t('list.operate')"
            class-name="operate"
            :width="150"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                v-if="!row.isDefault"
                class="edit"
                size="small"
                :circle="true"
                :title="t('operate.setDefaultCostCenter')"
                :icon="useRenderIcon('ep:setting')"
                @click="setDefaultCostCenter(row)"
              />
              <el-button
                class="delete"
                size="small"
                :circle="true"
                :title="t('operate.delete')"
                :icon="useRenderIcon('ep:delete')"
                @click="del(row)"
              />
            </template>
          </el-table-column>
        </el-table>
        <Pagination v-model:filter="filter" :total="state.total" @change="getList" />
      </el-col>
    </el-row>
    <AddDepartmentCostCenter ref="addDepartmentCostCenterRef" @refresh="getList" />
  </div>
</template>
