﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class EmployeeStationMeta
    {
        /// <summary>
        /// 姓名
        /// </summary>
        [MapFromProperty(typeof(EmployeeStation), EmployeeStation.Foreigns.Employee, Employee.Columns.DisplayName)]
        public string? EmployeeName { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        [MapFromProperty(typeof(EmployeeStation), EmployeeStation.Foreigns.Employee, Employee.Columns.JobNo)]
        public string? EmployeeJobNo { get; set; }

        /// <summary>
        /// 岗位
        /// </summary>
        [MapFromProperty(typeof(EmployeeStation), EmployeeStation.Foreigns.Station, Station.Columns.Name)]
        public string? StationName { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        [MapFromProperty(typeof(EmployeeStation), EmployeeStation.Foreigns.Station, Station.Foreigns.Position, Position.Columns.Name)]
        public string? PositionName { get; set; }

        [JsonDateTime(false)]
        public override DateTime? StartDate { get; set; }

        [JsonDateTime(false)]
        public override DateTime? EndDate { get; set; }
    }
}
