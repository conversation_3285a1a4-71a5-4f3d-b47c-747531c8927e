<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="App\**" />
    <EmbeddedResource Remove="App\**" />
    <None Remove="App\**" />
  </ItemGroup>
  <ItemGroup>
	<Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Shinsoft.Core">
      <HintPath>..\..\00.Reference\net8.0\Shinsoft.Core.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.14" />
	<PackageReference Include="System.Text.Json" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
	<Using Include="System.ComponentModel" />
	<Using Include="System.ComponentModel.DataAnnotations.Schema" />
	<Using Include="System.Text.Json.Serialization" />
	<Using Include="System.Xml.Serialization" />
	<Using Include="Shinsoft.Core" />
	<Using Include="Shinsoft.Core.Caching" />
	<Using Include="Shinsoft.Core.EntityFrameworkCore" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Entity\Biz.Entity.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Biz.Entity.cs</LastGenOutput>
    </None>
    <None Update="Entity\File.Entity.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>File.Entity.cs</LastGenOutput>
    </None>
    <None Update="Entity\Log.Entity.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Log.Entity.cs</LastGenOutput>
    </None>
    <None Update="Entity\Mail.Entity.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Mail.Entity.cs</LastGenOutput>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Entity\Biz.Entity.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Biz.Entity.tt</DependentUpon>
    </Compile>
    <Compile Update="Entity\File.Entity.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>File.Entity.tt</DependentUpon>
    </Compile>
    <Compile Update="Entity\Log.Entity.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Log.Entity.tt</DependentUpon>
    </Compile>
    <Compile Update="Entity\Mail.Entity.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Mail.Entity.tt</DependentUpon>
    </Compile>
  </ItemGroup>

</Project>
