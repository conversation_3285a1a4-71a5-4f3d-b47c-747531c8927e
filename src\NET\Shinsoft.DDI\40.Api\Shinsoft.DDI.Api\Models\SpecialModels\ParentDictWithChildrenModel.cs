using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    /// <summary>
    /// 父级字典及其子项模型
    /// </summary>
    [Description("父级字典及其子项模型")]
    public partial class ParentDictWithChildrenModel
    {
        /// <summary>
        /// 父级字典信息
        /// </summary>
        [Description("父级字典信息")]
        public DictModel ParentDict { get; set; } = new DictModel();

        /// <summary>
        /// 子项列表
        /// </summary>
        [Description("子项列表")]
        public List<DictModel> Children { get; set; } = new List<DictModel>();
    }
}
