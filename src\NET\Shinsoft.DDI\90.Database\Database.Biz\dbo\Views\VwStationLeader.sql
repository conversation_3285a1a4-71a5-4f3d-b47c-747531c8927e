﻿--岗位领导视图
CREATE VIEW [dbo].[VwStationLeader] AS

SELECT 
	CONCAT(s.ID ,'|',l_s.ID,'@',es.ID,'|',lm_es.ID,'|',l_es.ID) AS [ID],
	s.CompanyId,
	--员工
	es.EmployeeId,
	--岗位
	s.ID AS [StationId],					--KEY 1
	s.StartDate AS [StationStartDate],
	s.EndDate AS [StationEndDate],

	--直属上级岗
	lm_s.ID AS [LineManagerStationId],	
	lm_s.StartDate AS [LineManagerStationStartDate],	
	lm_s.EndDate AS [LineManagerStationEndDate],

	--直属上级
	lm_e.ID AS [LineManagerId],
	lm_es.StartDate AS [LineManagerStartDate],
	lm_es.EndDate AS [LineManagerEndDate],

	--越级领导岗
	l_s.ID AS [LeaderStationId],			--KEY 2 NULL	
	l_s.StartDate AS [LeaderStationStartDate],	
	l_s.EndDate AS [LeaderStationEndDate],

	--越级领导
	l_e.ID AS [LeaderId],
	l_es.StartDate AS [LeaderStartDate],
	l_es.EndDate AS [LeaderEndDate]
	--es.ID,						--KEY 3 NULL
	--lm_es.ID,						--KEY 4 NULL
	--l_es.ID,						--KEY 5 NULL
FROM dbo.Station AS s
LEFT JOIN dbo.EmployeeStation AS es ON es.StationId = s.ID	--人岗关系

--直属上级
LEFT JOIN dbo.Station AS lm_s ON lm_s.ID = s.ParentId AND lm_s.Deleted = 0 AND lm_s.Valid = 1				--直属上级岗	1:1
LEFT JOIN dbo.EmployeeStation AS lm_es ON lm_es.StationId = lm_s.ID											--直属上级，人岗关系
LEFT JOIN dbo.Employee AS lm_e ON lm_e.ID = lm_es.EmployeeId AND lm_e.Deleted = 0 AND lm_e.EnumStatus > 0	--直属上级

--越级领导
LEFT JOIN dbo.Station AS l_s ON l_s.Deleted = 0 AND l_s.Valid = 1 AND l_s.ID <> lm_s.ID AND lm_s.UidPath LIKE l_s.UidPath + '%'	--越级领导岗位（不包含直属上级岗位）
LEFT JOIN dbo.EmployeeStation AS l_es ON l_es.StationId = l_s.ID											--越级领导，人岗关系
LEFT JOIN dbo.Employee AS l_e ON l_e.ID = l_es.EmployeeId AND l_e.Deleted = 0 AND l_e.EnumStatus > 0		--越级领导

--岗位判断
WHERE s.Deleted = 0 
AND s.Valid = 1
