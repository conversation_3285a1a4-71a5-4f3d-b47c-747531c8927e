CREATE TABLE [dbo].[ProductAlias] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [Default_ProductAlias_ID] DEFAULT (NEWSEQUENTIALID()),
    [ProductSpecId]             UNIQUEIDENTIFIER            NOT NULL,
    [ReceiverId]                UNIQUEIDENTIFIER            NOT NULL,
    [ProductAliasName]          NVARCHAR(50)                NOT NULL,
    [ProductSpecAlias]          NVARCHAR(50)                NOT NULL,
    [Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL,
    [CreateTime]                DATETIME                    NULL,
    [LastEditor]                NVARCHAR(50)                NULL,
    [LastEditTime]              DATETIME                    NULL,
    CONSTRAINT [PK_ProductAlias] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ProductAlias_ProductSpec] FOREIGN KEY ([ProductSpecId]) REFERENCES [dbo].[ProductSpec] ([ID]),
    CONSTRAINT [FK_ProductAlias_Receiver] FOREIGN KEY ([ReceiverId]) REFERENCES [dbo].[Receiver] ([ID])
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'产品别名',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductAlias',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductAlias',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'产品规格ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductAlias',
    @level2type = N'COLUMN',
    @level2name = N'ProductSpecId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'收货方ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductAlias',
    @level2type = N'COLUMN',
    @level2name = N'ReceiverId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'产品别名名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductAlias',
    @level2type = N'COLUMN',
    @level2name = N'ProductAliasName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'产品规格别名',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductAlias',
    @level2type = N'COLUMN',
    @level2name = N'ProductSpecAlias'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductAlias',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductAlias',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductAlias',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductAlias',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ProductAlias',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'
