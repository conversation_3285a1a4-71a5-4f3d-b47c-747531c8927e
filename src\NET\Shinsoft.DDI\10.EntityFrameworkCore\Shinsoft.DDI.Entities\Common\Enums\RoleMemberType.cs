﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    /// <summary>
    /// 角色成员类型
    /// </summary>
    [Description("角色成员类型")]
    public enum RoleMemberType
    {
        None = 0,

        /// <summary>
        /// 用户
        /// </summary>
        [Description("用户")]
        [EnumGroup(Visible = false)]
        User = 1,

        /// <summary>
        /// 员工
        /// </summary>
        [Description("员工")]
        Employee = 2,

        /// <summary>
        /// 岗位
        /// </summary>
        [Description("岗位")]
        Station = 3,

        /// <summary>
        /// 职位
        /// </summary>
        [Description("职位")]
        Position = 4,

        /// <summary>
        /// 部门
        /// </summary>
        [Description("部门")]
        Department = 5,

        /// <summary>
        /// 群组
        /// </summary>
        [Description("群组")]
        Group = 10,
    }
}
