﻿--人岗关系视图
CREATE VIEW [dbo].[VwEmployeeStation] AS


SELECT 
	es.ID,
	es.CompanyId,
	es.EmployeeId,
	es.StationId,
	p_s.ID AS [ParentStationId],
	s.Name AS [StationName],
	s.StartDate AS [StationStartDate],
	s.EndDate AS [StationEndDate],
	es.StartDate,
	es.EndDate,
	es.Creator,
	es.CreateTime,
	es.LastEditor,
	es.LastEditTime
FROM  dbo.EmployeeStation AS es
INNER JOIN dbo.Employee AS e ON e.ID = es.EmployeeId
INNER JOIN dbo.Station AS s ON s.ID = es.StationId
INNER JOIN dbo.Department AS d ON d.ID = s.DepartmentId
INNER JOIN dbo.Position AS p ON p.ID = s.PositionId
LEFT JOIN dbo.Station AS p_s ON p_s.ID = s.ParentId
LEFT JOIN dbo.Department AS p_d ON p_d.ID = p_s.DepartmentId
LEFT JOIN dbo.Position AS p_p ON p_p.ID = p_s.PositionId
WHERE e.Deleted = 0			
AND e.EnumStatus > 0		--员工有效
AND s.Deleted = 0
AND s.Valid = 1				--岗位有效
AND d.Deleted = 0
AND d.Valid = 1				--部门有效
AND p.Deleted = 0
AND ISNULL(p_s.Deleted,0) = 0
AND ISNULL(p_s.Valid,1) = 1
AND ISNULL(p_d.Deleted,0) = 0
AND ISNULL(p_d.Valid,1) = 1
AND ISNULL(p_p.Deleted,0) = 0

