﻿--职位
CREATE TABLE [dbo].[Position] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_Position_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [ParentId]                  UNIQUEIDENTIFIER            NULL,
    [Uid]                       INT                         NOT NULL    IDENTITY (1, 1) ,
    [UidPath]                   VARCHAR(500)		        NOT NULL,
    [EnumFlags]                 INT                         NOT NULL,
    [Grade]                     INT                         NOT NULL,
    [Code]                      NVARCHAR(50)                NOT NULL,
    [Name]                      NVARCHAR(50)                NOT NULL,
    [Remark]                    NVARCHAR(500)               NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_Position] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Position_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_Position_Position_00_Parent_Children] FOREIGN KEY ([ParentId]) REFERENCES [dbo].[Position] ([ID]),
);
GO

CREATE UNIQUE INDEX [IX_Position_Uid] ON [dbo].[Position]
(
	[Uid] ASC
);
GO
CREATE INDEX [IX_Position_UidPath] ON [dbo].[Position]
(
	[UidPath] ASC
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'职位',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Position',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'上级职位ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Position',
    @level2type = N'COLUMN',
    @level2name = N'ParentId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'唯一码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Position',
    @level2type = N'COLUMN',
    @level2name = N'Uid'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'职级',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Position',
    @level2type = N'COLUMN',
    @level2name = N'Grade'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'编码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Position',
    @level2type = N'COLUMN',
    @level2name = N'Code'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Position',
    @level2type = N'COLUMN',
    @level2name = N'Name'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Position',
    @level2type = N'COLUMN',
    @level2name = N'Remark'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'唯一码路径',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Position',
    @level2type = N'COLUMN',
    @level2name = N'UidPath'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'标签',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Position',
    @level2type = N'COLUMN',
    @level2name = N'EnumFlags'
