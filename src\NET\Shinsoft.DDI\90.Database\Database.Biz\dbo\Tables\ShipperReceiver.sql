﻿CREATE TABLE [dbo].[ShipperReceiver]
(
	[ID]  UNIQUEIDENTIFIER            NOT NULL  CONSTRAINT [Default_ShipperReceiver_ID] DEFAULT (NEWSEQUENTIALID()), 
	[ShipperId] UNIQUEIDENTIFIER NOT NULL,
    [ReceiverId] UNIQUEIDENTIFIER NOT NULL,
	[Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL, 
    [CreateTime]                DATETIME                    NULL, 
    [LastEditor]                NVARCHAR(50)                NULL, 
    [LastEditTime]              DATETIME                    NULL, 
	CONSTRAINT [PK_ShipperReceiver] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ShipperReceiver_Manufacturer] FOREIGN KEY ([ShipperId]) REFERENCES [dbo].[Shipper] ([ID]),
	CONSTRAINT [FK_ShipperReceiver_Receiver] FOREIGN KEY ([ReceiverId]) REFERENCES [dbo].[Receiver] ([ID]),
)
GO


EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'货主经销商配置',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperReceiver',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperReceiver',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
 
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'货主Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperReceiver',
    @level2type = N'COLUMN',
    @level2name = N'ShipperId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'经销商Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperReceiver',
    @level2type = N'COLUMN',
    @level2name = N'ReceiverId'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'是否删除',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperReceiver',
    @level2type = N'COLUMN',
    @level2name = N'Deleted'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperReceiver',
    @level2type = N'COLUMN',
    @level2name = N'Creator'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'创建时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperReceiver',
    @level2type = N'COLUMN',
    @level2name = N'CreateTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑人',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperReceiver',
    @level2type = N'COLUMN',
    @level2name = N'LastEditor'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'最后编辑时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ShipperReceiver',
    @level2type = N'COLUMN',
    @level2name = N'LastEditTime'
