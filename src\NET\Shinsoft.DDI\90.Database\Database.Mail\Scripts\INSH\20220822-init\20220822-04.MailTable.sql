﻿/*
HSBC_Recharge_PRD 的部署脚本

此代码由工具生成。
如果重新生成此代码，则对此文件的更改可能导致
不正确的行为并将丢失。
*/

PRINT N'正在创建 架构 [mail]...';


GO
CREATE SCHEMA [mail]
    AUTHORIZATION [dbo];


GO
PRINT N'正在创建 表 [mail].[MailAttachment]...';


GO
CREATE TABLE [mail].[MailAttachment] (
    [ID]           UNIQUEIDENTIFIER NOT NULL,
    [CompanyId]    UNIQUEIDENTIFIER NOT NULL,
    [MailId]       UNIQUEIDENTIFIER NOT NULL,
    [FileIndexId]  UNIQUEIDENTIFIER NOT NULL,
    [FileSize]     BIGINT           NOT NULL,
    [ContentType]  NVARCHAR (200)   NOT NULL,
    [FileName]     NVARCHAR (500)   NOT NULL,
    [FileExt]      NVARCHAR (50)    NOT NULL,
    [Deleted]      BIT              NOT NULL,
    [Creator]      NVARCHAR (50)    NULL,
    [CreateTime]   DATETIME         NULL,
    [LastEditor]   NVARCHAR (50)    NULL,
    [LastEditTime] DATETIME         NULL,
    CONSTRAINT [PK_MailAttachment] PRIMARY KEY CLUSTERED ([ID] ASC)
);


GO
PRINT N'正在创建 表 [mail].[SmtpServer]...';


GO
CREATE TABLE [mail].[SmtpServer] (
    [ID]             UNIQUEIDENTIFIER NOT NULL,
    [CompanyId]      UNIQUEIDENTIFIER NOT NULL,
    [Host]           NVARCHAR (50)    NOT NULL,
    [Port]           INT              NOT NULL,
    [EnumSmtpSecure] INT              NOT NULL,
    [Username]       NVARCHAR (50)    NULL,
    [Password]       NVARCHAR (50)    NULL,
    CONSTRAINT [PK_SmtpServer] PRIMARY KEY CLUSTERED ([ID] ASC)
);


GO
PRINT N'正在创建 表 [mail].[MailTemplate]...';


GO
CREATE TABLE [mail].[MailTemplate] (
    [ID]           UNIQUEIDENTIFIER NOT NULL,
    [CompanyId]    UNIQUEIDENTIFIER NOT NULL,
    [Code]         NVARCHAR (50)    NULL,
    [IsHtmlBody]   BIT              NOT NULL,
    [Subject]      NVARCHAR (1000)  NOT NULL,
    [Content]      NVARCHAR (MAX)   NOT NULL,
    [Deleted]      BIT              NOT NULL,
    [Creator]      NVARCHAR (50)    NULL,
    [CreateTime]   DATETIME         NULL,
    [LastEditor]   NVARCHAR (50)    NULL,
    [LastEditTime] DATETIME         NULL,
    CONSTRAINT [PK_MailTemplate] PRIMARY KEY CLUSTERED ([ID] ASC)
);


GO
PRINT N'正在创建 表 [mail].[MailServer]...';


GO
CREATE TABLE [mail].[MailServer] (
    [ID]            UNIQUEIDENTIFIER NOT NULL,
    [CompanyId]     UNIQUEIDENTIFIER NOT NULL,
    [EnumType]      INT              NOT NULL,
    [Code]          NVARCHAR (50)    NOT NULL,
    [From]          NVARCHAR (500)   NOT NULL,
    [DebugTo]       NVARCHAR (500)   NOT NULL,
    [SendInterval]  INT              NOT NULL,
    [RetryInterval] INT              NOT NULL,
    [MaxRetry]      INT              NOT NULL,
    [MaxResend]     INT              NOT NULL,
    [Valid]         BIT              NOT NULL,
    [Description]   NVARCHAR (500)   NULL,
    [Deleted]       BIT              NOT NULL,
    [Creator]       NVARCHAR (50)    NULL,
    [CreateTime]    DATETIME         NULL,
    [LastEditor]    NVARCHAR (50)    NULL,
    [LastEditTime]  DATETIME         NULL,
    CONSTRAINT [PK_MailServer] PRIMARY KEY CLUSTERED ([ID] ASC)
);


GO
PRINT N'正在创建 表 [mail].[Mail]...';


GO
CREATE TABLE [mail].[Mail] (
    [ID]              UNIQUEIDENTIFIER NOT NULL,
    [CompanyId]       UNIQUEIDENTIFIER NOT NULL,
    [MailServerId]    UNIQUEIDENTIFIER NULL,
    [Trigger]         NVARCHAR (50)    NOT NULL,
    [ObjectType]      NVARCHAR (200)   NOT NULL,
    [ObjectId]        NVARCHAR (50)    NOT NULL,
    [ObjectName]      NVARCHAR (50)    NOT NULL,
    [IsHtmlBody]      BIT              NOT NULL,
    [From]            NVARCHAR (500)   NOT NULL,
    [RealFrom]        NVARCHAR (500)   NOT NULL,
    [DebugTo]         NVARCHAR (500)   NOT NULL,
    [To]              NVARCHAR (MAX)   NOT NULL,
    [Cc]              NVARCHAR (MAX)   NOT NULL,
    [Bcc]             NVARCHAR (MAX)   NOT NULL,
    [Subject]         NVARCHAR (1000)  NOT NULL,
    [Content]         NVARCHAR (MAX)   NOT NULL,
    [AttachmentPaths] VARCHAR (MAX)    NULL,
    [EnumStatus]      INT              NOT NULL,
    [PlanSendTime]    DATETIME         NULL,
    [SendTime]        DATETIME         NULL,
    [SendCount]       INT              NOT NULL,
    [SendMessage]     NVARCHAR (MAX)   NOT NULL,
    [Deleted]         BIT              NOT NULL,
    [Creator]         NVARCHAR (50)    NULL,
    [CreateTime]      DATETIME         NULL,
    [LastEditor]      NVARCHAR (50)    NULL,
    [LastEditTime]    DATETIME         NULL,
    CONSTRAINT [PK_Mail] PRIMARY KEY CLUSTERED ([ID] ASC)
);


GO
PRINT N'正在创建 默认约束 [mail].[DF_MailAttachment_ID]...';


GO
ALTER TABLE [mail].[MailAttachment]
    ADD CONSTRAINT [DF_MailAttachment_ID] DEFAULT (NEWSEQUENTIALID()) FOR [ID];


GO
PRINT N'正在创建 默认约束 [mail].[DF_MailTemplate_ID]...';


GO
ALTER TABLE [mail].[MailTemplate]
    ADD CONSTRAINT [DF_MailTemplate_ID] DEFAULT (NEWSEQUENTIALID()) FOR [ID];


GO
PRINT N'正在创建 默认约束 [mail].[DF_MailServer_ID]...';


GO
ALTER TABLE [mail].[MailServer]
    ADD CONSTRAINT [DF_MailServer_ID] DEFAULT (NEWSEQUENTIALID()) FOR [ID];


GO
PRINT N'正在创建 默认约束 [mail].[DF_Mail_ID]...';


GO
ALTER TABLE [mail].[Mail]
    ADD CONSTRAINT [DF_Mail_ID] DEFAULT (NEWSEQUENTIALID()) FOR [ID];


GO
PRINT N'正在创建 外键 [mail].[FK_MailAttachment_Mail_00_Mail_Attachments]...';


GO
ALTER TABLE [mail].[MailAttachment] WITH NOCHECK
    ADD CONSTRAINT [FK_MailAttachment_Mail_00_Mail_Attachments] FOREIGN KEY ([MailId]) REFERENCES [mail].[Mail] ([ID]);


GO
PRINT N'正在创建 外键 [mail].[FK_SmtpServer_MailServer]...';


GO
ALTER TABLE [mail].[SmtpServer] WITH NOCHECK
    ADD CONSTRAINT [FK_SmtpServer_MailServer] FOREIGN KEY ([ID]) REFERENCES [mail].[MailServer] ([ID]);


GO
PRINT N'正在创建 外键 [mail].[FK_Mail_MailServer]...';


GO
ALTER TABLE [mail].[Mail] WITH NOCHECK
    ADD CONSTRAINT [FK_Mail_MailServer] FOREIGN KEY ([MailServerId]) REFERENCES [mail].[MailServer] ([ID]);


GO
PRINT N'正在创建 扩展属性 [mail].[SmtpServer].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'''SMTP服务器', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'SmtpServer';


GO
PRINT N'正在创建 扩展属性 [mail].[SmtpServer].[Host].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'地址', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'SmtpServer', @level2type = N'COLUMN', @level2name = N'Host';


GO
PRINT N'正在创建 扩展属性 [mail].[SmtpServer].[Port].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'端口', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'SmtpServer', @level2type = N'COLUMN', @level2name = N'Port';


GO
PRINT N'正在创建 扩展属性 [mail].[SmtpServer].[EnumSmtpSecure].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'1:Ssl; 10:Tls; 11:Tls1; 12:Tls2; 13:Tls3', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'SmtpServer', @level2type = N'COLUMN', @level2name = N'EnumSmtpSecure';


GO
PRINT N'正在创建 扩展属性 [mail].[SmtpServer].[Username].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'用户名', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'SmtpServer', @level2type = N'COLUMN', @level2name = N'Username';


GO
PRINT N'正在创建 扩展属性 [mail].[SmtpServer].[Password].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'密码', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'SmtpServer', @level2type = N'COLUMN', @level2name = N'Password';


GO
PRINT N'正在创建 扩展属性 [mail].[MailTemplate].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'邮件模板', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'MailTemplate';


GO
PRINT N'正在创建 扩展属性 [mail].[MailServer].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'邮件服务器', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'MailServer';


GO
PRINT N'正在创建 扩展属性 [mail].[MailServer].[EnumType].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'邮件服务器类型', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'MailServer', @level2type = N'COLUMN', @level2name = N'EnumType';


GO
PRINT N'正在创建 扩展属性 [mail].[MailServer].[Code].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'邮件服务器编码', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'MailServer', @level2type = N'COLUMN', @level2name = N'Code';


GO
PRINT N'正在创建 扩展属性 [mail].[MailServer].[From].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'邮件发件人地址', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'MailServer', @level2type = N'COLUMN', @level2name = N'From';


GO
PRINT N'正在创建 扩展属性 [mail].[MailServer].[DebugTo].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Debug收件人地址', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'MailServer', @level2type = N'COLUMN', @level2name = N'DebugTo';


GO
PRINT N'正在创建 扩展属性 [mail].[Mail].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'邮件', @level0type = N'SCHEMA', @level0name = N'mail', @level1type = N'TABLE', @level1name = N'Mail';


GO
PRINT N'根据新创建的约束检查现有的数据';


GO

ALTER TABLE [mail].[MailAttachment] WITH CHECK CHECK CONSTRAINT [FK_MailAttachment_Mail_00_Mail_Attachments];

ALTER TABLE [mail].[SmtpServer] WITH CHECK CHECK CONSTRAINT [FK_SmtpServer_MailServer];

ALTER TABLE [mail].[Mail] WITH CHECK CHECK CONSTRAINT [FK_Mail_MailServer];


GO
PRINT N'更新完成。';


GO
