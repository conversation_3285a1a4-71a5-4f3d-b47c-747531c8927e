﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    [Flags]
    public enum AuthFlag
    {
        None = 0,

        /// <summary>
        /// 平台
        /// </summary>
        [Description("平台")]
        Platform = 1,

        /// <summary>
        /// 隐藏
        /// </summary>
        [Description("隐藏")]
        [EnumGroup("Sys")]
        Invisible = 2,

        /// <summary>
        /// 只有员工可用
        /// </summary>
        [Description("只有员工可用")]
        [EnumGroup("Sys")]
        EmployeeOnly = 4,

        /// <summary>
        /// 可代理
        /// </summary>
        [Description("可代理")]
        [EnumGroup("Sys")]
        Agentable = 8,

        /// <summary>
        /// 只可用于代理
        /// </summary>
        [Description("只可用于代理")]
        [EnumGroup("Sys")]
        AgentOnly = 16,
    }
}
