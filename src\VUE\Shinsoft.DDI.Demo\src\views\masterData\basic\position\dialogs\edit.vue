<script setup lang="ts">
import { basicMasterDataApi } from "@/api/basicMasterData";
import { selectorApi } from "@/api/selector";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { useEnumStoreHook } from "@/store/modules/enum";
import { ElMessage, FormInstance } from "element-plus";
import { EnumFlagsCheckbox } from "@/components/EnumFlags";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "position:edit"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.model?.id
    ? `${t("operate.edit")} ${tt("Entity.Position._Entity")} - ${state.model.name}`
    : `${t("operate.add")} ${tt("Entity.Role._Entity")}`;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      enumFlags: 0
    }
  },
  // 枚举定义
  enums: {
    positionFlag: [],
    positionAuthFlag: []
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  },
  // 按钮权限
  btn: {
    save: computed(() => {
      return state.model.id
        ? userStore.hasAnyAuth(["Position:Manage", "Position:Manage:Edit"])
        : userStore.hasAnyAuth(["Position:Manage", "Position:Manage:Add"]);
    })
  },
  positions: []
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  model: cfg.default.model as Record<string, any>
});

/**
 * 验证规则
 */
const rules = {
  form: {
    code: [
      { required: true, message: tt("Rule.Position.Code:Required"), trigger: "blur" },
      { max: 50, message: tt("Rule.Position.Code:Length"), trigger: "blur" }
    ],
    name: [
      { required: true, message: tt("Rule.Position.Name:Required"), trigger: "blur" },
      { max: 50, message: tt("Rule.Position.Name:Length"), trigger: "blur" }
    ],
    grade: [
      {
        required: true,
        trigger: "blur",
        message: tt("Rule.Position.Grade:Required")
      },
      {
        pattern: /((^[0-9]\d*))$/,
        message: tt("Rule.Position.Grade:Length"),
        trigger: "blur"
      }
    ],
    remark: [{ max: 500, message: tt("Rule.Position.Remark:Length"), trigger: "blur" }]
  }
};

/**
 * 存储
 */
const userStore = useUserStoreHook();
const enumStore = useEnumStoreHook();

/**
 * 当前组件ref
 */
const formRef = ref<FormInstance>();

/**
 * 初始化组件（异步）
 */
const init = () => {
  // 防止之前打开的数据残留，因此初始化时赋予model默认值
  initState();

  cfg.loading.form = true;

  const initPositionFlags = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("PositionFlag").then(enumInfos => {
        cfg.enums.positionFlag = enumInfos;
        resolve();
      });
    });
  };

  const initPositionAuthFlags = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("PositionAuthFlag").then(enumInfos => {
        cfg.enums.positionAuthFlag = enumInfos;
        resolve();
      });
    });
  };

  const initPositions = async () => {
    return new Promise<void>(resolve => {
      selectorApi.GetPositionSelectors().then(res => {
        cfg.positions = res.data;
        resolve();
      });
    });
  };

  const allInits = [initPositionFlags(), initPositionAuthFlags(), initPositions()];

  return new Promise<void>(() => {
    Promise.all(allInits).then(() => {
      cfg.loading.form = false;
      get();
    });
  });
};

/**
 * 设置model数据
 */
const setModel = (data: any) => {
  state.model = data;
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 初始化state数据
 */
const initState = () => {
  state.model = cfg.default.model;
};

/**
 * 清空mstate数据
 */
const clearState = () => {
  initState();
  formRef.value.clearValidate();
  formRef.value.resetFields();
};

/**
 * 获取model数据
 */
const get = () => {
  if (state.id) {
    cfg.loading.form = true;
    basicMasterDataApi
      .GetPosition(state.id)
      .then(res => {
        if (res.success) {
          setModel(res.data);
        } else {
          close();
        }
      })
      .finally(() => {
        cfg.loading.form = false;
      });
  }
};

/**
 * 新增数据
 */
const add = () => {
  cfg.loading.btn = true;

  const data = getModel();

  basicMasterDataApi
    .AddPosition(data, { messageBoxResult: true })
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh(t("operate.message.success"), "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 更新数据
 */
const update = () => {
  cfg.loading.btn = true;

  const data = getModel();

  basicMasterDataApi
    .UpdatePosition(data, { messageBoxResult: true })
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh(t("operate.message.success"), "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 按钮事件：【保存】
 */
const save = async () => {
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      if (state.model.id) {
        update();
      } else {
        add();
      }
    }
  });
};

/**
 * dialog:显示(父组件调用)
 */
const open = (id?: string) => {
  cfg.dialog.visible = true;
  state.id = id;
  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        v-loading="cfg.loading.form"
        :rules="rules.form"
        :model="state.model"
        label-position="right"
        :label-width="formLabelWidth"
        class="el-dialog-form"
      >
        <el-row :gutter="formGutter">
          <el-col :span="formColSpan">
            <el-form-item prop="code" :label="tt('Entity.Position.Code')">
              <el-input
                v-model="state.model.code"
                :placeholder="tt('Entity.Position.Code')"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="name" :label="tt('Entity.Position.Name')">
              <el-input
                v-model="state.model.name"
                :placeholder="tt('Entity.Position.Name')"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="false" :span="formColSpan">
            <el-form-item prop="parentId" :label="tt('Entity.Position.Parent')">
              <el-select
                v-model="state.model.parentId"
                clearable
                :placeholder="tt('Entity.Position.Parent')"
              >
                <el-option
                  v-for="item in cfg.positions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="grade" :label="tt('Entity.Position.Grade')">
              <el-input
                v-model="state.model.grade"
                :placeholder="tt('Entity.Position.Grade')"
                maxlength="10"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="enumFlags" :label="tt('Entity.Position.EnumFlags')">
              <enum-flags-checkbox
                v-model="state.model.enumFlags"
                enumType="PositionFlag"
                :enums="cfg.enums.positionFlag"
                border
              />
            </el-form-item>
          </el-col>
          <!--TODO:枚举没有值暂时先不显示-->
          <el-col v-if="false" :span="formColSpan">
            <el-form-item
              prop="enumPositionAuthFlags"
              :label="tt('Entity.Position.EnumPositionAuthFlags')"
            >
              <el-checkbox-group v-model="state.model.enumPositionAuthFlags">
                <el-checkbox
                  v-for="item in cfg.enums.positionAuthFlag"
                  :key="item"
                  :label="item.desc"
                  :value="item.value"
                  border
                />
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="formGutter">
          <el-col :span="24">
            <el-form-item prop="remark" :label="tt('Entity.Position.Remark')">
              <el-input
                v-model="state.model.remark"
                :placeholder="tt('Entity.Position.Remark')"
                maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.save"
            class="save"
            :round="btnRound"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnSaveIcon)"
            @click="save()"
          >
            {{ t("operate.save") }}
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="css">
.is-not-error > .el-input__wrapper {
  box-shadow: #dcdfe6 !important;
}
</style>
