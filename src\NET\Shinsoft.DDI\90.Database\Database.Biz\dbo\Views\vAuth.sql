CREATE VIEW [dbo].[vAuth] AS

SELECT 
	a.ID,
	a.<PERSON>,
	a.<PERSON>rentId,
	STUFF(
		(SELECT '/【'+ g.Name + '】'
		FROM dbo.Auth AS g
		WHERE a.UidPath LIKE g.UidPath + '%'
		AND g.EnumType = 0
		ORDER by a.UidPath ASC for xml path(''))
	,1,1,'') AS [Group], 
	a.Code,
	a.Name,
    CONVERT(BIT,CASE
        WHEN a.Valid = 0 OR CHARINDEX('[0]',STUFF(
		    (SELECT CONCAT( '/[', g.Valid,']')
		        FROM dbo.Auth AS g
		        WHERE a.UidPath LIKE g.UidPath + '%'
		        AND g.EnumType = 0
		        ORDER by a.UidPath ASC for xml path(''))
	        ,1,1,'')) > 0 THEN 0
        ELSE 1
    END) AS [Valid],
	a.Ordinal,
	a.Remark,
	a.<PERSON>,
	a<PERSON>,
	a.EnumProgramFlags,
	a.EnumFlags AS [EnumAuthFlags],
    a.<PERSON>agType,
	a.Creator,
	a.CreateTime,
	a.LastEditor,
	a.LastEditTime
FROM dbo.Auth AS a
WHERE a.Deleted = 0
AND a.EnumType = 1
