﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class ReceiverClientFilter
    {
        /// <summary>
        /// 编码
        /// </summary>
        [Description("编码")]
        [DynamicQueryColumn(typeof(ReceiverClient), ReceiverClient.Foreigns.Receiver, Receiver.Columns.Code, Operation = Operation.StringIntelligence)]
        public string? Code { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        [DynamicQueryColumn(typeof(ReceiverClient), ReceiverClient.Foreigns.Receiver, Receiver.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Name { get; set; }

        //状态

        //授权码

        /// <summary>
        /// 采集方式
        /// </summary>
        [Description("采集方式")]
        public string? TargetType { get; set; }
    }
}
