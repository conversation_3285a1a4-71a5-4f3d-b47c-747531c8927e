﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    [ApiExplorerSettings(GroupName = "设置")]
    public class CompanySetupController : BaseApiController<CompanySetupBll>
    {
        #region CompanySetting

        /// <summary>
        /// 查询扩展配置
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.System.Setup.CompanySetting_Maintain)]
        [LogApi(ApiType.Query, Operate = "查询扩展配置")]
        public QueryResult<CompanySettingQuery> QueryCompanySetting([FromQuery] CompanySettingFilter filter)
        {
            var exps = this.NewExps<CompanySetting>();

            return this.Repo.GetDynamicQuery<CompanySetting, CompanySettingQuery>(filter, exps);
        }

        /// <summary>
        /// 获取扩展配置
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.System.Setup.CompanySetting_Maintain)]
        [LogApi(ApiType.Query, Operate = "获取扩展配置")]
        public BizResult<CompanySettingModel> GetCompanySetting([FromQuery, Required] Guid id)
        {
            var result = new BizResult<CompanySettingModel>();

            var entity = this.Repo.Get<CompanySetting>(id);

            if (entity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, CompanySetting.I18ns._Entity);
            }
            else
            {
                var model = entity.Map<CompanySettingModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 编辑扩展配置
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.System.Setup.CompanySetting_Maintain)]
        [LogApi(ApiType.Save, Operate = "编辑扩展配置")]
        public BizResult<CompanySettingModel> UpdateCompanySetting(CompanySettingModel model)
        {
            var entity = model.Map<CompanySetting>();

            var result = this.Repo.UpdateCompanySetting(entity);

            return result.Map<CompanySettingModel>();
        }

        #endregion CompanySetting
    }
}