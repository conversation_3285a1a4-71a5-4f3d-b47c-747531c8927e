﻿CREATE PROCEDURE [log].[sp_WriteLog]
	@ID							UNIQUEIDENTIFIER,
	@CompanyId					NVARCHAR(50),
	@CompanyCode				NVARCHAR(50),
	@CompanyName				NVARCHAR(200),
	@Category					NVARCHAR(50),
	@Level						NVARCHAR(50),
	@LogTime					DATETIME,
	@Logger						NVARCHAR(500),
	@Platform					NVARCHAR(200),
	@Program					NVARCHAR(200),
	@Operate					NVARCHAR(200),
	@Job						NVARCHAR(200),
	@Message					NVARCHAR(MAX),
	@Duration					BIGINT,
	@Remark						NVARCHAR(MAX),
	@Culture					NVARCHAR(10),
	@UserId						NVARCHAR(50),
	@UserUniqueName				NVARCHAR(50),
	@UserDisplayName			NVARCHAR(50),
	@EmployeeId					NVARCHAR(50),
	@EmployeeName		        NVARCHAR(50),
	@AgentId					NVARCHAR(50),
	@AgentName			        NVARCHAR(50),
	@Controller					NVARCHAR(500),
	@Action						NVARCHAR(500),
	@Method						NVARCHAR(50),
	@Headers					NVARCHAR(MAX),
	@Url						NVARCHAR(MAX),
	@IsAuthenticated			BIT,
	@QueryString				NVARCHAR(MAX),
	@UserAgent					NVARCHAR(500),
	@Identity					NVARCHAR(500),
	@Host						NVARCHAR(200),
	@IP							NVARCHAR(50),
	@TargetName					NVARCHAR(200),
	@TargetType					NVARCHAR(200),
	@TargetId					NVARCHAR(50),
	@ApiType					NVARCHAR(20),
	@Success					BIT,
	@Input						NVARCHAR(MAX),
	@OutHeaders					NVARCHAR(MAX),
	@Output						NVARCHAR(MAX),
	@InterfaceSite				NVARCHAR(200),
	@InterfaceName				NVARCHAR(200),
	@InterfaceAddress			NVARCHAR(500),
	@InterfaceMethod			NVARCHAR(50),
	@InterfaceHeader			NVARCHAR(2000),
	@InterfaceRequest			NVARCHAR(MAX),
	@InterfaceResponse			NVARCHAR(MAX)
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @PlatformId INT = NULL;
	DECLARE @ProgramId INT = NULL;
	DECLARE @OperateId INT = NULL;
	DECLARE @JobId INT = NULL;
	DECLARE @TargetTypeId INT = NULL;

	SET @Platform = RTRIM(LTRIM(ISNULL(@Platform,'')));
	SET @Program = RTRIM(LTRIM(ISNULL(@Program,'')));
	SET @Operate = RTRIM(LTRIM(ISNULL(@Operate,'')));
	SET @Job = RTRIM(LTRIM(ISNULL(@Job,'')));
	SET @TargetName = RTRIM(LTRIM(ISNULL(@TargetName,'')));
	SET @TargetType = RTRIM(LTRIM(ISNULL(@TargetType,'')));
	SET @Url =  RTRIM(LTRIM(ISNULL(@Url,'')));
	SET @ApiType =  RTRIM(LTRIM(ISNULL(@ApiType,'')));
	SET @Input =  RTRIM(LTRIM(ISNULL(@Input,'')));
	SET @Output =  RTRIM(LTRIM(ISNULL(@Output,'')));
	SET @InterfaceSite =  RTRIM(LTRIM(ISNULL(@InterfaceSite,'')));
	SET @InterfaceName =  RTRIM(LTRIM(ISNULL(@InterfaceName,'')));
	SET @InterfaceAddress =  RTRIM(LTRIM(ISNULL(@InterfaceAddress,'')));
	SET @InterfaceMethod =  RTRIM(LTRIM(ISNULL(@InterfaceMethod,'')));
	SET @InterfaceHeader =  RTRIM(LTRIM(ISNULL(@InterfaceHeader,'')));
	SET @InterfaceRequest =  RTRIM(LTRIM(ISNULL(@InterfaceRequest,'')));
	SET @InterfaceResponse =  RTRIM(LTRIM(ISNULL(@InterfaceResponse,'')));

	IF @Platform <> ''
    BEGIN

		SELECT TOP 1
			@PlatformId = p.ID
		FROM [log].[LogPlatform] AS p
		WHERE p.Platform = @Platform
	
		IF @PlatformId IS NULL 
		BEGIN
			INSERT INTO [log].[LogPlatform] (
				[Platform]
			) VALUES (
				@Platform
			);

			SELECT @PlatformId = @@IDENTITY
		END 
	END
	
	IF @Program <> ''
    BEGIN

		SELECT TOP 1
			@ProgramId = pr.ID
		FROM [log].[LogProgram] AS pr
		WHERE pr.Program = @Program

		IF @ProgramId IS NULL 
		BEGIN
			INSERT INTO [log].[LogProgram] (
				[Program]
			) VALUES (
				@Program
			);

			SELECT @ProgramId = @@IDENTITY
		END 
	END

	IF @Operate <> ''
    BEGIN

		SELECT TOP 1
			@OperateId = o.ID
		FROM [log].[LogOperate] AS o
		WHERE o.Operate = @Operate
			
		IF @OperateId IS NULL
		BEGIN
			INSERT INTO [log].[LogOperate] (
				[Operate]
			) VALUES (
				@Operate
			);

			SELECT @OperateId = @@IDENTITY
		END 
	END

	IF @Job <> ''
    BEGIN

		SELECT TOP 1
			@JobId = j.ID
		FROM [log].[LogJob] AS j
		WHERE j.Job = @Job
			
		IF @JobId IS NULL
		BEGIN
			INSERT INTO [log].[LogJob] (
				[Job]
			) VALUES (
				@Job
			);

			SELECT @JobId = @@IDENTITY
		END 
	END

	-- Log主表，必须插入	
	INSERT INTO [log].[Log] (
		[ID],
		[CompanyId],
		[CompanyCode],
		[CompanyName],
		[Category],
		[Level],
		[LogTime],
		[Logger],
		[PlatformId],
		[ProgramId],
		[OperateId],
		[JobId],
		[Message],
		[Duration],
		[Remark],
        [Culture],
		[UserId],
		[UserUniqueName],
		[UserDisplayName],
		[EmployeeId],
		[EmployeeName],
		[AgentId],
		[AgentName]
    ) VALUES (
		@ID,
		@CompanyId,
		@CompanyCode,
		@CompanyName,
		ISNULL(@Category,''),
		@Level,
		ISNULL(@LogTime,GETDATE()),
		@Logger,
		@PlatformId,
		@ProgramId,
		@OperateId,
		@JobId,
		@Message,
		@Duration,
		@Remark,
        @Culture,
		@UserId,
		@UserUniqueName,
		@UserDisplayName,
		@EmployeeId,
		@EmployeeName,
		@AgentId,
		@AgentName
    );

	--@Url不为空，插入LogWeb表
	IF @Url <> ''
	BEGIN

		INSERT INTO [log].[LogWeb] (
			[ID],
			[CompanyId],
			[Controller],
			[Action],
			[Method],
			[Headers],
			[Url],
			[IsAuthenticated],
			[QueryString],
			[UserAgent],
			[Identity],
			[Host],
			[IP]
		) VALUES (
			@ID, -- ID - uniqueidentifier
			@CompanyId,
			@Controller,  -- Controller - NVARCHAR(500)
			@Action,  -- Action - NVARCHAR(500)
			@Method,  -- Method - NVARCHAR(10)
			@Headers,  -- Headers - TEXT
			@Url,  -- Url - TEXT
			ISNULL(@IsAuthenticated,0), -- IsAuthenticated - bit
			@QueryString,  -- QueryString - TEXT
			@UserAgent,  -- UserAgent - NVARCHAR(500)
			@Identity,  -- Identity - NVARCHAR(500)
			@Host,  -- Host - NVARCHAR(200)
			@IP   -- IP - VARCHAR(50)
		);

	END

	--@TargetType或者@Target不为空，插入LogBiz表
	IF @TargetType <> '' OR @TargetName <> ''
    BEGIN

		IF @TargetType <> ''
		BEGIN

			SELECT TOP 1
				@TargetTypeId = tt.ID
			FROM [log].[LogTargetType] AS tt
			WHERE tt.TargetType = @TargetType
			
			IF @TargetTypeId IS NULL
			BEGIN
				INSERT INTO [log].[LogTargetType] (
					[TargetType]
				) VALUES (
					@TargetType
				);

				SELECT @TargetTypeId = @@IDENTITY
			END 
		END

		INSERT INTO [log].[LogTarget] (
			[ID] ,
			[CompanyId],
			[LogTargetTypeId],
			[TargetName],
			[TargetId]
		) VALUES (
			@ID, -- ID - uniqueidentifier
			@CompanyId,
			@TargetTypeId,    -- LogBizTargetTypeId - int
			@TargetName,  -- TargetName - nvarchar(50)
			@TargetId   -- TargetId - nvarchar(50)
		)

	END
 
 
 	-- @Input或者@Output不为空，插入LogApi表
	IF @Category = 'API' OR @ApiType <> '' OR @Success IS NOT NULL OR @Input <> '' OR @Output <> ''
    BEGIN

		INSERT INTO [log].[LogApi] (
			[ID] ,
			[CompanyId],
			[ApiType],
			[Success],
			[Input],
			[OutHeaders],
			[Output]
		) VALUES (
			@ID, -- ID - uniqueidentifier
			@CompanyId,
			@ApiType, -- ApiType - nvarchar(20)
			@Success,	-- Success - bit
			@Input,  -- In - nvarchar(max)
			@OutHeaders,	 -- OutHeaders - nvarchar(max)
			@Output  -- Out - nvarchar(max)
		);

	END


	-- Interface日志 或者 @InterfaceAddress不为空，插入LogInterface表
	IF @Category = 'Interface' OR @InterfaceSite <> '' OR @InterfaceName <> '' OR @InterfaceAddress <> '' OR @InterfaceMethod <> '' OR @InterfaceHeader <> '' OR @InterfaceRequest <> '' OR @InterfaceResponse <> ''
    BEGIN

		INSERT INTO [log].[LogInterface] (
			[ID],
			[CompanyId],
			[Site],
			[Name],
			[Address],
			[Method],
			[Header],
			[Request],
			[Response]
		) VALUES (
			@ID, -- ID - uniqueidentifier
			@CompanyId,
			@InterfaceSite,
			@InterfaceName,
			@InterfaceAddress,
			@InterfaceMethod,
			@InterfaceHeader,
			@InterfaceRequest,  -- Request -  nvarchar(max)
			@InterfaceResponse  -- Reponse -  nvarchar(max)
		);

	END

END

GO

GRANT EXEC ON [log].[sp_WriteLog] TO PUBLIC
GO
