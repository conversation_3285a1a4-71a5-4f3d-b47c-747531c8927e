﻿CREATE TABLE [dbo].[CompanyAuth] (
    [ID]                UNIQUEIDENTIFIER        NOT NULL  CONSTRAINT [DF_CompanyAuth_ID]  DEFAULT NEWSEQUENTIALID(),
    [CompanyId]         UNIQUEIDENTIFIER        NOT NULL,
    [AuthId]            UNIQUEIDENTIFIER        NOT NULL,
    [Valid]             BIT                     NOT NULL,
    CONSTRAINT [PK_CompanyAuth] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_CompanyAuth_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_CompanyAuth_Auth_00_Auth] FOREIGN KEY ([AuthId]) REFERENCES [dbo].[Auth] ([ID])
);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_CompanyAuth] ON[dbo].[CompanyAuth]
(
    [CompanyId] ASC,
    [AuthId] ASC
)


