﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码是从模板生成的.
//
//     手动更改此文件可能会导致应用程序出现意外行为。
//     如果重新生成代码，将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.ComponentModel;
using System.Text.Json.Serialization;
using Shinsoft.Core;
using Shinsoft.Core.AutoMapper;
using Shinsoft.Core.DynamicQuery;
using Shinsoft.Core.NLog;
using Shinsoft.DDI.Entities;

#pragma warning disable CS8669

namespace Shinsoft.DDI.Api.Models
{

	#region Log

    /// <summary>
    /// 日志
    /// </summary>
	[Description("日志")]
    [MapFromType(typeof(Log), Reverse = true)]
	public abstract class LogRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(Log);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? CompanyId { get; set; }

        public virtual string? CompanyCode { get; set; }

        public virtual string? CompanyName { get; set; }

		/// <summary>
        /// 类别
        /// </summary>
		[Description("类别")]
        public virtual string? Category { get; set; }

		/// <summary>
        /// 级别 
        /// </summary>
		[Description("级别 ")]
        public virtual string? Level { get; set; }

		/// <summary>
        /// 时间
        /// </summary>
		[Description("时间")]
        public virtual DateTime LogTime { get; set; }

        public virtual string? Logger { get; set; }

		/// <summary>
        /// 平台ID
        /// </summary>
		[Description("平台ID")]
        public virtual int? PlatformId { get; set; }

		/// <summary>
        /// 程序ID
        /// </summary>
		[Description("程序ID")]
        public virtual int? ProgramId { get; set; }

		/// <summary>
        /// 操作ID
        /// </summary>
		[Description("操作ID")]
        public virtual int? OperateId { get; set; }

		/// <summary>
        /// 任务ID
        /// </summary>
		[Description("任务ID")]
        public virtual int? JobId { get; set; }

		/// <summary>
        /// 消息
        /// </summary>
		[Description("消息")]
        public virtual string? Message { get; set; }

		/// <summary>
        /// 执行时间
        /// </summary>
		[Description("执行时间")]
        public virtual long? Duration { get; set; }

		/// <summary>
        /// 备注
        /// </summary>
		[Description("备注")]
        public virtual string? Remark { get; set; }

        public virtual string? Culture { get; set; }

		/// <summary>
        /// 用户ID
        /// </summary>
		[Description("用户ID")]
        public virtual string? UserId { get; set; }

		/// <summary>
        /// 用户唯一名
        /// </summary>
		[Description("用户唯一名")]
        public virtual string? UserUniqueName { get; set; }

		/// <summary>
        /// 用户名称
        /// </summary>
		[Description("用户名称")]
        public virtual string? UserDisplayName { get; set; }

		/// <summary>
        /// 当前身份ID
        /// </summary>
		[Description("当前身份ID")]
        public virtual string? EmployeeId { get; set; }

		/// <summary>
        /// 当前身份名称
        /// </summary>
		[Description("当前身份名称")]
        public virtual string? EmployeeName { get; set; }

		/// <summary>
        /// 代理人ID
        /// </summary>
		[Description("代理人ID")]
        public virtual string? AgentId { get; set; }

		/// <summary>
        /// 代理人名称
        /// </summary>
		[Description("代理人名称")]
        public virtual string? AgentName { get; set; }

    }

    /// <summary>
    /// 日志
    /// </summary>
	[Description("日志")]
	public abstract partial class LogMeta : LogRaw
	{
	}

    /// <summary>
    /// 日志
    /// </summary>
	[Description("日志")]
	public partial class LogModel : LogMeta
	{
	}

    /// <summary>
    /// 日志
    /// </summary>
	[Description("日志")]
	public partial class LogQuery : LogMeta
	{
	}

    /// <summary>
    /// 日志
    /// </summary>
	[Description("日志")]
	public partial class LogSelector : LogMeta
	{
	}

    /// <summary>
    /// 日志查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Log))]
	public partial class LogFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 日志选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(Log))]
	public partial class LogSelectorFilter : PagingFilterModel
	{
	}

	#endregion Log


	#region LogApi

    /// <summary>
    /// API日志
    /// </summary>
	[Description("API日志")]
    [MapFromType(typeof(LogApi), Reverse = true)]
	public abstract class LogApiRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(LogApi);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? CompanyId { get; set; }

		/// <summary>
        /// Api类型
        /// </summary>
		[Description("Api类型")]
        public virtual string? ApiType { get; set; }

        public virtual bool? Success { get; set; }

		/// <summary>
        /// 输入
        /// </summary>
		[Description("输入")]
        public virtual string Input { get; set; } = string.Empty;

		/// <summary>
        /// 输出
        /// </summary>
		[Description("输出")]
        public virtual string Output { get; set; } = string.Empty;

        public virtual string OutHeaders { get; set; } = string.Empty;

    }

    /// <summary>
    /// API日志
    /// </summary>
	[Description("API日志")]
	public abstract partial class LogApiMeta : LogApiRaw
	{
	}

    /// <summary>
    /// API日志
    /// </summary>
	[Description("API日志")]
	public partial class LogApiModel : LogApiMeta
	{
	}

    /// <summary>
    /// API日志
    /// </summary>
	[Description("API日志")]
	public partial class LogApiQuery : LogApiMeta
	{
	}

    /// <summary>
    /// API日志
    /// </summary>
	[Description("API日志")]
	public partial class LogApiSelector : LogApiMeta
	{
	}

    /// <summary>
    /// API日志查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(LogApi))]
	public partial class LogApiFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// API日志选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(LogApi))]
	public partial class LogApiSelectorFilter : PagingFilterModel
	{
	}

	#endregion LogApi


	#region LogCategory

    [MapFromType(typeof(LogCategory), Reverse = true)]
	public abstract class LogCategoryRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(LogCategory);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual int ID { get; set; }

        public virtual string Category { get; set; } = string.Empty;

    }

	public abstract partial class LogCategoryMeta : LogCategoryRaw
	{
	}

	public partial class LogCategoryModel : LogCategoryMeta
	{
	}

	public partial class LogCategoryQuery : LogCategoryMeta
	{
	}

	public partial class LogCategorySelector : LogCategoryMeta
	{
	}

	[DynamicQueryEntity(typeof(LogCategory))]
	public partial class LogCategoryFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(LogCategory))]
	public partial class LogCategorySelectorFilter : PagingFilterModel
	{
	}

	#endregion LogCategory


	#region LogException

    /// <summary>
    /// 异常日志
    /// </summary>
	[Description("异常日志")]
    [MapFromType(typeof(LogException), Reverse = true)]
	public abstract class LogExceptionRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(LogException);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? CompanyId { get; set; }

		/// <summary>
        /// 异常
        /// </summary>
		[Description("异常")]
        public virtual string? Exception { get; set; }

		/// <summary>
        /// 堆栈跟踪
        /// </summary>
		[Description("堆栈跟踪")]
        public virtual string StackTrace { get; set; } = string.Empty;

    }

    /// <summary>
    /// 异常日志
    /// </summary>
	[Description("异常日志")]
	public abstract partial class LogExceptionMeta : LogExceptionRaw
	{
	}

    /// <summary>
    /// 异常日志
    /// </summary>
	[Description("异常日志")]
	public partial class LogExceptionModel : LogExceptionMeta
	{
	}

    /// <summary>
    /// 异常日志
    /// </summary>
	[Description("异常日志")]
	public partial class LogExceptionQuery : LogExceptionMeta
	{
	}

    /// <summary>
    /// 异常日志
    /// </summary>
	[Description("异常日志")]
	public partial class LogExceptionSelector : LogExceptionMeta
	{
	}

    /// <summary>
    /// 异常日志查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(LogException))]
	public partial class LogExceptionFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 异常日志选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(LogException))]
	public partial class LogExceptionSelectorFilter : PagingFilterModel
	{
	}

	#endregion LogException


	#region LogInterface

    /// <summary>
    /// 接口日志
    /// </summary>
	[Description("接口日志")]
    [MapFromType(typeof(LogInterface), Reverse = true)]
	public abstract class LogInterfaceRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => this.Name.GetValueOrDefault();

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(LogInterface);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? CompanyId { get; set; }

        public virtual string? Site { get; set; }

        public virtual string? Name { get; set; }

		/// <summary>
        /// 接口地址
        /// </summary>
		[Description("接口地址")]
        public virtual string? Address { get; set; }

		/// <summary>
        /// 接口方法
        /// </summary>
		[Description("接口方法")]
        public virtual string? Method { get; set; }

        public virtual string? Header { get; set; }

		/// <summary>
        /// 接口输入
        /// </summary>
		[Description("接口输入")]
        public virtual string? Request { get; set; }

		/// <summary>
        /// 接口输出
        /// </summary>
		[Description("接口输出")]
        public virtual string? Response { get; set; }

    }

    /// <summary>
    /// 接口日志
    /// </summary>
	[Description("接口日志")]
	public abstract partial class LogInterfaceMeta : LogInterfaceRaw
	{
	}

    /// <summary>
    /// 接口日志
    /// </summary>
	[Description("接口日志")]
	public partial class LogInterfaceModel : LogInterfaceMeta
	{
	}

    /// <summary>
    /// 接口日志
    /// </summary>
	[Description("接口日志")]
	public partial class LogInterfaceQuery : LogInterfaceMeta
	{
	}

    /// <summary>
    /// 接口日志
    /// </summary>
	[Description("接口日志")]
	public partial class LogInterfaceSelector : LogInterfaceMeta
	{
	}

    /// <summary>
    /// 接口日志查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(LogInterface))]
	public partial class LogInterfaceFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 接口日志选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(LogInterface))]
	public partial class LogInterfaceSelectorFilter : PagingFilterModel
	{
	}

	#endregion LogInterface


	#region LogJob

    [MapFromType(typeof(LogJob), Reverse = true)]
	public abstract class LogJobRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(LogJob);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual int ID { get; set; }

        public virtual string? Job { get; set; }

    }

	public abstract partial class LogJobMeta : LogJobRaw
	{
	}

	public partial class LogJobModel : LogJobMeta
	{
	}

	public partial class LogJobQuery : LogJobMeta
	{
	}

	public partial class LogJobSelector : LogJobMeta
	{
	}

	[DynamicQueryEntity(typeof(LogJob))]
	public partial class LogJobFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(LogJob))]
	public partial class LogJobSelectorFilter : PagingFilterModel
	{
	}

	#endregion LogJob


	#region LogOperate

    [MapFromType(typeof(LogOperate), Reverse = true)]
	public abstract class LogOperateRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(LogOperate);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual int ID { get; set; }

        public virtual string? Operate { get; set; }

    }

	public abstract partial class LogOperateMeta : LogOperateRaw
	{
	}

	public partial class LogOperateModel : LogOperateMeta
	{
	}

	public partial class LogOperateQuery : LogOperateMeta
	{
	}

	public partial class LogOperateSelector : LogOperateMeta
	{
	}

	[DynamicQueryEntity(typeof(LogOperate))]
	public partial class LogOperateFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(LogOperate))]
	public partial class LogOperateSelectorFilter : PagingFilterModel
	{
	}

	#endregion LogOperate


	#region LogPlatform

    [MapFromType(typeof(LogPlatform), Reverse = true)]
	public abstract class LogPlatformRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(LogPlatform);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual int ID { get; set; }

        public virtual string? Platform { get; set; }

    }

	public abstract partial class LogPlatformMeta : LogPlatformRaw
	{
	}

	public partial class LogPlatformModel : LogPlatformMeta
	{
	}

	public partial class LogPlatformQuery : LogPlatformMeta
	{
	}

	public partial class LogPlatformSelector : LogPlatformMeta
	{
	}

	[DynamicQueryEntity(typeof(LogPlatform))]
	public partial class LogPlatformFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(LogPlatform))]
	public partial class LogPlatformSelectorFilter : PagingFilterModel
	{
	}

	#endregion LogPlatform


	#region LogProgram

    [MapFromType(typeof(LogProgram), Reverse = true)]
	public abstract class LogProgramRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(LogProgram);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual int ID { get; set; }

        public virtual string? Program { get; set; }

    }

	public abstract partial class LogProgramMeta : LogProgramRaw
	{
	}

	public partial class LogProgramModel : LogProgramMeta
	{
	}

	public partial class LogProgramQuery : LogProgramMeta
	{
	}

	public partial class LogProgramSelector : LogProgramMeta
	{
	}

	[DynamicQueryEntity(typeof(LogProgram))]
	public partial class LogProgramFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(LogProgram))]
	public partial class LogProgramSelectorFilter : PagingFilterModel
	{
	}

	#endregion LogProgram


	#region LogTarget

    /// <summary>
    /// 业务日志
    /// </summary>
	[Description("业务日志")]
    [MapFromType(typeof(LogTarget), Reverse = true)]
	public abstract class LogTargetRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(LogTarget);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? CompanyId { get; set; }

		/// <summary>
        /// 业务类型ID
        /// </summary>
		[Description("业务类型ID")]
        public virtual int? LogTargetTypeId { get; set; }

		/// <summary>
        /// 目标
        /// </summary>
		[Description("目标")]
        public virtual string? TargetName { get; set; }

		/// <summary>
        /// 目标ID
        /// </summary>
		[Description("目标ID")]
        public virtual string? TargetId { get; set; }

    }

    /// <summary>
    /// 业务日志
    /// </summary>
	[Description("业务日志")]
	public abstract partial class LogTargetMeta : LogTargetRaw
	{
	}

    /// <summary>
    /// 业务日志
    /// </summary>
	[Description("业务日志")]
	public partial class LogTargetModel : LogTargetMeta
	{
	}

    /// <summary>
    /// 业务日志
    /// </summary>
	[Description("业务日志")]
	public partial class LogTargetQuery : LogTargetMeta
	{
	}

    /// <summary>
    /// 业务日志
    /// </summary>
	[Description("业务日志")]
	public partial class LogTargetSelector : LogTargetMeta
	{
	}

    /// <summary>
    /// 业务日志查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(LogTarget))]
	public partial class LogTargetFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 业务日志选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(LogTarget))]
	public partial class LogTargetSelectorFilter : PagingFilterModel
	{
	}

	#endregion LogTarget


	#region LogTargetType

    /// <summary>
    /// 目标类型
    /// </summary>
	[Description("目标类型")]
    [MapFromType(typeof(LogTargetType), Reverse = true)]
	public abstract class LogTargetTypeRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(LogTargetType);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual int ID { get; set; }

		/// <summary>
        /// 目标类型
        /// </summary>
		[Description("目标类型")]
        public virtual string? TargetType { get; set; }

    }

    /// <summary>
    /// 目标类型
    /// </summary>
	[Description("目标类型")]
	public abstract partial class LogTargetTypeMeta : LogTargetTypeRaw
	{
	}

    /// <summary>
    /// 目标类型
    /// </summary>
	[Description("目标类型")]
	public partial class LogTargetTypeModel : LogTargetTypeMeta
	{
	}

    /// <summary>
    /// 目标类型
    /// </summary>
	[Description("目标类型")]
	public partial class LogTargetTypeQuery : LogTargetTypeMeta
	{
	}

    /// <summary>
    /// 目标类型
    /// </summary>
	[Description("目标类型")]
	public partial class LogTargetTypeSelector : LogTargetTypeMeta
	{
	}

    /// <summary>
    /// 目标类型查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(LogTargetType))]
	public partial class LogTargetTypeFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 目标类型选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(LogTargetType))]
	public partial class LogTargetTypeSelectorFilter : PagingFilterModel
	{
	}

	#endregion LogTargetType


	#region LogWeb

    /// <summary>
    /// 网页日志
    /// </summary>
	[Description("网页日志")]
    [MapFromType(typeof(LogWeb), Reverse = true)]
	public abstract class LogWebRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(LogWeb);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? CompanyId { get; set; }

		/// <summary>
        /// Url地址
        /// </summary>
		[Description("Url地址")]
        public virtual string Url { get; set; } = string.Empty;

		/// <summary>
        /// 控制类
        /// </summary>
		[Description("控制类")]
        public virtual string? Controller { get; set; }

		/// <summary>
        /// 活动方式
        /// </summary>
		[Description("活动方式")]
        public virtual string? Action { get; set; }

		/// <summary>
        /// 方法
        /// </summary>
		[Description("方法")]
        public virtual string? Method { get; set; }

		/// <summary>
        /// 头文件
        /// </summary>
		[Description("头文件")]
        public virtual string? Headers { get; set; }

		/// <summary>
        /// 是否认证
        /// </summary>
		[Description("是否认证")]
        public virtual bool IsAuthenticated { get; set; }

		/// <summary>
        /// 查询条件
        /// </summary>
		[Description("查询条件")]
        public virtual string? QueryString { get; set; }

		/// <summary>
        /// 用户代理
        /// </summary>
		[Description("用户代理")]
        public virtual string? UserAgent { get; set; }

		/// <summary>
        /// 认证
        /// </summary>
		[Description("认证")]
        public virtual string? Identity { get; set; }

		/// <summary>
        /// 主机名称
        /// </summary>
		[Description("主机名称")]
        public virtual string? Host { get; set; }

		/// <summary>
        /// 主机IP
        /// </summary>
		[Description("主机IP")]
        public virtual string? IP { get; set; }

    }

    /// <summary>
    /// 网页日志
    /// </summary>
	[Description("网页日志")]
	public abstract partial class LogWebMeta : LogWebRaw
	{
	}

    /// <summary>
    /// 网页日志
    /// </summary>
	[Description("网页日志")]
	public partial class LogWebModel : LogWebMeta
	{
	}

    /// <summary>
    /// 网页日志
    /// </summary>
	[Description("网页日志")]
	public partial class LogWebQuery : LogWebMeta
	{
	}

    /// <summary>
    /// 网页日志
    /// </summary>
	[Description("网页日志")]
	public partial class LogWebSelector : LogWebMeta
	{
	}

    /// <summary>
    /// 网页日志查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(LogWeb))]
	public partial class LogWebFilter : PagingFilterModel
	{
	}
    /// <summary>
    /// 网页日志选择器查询条件
    /// </summary>
	[DynamicQueryEntity(typeof(LogWeb))]
	public partial class LogWebSelectorFilter : PagingFilterModel
	{
	}

	#endregion LogWeb


	#region VwLog

    [MapFromType(typeof(VwLog), Reverse = true)]
	public abstract class VwLogRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VwLog);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? CompanyId { get; set; }

        public virtual string? CompanyCode { get; set; }

        public virtual string? CompanyName { get; set; }

        public virtual string? Category { get; set; }

        public virtual string? Level { get; set; }

        public virtual DateTime LogTime { get; set; }

        public virtual string? Logger { get; set; }

        public virtual string? Platform { get; set; }

        public virtual string? Program { get; set; }

        public virtual string? Operate { get; set; }

        public virtual string? Job { get; set; }

        public virtual string? Message { get; set; }

        public virtual long? Duration { get; set; }

        public virtual string? Remark { get; set; }

        public virtual string? Culture { get; set; }

        public virtual string? UserId { get; set; }

        public virtual string? UserUniqueName { get; set; }

        public virtual string? UserDisplayName { get; set; }

        public virtual string? EmployeeId { get; set; }

        public virtual string? EmployeeName { get; set; }

        public virtual string? AgentId { get; set; }

        public virtual string? AgentName { get; set; }

        public virtual string? TargetType { get; set; }

        public virtual string? TargetName { get; set; }

        public virtual string? TargetId { get; set; }

        public virtual string? Controller { get; set; }

        public virtual string? Action { get; set; }

        public virtual string? Method { get; set; }

        public virtual string? Headers { get; set; }

        public virtual string? Url { get; set; }

        public virtual bool? IsAuthenticated { get; set; }

        public virtual string? QueryString { get; set; }

        public virtual string? UserAgent { get; set; }

        public virtual string? Identity { get; set; }

        public virtual string? Host { get; set; }

        public virtual string? IP { get; set; }

        public virtual string? ApiType { get; set; }

        public virtual bool? Success { get; set; }

        public virtual string? Input { get; set; }

        public virtual string? OutHeaders { get; set; }

        public virtual string? Output { get; set; }

        public virtual string? InterfaceSite { get; set; }

        public virtual string? InterfaceName { get; set; }

        public virtual string? InterfaceAddress { get; set; }

        public virtual string? InterfaceMethod { get; set; }

        public virtual string? InterfaceHeader { get; set; }

        public virtual string? InterfaceRequest { get; set; }

        public virtual string? InterfaceResponse { get; set; }

        public virtual string? Exception { get; set; }

        public virtual string? StackTrace { get; set; }

    }

	public abstract partial class VwLogMeta : VwLogRaw
	{
	}

	public partial class VwLogModel : VwLogMeta
	{
	}

	public partial class VwLogQuery : VwLogMeta
	{
	}

	public partial class VwLogSelector : VwLogMeta
	{
	}

	[DynamicQueryEntity(typeof(VwLog))]
	public partial class VwLogFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(VwLog))]
	public partial class VwLogSelectorFilter : PagingFilterModel
	{
	}

	#endregion VwLog


	#region VwLogApi

    [MapFromType(typeof(VwLogApi), Reverse = true)]
	public abstract class VwLogApiRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VwLogApi);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? CompanyId { get; set; }

        public virtual string? CompanyCode { get; set; }

        public virtual string? CompanyName { get; set; }

        public virtual string? Category { get; set; }

        public virtual string? Level { get; set; }

        public virtual DateTime LogTime { get; set; }

        public virtual string? Logger { get; set; }

        public virtual string? Platform { get; set; }

        public virtual string? Program { get; set; }

        public virtual string? Operate { get; set; }

        public virtual string? Message { get; set; }

        public virtual long? Duration { get; set; }

        public virtual string? Remark { get; set; }

        public virtual string? Culture { get; set; }

        public virtual string? UserId { get; set; }

        public virtual string? UserUniqueName { get; set; }

        public virtual string? UserDisplayName { get; set; }

        public virtual string? EmployeeId { get; set; }

        public virtual string? EmployeeName { get; set; }

        public virtual string? AgentId { get; set; }

        public virtual string? AgentName { get; set; }

        public virtual string? TargetType { get; set; }

        public virtual string? TargetName { get; set; }

        public virtual string? TargetId { get; set; }

        public virtual string? Controller { get; set; }

        public virtual string? Action { get; set; }

        public virtual string? Method { get; set; }

        public virtual string? Headers { get; set; }

        public virtual string? Url { get; set; }

        public virtual bool? IsAuthenticated { get; set; }

        public virtual string? QueryString { get; set; }

        public virtual string? UserAgent { get; set; }

        public virtual string? Identity { get; set; }

        public virtual string? Host { get; set; }

        public virtual string? IP { get; set; }

        public virtual string? ApiType { get; set; }

        public virtual bool? Success { get; set; }

        public virtual string? Input { get; set; }

        public virtual string? OutHeaders { get; set; }

        public virtual string? Output { get; set; }

        public virtual string? Exception { get; set; }

        public virtual string? StackTrace { get; set; }

    }

	public abstract partial class VwLogApiMeta : VwLogApiRaw
	{
	}

	public partial class VwLogApiModel : VwLogApiMeta
	{
	}

	public partial class VwLogApiQuery : VwLogApiMeta
	{
	}

	public partial class VwLogApiSelector : VwLogApiMeta
	{
	}

	[DynamicQueryEntity(typeof(VwLogApi))]
	public partial class VwLogApiFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(VwLogApi))]
	public partial class VwLogApiSelectorFilter : PagingFilterModel
	{
	}

	#endregion VwLogApi


	#region VwLogBiz

    [MapFromType(typeof(VwLogBiz), Reverse = true)]
	public abstract class VwLogBizRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VwLogBiz);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? CompanyId { get; set; }

        public virtual string? CompanyCode { get; set; }

        public virtual string? CompanyName { get; set; }

        public virtual string? Category { get; set; }

        public virtual string? Level { get; set; }

        public virtual DateTime LogTime { get; set; }

        public virtual string? Logger { get; set; }

        public virtual string? Platform { get; set; }

        public virtual string? Program { get; set; }

        public virtual string? Operate { get; set; }

        public virtual string? Message { get; set; }

        public virtual long? Duration { get; set; }

        public virtual string? Remark { get; set; }

        public virtual string? Culture { get; set; }

        public virtual string? UserId { get; set; }

        public virtual string? UserUniqueName { get; set; }

        public virtual string? UserDisplayName { get; set; }

        public virtual string? EmployeeId { get; set; }

        public virtual string? EmployeeName { get; set; }

        public virtual string? AgentId { get; set; }

        public virtual string? AgentName { get; set; }

        public virtual string? TargetType { get; set; }

        public virtual string? TargetName { get; set; }

        public virtual string? TargetId { get; set; }

        public virtual string? Controller { get; set; }

        public virtual string? Action { get; set; }

        public virtual string? Method { get; set; }

        public virtual string? Headers { get; set; }

        public virtual string? Url { get; set; }

        public virtual bool? IsAuthenticated { get; set; }

        public virtual string? QueryString { get; set; }

        public virtual string? UserAgent { get; set; }

        public virtual string? Identity { get; set; }

        public virtual string? Host { get; set; }

        public virtual string? IP { get; set; }

        public virtual string? Exception { get; set; }

        public virtual string? StackTrace { get; set; }

    }

	public abstract partial class VwLogBizMeta : VwLogBizRaw
	{
	}

	public partial class VwLogBizModel : VwLogBizMeta
	{
	}

	public partial class VwLogBizQuery : VwLogBizMeta
	{
	}

	public partial class VwLogBizSelector : VwLogBizMeta
	{
	}

	[DynamicQueryEntity(typeof(VwLogBiz))]
	public partial class VwLogBizFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(VwLogBiz))]
	public partial class VwLogBizSelectorFilter : PagingFilterModel
	{
	}

	#endregion VwLogBiz


	#region VwLogException

    [MapFromType(typeof(VwLogException), Reverse = true)]
	public abstract class VwLogExceptionRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VwLogException);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? CompanyId { get; set; }

        public virtual string? CompanyCode { get; set; }

        public virtual string? CompanyName { get; set; }

        public virtual string? Category { get; set; }

        public virtual string? Level { get; set; }

        public virtual DateTime LogTime { get; set; }

        public virtual string? Logger { get; set; }

        public virtual string? Platform { get; set; }

        public virtual string? Program { get; set; }

        public virtual string? Operate { get; set; }

        public virtual string? Message { get; set; }

        public virtual long? Duration { get; set; }

        public virtual string? Remark { get; set; }

        public virtual string? Culture { get; set; }

        public virtual string? UserId { get; set; }

        public virtual string? UserUniqueName { get; set; }

        public virtual string? UserDisplayName { get; set; }

        public virtual string? EmployeeId { get; set; }

        public virtual string? EmployeeName { get; set; }

        public virtual string? AgentId { get; set; }

        public virtual string? AgentName { get; set; }

        public virtual string? TargetType { get; set; }

        public virtual string? TargetName { get; set; }

        public virtual string? TargetId { get; set; }

        public virtual string? Controller { get; set; }

        public virtual string? Action { get; set; }

        public virtual string? Method { get; set; }

        public virtual string? Headers { get; set; }

        public virtual string? Url { get; set; }

        public virtual bool? IsAuthenticated { get; set; }

        public virtual string? QueryString { get; set; }

        public virtual string? UserAgent { get; set; }

        public virtual string? Identity { get; set; }

        public virtual string? Host { get; set; }

        public virtual string? IP { get; set; }

        public virtual string? Exception { get; set; }

        public virtual string? StackTrace { get; set; }

    }

	public abstract partial class VwLogExceptionMeta : VwLogExceptionRaw
	{
	}

	public partial class VwLogExceptionModel : VwLogExceptionMeta
	{
	}

	public partial class VwLogExceptionQuery : VwLogExceptionMeta
	{
	}

	public partial class VwLogExceptionSelector : VwLogExceptionMeta
	{
	}

	[DynamicQueryEntity(typeof(VwLogException))]
	public partial class VwLogExceptionFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(VwLogException))]
	public partial class VwLogExceptionSelectorFilter : PagingFilterModel
	{
	}

	#endregion VwLogException


	#region VwLogInterface

    [MapFromType(typeof(VwLogInterface), Reverse = true)]
	public abstract class VwLogInterfaceRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VwLogInterface);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? CompanyId { get; set; }

        public virtual string? CompanyCode { get; set; }

        public virtual string? CompanyName { get; set; }

        public virtual string? Category { get; set; }

        public virtual string? Level { get; set; }

        public virtual DateTime LogTime { get; set; }

        public virtual string? Logger { get; set; }

        public virtual string? Platform { get; set; }

        public virtual string? Program { get; set; }

        public virtual string? Operate { get; set; }

        public virtual string? Message { get; set; }

        public virtual long? Duration { get; set; }

        public virtual string? Remark { get; set; }

        public virtual string? Culture { get; set; }

        public virtual string? UserId { get; set; }

        public virtual string? UserUniqueName { get; set; }

        public virtual string? UserDisplayName { get; set; }

        public virtual string? EmployeeId { get; set; }

        public virtual string? EmployeeName { get; set; }

        public virtual string? AgentId { get; set; }

        public virtual string? AgentName { get; set; }

        public virtual string? TargetType { get; set; }

        public virtual string? TargetName { get; set; }

        public virtual string? TargetId { get; set; }

        public virtual string? Controller { get; set; }

        public virtual string? Action { get; set; }

        public virtual string? Method { get; set; }

        public virtual string? Headers { get; set; }

        public virtual string? Url { get; set; }

        public virtual bool? IsAuthenticated { get; set; }

        public virtual string? QueryString { get; set; }

        public virtual string? UserAgent { get; set; }

        public virtual string? Identity { get; set; }

        public virtual string? Host { get; set; }

        public virtual string? IP { get; set; }

        public virtual string? InterfaceSite { get; set; }

        public virtual string? InterfaceName { get; set; }

        public virtual string? InterfaceAddress { get; set; }

        public virtual string? InterfaceMethod { get; set; }

        public virtual string? InterfaceHeader { get; set; }

        public virtual string? InterfaceRequest { get; set; }

        public virtual string? InterfaceResponse { get; set; }

        public virtual string? Exception { get; set; }

        public virtual string? StackTrace { get; set; }

    }

	public abstract partial class VwLogInterfaceMeta : VwLogInterfaceRaw
	{
	}

	public partial class VwLogInterfaceModel : VwLogInterfaceMeta
	{
	}

	public partial class VwLogInterfaceQuery : VwLogInterfaceMeta
	{
	}

	public partial class VwLogInterfaceSelector : VwLogInterfaceMeta
	{
	}

	[DynamicQueryEntity(typeof(VwLogInterface))]
	public partial class VwLogInterfaceFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(VwLogInterface))]
	public partial class VwLogInterfaceSelectorFilter : PagingFilterModel
	{
	}

	#endregion VwLogInterface


	#region VwLogJob

    [MapFromType(typeof(VwLogJob), Reverse = true)]
	public abstract class VwLogJobRaw : BaseModel, ILogTarget
    {
        #region ILogTarget

        [JsonIgnore]
        string ILogTarget.TargetName => string.Empty;

        [JsonIgnore]
        string ILogTarget.TargetType => nameof(VwLogJob);

        [JsonIgnore]
        object? ILogTarget.TargetId => this.ID;

        #endregion ILogTarget

        public virtual Guid ID { get; set; }

        public virtual string? CompanyId { get; set; }

        public virtual string? CompanyCode { get; set; }

        public virtual string? CompanyName { get; set; }

        public virtual string? Category { get; set; }

        public virtual string? Level { get; set; }

        public virtual DateTime LogTime { get; set; }

        public virtual string? Logger { get; set; }

        public virtual string? Platform { get; set; }

        public virtual string? Program { get; set; }

        public virtual string? Operate { get; set; }

        public virtual string? Job { get; set; }

        public virtual string? Message { get; set; }

        public virtual long? Duration { get; set; }

        public virtual string? Remark { get; set; }

        public virtual string? InterfaceAddress { get; set; }

        public virtual string? InterfaceMethod { get; set; }

        public virtual string? InterfaceRequest { get; set; }

        public virtual string? InterfaceResponse { get; set; }

        public virtual string? Exception { get; set; }

        public virtual string? StackTrace { get; set; }

        public virtual string? Culture { get; set; }

        public virtual string? UserId { get; set; }

        public virtual string? UserUniqueName { get; set; }

        public virtual string? UserDisplayName { get; set; }

        public virtual string? EmployeeId { get; set; }

        public virtual string? EmployeeName { get; set; }

        public virtual string? AgentId { get; set; }

        public virtual string? AgentName { get; set; }

        public virtual string? TargetType { get; set; }

        public virtual string? TargetName { get; set; }

        public virtual string? TargetId { get; set; }

        public virtual string? Controller { get; set; }

        public virtual string? Action { get; set; }

        public virtual string? Method { get; set; }

        public virtual string? Headers { get; set; }

        public virtual string? Url { get; set; }

        public virtual bool? IsAuthenticated { get; set; }

        public virtual string? QueryString { get; set; }

        public virtual string? UserAgent { get; set; }

        public virtual string? Identity { get; set; }

        public virtual string? Host { get; set; }

        public virtual string? IP { get; set; }

    }

	public abstract partial class VwLogJobMeta : VwLogJobRaw
	{
	}

	public partial class VwLogJobModel : VwLogJobMeta
	{
	}

	public partial class VwLogJobQuery : VwLogJobMeta
	{
	}

	public partial class VwLogJobSelector : VwLogJobMeta
	{
	}

	[DynamicQueryEntity(typeof(VwLogJob))]
	public partial class VwLogJobFilter : PagingFilterModel
	{
	}
	[DynamicQueryEntity(typeof(VwLogJob))]
	public partial class VwLogJobSelectorFilter : PagingFilterModel
	{
	}

	#endregion VwLogJob

}

#pragma warning restore CS8669
