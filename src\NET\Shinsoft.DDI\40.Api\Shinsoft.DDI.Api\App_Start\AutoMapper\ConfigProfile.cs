﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Shinsoft.Core.AutoMapper;
using Shinsoft.Core.AutoMapper.TypeConverters;

namespace Shinsoft.DDI.Api
{
    public class ConfigProfile : Profile
    {
        public ConfigProfile()
        {
            this.CreateMaps<IModel>();
            this.CreateMaps<IExtModel>();

            this.CreateMaps<IEntityMapable>();

            //this.CreateMap<List<I18n>, Dictionary<string, object?>>()
            //    .ConvertUsing(new KeyValueEntityToDictTypeConverter<I18n>());

            //this.CreateMap<Dictionary<string, string>, List<SysSetting>>()
            //    .ConvertUsing<DictToKeyValueTypeConverter<SysSetting>>();
        }
    }
}