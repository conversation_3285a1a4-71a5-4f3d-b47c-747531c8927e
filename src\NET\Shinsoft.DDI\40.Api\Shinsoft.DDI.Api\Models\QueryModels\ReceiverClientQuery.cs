﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class ReceiverClientQuery
    {
        /// <summary>
        /// 省份
        /// </summary>
        [MapFromProperty(typeof(ReceiverClient), ReceiverClient.Foreigns.Receiver, Receiver.Foreigns.Province, Province.Columns.Name)]
        public string? ProvinceName { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        [MapFromProperty(typeof(ReceiverClient), ReceiverClient.Foreigns.Receiver, Receiver.Foreigns.City, City.Columns.Name)]
        public string? CityName { get; set; }
    }
}
