<script setup lang="ts">
import { sysSetupApi } from "@/api/sysSetup";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { getDefaultOrder, getColumnOrder } from "@/utils/table";
import { ElMessage, ElMessageBox } from "element-plus";
import EditI18nCulture from "../dialogs/editI18nCulture.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

defineOptions({
  name: "i18nCulture:query"
});

/**
 * 当前组件ref
 */
const filterRef = ref();
const listRef = ref();
const editRef = ref();
const listContainerRef = ref();

/**
 * 基本配置定义
 */
const cfg = reactive({
  filter: {
    gutter: 3,
    span: 4
  },
  list: {
    defaultSort: { prop: "culture", order: "ascending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.edit ? 45 : 0) + (cfg.btn.delete ? 45 : 0))
    }
  },
  btn: {
    add: computed(() => {
      return userStore.hasAnyAuth(["Platform:I18n:Manage"]);
    }),
    edit: computed(() => {
      return userStore.hasAnyAuth(["Platform:I18n:Manage"]);
    }),
    delete: computed(() => {
      return userStore.hasAnyAuth(["Platform:I18n:Manage"]);
    })
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  },
  positions: []
});

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  keywords: "",
  i18nId: null,
  pageSize: 5
});

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 当前用户存储
 */
const userStore = useUserStoreHook();

/**
 * 初始化组件(created时调用)
 */
const initPage = i18nId => {
  filter.i18nId = i18nId;

  initFilter().then(() => {
    query();
  });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  cfg.loading.filter = true;
  const allInits = [];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.filter = false;
      resolve();
    });
  });
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  sysSetupApi
    .QueryI18nCulture(filter)
    .then(res => {
      state.datas = res.datas;
      state.total = res.total;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filterRef.value.validate((valid, fields) => {
    if (valid) {
      filter.pageIndex = 1;
      getList();
    }
  });
};

/**
 * 新增：点击【新增】按钮事件
 */
const addRow = data => {
  editRef.value?.open(null, data);
};

/**
 * 编辑：点击列表中【编辑】按钮事件
 */
const editRow = (row: any) => {
  editRef.value?.open(row.id);
};

/**
 * 删除：点击列表中【删除】按钮事件
 */
const deleteRow = (row: any) => {
  if (row && row.id) {
    ElMessageBox.confirm(
      `${t("operate.confirm.delete")}<br />${t("operate.confirm.deleteHint")}`,
      `${t("operate.title.deleteConfirm")}`,
      {
        showClose: false,
        closeOnClickModal: false,
        draggable: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: t("operate.ok"),
        cancelButtonText: t("operate.cancel"),
        type: "warning"
      }
    )
      .then(() => {
        cfg.loading.btn = true;
        sysSetupApi
          .RemoveI18nCulture(row)
          .then(res => {
            // 仅提示
            if (res.success) {
              refresh(t("operate.message.success"), "success");
            }
          })
          .finally(() => {
            cfg.loading.btn = false;
          });
      })
      .catch(() => {
        close();
      });
  }
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

/**
 * 序号
 */
const indexMethod = index => {
  return (filter.pageIndex - 1) * filter.pageSize + index + 1;
};

/**
 * 刷新table数据
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  query();
};

/**
 * 清空
 */
const clear = () => {
  state.datas = [];
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  initPage,
  addRow,
  clear
});
</script>

<template>
  <div>
    <div>
      <el-form
        ref="filterRef"
        v-loading="cfg.loading.filter"
        class="filter-form"
        :rules="rules.filter"
        :model="filter"
      >
        <el-row :gutter="cfg.filter.gutter" type="flex">
          <el-col :span="cfg.filter.span">
            <el-input
              v-model="filter.keywords"
              clearable
              :placeholder="t('filter.keywords')"
              class="filter-item"
              @keyup.enter="query"
            />
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-button
              class="query"
              :loading="cfg.loading.list"
              :icon="useRenderIcon('ep:search')"
              @click="query"
            >
              {{ t("operate.query") }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div ref="listContainerRef" class="list-container">
      <el-table
        ref="listRef"
        v-loading="cfg.loading.list"
        :data="state.datas"
        row-key="id"
        stripe
        border
        class-name="list"
        style="margin: auto; font-size: 13px"
        :height="cfg.list.height"
        :default-sort="cfg.list.defaultSort"
        @sort-change="sortChange"
      >
        <template #empty>
          <NoDatas />
        </template>
        <el-table-column
          fixed
          :label="t('list.no')"
          :index="indexMethod"
          type="index"
          align="center"
          width="70"
        />
        <el-table-column sortable sort-by="i18n.Key" prop="i18nKey" label="key值" width="150" />
        <el-table-column
          sortable
          sort-by="i18n.Name"
          prop="i18nName"
          label="名称"
          min-width="100"
        />
        <el-table-column sortable="custom" prop="culture" label="语言" min-width="80" />
        <el-table-column sortable="custom" prop="text" label="内容" min-width="200" />
        <el-table-column
          fixed="right"
          :label="t('list.operate')"
          class-name="operate"
          :width="cfg.list.operate.width"
          align="center"
        >
          <template #default="{ row }">
            <el-button
              v-if="cfg.btn.edit"
              class="edit"
              size="small"
              :circle="true"
              :title="t('operate.edit')"
              :icon="useRenderIcon('ep:edit')"
              @click="editRow(row)"
            />
            <el-button
              v-if="cfg.btn.delete"
              class="delete"
              size="small"
              :circle="true"
              :title="t('operate.delete')"
              :icon="useRenderIcon('ep:delete')"
              @click="deleteRow(row)"
            />
          </template>
        </el-table-column>
      </el-table>
      <Pagination v-model:filter="filter" :total="state.total" @change="getList" />
    </div>
    <EditI18nCulture ref="editRef" @refresh="getList" />
  </div>
</template>
