using System.ComponentModel;

namespace Shinsoft.DDI.Api.Models
{
    /// <summary>
    /// 产品级联选择器节点
    /// 用于构建药企\产品\规格的三级级联选择器数据结构
    /// </summary>
    [Description("产品级联选择器节点")]
    public class ProductCascaderSelector
    {
        /// <summary>
        /// 节点值
        /// </summary>
        [Description("节点值")]
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 节点标签
        /// </summary>
        [Description("节点标签")]
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// 子节点
        /// </summary>
        [Description("子节点")]
        public List<ProductCascaderSelector>? Children { get; set; }
    }
}
