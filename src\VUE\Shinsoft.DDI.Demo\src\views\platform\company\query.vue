<script setup lang="ts">
import { FormRules, Sort } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { sysSetupApi } from "@/api/sysSetup";
import ViewDialog from "./dialogs/view.vue";
import EditDialog from "./dialogs/edit.vue";
import { getDefaultOrder, getColumnOrder } from "@/utils/table";

defineOptions({
  name: "platform:company:query"
});

const viewRef = ref();
const editRef = ref();
const filterRef = ref();

const total = ref(0);
const datas = ref([]);
const loading = ref(false);
const filterLoading = ref(false);

const cfg = reactive({
  filter: {
    gutter: 3,
    span: 4,
    maxTags: 3
  },
  list: {
    height: 500,
    defaultSort: reactive<Sort>({ prop: "code", order: "ascending" }),
    operate: {
      width: 90
    }
  }
});

const filter: PagingFilter = reactive({
  keywords: "",
  valids: []
});

const filterRules: FormRules = reactive({});

const init = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);
  query();
};

const getList = () => {
  loading.value = true;
  sysSetupApi
    .QueryCompany(filter)
    .then(res => {
      datas.value = res.datas;
      total.value = res.total;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      loading.value = false;
    });
};

const query = () => {
  filter.pageIndex = 1;
  getList();
};

const addRow = () => {
  editRef.value?.open();
};

const editRow = (row: any) => {
  editRef.value?.open(row.id);
};

const viewRow = (row: any) => {
  viewRef.value?.open(row.id);
};

const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);

  getList();
};

init();
</script>

<template>
  <div>
    <div class="filter-container">
      <el-form
        ref="filterRef"
        v-loading="filterLoading"
        class="filter-form"
        :rules="filterRules"
        :model="filter"
      >
        <el-row :gutter="cfg.filter.gutter" type="flex">
          <el-col :span="cfg.filter.span">
            <el-input
              v-model="filter.keywords"
              clearable
              placeholder="关键字"
              class="filter-item"
              @keyup.enter="query"
            />
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-select
              v-model="filter.valids"
              multiple
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="3"
              placeholder="有效性"
            >
              <el-option key="true" label="有效" value="true" />
              <el-option key="false" label="无效" value="false" />
            </el-select>
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-button class="query" :icon="useRenderIcon('ep:search')" @click="query">
              查询
            </el-button>
            <el-button query="new" :icon="useRenderIcon('ep:document-add')" @click="addRow">
              新建
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="list-container">
      <el-table
        v-loading="loading"
        :data="datas"
        row-key="id"
        stripe
        border
        class-name="list"
        style="width: 100%"
        :height="cfg.list.height"
        :default-sort="cfg.list.defaultSort"
        @sort-change="sortChange"
      >
        <template #empty>
          <NoDatas />
        </template>
        <el-table-column fixed sortable="custom" prop="code" label="编码" width="150" />
        <el-table-column fixed sortable="custom" prop="name" label="名称" min-width="200" />
        <el-table-column sortable="custom" prop="valid" label="有效" width="120" align="center">
          <template #default="{ row }">
            <span v-if="row.valid">是</span>
            <span v-if="!row.valid">否</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" :width="cfg.list.operate.width" align="center">
          <template #default="{ row }">
            <el-button
              class="view"
              size="small"
              :circle="true"
              title="查看"
              :icon="useRenderIcon('ep:document')"
              @click="viewRow(row)"
            />
            <el-button
              class="edit"
              size="small"
              :circle="true"
              title="编辑"
              :icon="useRenderIcon('ep:edit')"
              @click="editRow(row)"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination v-model:filter="filter" :total="total" @change="getList" />
    <view-dialog ref="viewRef" />
    <edit-dialog ref="editRef" @refresh="getList" />
  </div>
</template>
