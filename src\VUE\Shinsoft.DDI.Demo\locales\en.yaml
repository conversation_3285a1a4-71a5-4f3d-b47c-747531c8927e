buttons:
  pureLoginOut: LoginOut
  pureLogin: Login
  pureOpenSystemSet: Open System Configs
  pureReload: Reload
  pureCloseCurrentTab: Close CurrentTab
  pureCloseLeftTabs: Close LeftTabs
  pureCloseRightTabs: Close RightTabs
  pureCloseOtherTabs: Close OtherTabs
  pureCloseAllTabs: Close AllTabs
  pureContentFullScreen: Content FullScreen
  pureContentExitFullScreen: Content ExitFullScreen
  pureClickCollapse: Collapse
  pureClickExpand: Expand
  pureConfirm: Confirm
  pureSwitch: Switch
  pureClose: Close
  pureBackTop: BackTop
  pureOpenText: Open
  pureCloseText: Close
search:
  pureTotal: Total
  pureHistory: History
  pureCollect: Collect
  pureDragSort: （Drag Sort）
  pureEmpty: Empty
  purePlaceholder: Search Menu
panel:
  pureSystemSet: System Configs
  pureCloseSystemSet: Close System Configs
  pureClearCacheAndToLogin: Clear cache and return to login page
  pureClearCache: Clear Cache
  pureOverallStyle: Overall Style
  pureOverallStyleLight: Light
  pureOverallStyleLightTip: Set sail freshly and light up the comfortable work interface
  pureOverallStyleDark: Dark
  pureOverallStyleDarkTip: Moonlight Overture, indulge in the tranquility and elegance of the night
  pureOverallStyleSystem: Auto
  pureOverallStyleSystemTip: Synchronize time, the interface naturally responds to morning and dusk
  pureThemeColor: Theme Color
  pureLayoutModel: Layout Model
  pureVerticalTip: The menu on the left is familiar and friendly
  pureHorizontalTip: Top menu, concise overview
  pureMixTip: Mixed menu, flexible
  pureStretch: Stretch Page
  pureStretchFixed: Fixed
  pureStretchFixedTip: Compact pages make it easy to find the information you need
  pureStretchCustom: Custom
  pureStretchCustomTip: Minimum 1280, maximum 1600
  pureTagsStyle: Tags Style
  pureTagsStyleSmart: Smart
  pureTagsStyleSmartTip: Smart tags add fun and brilliance
  pureTagsStyleCard: Card
  pureTagsStyleCardTip: Card tags for efficient browsing
  pureTagsStyleChrome: Chrome
  pureTagsStyleChromeTip: Chrome style is classic and elegant
  pureInterfaceDisplay: Interface Display
  pureGreyModel: Grey Model
  pureWeakModel: Weak Model
  pureHiddenTags: Hidden Tags
  pureHiddenFooter: Hidden Footer
  pureMultiTagsCache: MultiTags Cache
status:
  pureLoad: Loading...
  pureMessage: Message
  pureNotify: Notify
  pureTodo: Todo
  pureNoMessage: No Message
  pureNoNotify: No Notify
  pureNoTodo: No Todo
login:
  pureUsername: Username
  purePassword: Password
  pureLogin: Login
  pureLoginSuccess: Login Success
  pureLoginFail: Login Fail
  pureUsernameReg: Please enter username
  purePassWordReg: Please enter password
  purePassWordRuleReg: The password format should be any combination of 8-18 digits
titles:
  info: Information
  warning: Warning
  error: Error
shinsoft:
  select: Please select
  accountSettings: Account settings
  delegate: Delegate
  editPwd: Edit Password
  setDelegate: Set delegate
  current: Current
  delegating: Delegating
  delegateAble: Delegate able
  basicInfo: Basic Info
  valid: Valid
  invalid: Invalid
filter:
  keywords: Keywords
  startDate: Start Date
  endDate: End Date
list:
  no: No
  operate: Operate 
  noDatas: No Datas
  onDuty: On Duty
operate:
  ok: OK
  cancel: Cancel
  query: Query
  view: View
  close: Close
  add: Add
  edit: Edit
  delete: Delete 
  save: Save
  submit: Submit
  append: Add
  select: Select
  saveDraft: Save draft
  setMajorStation: Set MajorStation
  setDefaultCostCenter: Set Default CostCenter
  noCheckedItems: Please Select The Items To Add
  title:
    deleteConfirm: Confirm to delete
    operateConfirm: Confirm to Operate
  message:
    success:  Operate success
  confirm:
    delete: Please confirm to delete?
    deleteHint: "Hint: It will not be recoverable!"
    valid: Please confirm to validity operate?
    validHint: "Hint: Confirm the operation!"
  yes: Yes
  no: No
menus:
  pureHome: Home
  pureLogin: Login
  pureAbnormal: Abnormal Page
  pureFourZeroFour: "404"
  pureFourZeroOne: "403"
  pureFive: "500"
  purePermission: Permission Manage
  purePermissionPage: Page Permission
  purePermissionButton: Button Permission 
  authorize:
    _group: Authorize
    employee: Employee Manage
    role: Role Manage
  app:
    _group: App,
    edit: Apply
    view: Review
    audit: Audit
    editLeaveConfirmText: Leaving the current page soon, any unsaved data will be lost
app:
  editTitle: Edit App
  viewTitle: View App
  errorTitle: App Error
role:
  allTags: All
  operate:
    auth: auth
    member: member
  tab:
    auth: Authorize
    member: Members
    employee: Employees
    baseAuth: Base Authorize
    tagAuth: Content Authorize
  dialog:  
    roleMember: Role Members 
  ui:
    authHint: Please complete the authorization settings within all tabs before saving