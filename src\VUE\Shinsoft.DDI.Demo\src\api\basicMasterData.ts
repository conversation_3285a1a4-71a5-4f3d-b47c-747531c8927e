import { useHttpApi } from "./libs/httpApi";

const controller = "BasicMasterData";

const api = useHttpApi(controller);

export const basicMasterDataApi = {
  QueryPosition(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryPosition", data, config);
  },
  GetPosition(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetPosition", { id }, config);
  },
  AddPosition(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddPosition", data, config);
  },
  UpdatePosition(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdatePosition", data, config);
  },
  DeletePosition(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("DeletePosition", { id: data.id }, config);
  },
  QueryCostCenter(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryCostCenter", data, config);
  },
  GetCostCenter(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetCostCenter", { id }, config);
  },
  AddCostCenter(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddCostCenter", data, config);
  },
  UpdateCostCenter(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateCostCenter", data, config);
  },
  DeleteCostCenter(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("DeleteCostCenter", { id: data.id }, config);
  },
  SetCostCenterValid(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("SetCostCenterValid", { id: data.id }, config);
  },
  QuerySubCompany(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QuerySubCompany", data, config);
  },
  GetSubCompany(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetSubCompany", { id }, config);
  },
  AddSubCompany(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddSubCompany", data, config);
  },
  UpdateSubCompany(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateSubCompany", data, config);
  },
  DeleteSubCompany(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("DeleteSubCompany", { id: data.id }, config);
  },
  GetOrganizationTree() {
    return api.get<BizResult>("GetOrganizationTree");
  },
  QueryEmployeeStation(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryEmployeeStation", data, config);
  },
  GetEmployeeStation(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetEmployeeStation", { id }, config);
  },
  DeleteEmployeeStation(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("DeleteEmployeeStation", data, config);
  },
  AddEmployeeStation(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddEmployeeStation", data, config);
  },
  UpdateEmployeeStation(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateEmployeeStation", data, config);
  },
  QueryDepartment(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryDepartment", data, config);
  },
  GetDepartment(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetDepartment", { id }, config);
  },
  AddDepartment(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddDepartment", data, config);
  },
  UpdateDepartment(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateDepartment", data, config);
  },
  DeleteDepartment(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("DeleteDepartment", data, config);
  },
  QueryStation(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryStation", data, config);
  },
  GetStation(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetStation", { id }, config);
  },
  AddStation(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddStation", data, config);
  },
  UpdateStation(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateStation", data, config);
  },
  DeleteStation(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("DeleteStation", data, config);
  },
  QueryDepartmentCostCenter(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryDepartmentCostCenter", data, config);
  },
  AddDepartmentCostCenters(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddDepartmentCostCenters", data, config);
  },
  RemoveDepartmentCostCenter(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("RemoveDepartmentCostCenter", data, config);
  },
  SetDepartmentCostCenterDefault(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("SetDepartmentCostCenterDefault", data, config);
  }
};
