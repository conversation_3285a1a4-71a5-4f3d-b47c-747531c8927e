import { useHttpApi } from "./libs/httpApi";

const controller = "Authorize";

const api = useHttpApi(controller);

export const authorizeApi = {
  QueryEmployee(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryEmployee", data, config);
  },
  GetEmployee(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetEmployee", { id }, config);
  },
  GetEmployeeRoles(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetEmployeeRoles", { id }, config);
  },
  GetEmployeeStations(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetEmployeeStations", { id }, config);
  },
  GetEmployeeCfg(config?: ApiRequestConfig) {
    return api.get<BizResult>("GetEmployeeCfg", null, config);
  },
  AddEmployee(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddEmployee", data, config);
  },
  UpdateEmployee(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateEmployee", data, config);
  },
  DeleteEmployee(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("DeleteEmployee", { id: data.id }, config);
  },
  QueryRole(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryRole", data, config);
  },
  GetRole(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetRole", { id }, config);
  },
  AddRole(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddRole", data, config);
  },
  UpdateRole(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateRole", data, config);
  },
  DeleteRole(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("DeleteRole", { id: data.id }, config);
  },
  GetRoleAuths(roleId: string, isEdit?: boolean, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetRoleAuths", { roleId, isEdit }, config);
  },
  SetRoleAuths(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("SetRoleAuths", data, config);
  },
  QueryRoleMember(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryRoleMember", data, config);
  },
  AddRoleMembers(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddRoleMembers", data, config);
  },
  DeleteRoleMember(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("DeleteRoleMember", { id: data.id }, config);
  },
  QueryRoleEmployee(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryRoleEmployee", data, config);
  },
  SetEmployeeMajor(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("SetEmployeeMajor", data, config);
  }
};
