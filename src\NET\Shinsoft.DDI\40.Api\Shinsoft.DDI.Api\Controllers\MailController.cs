﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    [ApiExplorerSettings(GroupName = "邮件")]
    public class MailController : BaseApiController<MailBll>
    {
        #region Mail

        /// <summary>
        /// 查询邮件
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.System.Mail.Mail_Query)]
        [Auth(AuthCodes.System.Mail.Mail_Manage)]
        [LogApi(ApiType.Query, Operate = "查询邮件")]
        public QueryResult<MailQuery> QueryMail([FromQuery] MailFilter filter)
        {
            var exps = this.NewExps<Mail>();

            var result = this.Repo.GetDynamicQuery<Mail, MailQuery>(filter, exps);

            return result;
        }

        /// <summary>
        /// 获取邮件
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.System.Mail.Mail_Manage)]
        [LogApi(ApiType.Query, Operate = "获取邮件")]
        public BizResult<MailModel> GetMail([FromQuery, Required] Guid id)
        {
            var result = new BizResult<MailModel>();
            var entity = this.Repo.Get<Mail>(id);

            if (entity == null)
            {
                result.Error("邮件不存在");
            }
            else
            {
                var model = entity.Map<MailModel>();

                var attachments = this.Repo.GetEntities<MailAttachment>(p => p.MailId == id);
                model.MailAttachments = attachments.Maps<MailAttachmentQuery>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 发送邮件
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.System.Mail.Mail_Manage)]
        [LogApi(ApiType.Save, Operate = "发送邮件")]
        public BizResult SendMail(MailModel model)
        {
            return this.Repo.SendMail(model.ID);
        }

        #endregion Mail
    }
}
