// 框架改造
import { $t } from "@/plugins/i18n";

export default [
  {
    path: "/403",
    name: "403",
    component: () => import("@/views/error/403.vue"),
    meta: {
      title: $t("menus.pureFourZeroOne"),
      anonymous: true,
      showLink: true
    }
  },
  {
    path: "/404",
    name: "404",
    component: () => import("@/views/error/404.vue"),
    meta: {
      title: $t("menus.pureFourZeroFour"),
      anonymous: true,
      showLink: false
    }
  },
  {
    path: "/500",
    name: "500",
    component: () => import("@/views/error/500.vue"),
    meta: {
      title: $t("menus.pureFive"),
      anonymous: true,
      showLink: false
    }
  }
] satisfies Array<RouteConfigsTable>;
