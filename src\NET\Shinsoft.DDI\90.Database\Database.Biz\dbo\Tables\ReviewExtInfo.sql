﻿--数据审核扩展信息
--数据审核扩展信息
CREATE TABLE [dbo].[ReviewExtInfo]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL,
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [Code]                      NVARCHAR(500)               NOT NULL,
    [Name]                      NVARCHAR(500)               NOT NULL,
    [Title]                     NVARCHAR(500)               NOT NULL, 
    CONSTRAINT [PK_ReviewExtInfo] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_ReviewExtInfo_ReviewIndex] FOREIGN KEY ([ID]) REFERENCES [dbo].[ReviewIndex] ([ID]),
    CONSTRAINT [FK_ReviewExtInfo_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
 )


GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'数据审核扩展信息',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewExtInfo',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'标题',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReviewExtInfo',
    @level2type = N'COLUMN',
    @level2name = N'Title'
