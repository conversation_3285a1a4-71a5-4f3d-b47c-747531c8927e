using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    /// <summary>
    /// 药企字典类
    /// 定义药企相关的字典数据，包括导出列定义等
    /// </summary>
    public class ManufacturerDictionary
    {
        /// <summary>
        /// 导出药企信息的列定义
        /// </summary>
        public static Dictionary<string, string> ExportManufacturerColumns
        {
            get
            {
                return new Dictionary<string, string>
                {
                    {"厂家编码", "Code"},
                    {"厂家名称", "Name"},
                    {"厂家简称", "ShortName"},
                    {"国家", "Country"},
                    {"状态", "EnumStatusDesc"},
                    {"备注", "Remark"},
                };
            }
        }
    }
}