<script setup lang="ts">
import { SplitPane } from "@/components/SplitPane";
import OrganizationTree from "./components/organizationTree.vue";
import OrganizationBaseInfo from "./components/organizationBaseInfo.vue";
import OrganizationEmployee from "./components/organizationEmployee.vue";
import OrganizationStation from "./components/organizationStation.vue";
import OrganizationDepartment from "./components/organizationDepartment.vue";
import OrganizationDeptCostCenter from "./components/organizationDeptCostCenter.vue";

import { basicMasterDataApi } from "@/api/basicMasterData";
import EditDepartment from "./dialogs/editDepartment.vue";
import EditStation from "./dialogs/editStation.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "organization:query"
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  activeName: "employeeTab",
  showDepartment: true
});

/**
 * 当前组件ref
 */
const organizationEmployeeRef = ref();
const editDepartmentRef = ref();
const editStationRef = ref();
const organizationStationRef = ref();
const organizationDepartmentRef = ref();
const organizationDeptCostCenterRef = ref();

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：当前页
  data: []
});

const model = defineModel<any>();

/**
 * 初始化组件(created时调用)
 */
const init = () => {
  getList();
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  basicMasterDataApi
    .GetOrganizationTree()
    .then(res => {
      state.data = res.data;
    })
    .finally(() => {});
};

/**
 * 树节点点击事件
 */
const handleNodeClick = node => {
  model.value = node.data;
  model.value.parentName = node.parent.data.name;
  cfg.activeName = "employeeTab";
  if (!node.data.stationId) {
    cfg.showDepartment = true;
  } else {
    cfg.showDepartment = false;
  }
  //初始化默认Tab
  handleTabChange(cfg.activeName);
};

/**
 * 部门操作：增、删、改
 */
const departmentOperation = (operationType: any, data: any) => {
  switch (operationType) {
    case "appendDepartment":
      appendDepartment(data);
      break;
    case "editDepartment":
      editDepartment(data);
      break;
    case "deleteDepartment":
      deleteDepartment(data);
      break;
  }
};

/**
 * 添加部门
 */
const appendDepartment = data => {
  editDepartmentRef.value?.open(null, data.subCompanyId, data.departmentId);
};

/**
 * 编辑部门
 */
const editDepartment = data => {
  editDepartmentRef.value?.open(data.departmentId);
};

/**
 * 删除部门
 */
const deleteDepartment = (data?: any) => {
  let model = { id: data.departmentId };
  if (model && model.id) {
    ElMessageBox.confirm(
      `${t("operate.confirm.delete")}<br />${t("operate.confirm.deleteHint")}`,
      `${t("operate.title.deleteConfirm")} - ${data.name}`,
      {
        showClose: false,
        closeOnClickModal: false,
        draggable: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: t("operate.ok"),
        cancelButtonText: t("operate.cancel"),
        type: "error"
      }
    )
      .then(() => {
        basicMasterDataApi
          .DeleteDepartment(model)
          .then(res => {
            // 仅提示
            if (res.success) {
              refresh(t("operate.message.success"), "success");
            }
          })
          .finally(() => {});
      })
      .catch(() => {
        close();
      });
  }
};

/**
 * 岗位操作：增、删、改
 */
const stationOperation = (operationType: any, data: any) => {
  switch (operationType) {
    case "appendStation":
      appendStation(data);
      break;
    case "editStation":
      editStation(data);
      break;
    case "deleteStation":
      deleteStation(data);
      break;
  }
};

/**
 * 添加岗位
 */
const appendStation = data => {
  editStationRef.value?.open(null, data.subCompanyId, data.departmentId);
};

/**
 * 编辑岗位
 */
const editStation = data => {
  editStationRef.value?.open(data.stationId);
};

/**
 * 删除岗位
 */
const deleteStation = (data?: any) => {
  let model = { id: data.stationId };
  if (model && model.id) {
    ElMessageBox.confirm(
      `${t("operate.confirm.delete")}<br />${t("operate.confirm.deleteHint")}`,
      `${t("operate.title.deleteConfirm")} - ${data.name}`,
      {
        showClose: false,
        closeOnClickModal: false,
        draggable: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: t("operate.ok"),
        cancelButtonText: t("operate.cancel"),
        type: "error"
      }
    )
      .then(() => {
        basicMasterDataApi
          .DeleteStation(model)
          .then(res => {
            // 仅提示
            if (res.success) {
              refresh(t("operate.message.success"), "success");
            }
          })
          .finally(() => {});
      })
      .catch(() => {
        close();
      });
  }
};

/**
 * ElMessage: 操作完成提示；
 */
const elMessage = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
};

const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  getList();
  elMessage(msg, type);
};

const handleTabChange = tabPanelName => {
  switch (tabPanelName) {
    case "departmentTab":
      //部门
      organizationDepartmentRef.value.initPage(model.value.subCompanyId, model.value.departmentId);
      break;
    case "costCenterTab":
      //成本中心
      organizationDeptCostCenterRef.value.initPage(
        model.value.subCompanyId,
        model.value.departmentId
      );
      break;
    case "stationTab":
      //岗位信息
      organizationStationRef.value.initPage(
        model.value.subCompanyId,
        model.value.departmentId,
        model.value.stationId
      );
      break;
    case "employeeTab":
      //人员信息
      organizationEmployeeRef.value.initPage(
        model.value.subCompanyId,
        model.value.departmentId,
        model.value.stationId
      );
      break;
  }
};
init();
</script>

<template>
  <div>
    <split-pane :panelLeftWidth="350">
      <template #left>
        <div style="margin-top: 1px">
          <Organization-Tree
            ref="organizationTreeRef"
            v-model:data="state.data"
            @node-Click="handleNodeClick"
            @departmentOperation="departmentOperation"
            @stationOperation="stationOperation"
          />
        </div>
      </template>
      <template #right>
        <div class="right-container">
          <Organization-BaseInfo v-model="model" />
          <el-tabs v-model="cfg.activeName" @tab-change="handleTabChange">
            <el-tab-pane
              v-if="cfg.showDepartment"
              :label="tt('Entity.Department._Entity')"
              name="departmentTab"
            >
              <OrganizationDepartment ref="organizationDepartmentRef" />
            </el-tab-pane>
            <el-tab-pane
              v-if="cfg.showDepartment"
              :label="tt('Entity.CostCenter._Entity')"
              name="costCenterTab"
            >
              <OrganizationDeptCostCenter ref="organizationDeptCostCenterRef" />
            </el-tab-pane>
            <el-tab-pane :label="tt('Entity.Station._Entity')" name="stationTab">
              <OrganizationStation ref="organizationStationRef" />
            </el-tab-pane>
            <el-tab-pane :label="tt('Entity.Employee._Entity')" name="employeeTab">
              <OrganizationEmployee ref="organizationEmployeeRef"
            /></el-tab-pane>
          </el-tabs>
        </div>
      </template>
    </split-pane>
    <EditDepartment ref="editDepartmentRef" @refresh="getList" />
    <EditStation ref="editStationRef" @refresh="getList" />
  </div>
</template>
<style>
.right-container {
  background-color: #f5f6f8;
  height: 100%;
  overflow: auto;
  padding: 0px 20px;
}
.stationInfo {
  padding-top: 17px;
  padding-left: 10px;
  height: auto;
  min-height: 70px;
  background-color: white;
  margin-bottom: 15px;
  color: #606266;
  font-size: 13px;
}
.product-content {
  padding-bottom: 10px;
}
.station-title {
  font-weight: bold;
  padding-bottom: 15px;
}
.station-table {
  padding-bottom: 15px;
}
</style>
