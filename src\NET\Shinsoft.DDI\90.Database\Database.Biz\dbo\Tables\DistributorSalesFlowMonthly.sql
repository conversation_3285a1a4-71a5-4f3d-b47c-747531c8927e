﻿CREATE TABLE [dbo].[DistributorSalesFlowMonthly]
(
	[ID]  UNIQUEIDENTIFIER            NOT NULL  CONSTRAINT [Default_DistributorSalesFlowMonthly_ID] DEFAULT (NEWSEQUENTIALID()), 
	[ShipperIds]                NVARCHAR(Max) NOT NULL,
    [DistributorId]             UNIQUEIDENTIFIER NOT NULL,
    [ReceiverId]                UNIQUEIDENTIFIER NOT NULL,
    [ManufacturerId]            UNIQUEIDENTIFIER NOT NULL,
	[ProductId]                 UNIQUEIDENTIFIER NOT NULL,
	[ProductSpecId]             UNIQUEIDENTIFIER NULL,
	[SaleDate]                  datetime NOT NULL,
    [ExpireDate]                datetime  NULL,
    [ReceiverAliasId]           UNIQUEIDENTIFIER NOT NULL,
	[Quantity]                  decimal (18, 2) NOT NULL,
    [OrderNumber]               NVARCHAR(50)                NULL, 
	[Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL, 
    [CreateTime]                DATETIME                    NULL, 
    [LastEditor]                NVARCHAR(50)                NULL, 
    [LastEditTime]              DATETIME                    NULL, 
	CONSTRAINT [PK_DistributorSalesFlowMonthly] PRIMARY KEY CLUSTERED ([ID]),
	CONSTRAINT [FK_DistributorSalesFlowMonthly_Receiver_00_Distributor] FOREIGN KEY ([DistributorId]) REFERENCES [dbo].[Receiver] ([ID]),
    CONSTRAINT [FK_DistributorSalesFlowMonthly_Receiver_01_Receiver] FOREIGN KEY ([ReceiverId]) REFERENCES [dbo].[Receiver] ([ID]),
    CONSTRAINT [FK_DistributorSalesFlowMonthly_Manufacturer] FOREIGN KEY ([ManufacturerId]) REFERENCES [dbo].[Manufacturer] ([ID]),
	CONSTRAINT [FK_DistributorSalesFlowMonthly_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ID]),
	CONSTRAINT [FK_DistributorSalesFlowMonthly_ProductSpec] FOREIGN KEY ([ProductSpecId]) REFERENCES [dbo].[ProductSpec] ([ID]),
)
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'月库存',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorSalesFlowMonthly',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'主键ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorSalesFlowMonthly',
    @level2type = N'COLUMN',
    @level2name = N'ID'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'发货方Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorSalesFlowMonthly',
    @level2type = N'COLUMN',
    @level2name = 'DistributorId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'厂商Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorSalesFlowMonthly',
    @level2type = N'COLUMN',
    @level2name = N'ManufacturerId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'收货方Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorSalesFlowMonthly',
    @level2type = N'COLUMN',
    @level2name = N'ReceiverId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'产品Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorSalesFlowMonthly',
    @level2type = N'COLUMN',
    @level2name = N'ProductId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'规格Id',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorSalesFlowMonthly',
    @level2type = N'COLUMN',
    @level2name = N'ProductSpecId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'销售',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorSalesFlowMonthly',
    @level2type = N'COLUMN',
    @level2name = 'SaleDate'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'数量',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'DistributorSalesFlowMonthly',
    @level2type = N'COLUMN',
    @level2name = N'Quantity'
GO
