﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public interface IAttachments
    {
        /// <summary>
        /// 图片
        /// </summary>
        List<AttachmentQuery>? Images { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        List<AttachmentQuery>? Attachs { get; set; }
    }
}