﻿CREATE TABLE [dbo].[DepartmentCostCenter]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_DepartmentCostCenter_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [DepartmentId]              UNIQUEIDENTIFIER            NOT NULL,
    [CostCenterId]		        UNIQUEIDENTIFIER	        NOT NULL,
    [IsDefault]                 BIT                         NOT NULL,
	CONSTRAINT [PK_DepartmentCostCenter] PRIMARY KEY CLUSTERED ([ID] ASC), 
    CONSTRAINT [FK_DepartmentCostCenter_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].Company([ID]), 
    CONSTRAINT [FK_DepartmentCostCenter_Department] FOREIGN KEY ([DepartmentId]) REFERENCES [dbo].[Department]([ID]), 
    CONSTRAINT [FK_DepartmentCostCenter_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter]([ID]), 
)
GO

CREATE UNIQUE INDEX [IX_DepartmentCostCenter] ON [dbo].[DepartmentCostCenter]
(
	[CompanyId] ASC, [DepartmentId] ASC, [CostCenterId] ASC
);
GO
