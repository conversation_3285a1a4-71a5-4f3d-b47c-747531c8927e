﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    /// <summary>
    /// 公司缓存
    /// </summary>
    [ApiExplorerSettings(GroupName = "缓存")]
    public class CompanyCacheController : BaseApiController<CompanyBll>
    {
        /// <summary>
        /// 获取公司设置
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取公司设置")]
        [Auth(AuthCodes.System.Setup.CompanyCache_Operate)]
        public BizResult<CompanySettingModel> GetCompanySettingByKey([FromQuery, Required] string key)
        {
            var result = new BizResult<CompanySettingModel>();
            var setting = this.CompanyCache.GetCompanySetting(key);
            result.Data = setting?.Map<CompanySettingModel>();
            return result;
        }

        /// <summary>
        /// 清空公司缓存
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Special, Operate = "清空公司缓存")]
        [Auth(AuthCodes.System.Setup.CompanyCache_Operate)]
        public BizResult FlushCompanyCache(string companyCode)
        {
            var result = new BizResult();

            Guid? companyId = null;

            if (companyCode.IsEmpty())
            {
                companyId = Config.DefaultCompanyId;
            }
            else
            {
                var company = this.SysCache.GetCompany(companyCode);

                if (company?.Valid == true)
                {
                    companyId = company.ID;
                }
                else
                {
                    result.Error("公司不存在或被禁用");
                }
            }

            if (companyId.HasValue)
            {
                this.CompanyCachePool.FlushCache(companyId.Value);
            }

            return result;
        }

        /// <summary>
        /// 根据CacheKey获取缓存
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.System.Setup.CompanyCache_Operate)]
        [LogApi(ApiType.Special, Operate = "根据CacheKey获取缓存")]
        public IActionResult GetCompanyCacheByKey(string? companyCode, [Required] string cacheKey)
        {
            Guid? companyId = null;

            if (companyCode.IsEmpty())
            {
                companyId = Config.DefaultCompanyId;
            }
            else
            {
                var company = this.SysCache.GetCompany(companyCode.GetValueOrDefault());

                if (company?.Valid == true)
                {
                    companyId = company.ID;
                }
                else
                {
                    return Json("公司不存在或被禁用");
                }
            }

            if (companyId.HasValue)
            {
                if (this.GetCompanyCache(companyId.Value).GetAllKeys().Any(p => p == cacheKey))
                {
                    return Json(this.GetCompanyCache(companyId.Value).GetKey(cacheKey));
                }
                else
                {
                    return Json("cacheKey不存在");
                }
            }
            else
            {
                return Json("请设置默认公司");
            }
        }

        /// <summary>
        /// 清空所有缓存
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.System.Setup.CompanyCache_Operate)]
        [LogApi(ApiType.Special, Operate = "清空所有缓存")]
        public BizResult FlushAll()
        {
            var result = new BizResult();

            this.CompanyCachePool.FlushAll();

            return result;
        }

        /// <summary>
        /// 获取公司缓存所有键
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.System.Setup.CompanyCache_Operate)]
        [LogApi(ApiType.Cache, Operate = "获取公司缓存所有键")]
        public List<string> GetAllKeys()
        {
            return this.CompanyCache.GetAllKeys();
        }

        /// <summary>
        /// 销毁公司缓存
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.System.Setup.CompanyCache_Operate)]
        [LogApi(ApiType.Cache, Operate = "销毁公司缓存")]
        public void FlushCache([Required] string key)
        {
            this.CompanyCache.RemoveKey(key);
        }
    }
}
