﻿<#@ assembly name="System.Data" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.Common" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Text.RegularExpressions" #>
<#+
#nullable enable

    /// <summary>
    /// 数据库对象基类
    /// </summary>
    public abstract class Base
    {
        /// <summary>
        /// 备注
        /// </summary>
        public string? Comment { get; set; }
    }

    /// <summary>
    /// 表/视图对象
    /// </summary>
    public class Table : Base
    {
        public string SysTypeName => $"{this.Prefix}{this.Name}";

        public string Prefix { get; set; } = string.Empty;

        /// <summary>
        /// 是否是视图
        /// </summary>
        public bool IsView { get; set; }

        public string Schema { get; set; } = string.Empty;

        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 是否继承IDeleteable
        /// </summary>
        public bool IsDeleteable { get; set; }

        /// <summary>
        /// 是否继承IOperateInfo
        /// </summary>
        public bool IsOperateInfo { get; set; }

        /// <summary>
        /// 是否继承 IPrimaryKey
        /// </summary>
        public bool IsPrimaryKey { get; set; }

        /// <summary>
        /// 主键系统类型名称
        /// </summary>
        public string? PrimaryKeySysTypeName { get; set; }

        /// <summary>
        /// 字段列表
        /// </summary>
        public List<Column> Columns { get; set; } = new();
    }

    /// <summary>
    /// 字段
    /// </summary>
    public class Column : Base
    {
        public string TableSysTypeName { get; set; } = string.Empty;

        public string TableSchema { get; set; } = string.Empty;

        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// 表内位置
        /// </summary>
        public int OrdinalPosition { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; } = string.Empty;

        /// <summary>
        /// 最大长度
        /// </summary>
        public long? MaxLength { get; set; }

        /// <summary>
        /// 数字整数精度
        /// </summary>
        public int? NumPrecision { get; set; }

        /// <summary>
        /// 数字小数精度
        /// </summary>
        public int? NumScale { get; set; }

        /// <summary>
        /// 是否允许NULL
        /// </summary>
        public bool IsNullable { get; set; }

        /// <summary>
        /// 是否子增长
        /// </summary>
        public bool IsIdentity { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public DbType DbType { get; set; }

        /// <summary>
        /// 系统类型名称
        /// </summary>
        public string SysTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 数据类型名称
        /// </summary>
        public string DbTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 是否是主键
        /// </summary>
        public bool IsPrimaryKey { get; set; }

        /// <summary>
        /// 是否是外键
        /// </summary>
        public bool IsForeignKey { get; set; }

        /// <summary>
        /// 是否是枚举
        /// </summary>
        public bool IsEnum { get; set; }

        /// <summary>
        /// 约束名
        /// </summary>
        public string? ConstraintName { get; set; }

        /// <summary>
        /// 外键表Schema
        /// </summary>
        public string? ForeignTableSchema { get; set; }

        /// <summary>
        /// 外键表名
        /// </summary>
        public string? ForeignTableName { get; set; }

        /// <summary>
        /// 外键表
        /// </summary>
        public Table? ForeignTable { get; set; }

        /// <summary>
        /// 外键对象系统类型名称
        /// </summary>
        public string? ForeignSysTypeName => this.IsForeignKey
            ? this.ForeignTable?.SysTypeName
            : null;

        /// <summary>
        /// 外键对象变量名
        /// </summary>
        public string? ForeignName { get; set; }

        /// <summary>
        /// 外键对象备注
        /// </summary>
        public string? ForeignComment { get; set; }

        /// <summary>
        /// 外键对象逆转对象系统类型
        /// </summary>
        public string? InverseSysTypeName => this.IsForeignKey
            ? this.IsPrimaryKey
                ? this.TableSysTypeName
                : $"List<{this.TableSysTypeName}>"
            : null;

        /// <summary>
        /// 外键对象逆转对象变量名
        /// </summary>
        public string? InverseName { get; set; }

        /// <summary>
        /// 外键对象逆转对象备注
        /// </summary>
        public string? InverseComment { get; set; }
    }

    public abstract class BaseDbHelper : IDisposable
    {
        private const string FK_REGEX = "^FK_[a-zA-Z0-9]+_[a-zA-Z0-9]+$";
        private const string FK_REGEX_FOREIGN = "^FK_[a-zA-Z0-9]+_[a-zA-Z0-9]+_[0-9]{2}_[a-zA-Z0-9]+$";
        private const string FK_REGEX_FOREIGN_AND_INVERSE = "^FK_[a-zA-Z0-9]+_[a-zA-Z0-9]+_[0-9]{2}_[a-zA-Z0-9]+_[a-zA-Z0-9]+$";

        private const string Enum_Regex1 = "^Enum[A-Z][a-zA-Z0-9]*$";
        private const string Enum_Regex2 = "^Enum[A-Z][a-zA-Z0-9]*_[a-zA-Z0-9]*$";

        protected BaseDbHelper(DbConnection connection, string database = "")
        {
            this.DbConnection = connection;
            this.DbConnection.Open();
            this.Database = database;
        }

        protected string Database { get; set; }

        protected DbConnection DbConnection { get; set; }

        protected virtual DbDataReader ExecuteReader(string sql)
        {
            DbCommand dbCommand = this.DbConnection.CreateCommand();
            dbCommand.CommandText = sql;
            return dbCommand.ExecuteReader();
        }

        private List<Table>? _tables;

        protected List<Table> Tables => _tables ??= this.GetAllTables();

        private List<Column>? _columns;

        protected List<Column> Columns => _columns ??= this.GetAllColums();

        public List<Table> GetTables(string entityPrefix, string[] entitySchemas, string[] entityTables, string[] entityViews, string[] entityViewPrefixes)
        {
            var tables = this.Tables.Where(p => !p.Name.StartsWith("_") && p.Name != "sysdiagrams").ToList();

            var columns = this.Columns;

            if (entitySchemas.Any())
            {
                tables = tables.Where(p => entitySchemas.Contains(p.Schema)).ToList();
                columns = columns.Where(p => entitySchemas.Contains(p.TableSchema)).ToList();
            }

            if (entityTables.Any() || entityViews.Any() || entityViewPrefixes.Any())
            {
                tables = tables
                    .Where(p => (entityTables.Any() && !p.IsView && entityTables.Contains(p.Name))                          //限定表
                        || (!entityTables.Any() && !p.IsView)                                                               //未限定表
                        || (entityViews.Any() && p.IsView && entityViews.Contains(p.Name))                                  //限定视图
                        || (entityViewPrefixes.Any() && p.IsView && entityViewPrefixes.Any(p1 => p.Name.StartsWith(p1)))    //限定视图前缀                                         //未限定视图
                        || (!entityViews.Any() && !entityViewPrefixes.Any() && p.IsView)                                    //未限定视图
                    ).ToList();
                _tables = tables;
                columns = columns.Where(p => tables.Any(p1 => p1.Name == p.TableName)).ToList();
                _columns = columns;
            }

            foreach (var column in columns)
            {
                this.InitColumn(column);
            }

            foreach (var table in tables)
            {
                table.Prefix = entityPrefix;

                var cols = columns
                    .Where(p => p.TableSchema == table.Schema && p.TableName == table.Name)
                    .OrderBy(p => p.IsPrimaryKey ? 0 : 1)
                    .ThenBy(p => p.OrdinalPosition)
                    .ToList();

                table.Columns = new List<Column>();

                foreach (var col in cols)
                {
                    var column = table.Columns.FirstOrDefault(p => p.Name == col.Name);

                    if (column == null)
                    {
                        col.TableSysTypeName = table.SysTypeName;

                        table.Columns.Add(col);
                    }
                    else
                    {
                        column.IsPrimaryKey = column.IsPrimaryKey || col.IsPrimaryKey;

                        if (col.IsForeignKey)
                        {
                            column.IsForeignKey = true;
                            column.ConstraintName = col.ConstraintName;
                            column.ForeignTableSchema = col.ForeignTableSchema;
                            column.ForeignTableName = col.ForeignTableName;
                            column.ForeignName = col.ForeignName;
                            column.ForeignTable = col.ForeignTable;
                            column.ForeignComment = col.ForeignComment;
                            column.InverseName = col.InverseName;
                            column.InverseComment = col.InverseComment;
                        }
                    }
                }

                table.IsDeleteable = table.Columns.Any(p => !p.IsNullable
                                                            && p.DbType == DbType.Boolean
                                                            && p.Name == "Deleted");
                table.IsOperateInfo = table.Columns.Any(p => p.IsNullable
                                                             && p.DbType == DbType.String
                                                             && p.Name == "Creator")
                                      && table.Columns.Any(p => p.IsNullable
                                                                && p.DbType == DbType.DateTime
                                                                && p.Name == "CreateTime")
                                      && table.Columns.Any(p => p.IsNullable
                                                                && p.DbType == DbType.String
                                                                && p.Name == "LastEditor")
                                      && table.Columns.Any(p => p.IsNullable
                                                                && p.DbType == DbType.DateTime
                                                                && p.Name == "LastEditTime");

                if (table.IsView)
                {
                    var col = table.Columns.FirstOrDefault(p => p.Name == "ID");

                    if (col != null)
                    {
                        col.IsPrimaryKey = true;
                    }
                }

                var keys = table.Columns.Where(p => p.IsPrimaryKey && p.Name == "ID").ToList();

                if (keys.Count == 1)
                {
                    table.IsPrimaryKey = true;
                    table.PrimaryKeySysTypeName = keys[0].SysTypeName;
                }
            }

            return tables;
        }

        public void Dispose()
        {
            this.DbConnection.Close();
            this.DbConnection.Dispose();
            GC.SuppressFinalize(this);
        }

        protected virtual void InitColumn(Column column)
        {
            var tables = this.Tables;
            var table = tables.First(p => p.Schema == column.TableSchema && p.Name == column.TableName);

            column.TableSysTypeName = table.SysTypeName;
            column.DbType = this.GetColumnDbType(column.DataType, column.MaxLength);

            var sysTypeName = string.Empty;

            switch (column.DbType)
            {
                case DbType.Boolean:
                    sysTypeName = "bool";
                    break;

                case DbType.Byte:
                    sysTypeName = "byte";
                    break;

                case DbType.Int16:
                    sysTypeName = "short";
                    break;

                case DbType.Int32:
                    sysTypeName = "int";
                    break;

                case DbType.Int64:
                    sysTypeName = "long";
                    break;

                case DbType.Single:
                    sysTypeName = "float";
                    break;

                case DbType.Double:
                    sysTypeName = "double";
                    break;

                case DbType.Decimal:
                    sysTypeName = "decimal";
                    break;

                case DbType.Date:
                case DbType.DateTime:
                    sysTypeName = typeof(DateTime).Name;
                    break;

                case DbType.Time:
                    sysTypeName = typeof(TimeSpan).Name;
                    break;

                case DbType.Binary:
                    sysTypeName = "byte[]";
                    break;

                case DbType.String:
                    sysTypeName = "string";
                    break;

                case DbType.Guid:
                    sysTypeName = typeof(Guid).Name;
                    break;

                case DbType.Xml:
                    sysTypeName = "string";
                    break;

                case DbType.Object:
                    sysTypeName = "object";
                    break;
            }

            if (column.DbType != DbType.Decimal)
            {
                column.NumPrecision = null;
                column.NumScale = null;
            }

            if (column.DbType != DbType.String)
            {
                column.MaxLength = null;
            }

            if (column.DbType == DbType.Int32
                && (Regex.IsMatch(column.Name, Enum_Regex1)
                || Regex.IsMatch(column.Name, Enum_Regex2)))
            {
                column.IsEnum = true;

                var tableName = column.TableName;

                if (table.IsView)
                {
                    var start = tableName.StartsWith("v", StringComparison.Ordinal)
                        ? 1
                        : tableName.StartsWith("Vw", StringComparison.Ordinal)
                            ? 2
                            : 0;

                    var end = tableName.IndexOf('_');

                    if (end > 0)
                    {
                        tableName = tableName.Substring(start, end - start);
                    }
                    else
                    {
                        tableName = tableName.Substring(start);
                    }
                }

                if (Regex.IsMatch(column.Name, Enum_Regex1))
                {
                    sysTypeName = column.Name.Substring(4);
                }
                else if (Regex.IsMatch(column.Name, Enum_Regex2))
                {
                    var length = column.Name.IndexOf("_") - 4;
                    sysTypeName = column.Name.Substring(4, length);
                }

                if (sysTypeName == "Status" || sysTypeName == "State" || sysTypeName == "Type" || sysTypeName == "Level" || sysTypeName == "Category")
                {
                    sysTypeName = tableName + sysTypeName;
                }
                else if (sysTypeName == "Flag" || sysTypeName == "Flags")
                {
                    sysTypeName = tableName + "Flag";
                }
                else if (sysTypeName.EndsWith("Flags"))
                {
                    sysTypeName = sysTypeName.Substring(0, sysTypeName.Length - 1);
                }
            }

            if (column.IsNullable)
            {
                sysTypeName += "?";
            }

            column.SysTypeName = sysTypeName;

            if (string.IsNullOrEmpty(column.Comment))
            {
                if (column.DbType == DbType.Boolean && !column.IsNullable && column.Name == "Deleted")
                {
                    column.Comment = "逻辑删除标记";
                }
                else if (column.DbType == DbType.String && column.IsNullable)
                {
                    switch (column.Name)
                    {
                        case "Creator":
                            column.Comment = "创建人";
                            break;

                        case "LastEditor":
                            column.Comment = "最后修改人";
                            break;
                    }
                }
                else if (column.DbType == DbType.DateTime && column.IsNullable)
                {
                    switch (column.Name)
                    {
                        case "CreateTime":
                            column.Comment = "创建时间";
                            break;

                        case "LastEditTime":
                            column.Comment = "最后修改时间";
                            break;
                    }
                }
            }

            if (column.IsForeignKey)
            {
                var foreignTable = tables.FirstOrDefault(p => p.Schema == column.ForeignTableSchema && p.Name == column.ForeignTableName);

                if (foreignTable == null)
                {
                    column.IsForeignKey = false;
                }
                else
                {
                    column.ForeignTable = foreignTable;

                    if (column.ConstraintName != null && !string.IsNullOrWhiteSpace(column.ConstraintName))
                    {
                        var array = column.ConstraintName.Split('_');

                        if (Regex.IsMatch(column.ConstraintName, FK_REGEX_FOREIGN_AND_INVERSE))
                        {
                            column.ForeignName = array[4];
                            column.InverseName = array[5];
                        }
                        else if (Regex.IsMatch(column.ConstraintName, FK_REGEX_FOREIGN))
                        {
                            column.ForeignName = array[4];
                        }
                        else if (Regex.IsMatch(column.ConstraintName, FK_REGEX))
                        {
                            column.ForeignName = array[2];
                            column.InverseName = array[1];
                        }
                        else
                        {
                            column.ForeignName = foreignTable.Name;
                            column.InverseName = column.TableName;
                        }
                    }
                    else
                    {
                        column.ForeignName = foreignTable.Name;
                        column.InverseName = column.TableName;
                    }

                    if (column.Comment != null && !string.IsNullOrEmpty(column.Comment))
                    {
                        if (column.Comment.EndsWith("ID", StringComparison.OrdinalIgnoreCase))
                        {
                            column.ForeignComment = column.Comment.Substring(0, column.Comment.Length - 2);
                        }
                        else
                        {
                            column.ForeignComment = column.Comment;
                        }
                    }

                    if (!string.IsNullOrEmpty(table.Comment))
                    {
                        column.InverseComment = column.InverseName switch
                        {
                            "Children" => $"子{table.Comment}",
                            "Descendants" => $"派生{table.Comment}",
                            _ => table.Comment,
                        };
                    }
                }
            }
        }

        protected abstract List<Table> GetAllTables();

        protected abstract List<Column> GetAllColums();

        protected abstract DbType GetColumnDbType(string dataType, long? length);
    }

#nullable restore
#>