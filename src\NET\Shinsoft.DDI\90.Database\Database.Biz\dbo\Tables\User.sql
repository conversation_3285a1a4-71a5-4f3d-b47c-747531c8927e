﻿--用户表
CREATE TABLE [dbo].[User] (
    [ID]					        UNIQUEIDENTIFIER    	    NOT NULL        CONSTRAINT [DF_User_ID]  DEFAULT (NEWSEQUENTIALID()),
    [GroupCompanyId]                UNIQUEIDENTIFIER            NOT NULL,
    [EnumType]				        INT					        NOT NULL,		--用户类型,枚举：员工，开发账号等。
    [LoginName]				        NVARCHAR(50)		        NOT NULL,		--登录名
    [DisplayName]			        NVARCHAR(100)		        NOT NULL,		--姓名
    [Email]					        NVARCHAR(50)		        NOT NULL,		--邮件
    [Mobile]				        NVARCHAR(20)		        NOT NULL,		--手机
    [EnumPwdType]	                INT					        NOT NULL,		--密码加密类型
    [PwdText]				        NVARCHAR(200)		        NOT NULL,		--文本密码
    [PwdExpireTime]                 DATETIME                    NULL,           --密码过期时间
    [IdentitySeed]			        INT		                    NOT NULL,		--身份验证种子，每次修改密码后改变
    [Culture]                       NVARCHAR(10)		        NOT NULL,
    [EnumStatus]			        INT					        NOT NULL,		--状态
    [DefaultCompanyId]              UNIQUEIDENTIFIER            NULL,
    [DefaultEmployeeId]             UNIQUEIDENTIFIER            NULL,           --默认员工ID,数据库不做外键,防止交叉外键影响保存，在代码中实现外键
    [WeChatId]                      NVARCHAR(200)               NOT NULL,
    [WeChatAvatar]                  NVARCHAR(500)               NOT NULL,
    [Deleted]				        BIT					        NOT NULL,
    [Creator]				        NVARCHAR(50)		        NULL, 
    [CreateTime]			        DATETIME			        NULL, 
    [LastEditor]			        NVARCHAR(50)		        NULL, 
    [LastEditTime]			        DATETIME			        NULL, 
    CONSTRAINT [PK_User] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_User_Company_00_GroupCompany] FOREIGN KEY ([GroupCompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_User_Company_01_DefaultCompany] FOREIGN KEY ([DefaultCompanyId]) REFERENCES [dbo].[Company] ([ID]),
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'用户',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = N'COLUMN',
    @level2name = N'EnumType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'登录名',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = N'COLUMN',
    @level2name = N'LoginName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'显示名',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = N'COLUMN',
    @level2name = N'DisplayName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'邮箱',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = N'COLUMN',
    @level2name = N'Email'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'手机',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = N'COLUMN',
    @level2name = N'Mobile'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'文本密码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = N'COLUMN',
    @level2name = N'PwdText'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'用户状态',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = N'COLUMN',
    @level2name = N'EnumStatus'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'密码加密类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = N'COLUMN',
    @level2name = N'EnumPwdType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'默认员工ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = N'COLUMN',
    @level2name = N'DefaultEmployeeId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'身份验证种子，每次修改密码后改变',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = N'COLUMN',
    @level2name = N'IdentitySeed'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'密码过期时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = N'COLUMN',
    @level2name = N'PwdExpireTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'默认公司ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'User',
    @level2type = N'COLUMN',
    @level2name = N'DefaultCompanyId'