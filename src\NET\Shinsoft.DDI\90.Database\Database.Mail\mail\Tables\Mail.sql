﻿CREATE TABLE [mail].[Mail] (
    [ID]					    UNIQUEIDENTIFIER			NOT NULL    CONSTRAINT [DF_Mail_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [<PERSON><PERSON><PERSON>rId]			    UNIQUEIDENTIFIER			NULL,
    [Trigger]				    NVARCHAR(50)			    NOT NULL,
    [ObjectType]				NVARCHAR(200)			    NOT NULL,
    [ObjectId]				    NVARCHAR(50)			    NOT NULL,
    [ObjectName]				NVARCHAR(50)			    NOT NULL,
	[IsHtmlBody]				BIT					        NOT NULL,	
    [From]					    NVARCHAR(500)			    NOT NULL,
    [RealFrom]				    NVARCHAR(500)			    NOT NULL,
    [DebugTo]				    NVARCHAR(500)			    NOT NULL,
    [To]						NVARCHAR(MAX)				NOT NULL,
    [Cc]						NVARCHAR(MAX)				NOT NULL,
    [Bcc]					    NVARCHAR(MAX)				NOT NULL,
    [Subject]				    NVARCHAR(1000)		        NOT NULL,
    [Content]				    NVARCHAR(MAX)				NOT NULL,
    [AttachmentPaths]		    VARCHAR(MAX)				NULL,
    [EnumStatus]				INT					        NOT NULL,
    [PlanSendTime]			    DATETIME				    NULL,
    [SendTime]				    DATETIME				    NULL,
    [SendCount]				    INT					        NOT NULL,
    [SendMessage]			    NVARCHAR(MAX)			    NOT NULL,
    [Deleted]				    BIT					        NOT NULL, 
    [Creator]				    NVARCHAR(50)			    NULL, 
    [CreateTime]				DATETIME				    NULL, 
    [LastEditor]				NVARCHAR(50)			    NULL, 
    [LastEditTime]			    DATETIME				    NULL, 
    CONSTRAINT [PK_Mail] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_Mail_MailServer] FOREIGN KEY ([MailServerId]) REFERENCES [mail].[MailServer] ([ID])
);
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'邮件',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'Mail',
    @level2type = NULL,
    @level2name = NULL