﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    /// <summary>
    /// 枚举扩展类
    /// 提供邮件触发器与业务类型之间的映射关系
    /// </summary>
    public static partial class EnumExtender
    {
        /// <summary>
        /// 邮件触发器与业务类型的映射字典
        /// </summary>
        private static readonly Dictionary<MailTrigger, BizType> _mailTriggerMappig = new()
        {
        };

        /// <summary>
        /// 将邮件触发器转换为对应的业务类型
        /// </summary>
        /// <param name="trigger">邮件触发器</param>
        /// <param name="defaultBizType">默认业务类型</param>
        /// <returns>对应的业务类型</returns>
        public static BizType ToBizType(this MailTrigger trigger, BizType defaultBizType = BizType.None)
        {
            if (_mailTriggerMappig.TryGetValue(trigger, out BizType bizType))
            {
                return bizType;
            }

            return defaultBizType;
        }
    }

    /// <summary>
    /// 邮件触发器枚举
    /// 用于定义邮件发送的触发条件和时机
    /// </summary>
    public enum MailTrigger
    {
        /// <summary>
        /// 无触发器
        /// 默认值，表示不触发邮件发送
        /// </summary>
        [Description("无")]
        None = 0,
    }
}