﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public abstract class OperateInfoModel : BaseModel
    {
        /// <summary>
        /// 创建人
        /// </summary>
        [Description("创建人")]
        [MapFromProperty(Reverse = false)]
        public virtual string? Creator { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        [JsonDateTime(true, true, Nullable = true)]
        [MapFromProperty(Reverse = false)]
        public virtual DateTime? CreateTime { get; set; }

        /// <summary>
        /// 最后修改人
        /// </summary>
        [Description("最后修改人")]
        [MapFromProperty(Reverse = false)]
        public virtual string? LastEditor { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Description("最后修改时间")]
        [JsonDateTime(true, true, Nullable = true)]
        [MapFromProperty(Reverse = false)]
        public virtual DateTime? LastEditTime { get; set; }
    }
}