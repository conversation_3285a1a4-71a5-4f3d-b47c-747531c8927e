﻿using Shinsoft.Core.AutoMapper;
using Shinsoft.DDI.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Serialization;

namespace Shinsoft.DDI.Common
{
    [MapFromType(typeof(ReceiverClient), Reverse = false)]
    [XmlRoot("DDI")]
    public class ReceiverCfg : IExtModel
    {
        [MapFromProperty(typeof(ReceiverClient), ReceiverClient.Foreigns.Receiver, Receiver.Columns.SdrCode)]
        public string? Code { get; set; }

        /// <summary>
        /// 服务端检测频率（无抓取任务时每隔x分钟向服务端发请求校验版本等信息），单位：分钟
        /// </summary>
        public int VerifyFrequency { get; set; }

        #region 程序版本

        [XmlIgnore]
        public bool AutoUpdate { get; set; }

        /// <summary>
        /// 程序是否自动升级：1升级/0不升级
        /// </summary>
        [MapFromIgnore]
        [XmlElement("AutoUpdate")]
        public int XmlAutoUpdate
        {
            get => this.AutoUpdate ? 1 : 0;
            set => this.AutoUpdate = value >= 0;
        }

        /// <summary>
        /// 需升级到的版本号
        /// </summary>
        public string UpdateToVersion { get; set; } = string.Empty;

        /// <summary>
        /// 升级程序的下载地址，[PASV]开头表示被动式升级，需要存在DDI_Update.exe和DDI_Update.xml
        /// </summary>
        public string UpdateUrl { get; set; } = string.Empty;

        #endregion 程序版本

        #region 运行计划

        /// <summary>
        /// 重复方式：Daily/Monthly
        /// </summary>
        public string ScheduleType { get; set; } = string.Empty;

        /// <summary>
        /// Monthly方式时指定运行的日，多日以逗号分隔
        /// </summary>
        public string ScheduleDay { get; set; } = string.Empty;

        /// <summary>
        /// 运行的时间点，格式为HH:MM，多时间点以逗号分隔，BOOT：程序启动时立即抓取数据
        /// </summary>
        public string ScheduleTime { get; set; } = string.Empty;

        #endregion 运行计划

        #region 数据读写方式

        /// <summary>
        /// 数据源获取方式：DB/FixPath/FixFile
        /// </summary>
        public string SourceType { get; set; } = string.Empty;

        /// <summary>
        /// 目标上传方式：HTTP/FTP/WebService/Path
        /// </summary>
        public string TargetType { get; set; } = string.Empty;

        /// <summary>
        /// 日志记录方式：Local/Remote/All
        /// </summary>
        public string LogType { get; set; } = string.Empty;

        #endregion 数据读写方式

        #region 数据源定义

        /// <summary>
        /// 数据库连接方式：Ado32/Odbc64/OleDb64/OracleClient64/SqlClient64
        /// </summary>
        public string DBConnectType { get; set; } = string.Empty;

        /// <summary>
        /// DB数据源的数据库连接，密码支持<PasswordName>以本地密码替换
        /// </summary>
        public string DBConnect { get; set; } = string.Empty;

        /// <summary>
        /// DB数据源买进的SQL查询语句，{[数据库连接]}为自定义数据库连接
        /// </summary>
        public string DBSqlB { get; set; } = string.Empty;

        /// <summary>
        /// DB数据源卖出的SQL查询语句，{[数据库连接]}为自定义数据库连接
        /// </summary>
        public string DBSqlS { get; set; } = string.Empty;

        /// <summary>
        /// DB数据源库存的SQL查询语句，{[数据库连接]}为自定义数据库连接
        /// </summary>
        public string DBSqlI { get; set; } = string.Empty;

        /// <summary>
        /// 固定的买进目录
        /// </summary>
        public string FixPathB { get; set; } = string.Empty;

        /// <summary>
        /// 固定的卖出目录
        /// </summary>
        public string FixPathS { get; set; } = string.Empty;

        /// <summary>
        /// 固定的库存目录
        /// </summary>
        public string FixPathI { get; set; } = string.Empty;

        /// <summary>
        /// 固定的买进文件
        /// </summary>
        public string FixFileB { get; set; } = string.Empty;

        /// <summary>
        /// 固定的卖出文件
        /// </summary>
        public string FixFileS { get; set; } = string.Empty;

        /// <summary>
        /// 固定的库存文件
        /// </summary>
        public string FixFileI { get; set; } = string.Empty;

        #endregion 数据源定义

        #region 目标定义

        /// <summary>
        /// WebService的地址
        /// </summary>
        public string WSUrl { get; set; } = string.Empty;

        /// <summary>
        /// WebService的用户名
        /// </summary>
        public string WSUsername { get; set; } = string.Empty;

        /// <summary>
        /// WebService的密码
        /// </summary>
        public string WSPassword { get; set; } = string.Empty;

        /// <summary>
        /// HTTP上传的地址
        /// </summary>
        public string HTTPUrl { get; set; } = string.Empty;

        /// <summary>
        /// HTTP上传的用户名
        /// </summary>
        public string HTTPUsername { get; set; } = string.Empty;

        /// <summary>
        /// HTTP上传的密码
        /// </summary>
        public string HTTPPassword { get; set; } = string.Empty;

        /// <summary>
        /// FTP的主机地址
        /// </summary>
        public string FTPServer { get; set; } = string.Empty;

        /// <summary>
        /// FTP的端口
        /// </summary>
        public string FTPPort { get; set; } = string.Empty;

        /// <summary>
        /// FTP类型
        /// </summary>
        public string FTPType { get; set; } = string.Empty;

        /// <summary>
        /// FTP的用户名
        /// </summary>
        public string FTPUsername { get; set; } = string.Empty;

        /// <summary>
        /// FTP的密码
        /// </summary>
        public string FTPPassword { get; set; } = string.Empty;

        /// <summary>
        /// FTP的路径
        /// </summary>
        public string FTPPath { get; set; } = string.Empty;

        /// <summary>
        /// Path方式时保存的目录
        /// </summary>
        public string SavePath { get; set; } = string.Empty;

        #endregion 目标定义

        /// <summary>
        /// 记录日志的地址
        /// </summary>
        public string LogHttpUrl { get; set; } = string.Empty;

        /// <summary>
        /// 记录日志的用户名
        /// </summary>
        public string LogHttpUsername { get; set; } = string.Empty;

        /// <summary>
        /// 记录日志的密码
        /// </summary>
        public string LogHttpPassword { get; set; } = string.Empty;

        /// <summary>
        /// HTTP代理服务器地址及端口，IE：使用IE代理配置
        /// </summary>
        public string HTTPProxy { get; set; } = string.Empty;

        /// <summary>
        /// HTTP代理服务器帐号
        /// </summary>
        public string HTTPProxyUsername { get; set; } = string.Empty;

        /// <summary>
        /// HTTP代理服务器密码
        /// </summary>
        public string HTTPProxyPassword { get; set; } = string.Empty;

        /// <summary>
        /// FTP代理服务器地址及端口，IE：使用IE代理配置
        /// </summary>
        public string FTPProxy { get; set; } = string.Empty;

        /// <summary>
        /// FTP代理服务器帐号
        /// </summary>
        public string FTPProxyUsername { get; set; } = string.Empty;

        /// <summary>
        /// FTP代理服务器密码
        /// </summary>
        public string FTPProxyPassword { get; set; } = string.Empty;

        /// <summary>
        /// 是否删除客户端的临时数据文件：1删除/0保留
        /// </summary>
        public int DeleteClientData { get; set; }

        /// <summary>
        /// CSV文件编码：Unicode/Ascii
        /// </summary>
        public string FileEncoding { get; set; } = string.Empty;

        /// <summary>
        /// 客户端环境变量，格式：变量名1=变量值1||变量名2=变量值2
        /// </summary>
        public string EnvironmentVariable { get; set; } = string.Empty;

        /// <summary>
        /// 运行模式：Normal/Track/Debug
        /// </summary>
        public string Mode { get; set; } = string.Empty;

        /// <summary>
        /// 程序重启时间，格式为HH:MM，只能一个时间点，为空表示不自动重启
        /// </summary>
        public string RestartTime { get; set; } = string.Empty;

        #region 其他配置

        [XmlElement("OtherXml")]
        public XmlNode OtherNode
        {
            get
            {
                XmlNode node = new XmlDocument().CreateNode(XmlNodeType.CDATA, "", "");

                node.InnerText = this.OtherXml;

                return node;
            }
            set
            {
                // 没有设置方法，则不会被xml序列化
            }
        }

        [XmlIgnore]
        public string OtherXml { get; set; } = string.Empty;

        #endregion 其他配置
    }
}