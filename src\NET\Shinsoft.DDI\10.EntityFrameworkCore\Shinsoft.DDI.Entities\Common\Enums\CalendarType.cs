﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace Shinsoft.DDI.Entities
{
    /// <summary>
    /// 日历类型枚举
    /// 用于定义日期的不同类型分类，如工作日、周末、节假日等
    /// </summary>
    public enum CalendarType
    {
        /// <summary>
        /// 无日历类型
        /// 默认值，表示未指定日期类型
        /// </summary>
        [Description("无")]
        None = 0,

        /// <summary>
        /// 工作日
        /// </summary>
        [Description("工作日")]
        Workday = 1,

        /// <summary>
        /// 周末
        /// </summary>
        [Description("周末")]
        Weekend = 10,

        /// <summary>
        /// 节假日
        /// </summary>
        [Description("节假日")]
        Holiday = 20,
    }
}