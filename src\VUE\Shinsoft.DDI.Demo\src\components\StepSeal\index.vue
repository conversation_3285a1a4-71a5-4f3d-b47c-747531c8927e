<script setup lang="ts">
var props = defineProps({
  type: {
    type: String,
    defalut: "approved"
  },
  width: {
    type: Number,
    defalut: 200
  },
  model: {
    type: Object,
    default: () => {
      return {};
    }
  }
});

let colors = {
  approved: "rgb(123, 245, 111)",
  current: "rgb(248, 224, 146)",
  pending: "rgb(175, 175, 175)",
  waiting: "rgb(228, 228, 228)",
  reject: "rgb(240, 2, 2)",
  forward: "rgba(57, 179, 250)"
};

// var opts = defineModel<object>("opts");
var componentWidth = reactive({
  width: props.width + (props.width <= 50 ? "em" : "px")
});
var style = reactive({
  width: props.width + (props.width <= 50 ? "em" : "px"),
  height: props.width + (props.width < 50 ? "em" : "px"),
  borderColor: colors[props.type],
  color: props.type !== "current" ? colors[props.type] : ""
});
// onMounted(() => {
//   style = {
//     width: props.width + (props.width <= 50 ? "em" : "px"),
//     height: props.width + (props.width < 50 ? "em" : "px"),
//     borderColor: colors[props.type],
//     color: props.type !== "current" ? colors[props.type] : ""
//   };
// });

// console.log(style.value);
</script>
<template>
  <div class="seal-com" :style="componentWidth">
    <div class="job-title">
      {{ model.position }}
    </div>
    <div class="seal-border" :style="style">
      <div>{{ model.name }}</div>
      <div>{{ model.date }}</div>
      <div>{{ model.state }}</div>
    </div>
  </div>
</template>
