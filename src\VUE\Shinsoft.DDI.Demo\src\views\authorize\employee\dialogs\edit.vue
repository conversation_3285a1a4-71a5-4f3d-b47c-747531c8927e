<script setup lang="ts">
import { authorizeApi } from "@/api/authorize";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { useEnumStoreHook } from "@/store/modules/enum";
import { ElMessage, FormInstance } from "element-plus";

defineOptions({
  name: "employee:edit"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // 标题：新建
  newTitle: {
    type: String,
    default: "新建员工"
  },
  // 标题：编辑
  editTitle: {
    type: String,
    default: "编辑员工"
  },
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.model.id ? `${props.editTitle} - ${state.model.displayName}` : props.newTitle;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      enumStatus: 1
    }
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  },
  // 枚举定义
  enums: {
    gender: [],
    employeeStatus: []
  },
  // 按钮权限
  btn: {
    save: computed(() => {
      return state.model.id
        ? userStore.hasAnyAuth(["Employee:Manage", "Employee:Manage:Edit"])
        : userStore.hasAnyAuth(["Employee:Manage", "Employee:Manage:Add"]);
    })
  },
  // 员工配置
  employeeCfg: {
    loginNameType: 0,
    emailSuffix: []
  },
  // 登录名相关配置
  loginName: {
    useLoginName: computed(() => cfg.employeeCfg.loginNameType === 0)
  },
  // 邮箱相关配置
  email: {
    useEmailSuffix: computed(() => cfg.employeeCfg.emailSuffix?.length > 0),
    prefixStyle: computed(() => (cfg.email.useEmailSuffix ? "width:200px" : "")),
    prefixClass: "",
    suffixClass: ""
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  model: cfg.default.model as Record<string, any>,
  email: {
    prefix: computed({
      get: () => {
        return cfg.email.useEmailSuffix && state.model.email?.indexOf("@") >= 0
          ? state.model.email.split("@")[0]
          : state.model.email;
      },
      set: val => {
        if (cfg.email.useEmailSuffix && state.model.email?.indexOf("@") >= 0) {
          const address = state.model.email.split("@");
          state.model.email = val + "@" + address[1];
        } else {
          state.model.email = val;
        }
      }
    }),
    suffix: computed({
      get: () => {
        return cfg.email.useEmailSuffix && state.model.email?.indexOf("@") >= 0
          ? `@${state.model.email.split("@")[1]}`
          : "";
      },
      set: val => {
        if (cfg.email.useEmailSuffix && state.model.email?.indexOf("@") >= 0) {
          const address = state.model.email.split("@");
          if (val) {
            state.model.email = address[0] + val;
          } else {
            state.model.email = address[0];
          }
        } else {
          state.model.email += val;
        }
      }
    })
  }
});

/**
 * 验证规则
 */
const rules = {
  form: {
    displayName: [
      { required: true, message: "请输入姓名", trigger: "blur" },
      { max: 100, message: "姓名长度不可以超过100个字符", trigger: "blur" }
    ],
    jobNo: [
      { required: cfg.employeeCfg.loginNameType === 1, message: "请输入工号", trigger: "blur" },
      { max: 50, message: "工号长度不可以超过50个字符", trigger: "blur" }
    ],
    email: [
      {
        required: true,
        message: cfg.email.useEmailSuffix ? "请输入邮箱和后缀" : "请输入邮箱和",
        trigger: "blur"
      },
      { max: 100, message: "邮箱长度不可以超过100个字符", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (value && cfg.email.useEmailSuffix) {
            cfg.email.prefixClass = "";
            cfg.email.suffixClass = "";

            if (value.indexOf("@") < 0) {
              cfg.email.prefixClass = "is-not-error";
              callback(new Error("请选择邮箱后缀"));
            } else if (!value.split("@")[0]) {
              cfg.email.suffixClass = "is-not-error";
              callback(new Error("请输入邮箱"));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: ["blue", "focus", "clear"]
      }
    ],
    enumStatus: [
      { required: true, message: "请选择员工状态", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (cfg.enums.employeeStatus.some(enumInfo => enumInfo.value == value)) {
            callback();
          } else {
            callback(new Error("员工状态错误，请重新选择"));
          }
        },
        trigger: "change"
      }
    ],
    mobile: [{ max: 20, message: "手机长度不可以超过20个字符", trigger: "blur" }],
    tel: [{ max: 100, message: "电话长度不可以超过50个字符", trigger: "blur" }],
    title: [{ max: 100, message: "职称长度不可以超过100个字符", trigger: "blur" }],
    position: [{ max: 100, message: "职务长度不可以超过100个字符", trigger: "blur" }],
    remark: [{ max: 500, message: "备注长度不可以超过500个字符", trigger: "blur" }]
  }
};

/**
 * 存储
 */
const userStore = useUserStoreHook();
const enumStore = useEnumStoreHook();

/**
 * 当前组件ref
 */
const formRef = ref<FormInstance>();

/**
 * 初始化组件（异步）
 */
/**
 * 初始化组件
 */
const init = () => {
  // 防止之前打开的数据残留，因此初始化时赋予model默认值
  initState();

  cfg.loading.form = true;

  const initGender = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("Gender").then(enumInfos => {
        cfg.enums.gender = enumInfos;
        resolve();
      });
    });
  };

  const initEmployeeStatus = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("EmployeeStatus").then(enumInfos => {
        cfg.enums.employeeStatus = enumInfos;
        resolve();
      });
    });
  };

  const initEmployeeCfg = async () => {
    return new Promise<void>(resolve => {
      authorizeApi.GetEmployeeCfg().then(res => {
        cfg.employeeCfg = res.data;
        if (cfg.email.useEmailSuffix) {
          cfg.default.model.email = cfg.employeeCfg.emailSuffix[0];
        }
        resolve();
      });
    });
  };

  const allInits = [initGender(), initEmployeeStatus(), initEmployeeCfg()];
  return new Promise<void>(() => {
    Promise.all(allInits).then(() => {
      cfg.loading.form = false;
      get();
    });
  });
};

/**
 * 设置model数据
 */
const setModel = (data: any) => {
  state.model = data;
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 初始化state数据
 */
const initState = () => {
  state.model = cfg.default.model;
};

/**
 * 清空mstate数据
 */
const clearState = () => {
  initState();
  formRef.value.clearValidate();
  formRef.value.resetFields();
};

/**
 * 获取model数据
 */
const get = () => {
  if (state.id) {
    cfg.loading.form = true;
    authorizeApi
      .GetEmployee(state.id)
      .then(res => {
        if (res.success) {
          setModel(res.data);
        } else {
          close();
        }
      })
      .finally(() => {
        cfg.loading.form = false;
      });
  }
};

/**
 * 新增数据
 */
const add = () => {
  cfg.loading.btn = true;

  const data = getModel();

  authorizeApi
    .AddEmployee(data, { messageBoxResult: true })
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh("新增成功", "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 更新数据
 */
const update = () => {
  cfg.loading.btn = true;

  const data = getModel();

  authorizeApi
    .UpdateEmployee(data, { messageBoxResult: true })
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh("保存成功", "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 按钮事件：【保存】
 */
const save = async () => {
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      if (state.model.id) {
        update();
      } else {
        add();
      }
    }
  });
};

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string) => {
  cfg.dialog.visible = true;
  state.id = id;
  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        v-loading="cfg.loading.form"
        :rules="rules.form"
        :model="state.model"
        label-position="right"
        :label-width="formLabelWidth"
        class="el-dialog-form"
      >
        <el-row :gutter="formGutter">
          <el-col v-if="cfg.loginName.useLoginName" :span="formColSpan">
            <el-form-item prop="loginName" label="登录名">
              <el-input
                v-model="state.model.loginName"
                clearable
                show-word-limit
                placeholder="登录名留空：表示不创建用户"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="displayName" label="姓名">
              <el-input
                v-model="state.model.displayName"
                clearable
                show-word-limit
                placeholder="姓名"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="formGutter">
          <el-col :span="formColSpan">
            <el-form-item prop="jobNo" label="工号">
              <el-input
                v-model="state.model.jobNo"
                clearable
                show-word-limit
                placeholder="工号"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="email" label="邮箱">
              <el-input
                v-model="state.email.prefix"
                clearable
                show-word-limit
                maxlength="50"
                :class="cfg.email.prefixClass"
                :style="cfg.email.prefixStyle"
                placeholder="邮箱"
              />
              <el-select
                v-if="cfg.email.useEmailSuffix"
                v-model="state.email.suffix"
                clearable
                :class="cfg.email.suffixClass"
                style="min-width: 100px; max-width: 200px; margin-left: 5px"
                placeholder="后缀"
              >
                <el-option
                  v-for="item in cfg.employeeCfg.emailSuffix"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="enumGender" label="性别">
              <el-select v-model="state.model.enumGender" clearable placeholder="性别">
                <el-option
                  v-for="item in cfg.enums.gender"
                  :key="item.value"
                  :label="item.text"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="enumStatus" label="状态">
              <el-select v-model="state.model.enumStatus" clearable placeholder="状态">
                <el-option
                  v-for="item in cfg.enums.employeeStatus"
                  :key="item.value"
                  :label="item.text"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="mobile" label="手机">
              <el-input
                v-model="state.model.model"
                clearable
                show-word-limit
                placeholder="手机"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="tel" label="电话">
              <el-input
                v-model="state.model.tel"
                clearable
                show-word-limit
                placeholder="电话"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="title" label="职称">
              <el-input
                v-model="state.model.title"
                clearable
                show-word-limit
                placeholder="职称"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="position" label="职务">
              <el-input v-model="state.model.title" clearable placeholder="职称" maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="formGutter">
          <el-col :span="24">
            <el-form-item prop="remark" label="备注">
              <el-input
                v-model="state.model.remark"
                clearable
                show-word-limit
                placeholder="备注"
                maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.save"
            class="save"
            :round="btnRound"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnSaveIcon)"
            @click="save()"
          >
            保存
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="css">
.is-not-error > .el-input__wrapper {
  box-shadow: #dcdfe6 !important;
}
</style>
