﻿CREATE TABLE [mail].[MailTemplate] (
    [ID]						UNIQ<PERSON><PERSON>ENTIFIER			NOT NULL    CONSTRAINT [DF_MailTemplate_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [Code]					    NVARCHAR(50)			    NULL,
	[IsHtmlBody]				BIT					        NOT NULL,	
    [Subject]				    NVARCHAR(1000)		        NOT NULL,
    [Content]				    NVARCHAR(MAX)				NOT NULL,
    [Deleted]				    BIT					        NOT NULL, 
    [Creator]				    NVARCHAR(50)			    NULL, 
    [CreateTime]				DATETIME				    NULL, 
    [LastEditor]				NVARCHAR(50)			    NULL, 
    [LastEditTime]			    DATETIME				    NULL, 
    CONSTRAINT [PK_MailTemplate] PRIMARY KEY CLUSTERED ([ID] ASC),
) 

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'邮件模板',
    @level0type = N'SCHEMA',
    @level0name = N'mail',
    @level1type = N'TABLE',
    @level1name = N'MailTemplate',
    @level2type = NULL,
    @level2name = NULL