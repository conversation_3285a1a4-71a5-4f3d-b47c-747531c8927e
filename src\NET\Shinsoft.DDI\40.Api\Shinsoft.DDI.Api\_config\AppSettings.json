﻿{
  "Title": "鑫磊DDI管理系统",
  "Debug": true, // 调试模式。调试模式时不验证用户密码
  "DebugPwd": "", // 调试模式下，用户密码，为空则不验证
  "UseSwagger": true, // 是否使用 Swagger
  "DefaultCompanyId": "88888888-8888-8888-8888-888888888888",
  "Secret": {
    "AesKey": "SHINSOFT-AES-KEY-AB4AB1EA134176E54DA", // AES加密秘钥（长度必须大于16位）
    "DesKey": "SHINSOFT-DES-KEY-AB4AB1EA134176E54DA" // DES加密秘钥（长度必须大于8位）
  },
  "UserAuthorize": {
    "SlidingMinutes": 60, //滑动过期时间（分钟），不设置默认为30分钟
    "AbsoluteRelativeToNow": null, //绝对过期时间（分钟）
    "RefreshToken": {
      "Allow": false, // 默认：true
      //"AesKey": "", // 不设置时取Secret.AesKey
      "ExpireMinutes": 120 //刷新令牌有效时间（分钟），不设置默认为60分钟
    },
    "AutoLogin": {
      "Allow": false, // 默认：false
      //"AesKey": "", // 不设置时取Secret.AesKey
      "ExpireDays": 14 //自动登录有效天数，不设置默认为14天
    }
  },
  "Jwt": {
    "Issuer": "Shinsoft",
    "Audience": "Shinsoft",
    "SecurityKey": "Shinsoft.DDI.Api_SecurityKey_AB2EAE17-4BD3-435B-BD25-E4449935B338",
    "ValidateIssuer": false,
    "ValidateAudience": false,
    "ValidateLifetime": true,
    "ValidateIssuerSigningKey": true,
    "ClockSkewMins": 5 //过期最大误差时间(分钟)，不设置则默认为5分钟
  },
  "Enums": [
    {
      "Assembly": "Shinsoft.DDI.Entities",
      "Namespace": "Shinsoft.DDI.Entities"
    },
    {
      "Assembly": "Shinsoft.DDI.Common",
      "Namespace": "Shinsoft.DDI.Common"
    },
    {
      "Assembly": "Shinsoft.Core",
      "Namespace": "Shinsoft.Core"
    },
    {
      "Assembly": "Shinsoft.Core",
      "Namespace": "Shinsoft.Core.NLog"
    }
  ],
  //SDR 授权设置
  "SDR": {
    "Debug": false, //默认false
    "CodeHeader": "ddicode", // 授权码
    "TokenHeader": "dditoken" // 授权令牌
  },
  "SysRedis": "127.0.0.1:6379,password=123456,defaultDatabase=5,ssl=false,prefix=sf:",
  "File": {
    "ImportFilePath": "E:\\Shinsoft\\DDI\\ImportFile\\",
    "PdfSavePath": "E:\\Shinsoft\\DDI\\PdfFile\\",
    "InterfaceFilePath": "DDIFile"
  },
  "SitePath": {
    "Base": "https://ddi.corp.shinsoft.net:56789/",
    "Api": "api",
    "Mobile": "",
    "PC": ""
  },
  "SsoSite": {
    "AesKey": null, // 不设置时取Secret.AesKey
    "ExpireMinute": 5 //SSO登录令牌有效时间（分钟），不设置默认为5分钟
  },
  // 系统默认配置（未配置则取默认值） BEGIN
  "GuidOrder": 2,
  "Paging": {
    "StartIndex": 1
  },
  "FileStore": {
    "Type": 2, //文件存储方式,1:文件,2:数据库 (默认：数据库)
    "BaseFolder": "E:\\Shinsoft\\DDI\\", //文件默认存储目录，仅当文件存储方式为文件时有效（默认：空）
    "SubFolder": "{yyyy}\\{MM}" //默认附件存储子目录（以"\"分割的字符串，其中{}内为日期格式化字符串）（默认："{yyyy}\\{MM}"）
  },
  "Mail": {
    "Send": false, //默认:true
    "Debug": true //默认:false
  }
  // 系统默认配置（未配置则取默认值） END
}