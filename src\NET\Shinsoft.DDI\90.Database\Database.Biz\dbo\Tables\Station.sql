﻿--岗位
CREATE TABLE [dbo].[Station] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_Station_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [ParentId]                  UNIQUEIDENTIFIER            NULL,
    [Uid]                       INT                         NOT NULL    IDENTITY (1, 1),
    [UidPath]                   VARCHAR(500)		        NOT NULL,
    [DepartmentId]              UNIQUEIDENTIFIER            NOT NULL,
    [PositionId]                UNIQUEIDENTIFIER            NOT NULL,
    [Name]                      NVARCHAR(50)		        NULL,
    [Valid]                     BIT                         NOT NULL,
    [StartDate]                 DATETIME                    NULL,
    [EndDate]                   DATETIME                    NULL,
    [Remark]                    NVARCHAR(500)               NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_Station] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Station_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
	CONSTRAINT [FK_Station_Station_00_Parent_Children] FOREIGN KEY ([ParentId]) REFERENCES [dbo].[Station] ([ID]),
	CONSTRAINT [FK_Station_Department_00_Department] FOREIGN KEY ([DepartmentId]) REFERENCES [dbo].[Department] ([ID]),
	CONSTRAINT [FK_Station_Position_00_Position] FOREIGN KEY ([PositionId]) REFERENCES [dbo].[Position] ([ID]),
);

GO
CREATE UNIQUE INDEX [IX_Station_Uid] ON [dbo].[Station]
(
	[Uid] ASC
);
GO
CREATE INDEX [IX_Station_UidPath] ON [dbo].[Station]
(
	[UidPath] ASC
);
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'岗位',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Station',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'部门ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Station',
    @level2type = N'COLUMN',
    @level2name = N'DepartmentId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'职位ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Station',
    @level2type = N'COLUMN',
    @level2name = N'PositionId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'有效性',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Station',
    @level2type = N'COLUMN',
    @level2name = N'Valid'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Station',
    @level2type = N'COLUMN',
    @level2name = 'Remark'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'上级岗位ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Station',
    @level2type = N'COLUMN',
    @level2name = N'ParentId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'岗位名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Station',
    @level2type = N'COLUMN',
    @level2name = N'Name'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'Uid路径',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Station',
    @level2type = N'COLUMN',
    @level2name = N'UidPath'
GO

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'开始日期',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Station',
    @level2type = N'COLUMN',
    @level2name = N'StartDate'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'结束日期',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Station',
    @level2type = N'COLUMN',
    @level2name = N'EndDate'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'唯一码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Station',
    @level2type = N'COLUMN',
    @level2name = N'Uid'
GO


