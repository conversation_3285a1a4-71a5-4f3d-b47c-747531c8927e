import { useHttpApi } from "./libs/httpApi";

const controller = "Log";

const api = useHttpApi(controller);

export const logApi = {
  QueryLog(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryLog", data, config);
  },
  GetLog(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetLog", { id }, config);
  },
  GetlogsSelector() {
    return api.get<QueryResult>("GetlogsSelector");
  }
};
