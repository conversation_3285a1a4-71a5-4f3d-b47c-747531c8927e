﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class DepartmentCostCenterMeta
    {
        /// <summary>
        /// 部门编码
        /// </summary>
        public string DepartmentCode { get; set; } = string.Empty;

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// 父显示文字
        /// </summary>
        public string? DepartmentText => this.DepartmentCode.IsEmpty() ? this.DepartmentName : $"[{this.DepartmentCode}] {this.DepartmentName}";

        /// <summary>
        /// 成本中心编码
        /// </summary>
        public string CostCenterCode { get; set; } = string.Empty;

        /// <summary>
        /// 成本中心名称
        /// </summary>
        public string CostCenterName { get; set; } = string.Empty;

        /// <summary>
        /// 父显示文字
        /// </summary>
        public string? CostCenterText => this.CostCenterCode.IsEmpty() ? this.CostCenterName : $"[{this.CostCenterCode}] {this.CostCenterName}";
    }
}
