﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    [ApiExplorerSettings(GroupName = "个人信息")]
    public class PersonController : BaseApiController<PersonBll>
    {
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取个人信息")]
        public BizResult<EmployeeModel> GetPersonInfo()
        {
            var result = new BizResult<EmployeeModel>();

            var entity = this.Repo.Get<Employee>(this.CurrentEmployeeId);
            if (entity == null)
            {
                result.Error(I18ns.Rule.Employee.NotExist);
            }
            else
            {
                var model = entity.Map<EmployeeModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 修改个人密码
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.ChangeMyPassword, Operate = "修改个人密码")]
        public BizResult ChangeMyPwd([FromBody] ChangeMyPwdRequest request)
        {
            var result = this.Repo.ChangeMyPassword(request.OldPwd, request.NewPwd);

            return result;
        }

        #region My EmployeeDelegate

        /// <summary>
        /// 查询个人代理设置
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询个人代理设置")]
        public QueryResult<EmployeeDelegateQuery> QueryMyDelegates([FromQuery] EmployeeDelegateFilter filter)
        {
            var result = new QueryResult<EmployeeDelegateQuery>();

            if (this.CurrentUser.Employee == null)
            {
                result.Error(I18ns.Rule.Employee.CanNotSetProxy);
            }
            else if (this.CurrentUser.IsAgent)
            {
                result.Error(I18ns.Rule.Employee.NoReightSetProxy);
            }
            else
            {
                var exps = this.NewExps<EmployeeDelegate>(p => p.EmployeeId == this.CurrentEmployeeId);

                if (filter.Dates?.Count > 0)
                {
                    var startDate = filter.Dates[0]?.Date;
                    var endDate = filter.Dates.Count > 1 ? filter.Dates[1]?.Date : null;

                    if (startDate != null)
                    {
                        exps.And(p => !p.EndDate.HasValue || p.EndDate >= startDate);
                    }

                    if (endDate != null)
                    {
                        exps.And(p => !p.StartDate.HasValue || p.StartDate <= endDate);
                    }
                }

                if (filter.Order.IsEmpty())
                {
                    filter.Order = $"{EmployeeDelegate.Columns.Valid} DESC";
                }

                result = this.Repo.GetDynamicQuery<EmployeeDelegate, EmployeeDelegateQuery>(filter, exps);
            }

            return result;
        }

        /// <summary>
        /// 获取个人代理设置
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取个人代理设置")]
        public BizResult<EmployeeDelegateModel> GetMyDelegate([FromQuery, Required] Guid id)
        {
            var result = new BizResult<EmployeeDelegateModel>();

            if (this.CurrentUser?.Employee == null)
            {
                result.Error(I18ns.Rule.Employee.CanNotSetProxy);
            }
            else if (this.CurrentUser.IsAgent)
            {
                result.Error(I18ns.Rule.Employee.NoReightSetProxy);
            }
            else
            {
                var entity = this.Repo.Get<EmployeeDelegate>(id);

                if (entity == null)
                {
                    result.Error(I18ns.Rule.Employee.ProxyNotExist);
                }
                else if (entity.EmployeeId != this.CurrentEmployeeId)
                {
                    result.Error(I18ns.Rule.Employee.NoRightViewProxy);
                }
                else
                {
                    var model = entity.Map<EmployeeDelegateModel>();

                    result.Data = model;
                }
            }

            return result;
        }

        /// <summary>
        /// 新增个人代理设置
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "新增个人代理设置")]
        public BizResult<EmployeeDelegateModel> AddMyDelegate(EmployeeDelegateModel model)
        {
            var entity = model.Map<EmployeeDelegate>();

            var result = this.Repo.AddMyEmployeeDelegate(entity);

            return result.Map<EmployeeDelegateModel>();
        }

        /// <summary>
        /// 修改个人代理设置
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "修改个人代理设置")]
        public BizResult<EmployeeDelegateModel> UpdateMyDelegate(EmployeeDelegateModel model)
        {
            var entity = model.Map<EmployeeDelegate>();

            var result = this.Repo.UpdateMyEmployeeDelegate(entity);

            return result.Map<EmployeeDelegateModel>();
        }

        /// <summary>
        /// 启/停用个人代理设置
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "启/停用个人代理设置")]
        public BizResult<EmployeeDelegateModel> ToggleMyDelegate(EmployeeDelegateModel model)
        {
            var result = this.Repo.ToggleMyEmployeeDelegate(model.ID);

            if (result.Success)
            {
                var entity = result.Data.Value();

                if (entity.Valid)
                {
                    this.SetLogMessage($"启用【{entity.Agent.DisplayName}】的代理设置");
                }
                else
                {
                    this.SetLogMessage($"停用【{entity.Agent.DisplayName}】的代理设置");
                }
            }

            return result.Map<EmployeeDelegateModel>();
        }

        /// <summary>
        /// 删除个人代理设置
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Query, Operate = "删除个人代理设置")]
        public BizResult DeleteMyDelegate(EmployeeDelegateModel model)
        {
            return this.Repo.DeleteMyEmployeeDelegate(model.ID);
        }

        #endregion My EmployeeDelegate
    }
}
