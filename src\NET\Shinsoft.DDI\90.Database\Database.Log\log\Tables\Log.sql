CREATE TABLE [log].[Log] (
    [ID]                        UNIQUEIDENTIFIER        NOT NULL        CONSTRAINT [DF_FileContent_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 NVARCHAR (50)           NULL,
    [CompanyCode]               NVARCHAR (50)           NULL,
    [CompanyName]               NVARCHAR (200)           NULL,
    [Category]                  NVARCHAR (50)           NULL,
    [Level]                     NVARCHAR (50)           NULL,
    [LogTime]                   DATETIME                NOT NULL,
    [Logger]                    NVARCHAR (500)          NULL,
    [PlatformId]                INT                     NULL,
    [ProgramId]                 INT                     NULL,
    [OperateId]                 INT                     NULL,
    [JobId]                     INT                     NULL,
    [Message]                   NVARCHAR (MAX)          NULL,
    [Duration]                  BIGINT                  NULL,
    [Remark]                    NVARCHAR (MAX)          NULL,
    [Culture]                   NVARCHAR (10)           NULL,
    [UserId]                    NVARCHAR (50)           NULL,
    [UserUniqueName]            NVARCHAR (50)           NULL,
    [UserDisplayName]           NVARCHAR (50)           NULL,
    [EmployeeId]                NVARCHAR (50)           NULL,
    [EmployeeName]              NVARCHAR (50)           NULL,
    [AgentId]                   NVARCHAR (50)           NULL,
    [AgentName]                 NVARCHAR (50)           NULL,
    CONSTRAINT [PK_Log] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_Log_LogJob] FOREIGN KEY ([JobId]) REFERENCES [log].[LogJob] ([ID]),
    CONSTRAINT [FK_Log_LogOperate] FOREIGN KEY ([OperateId]) REFERENCES [log].[LogOperate] ([ID]),
    CONSTRAINT [FK_Log_LogPlatform] FOREIGN KEY ([PlatformId]) REFERENCES [log].[LogPlatform] ([ID]),
    CONSTRAINT [FK_Log_LogProgram] FOREIGN KEY ([ProgramId]) REFERENCES [log].[LogProgram] ([ID])
);
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'日志',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'类别',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'Category';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'级别 ',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'Level';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'时间',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'LogTime';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'平台ID',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'PlatformId';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'程序ID',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'ProgramId';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'操作ID',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'OperateId';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'任务ID',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'JobId';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'消息',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'Message';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'执行时间',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'Duration';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'Remark';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'用户ID',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'UserId';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'用户唯一名',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'UserUniqueName';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'用户名称',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'UserDisplayName';
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'当前身份ID',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = 'EmployeeId'
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'当前身份名称',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'EmployeeName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'代理人ID',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = 'AgentId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'代理人名称',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'Log',
    @level2type = N'COLUMN',
    @level2name = N'AgentName'
