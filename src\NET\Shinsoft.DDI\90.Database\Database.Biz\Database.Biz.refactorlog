﻿<?xml version="1.0" encoding="utf-8"?>
<Operations Version="1.0" xmlns="http://schemas.microsoft.com/sqlserver/dac/Serialization/2012/02">
  <Operation Name="Rename Refactor" Key="71e314ee-51bc-4101-94c7-7795634171c3" ChangeDateTime="07/30/2022 15:15:50">
    <Property Name="ElementName" Value="[dbo].[PR].[PurchaseCatalogType]" />
    <Property Name="ElementType" Value="SqlSimpleColumn" />
    <Property Name="ParentElementName" Value="[dbo].[PR]" />
    <Property Name="ParentElementType" Value="SqlTable" />
    <Property Name="NewName" Value="PrPurchaseCatalogType" />
  </Operation>
  <Operation Name="Rename Refactor" Key="1f8c41a2-3e06-43e1-afb9-727dbd62347f" ChangeDateTime="07/30/2022 15:22:24">
    <Property Name="ElementName" Value="[dbo].[PR].[PrPurchaseCatalogType]" />
    <Property Name="ElementType" Value="SqlSimpleColumn" />
    <Property Name="ParentElementName" Value="[dbo].[PR]" />
    <Property Name="ParentElementType" Value="SqlTable" />
    <Property Name="NewName" Value="EnumPrPurchaseCatalogType" />
  </Operation>
  <Operation Name="Rename Refactor" Key="7b5c6b39-051d-44bc-a923-ed7b277bcac2" ChangeDateTime="07/30/2022 16:23:01">
    <Property Name="ElementName" Value="[dbo].[PR].[EnumPrPurchaseCatalogType]" />
    <Property Name="ElementType" Value="SqlSimpleColumn" />
    <Property Name="ParentElementName" Value="[dbo].[PR]" />
    <Property Name="ParentElementType" Value="SqlTable" />
    <Property Name="NewName" Value="EnumPurchaseType" />
  </Operation>
  <Operation Name="Rename Refactor" Key="70eebc04-8010-4ed9-8ccc-b3d94656da18" ChangeDateTime="08/01/2025 14:43:50">
    <Property Name="ElementName" Value="[dbo].[Receiver].[ShortName]" />
    <Property Name="ElementType" Value="SqlSimpleColumn" />
    <Property Name="ParentElementName" Value="[dbo].[Receiver]" />
    <Property Name="ParentElementType" Value="SqlTable" />
    <Property Name="NewName" Value="ReceiverTypeId" />
  </Operation>
  <Operation Name="Rename Refactor" Key="29da0b52-bf9d-4846-a942-3235769feb74" ChangeDateTime="08/04/2025 07:50:57">
    <Property Name="ElementName" Value="[dbo].[DistributorPurchaseMonthly].[UpstreamDistributorID]" />
    <Property Name="ElementType" Value="SqlSimpleColumn" />
    <Property Name="ParentElementName" Value="[dbo].[DistributorPurchaseMonthly]" />
    <Property Name="ParentElementType" Value="SqlTable" />
    <Property Name="NewName" Value="UpstreamDistributorId" />
  </Operation>
  <Operation Name="Rename Refactor" Key="f522460b-e7e6-4b7b-a545-fa3cb7e721c8" ChangeDateTime="08/04/2025 07:51:01">
    <Property Name="ElementName" Value="[dbo].[DistributorPurchaseMonthly].[DistributorID]" />
    <Property Name="ElementType" Value="SqlSimpleColumn" />
    <Property Name="ParentElementName" Value="[dbo].[DistributorPurchaseMonthly]" />
    <Property Name="ParentElementType" Value="SqlTable" />
    <Property Name="NewName" Value="DistributorId" />
  </Operation>
  <Operation Name="Rename Refactor" Key="7d578e4f-b0d5-421e-b9da-711a96e68ac9" ChangeDateTime="08/04/2025 07:54:34">
    <Property Name="ElementName" Value="[dbo].[DistributorSalesFlowMonthly].[PurchaseDate]" />
    <Property Name="ElementType" Value="SqlSimpleColumn" />
    <Property Name="ParentElementName" Value="[dbo].[DistributorSalesFlowMonthly]" />
    <Property Name="ParentElementType" Value="SqlTable" />
    <Property Name="NewName" Value="eDate" />
  </Operation>
  <Operation Name="Rename Refactor" Key="de223177-f3d3-43f1-887c-2dec4a277953" ChangeDateTime="08/04/2025 07:55:25">
    <Property Name="ElementName" Value="[dbo].[DistributorSalesFlowMonthly].[eDate]" />
    <Property Name="ElementType" Value="SqlSimpleColumn" />
    <Property Name="ParentElementName" Value="[dbo].[DistributorSalesFlowMonthly]" />
    <Property Name="ParentElementType" Value="SqlTable" />
    <Property Name="NewName" Value="SaleDate" />
  </Operation>
</Operations>