﻿CREATE TABLE [log].[LogApi] (
    [ID]                        UNIQUEIDENTIFIER        NOT NULL,
    [CompanyId]                 NVARCHAR (50)           NULL,
    [ApiType]                   NVARCHAR (20)           NULL,
    [Success]                   BIT                     NULL,
    [Input]                     NVARCHAR (MAX)          NOT NULL,
    [Output]                    NVARCHAR (MAX)          NOT NULL,
    [OutHeaders]                NVARCHAR (MAX)          NOT NULL,
    CONSTRAINT [PK_LogApi] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_LogApi_Log] FOREIGN KEY ([ID]) REFERENCES [log].[Log] ([ID])
);
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'API日志',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogApi';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'Api类型',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogApi',
    @level2type = N'COLUMN',
    @level2name = 'ApiType';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'输入',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogApi',
    @level2type = N'COLUMN',
    @level2name = N'Input';
GO

EXECUTE sp_addextendedproperty @name = N'MS_Description',
    @value = N'输出',
    @level0type = N'SCHEMA',
    @level0name = N'log',
    @level1type = N'TABLE',
    @level1name = N'LogApi',
    @level2type = N'COLUMN',
    @level2name = N'Output';

