<script setup lang="ts">
import { basicMasterDataApi } from "@/api/basicMasterData";
import { useUserStoreHook } from "@/store/modules/user";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "CostCenter:view"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // 标题：查看
  viewTitle: {
    type: String,
    default: "查看成本中心"
  },
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "180px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.model?.displayName
    ? `${props.viewTitle} - ${state.model.displayName}`
    : props.viewTitle;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {}
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  },
  // 按钮权限
  btn: {
    delete: computed(() => {
      return userStore.hasAnyAuth(["CostCenter:Manage", "CostCenter:Manage:Delete"]);
    })
  }
});

/**
 * 当前用户存储
 */
const userStore = useUserStoreHook();
/**
 * 数据变量
 */
const state = reactive({
  id: "",
  model: cfg.default.model as Record<string, any>
});
/**
 * 初始化组件（异步）
 */
const init = () => {
  state.model = cfg.default.model;

  return new Promise<void>(resolve => {
    resolve();
  });
};

/**
 * 获取model数据
 */
const get = (id: string) => {
  if (id) {
    cfg.loading.form = true;
    basicMasterDataApi
      .GetCostCenter(id)
      .then(res => {
        if (res.success) {
          state.model = res.data;
          emit("refresh", state.model);
        }
      })
      .finally(() => {
        cfg.loading.form = false;
      });
  }
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 按钮事件：【删除】
 */
const del = (data?: any) => {
  // 内部调用时，data为空
  // 外部调用时，需要传入{id:""}的对象
  data = data ?? getModel();

  if (data && data.id) {
    ElMessageBox.confirm(
      `${t("operate.confirm.delete")}<br />${t("operate.confirm.deleteHint")}`,
      `${t("operate.title.deleteConfirm")} - ${data.name}`,
      {
        showClose: false,
        closeOnClickModal: false,
        draggable: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: t("operate.ok"),
        cancelButtonText: t("operate.cancel"),
        type: "warning"
      }
    )
      .then(() => {
        cfg.loading.btn = true;
        basicMasterDataApi
          .DeleteCostCenter(data)
          .then(res => {
            // 仅提示
            if (res.success) {
              refresh(t("operate.message.success"), "success");
            }
          })
          .finally(() => {
            cfg.loading.btn = false;
          });
      })
      .catch(() => {
        close();
      });
  }
};
/**
 * 按钮事件：【删除】
 */
const valid = (data?: any) => {
  // 内部调用时，data为空
  // 外部调用时，需要传入{id:""}的对象
  data = data ?? getModel();

  if (data && data.id) {
    ElMessageBox.confirm(
      `${t("operate.confirm.valid")}<br />${t("operate.confirm.validHint")}`,
      `${t("operate.title.validConfirm")} - ${data.name}`,
      {
        showClose: false,
        closeOnClickModal: false,
        draggable: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: t("operate.ok"),
        cancelButtonText: t("operate.cancel"),
        type: "warning"
      }
    )
      .then(() => {
        cfg.loading.btn = true;
        basicMasterDataApi
          .SetCostCenterValid(data)
          .then(res => {
            // 仅提示
            if (res.success) {
              refresh(t("operate.message.success"), "success");
            }
          })
          .finally(() => {
            cfg.loading.btn = false;
          });
      })
      .catch(() => {
        close();
      });
  }
};

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = async (id: string) => {
  cfg.dialog.visible = true;
  if (id) {
    state.model.id = id;
  }
  init().then(() => {
    if (id) {
      get(id);
    }
  });
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  init();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open,
  del,
  valid
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      v-model:title="title"
      append-to-body
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <div>
        <el-form
          ref="formRef"
          v-loading="cfg.loading.form"
          :model="state.model"
          label-position="right"
          :label-width="formLabelWidth"
          class="el-dialog-form"
        >
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="name" :label="tt('Entity.CostCenter.Name')">
                <span>{{ state.model.name }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="code" :label="tt('Entity.CostCenter.Code')">
                <span>{{ state.model.code }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="parent.name" :label="tt('Entity.CostCenter.Parent')">
                <span>{{ state.model.parentName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="formColSpan">
              <el-form-item prop="SubCompany.name" :label="tt('Entity.CostCenter.SubCompany')">
                <span>{{ state.model.subCompanyName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="valid" :label="tt('Entity.CostCenter.Valid')">
                <span v-if="state.model.valid">{{ t("operate.yes") }}</span>
                <span v-if="!state.model.valid">{{ t("operate.no") }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.delete"
            class="delete"
            style="float: left"
            :round="btnRound"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.form"
            :icon="useRenderIcon(btnDeleteIcon)"
            @click="del()"
          >
            {{ t("operate.delete") }}
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
