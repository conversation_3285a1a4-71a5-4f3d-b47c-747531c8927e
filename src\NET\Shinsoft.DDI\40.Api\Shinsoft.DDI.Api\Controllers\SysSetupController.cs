﻿using Shinsoft.DDI.Bll;
using Shinsoft.Core.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    /// <summary>
    /// 系统设置
    /// </summary>
    [ApiExplorerSettings(GroupName = "设置")]
    public class SysSetupController : BaseApiController<SysSetupBll>
    {
        #region Company

        /// <summary>
        /// 查询公司
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Platform.Company.Company_Config)]
        [LogApi(ApiType.Query, Operate = "查询平台公司")]
        public QueryResult<CompanyQuery> QueryCompany([FromQuery] CompanyFilter filter)
        {
            var exps = this.NewExps<Company>();

            return this.Repo.GetDynamicQuery<Company, CompanyQuery>(filter, exps);
        }

        /// <summary>
        /// 获取平台公司
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Platform.Company.Company_Config)]
        [LogApi(ApiType.Query, Operate = "获取平台公司")]
        public BizResult<CompanyModel> GetCompany([FromQuery, Required] Guid id)
        {
            var result = new BizResult<CompanyModel>();

            var entity = this.Repo.Get<Company>(id);

            if (entity == null)
            {
                result.Error("平台公司不存在");
            }
            else
            {
                var model = entity.Map<CompanyModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增平台公司
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Platform.Company.Company_Config)]
        [LogApi(ApiType.Query, Operate = "新增平台公司")]
        public BizResult<CompanyModel> AddCompany(CompanyModel model)
        {
            var entity = model.Map<Company>();
            var cfg = model.Map<CompanyCfg>();

            var result = this.Repo.AddCompany(entity, cfg);

            return result.Map<CompanyModel>();
        }

        /// <summary>
        /// 编辑平台公司
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Platform.Company.Company_Config)]
        [LogApi(ApiType.Save, Operate = "编辑平台公司")]
        public BizResult<CompanyModel> UpdateCompany(CompanyModel model)
        {
            var entity = model.Map<Company>();
            var cfg = model.Map<CompanyCfg>();

            var result = this.Repo.UpdateCompany(entity, cfg);

            return result.Map<CompanyModel>();
        }

        /// <summary>
        /// 删除平台公司
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Platform.Company.Company_Config)]
        [LogApi(ApiType.Save, Operate = "删除平台公司")]
        public BizResult DeleteCompany(CompanyModel model)
        {
            return this.Repo.DeleteCompany(model.ID);
        }

        /// <summary>
        /// 设置平台公司有效性
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Platform.Company.Company_Config)]
        [LogApi(ApiType.Save, Operate = "设置平台公司有效性")]
        public BizResult<CompanyModel> SetCompanyValid(CompanyModel model)
        {
            var entity = model.Map<Company>();

            var result = this.Repo.SetCompanyValid(entity);

            return result.Map<CompanyModel>();
        }

        #endregion Company

        #region I18n

        /// <summary>
        /// 获取多语言树
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Auth(AuthCodes.Platform.Setup.I18n_Manage)]
        [Auth(AuthCodes.Platform.Setup.SysCulture_Manage)]
        [LogApi(ApiType.Query, Operate = "获取多语言树")]
        public BizResult<List<I18nTree>> GetI18nTree()
        {
            var entities = this.SysCache.I18ns;

            var i18nTrees = entities.Where(a => !a.ParentId.HasValue).Select(a => ToI18nTree(a, entities)).ToList();

            return this.BizResult(i18nTrees);
        }

        /// <summary>
        /// 组合树形结构
        /// </summary>
        /// <param name="i18n"></param>
        /// <param name="all"></param>
        /// <returns></returns>
        private I18nTree ToI18nTree(I18n i18n, List<I18n> all)
        {
            var tree = i18n.Map<I18nTree>();

            var children = all.Where(p => p.ParentId == i18n.ID).ToList();

            if (children.Count > 0)
            {
                tree.Children = children.Select(p => ToI18nTree(p, all)).ToList();
            }

            return tree;
        }

        /// <summary>
        /// 获取多语言主表
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Auth(AuthCodes.Platform.Setup.I18n_Manage)]
        [LogApi(ApiType.Query, Operate = "获取多语言主表")]
        public BizResult<I18nModel> GetI18n([FromQuery, Required] Guid id)
        {
            var result = new BizResult<I18nModel>();

            var entity = this.Repo.Get<I18n>(id);

            if (entity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, I18n.I18ns._Entity);
            }
            else
            {
                var model = entity.Map<I18nModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增多语言主表数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Auth(AuthCodes.Platform.Setup.I18n_Manage)]
        [LogApi(ApiType.Save, Operate = "新增多语言主表数据")]
        public BizResult<I18nModel> AddI18n(I18nModel model)
        {
            var entity = model.Map<I18n>();

            var result = this.Repo.AddI18n(entity);

            return result.Map<I18nModel>();
        }

        /// <summary>
        /// 编辑多语言主表数据
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Platform.Setup.I18n_Manage)]
        [LogApi(ApiType.Save, Operate = "编辑多语言主表数据")]
        public BizResult<I18nModel> UpdateI18n(I18nModel model)
        {
            var entity = model.Map<I18n>();

            var result = this.Repo.UpdateI18n(entity);

            return result.Map<I18nModel>();
        }

        /// <summary>
        /// 移除多语言
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Auth(AuthCodes.Platform.Setup.I18n_Manage)]
        [LogApi(ApiType.Save, Operate = "移除多语言")]
        public BizResult RemoveI18n(I18nModel model)
        {
            return this.Repo.RemoveI18n(model.ID);
        }

        #endregion I18n

        #region I18nCulture

        /// <summary>
        /// 查询多语言
        /// </summary>
        [HttpGet]
        [Auth(AuthCodes.Platform.Setup.I18n_Manage)]
        [Auth(AuthCodes.Platform.Setup.SysCulture_Manage)]
        [LogApi(ApiType.Query, Operate = "查询多语言")]
        public QueryResult<I18nCultureQuery> QueryI18nCulture([FromQuery] I18nCultureFilter filter)
        {
            var exps = this.NewExps<I18nCulture>();

            if (filter.I18nId.HasValue)
            {
                List<Guid> i18nIds = new List<Guid>();
                this.GetI18nAndDetailIds(filter.I18nId.Value, this.CompanyCache.I18ns, i18nIds);

                exps.And(a => i18nIds.Contains(a.I18nId));
            }

            return this.Repo.GetDynamicQuery<I18nCulture, I18nCultureQuery>(filter, exps);
        }

        private void GetI18nAndDetailIds(Guid i18nId, List<I18n> all, List<Guid> i18nIds)
        {
            i18nIds.Add(i18nId);

            var children = all.Where(p => p.ParentId == i18nId).ToList();

            if (children.Count > 0)
            {
                foreach (var child in children)
                {
                    GetI18nAndDetailIds(child.ID, all, i18nIds);
                }
            }
        }

        /// <summary>
        /// 获取多语言
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Auth(AuthCodes.Platform.Setup.I18n_Manage)]
        [Auth(AuthCodes.Platform.Setup.SysCulture_Manage)]
        [LogApi(ApiType.Query, Operate = "获取多语言")]
        public BizResult<I18nCultureModel> GetI18nCulture([FromQuery, Required] Guid id)
        {
            var result = new BizResult<I18nCultureModel>();

            var entity = this.Repo.Get<I18nCulture>(id);

            if (entity == null)
            {
                result.Error(I18ns.Message.Entity.Format.NotExist, I18nCulture.I18ns._Entity);
            }
            else
            {
                var model = entity.Map<I18nCultureModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增多语言
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Auth(AuthCodes.Platform.Setup.I18n_Manage)]
        [Auth(AuthCodes.Platform.Setup.SysCulture_Manage)]
        [LogApi(ApiType.Save, Operate = "新增多语言")]
        public BizResult<I18nCultureModel> AddI18nCulture(I18nCultureModel model)
        {
            var entity = model.Map<I18nCulture>();

            var result = this.Repo.AddI18nCulture(entity);

            return result.Map<I18nCultureModel>();
        }

        /// <summary>
        /// 编辑多语言
        /// </summary>
        [HttpPost]
        [Auth(AuthCodes.Platform.Setup.I18n_Manage)]
        [Auth(AuthCodes.Platform.Setup.SysCulture_Manage)]
        [LogApi(ApiType.Save, Operate = "编辑多语言")]
        public BizResult<I18nCultureModel> UpdateI18nCulture(I18nCultureModel model)
        {
            var entity = model.Map<I18nCulture>();

            var result = this.Repo.UpdateI18nCulture(entity);

            return result.Map<I18nCultureModel>();
        }

        /// <summary>
        /// 移除多语言
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Auth(AuthCodes.Platform.Setup.I18n_Manage)]
        [Auth(AuthCodes.Platform.Setup.SysCulture_Manage)]
        [LogApi(ApiType.Save, Operate = "移除多语言")]
        public BizResult RemoveI18nCulture(I18nCultureModel model)
        {
            return this.Repo.RemoveI18nCulture(model.ID);
        }

        #endregion I18nCulture
    }
}