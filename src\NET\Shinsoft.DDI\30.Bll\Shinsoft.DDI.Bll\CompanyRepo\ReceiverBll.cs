using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;
using System.ComponentModel;

namespace Shinsoft.DDI.Bll
{
    /// <summary>
    /// 收货方业务逻辑层
    /// 提供收货方相关的业务操作，包括增删改查、数据验证等功能
    /// </summary>
    [Description("收货方业务逻辑层")]
    public class ReceiverBll : BaseBll
    {
        #region Constructs

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="operatorUser">操作用户</param>
        public ReceiverBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="operatorUniqueName">操作用户唯一名称</param>
        public ReceiverBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="operatorUser">操作用户</param>
        public ReceiverBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="bll">业务逻辑层接口</param>
        public ReceiverBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs

        #region Receiver 收货方管理

        /// <summary>
        /// 添加收货方
        /// </summary>
        /// <param name="receiver">收货方实体</param>
        /// <returns>操作结果</returns>
        public BizResult<Receiver> AddReceiver(Receiver entity)
        {
            var result = new BizResult<Receiver>();

            // 验证必填字段
            if (entity.Name.IsEmpty())
            {
                result.Error("请输入收货方名称");
            }
            else
            {
                // 检查名称是否重复
                var existByName = this.GetEntity<Receiver>(p => p.Name == entity.Name);
                if (existByName != null)
                {
                    result.Error("已存在相同名称的收货方");
                }
            }

            if (result.Success)
            {
                // 验证统一社会信用代码是否重复（如果提供了）
                if (!string.IsNullOrEmpty(entity.UnifiedSocialCreditCode))
                {
                    var existByCredit = this.GetEntity<Receiver>(p => p.UnifiedSocialCreditCode == entity.UnifiedSocialCreditCode);
                    if (existByCredit != null)
                    {
                        result.Error("已存在相同统一社会信用代码的收货方");
                    }
                }
            }

            if (result.Success)
            {
                entity.ID = Guid.NewGuid();
                // 自动生成编码
                entity.Code = GetSerialNumber(ConstDefinition.CodePrefix.Receiver_CodePrefix);
                entity.SdrCode = entity.ID.ToString().Replace("-", "").ToLower();
                entity.EnumStatus = ReceiverStatus.Valid;
                entity = this.Add(entity);
                result.Data = entity;
            }

            return result;
        }

        /// <summary>
        /// 更新收货方
        /// </summary>
        /// <param name="entity">收货方实体</param>
        /// <returns>操作结果</returns>
        public BizResult<Receiver> UpdateReceiver(Receiver entity)
        {
            var result = new BizResult<Receiver>();

            var dbEntity = this.Get<Receiver>(entity.ID);
            if (dbEntity == null)
            {
                result.Error("收货方不存在");
            }
            else
            {

                // 验证收货方名称是否重复（排除自己）
                var existByName = this.GetEntity<Receiver>(p => p.Name == entity.Name && p.ID != entity.ID);
                if (existByName != null)
                {
                    result.Error("已存在相同名称的收货方");
                }

                if (result.Success)
                {
                    // 验证收货方编码是否重复（排除自己）
                    if (!string.IsNullOrEmpty(entity.Code))
                    {
                        var existByCode = this.GetEntity<Receiver>(p => p.Code == entity.Code && p.ID != entity.ID);
                        if (existByCode != null)
                        {
                            result.Error("已存在相同编码的收货方");
                        }
                    }
                }

                if (result.Success)
                {
                    // 验证统一社会信用代码是否重复（排除自己）
                    if (!string.IsNullOrEmpty(entity.UnifiedSocialCreditCode))
                    {
                        var existByCredit = this.GetEntity<Receiver>(p => p.UnifiedSocialCreditCode == entity.UnifiedSocialCreditCode && p.ID != entity.ID);
                        if (existByCredit != null)
                        {
                            result.Error("已存在相同统一社会信用代码的收货方");
                        }
                    }
                }

                if (result.Success)
                {
                    this.Update(entity);

                    result.Data = entity;
                }
            }

            return result;
        }

        /// <summary>
        /// 删除收货方
        /// </summary>
        /// <param name="id">收货方ID</param>
        /// <returns>操作结果</returns>
        public BizResult DeleteReceiver(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<Receiver>(id);
            if (dbEntity == null)
            {
                result.Error("收货方不存在");
            }
            else
            {
                var shipperReceiver = this.GetEntity<ShipperReceiver>(s => s.ReceiverId == id);
                if (shipperReceiver != null)
                {
                    result.Error("已与货主关联，不能删除");
                }
                else
                {
                    var distributorSalesFlowDaily = this.GetEntity<DistributorSalesFlowDaily>(s => s.ReceiverId == id || s.DistributorId == id);
                    if (distributorSalesFlowDaily != null)
                    {
                        result.Error("存在日流向信息，不能删除");
                    }

                    var distributorSalesFlowMonthly = this.GetEntity<DistributorSalesFlowMonthly>(s => s.ReceiverId == id || s.DistributorId == id);
                    if (distributorSalesFlowMonthly != null)
                    {
                        result.Error("存在月流向信息，不能删除");
                    }

                    var distributorPurchaseMonthly = this.GetEntity<DistributorPurchaseMonthly>(s => s.DistributorId == id || s.UpstreamDistributorId == id);
                    if (distributorPurchaseMonthly != null)
                    {
                        result.Error("存在月采购信息，不能删除");
                    }

                    var distributorPurchaseDaily = this.GetEntity<DistributorPurchaseDaily>(s => s.DistributorId == id || s.UpstreamDistributorId == id);
                    if (distributorPurchaseDaily != null)
                    {
                        result.Error("存在日采购信息，不能删除");
                    }

                    var distributorInventoryDaily = this.GetEntity<DistributorInventoryDaily>(s => s.DistributorId == id);
                    if (distributorInventoryDaily != null)
                    {
                        result.Error("存在库存信息，不能删除");
                    }

                    if (result.Success) {
                        var receiverAlias = this.GetEntity<ReceiverAlias>(r => r.ReceiverId == id);
                        if (receiverAlias != null) {
                            result.Error("存在收货方别名，不能删除");
                        }

                        var productAlias = this.GetEntity<ProductAlias>(r => r.ReceiverId == id);
                        if (productAlias != null)
                        {
                            result.Error("存在产品别名，不能删除");
                        }
                    }

                    if (result.Success) {
                        var receiverClient = this.GetEntity<ReceiverClient>(r => r.ID == id);
                        if (receiverClient != null)
                        {
                            result.Error("存在配置信息，不能删除");
                        }

                        var columnMapping = this.GetEntity<ColumnMapping>(r => r.ReceiverId == id);
                        if (columnMapping != null)
                        {
                            result.Error("存在配置列映射信息，不能删除");
                        }
                    }
                }
                if (result.Success)
                {
                    this.Delete(dbEntity);
                }
            }

            return result;
        }

        /// <summary>
        /// 获取收货方详情
        /// </summary>
        /// <param name="id">收货方ID</param>
        /// <returns>收货方实体</returns>
        public Receiver? GetReceiver(Guid id)
        {
            return this.GetEntity<Receiver>(p => p.ID == id);
        }

        #endregion Receiver
    }
}