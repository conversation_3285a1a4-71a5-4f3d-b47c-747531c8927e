<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>系统管理</el-breadcrumb-item>
        <el-breadcrumb-item>日志管理</el-breadcrumb-item>
        <el-breadcrumb-item>客户端日志查询</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="4">
          <el-input v-model="filter.receiverCode" placeholder="客户编码" clearable />
        </el-col>
        <el-col :span="4">
          <el-input v-model="filter.receiverName" placeholder="客户名称" clearable />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.message"
            placeholder="日志内容"
            clearable
          />
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="changeTimeRange"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-button icon="Search" @click="search" :loading="loading"
            >查询</el-button
          >
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 - 预留空白区域 -->
    <div class="action-container">
      <div class="action-buttons"></div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="dataList"
        stripe
        size="small"
        @sort-change="handleSortChange"
        style="width: 100%"
      >
      <el-table-column label="序号" width="60">
          <template #default="{ $index }">
            {{ (filter.pageIndex - 1) * filter.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="receiverCode" label="客户编码" width="150" />
        <el-table-column prop="receiverName" label="客户名称" min-width="150" />
        <el-table-column
          prop="logtime"
          label="日志时间"
          width="160"
          align="center"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.logTime) }}
          </template>
        </el-table-column>
         <el-table-column prop="message"  min-width="200" label="日志内容" />
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.pageIndex"
        v-model:page-size="filter.pageSize"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>
  </div>
</template>

<script>
import moment from "moment";
import { managementApi } from '@/api/managementApi'

export default {
  name: "ClientLogQuery",
  data() {
    return {
      loading: false,
      totalCount: 0,
      timeRange: [], // 时间范围选择器的值
      pageSizeOpts: [10, 20, 50, 100],
      filter: {
        pageIndex: 1,
        pageSize: 10,
        order: "logTime desc",
        receiverCode: "",
        receiverName: "",
        message: "",
        logTime: [], // 修正参数名，与后端保持一致
      },
      dataList: [],
    };
  },
  mounted() {
    this.queryDataList();
  },
  watch: {
    // 监听时间范围变化，确保与filter同步
    timeRange(newVal) {
      this.filter.logTime = newVal || [];
    }
  },
  methods: {
    // 时间范围变化处理
    changeTimeRange(value) {
      // 将时间范围赋值给filter.logTime，与后端期望的参数名保持一致
      this.filter.logTime = value || [];
    },

    // 查询
    search() {
      this.filter.pageIndex = 1;
      this.queryDataList();
    },

    // 获取列表数据
    queryDataList() {
      this.loading = true;
      managementApi.queryReceiverClientLog(this.filter)
        .then((response) => {
          if (response.data && response.data.success) {
            // axios拦截器已经处理了嵌套的data结构，直接访问datas即可
            this.dataList = response.data.datas || [];
            this.totalCount = response.data.total || 0;

          } else {
            this.dataList = [];
            this.totalCount = 0;
          }
        })
        .catch((error) => {
          this.dataList = [];
          this.totalCount = 0;

          console.error('查询客户端日志失败:', error);
          this.$message.error("查询客户端日志失败");
        })
        .finally(() => {
          this.loading = false;
        });
     
    },

    // 改变页面大小
    changePageSize(value) {
      this.filter.pageSize = value;
      this.filter.pageIndex = 1;
      this.queryDataList();
    },

    // 改变页码
    changePage(value) {
      this.filter.pageIndex = value;
      this.queryDataList();
    },

    // 处理排序变化
    handleSortChange({ prop, order }) {
      if (order) {
        const sortOrder = order === "ascending" ? "asc" : "desc";
        this.filter.order = `${prop} ${sortOrder}`;
      } else {
        this.filter.order = "serverTime desc"; // 默认排序
      }
      this.queryDataList();
    },

    // 序号计算方法
    indexMethod(index) {
      return (this.filter.pageIndex - 1) * this.filter.pageSize + index + 1;
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return "";
      return moment(dateTime).format("YYYY-MM-DD HH:mm:ss");
    },
  }
}
</script>