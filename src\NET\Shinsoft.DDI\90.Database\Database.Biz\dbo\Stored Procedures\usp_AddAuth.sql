CREATE PROCEDURE [dbo].[usp_AddAuth]
	@ID UNIQUEIDENTIFIER,
	@ParentFullCode NVARCHAR(50),
	@EnumProgramFlags INT,
	@EnumFlags INT,
	@EnumAuthTagType INT,
	@Code NVARCHAR(50),
	@Name NVARCHAR(200),
	@Valid BIT,
	@Ordinal INT,
	@Remark NVARCHAR(500),
	@CreateTime DATETIME = NULL
AS
	DECLARE @RootId UNIQUEIDENTIFIER
	DECLARE @ParentId UNIQUEIDENTIFIER
	DECLARE @ParentEnumType INT
	DECLARE @ParentUidPath VARCHAR (500)
	DECLARE @ParentOrdinal INT
    DECLARE @ParentPlatformFlag INT
    DECLARE @ParentRank INT


	SET @ParentFullCode = LTRIM(RTRIM(ISNULL(@ParentFullCode,'')))

	IF @ParentFullCode <> ''
	BEGIN

		SELECT
			@RootId = a.RootId,
			@ParentId = a.ID,
			@ParentEnumType = a.EnumType,
            @ParentPlatformFlag = (a.EnumFlags & 1),
            @ParentRank = a.Rank,
			@ParentUidPath = a.UidPath,
			@ParentOrdinal = a.Ordinal
		FROM dbo.Auth as a
		WHERE a.Deleted = 0
		AND  STUFF(
			(SELECT '/'+ g.Code + ''
				FROM dbo.Auth AS g
				WHERE a.UidPath LIKE g.UidPath + '%'
				ORDER by g.UidPath ASC for xml path(''))
			,1,1,'') = @ParentFullCode	

	END
	ELSE
	BEGIN
		SET @RootId = @ID
		SET @ParentUidPath = '|'
		SET @ParentOrdinal = 0
        SET @ParentPlatformFlag = 0
        SET @ParentRank = 0

	END

	IF @ParentId IS NULL AND @ParentFullCode <> ''
	BEGIN
		PRINT('没有找到上级权限：' + @ParentFullCode)
	END
	ELSE
	BEGIN
		INSERT INTO dbo.Auth (
			ID,
			RootId,
			ParentId,
			EnumProgramFlags,
			EnumType,
			EnumFlags,
			EnumAuthTagType,
			Code,
			Name,
            Rank,
			Valid,
			Ordinal,
			Remark,
			UidPath,
			Deleted,
			Creator,
			CreateTime,
			LastEditor,
			LastEditTime
		) VALUES (
			@ID,									-- ID - uniqueidentifier
			@RootId,								-- RootId - uniqueidentifier
			@ParentId,								-- ParentId - uniqueidentifier
			@EnumProgramFlags,						-- EnumProgramFlags - int
			1,										-- EnumType - int 0:Group,1:Permission
			@EnumFlags | @ParentPlatformFlag,		-- EnumFlags - int
			@EnumAuthTagType,						-- EnumTagType - int
			@Code,									-- Code - nvarchar(50)
			@Name,									-- Name - nvarchar(200)
            @ParentRank + 1,                        -- Rank - int
			@Valid,									-- Valid -bit
			@ParentOrdinal * 1000 + ISNULL(@Ordinal,0),	-- Ordinal - int
			ISNULL(@Remark,''),						-- Remark - nvarchar(500)
			N'',									-- UidPath - varchar(500)
			0,										-- Deleted - bit
			'',										-- Creator - nvarchar(50)
			ISNULL(@CreateTime,GETDATE()),			-- CreateTime - datetime
			'',										-- LastEditor - nvarchar(50)
			ISNULL(@CreateTime,GETDATE())			-- LastEditTime - datetime
		)

		UPDATE dbo.Auth SET
			UidPath = @ParentUidPath + CONVERT(VARCHAR(10),Uid) +  '|'
		WHERE ID = @ID

		IF @ParentId IS NOT NULL AND @ParentEnumType = 1 
		BEGIN
			UPDATE dbo.Auth SET
				EnumProgramFlags = 0,
				EnumType = 0
			WHERE ID = @ParentId
		END

	END


RETURN 0
