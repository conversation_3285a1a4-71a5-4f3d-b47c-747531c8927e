﻿CREATE TABLE [dbo].[ReviewTask]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_ReviewTask_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [ReviewIndexId]             UNIQUEIDENTIFIER            NOT NULL,
    [Code]                      NVARCHAR(50)                NOT NULL,
    [Name]                      NVARCHAR(100)               NOT NULL,
    [AuditorNames]              NVARCHAR(500)               NOT NULL,
    [Remark]                    NVARCHAR(200)               NOT NULL,
    [Sequence]                  FLOAT                       NOT NULL,
    [ReviewEmployeeId]          UNIQUEIDENTIFIER            NULL,
    [ReviewTime]                DATETIME                    NULL,
    [ReviewRemark]              NVARCHAR(200)               NOT NULL,
    [EnumReviewStatus]          INT                         NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_ReviewTask] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ReviewTask_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_ReviewTask_ReviewIndex_00_ReviewIndex_ReviewTasks] FOREIGN KEY ([ReviewIndexId]) REFERENCES [dbo].[ReviewIndex] ([ID]),
    CONSTRAINT [FK_ReviewTask_Employee_02_ReviewEmployee] FOREIGN KEY ([ReviewEmployeeId]) REFERENCES [dbo].[Employee] ([ID]),
)
