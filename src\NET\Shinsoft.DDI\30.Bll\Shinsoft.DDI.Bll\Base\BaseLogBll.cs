﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Shinsoft.DDI.Dal;

namespace Shinsoft.DDI.Bll
{
    public abstract class BaseLogBll : CompanyRepository<LogDbContext, Guid>, ICompanyRepo
    {
        #region Constructs

        protected BaseLogBll(IUser? operatorUser = null)
            : base(operatorUser)
        {
        }

        protected BaseLogBll(string operatorUniqueName)
            : base(operatorUniqueName)
        {
        }

        protected BaseLogBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        protected BaseLogBll(IRepo bll)
            : base(bll.LogRepo)
        {
        }

        #endregion Constructs

        #region IRepo

        protected virtual IRepo SysRepo => this.SysBll;
        protected virtual IRepo BizRepo => this.CompanyBll;
        protected virtual IRepo FileRepo => this.FileBll;
        protected virtual IRepo LogRepo => this.LogBll;
        protected virtual IRepo MailRepo => this.MailBll;

        protected virtual SysBll SysBll => this.GetRepo<SysBll>();
        protected virtual CompanyBll CompanyBll => this.GetRepo<CompanyBll>();
        protected virtual FileBll FileBll => this.GetRepo<FileBll>();
        protected virtual LogBll LogBll => this.GetRepo<LogBll>();
        protected virtual MailBll MailBll => this.GetRepo<MailBll>();

        IRepo IRepo.SysRepo => this.SysRepo;
        IRepo IRepo.BizRepo => this.BizRepo;
        IRepo IRepo.FileRepo => this.FileRepo;
        IRepo IRepo.LogRepo => this.LogRepo;
        IRepo IRepo.MailRepo => this.MailRepo;

        #endregion IRepo

        #region Current Company

        private Guid? _currentCompanyId;

        public override Guid CurrentCompanyId
        {
            get => _currentCompanyId ??= this.GetCurrentCompanyId();
            set => _currentCompanyId = value;
        }

        protected Guid GetCurrentCompanyId()
        {
            var companyId = this.OperatorUser?.OperatorCompanyId;

            if (!companyId.HasValue)
            {
                throw new InvalidOperationException($"【{this.GetType().Name}】无法获取当前公司ID");
            }

            return companyId.Value;
        }

        #endregion Current Company

        #region Operator User

        /// <summary>
        /// 操作用户（可能为空）
        /// </summary>
        public new User? OperatorUser => base.OperatorUser as User;

        /// <summary>
        /// 操作用户ID（可能为空）
        /// </summary>
        public virtual Guid? OperatorUserId => this.OperatorUser?.ID;

        /// <summary>
        /// 操作员工（可能为空）
        /// </summary>

        public virtual Employee? OperatorEmployee => this.OperatorUser?.Employee;

        /// <summary>
        /// 操作员工ID（可能为空）
        /// </summary>
        public virtual Guid? OperatorEmployeeId => this.OperatorEmployee?.ID;

        #endregion Operator User

        #region Current User

        /// <summary>
        /// 当前用户（操作用户为空时报错）
        /// </summary>
        public new User CurrentUser => (User)base.CurrentUser;

        /// <summary>
        /// 当前用户ID（操作用户为空时报错）
        /// </summary>
        public virtual Guid CurrentUserId => this.CurrentUser.ID;

        /// <summary>
        /// 当前员工（为空时报错）
        /// </summary>
        public virtual Employee CurrentEmployee => this.CurrentUser.Employee ?? throw new InvalidOperationException("当前员工不存在");

        /// <summary>
        /// 当前员工ID（为空时报错）
        /// </summary>
        public virtual Guid CurrentEmployeeId => this.CurrentEmployee.ID;

        #endregion Current User

        #region SysCache

        private SysCache? _sysCache = null;

        protected virtual SysCache SysCache => _sysCache ??= this.GetRequiredService<SysCache>();

        #endregion SysCache

        #region CompanyCache

        protected virtual CompanyCachePool CompanyCachePool => this.GetRequiredService<CompanyCachePool>();

        private CompanyCache? _companyCache;

        protected virtual CompanyCache CompanyCache
        {
            get
            {
                if (_companyCache == null)
                {
                    if (this.CurrentCompanyId.IsEmpty())
                    {
                        throw new InvalidOperationException($"【{this.GetType().Name}】:无法获取当前公司ID");
                    }

                    _companyCache = this.CompanyCachePool.GetCompanyCache(this.CurrentCompanyId);
                }

                return _companyCache;
            }
        }

        #endregion CompanyCache

        protected override DbContextOptionsBuilder BuildDbOptions()
        {
            var optionsBuilder = new DbContextOptionsBuilder();

            optionsBuilder.UseDbConfig(nameof(LogDbContext), Config.DecryptConnStr);

            return optionsBuilder;
        }
    }
}
