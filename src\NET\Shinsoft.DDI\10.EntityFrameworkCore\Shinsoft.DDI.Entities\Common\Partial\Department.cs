﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public partial class Department : IUid<Department>, IOrder
    {
        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent

        #region IOrder

        IComparable IOrder.Order => this.Name;

        #endregion IOrder

        [NotMapped, XmlIgnore, JsonIgnore]
        public string Text => this.Code.IsEmpty() ? this.Name : $"[{this.Code}] {this.Name}";

        [NotMapped, XmlIgnore, JsonIgnore]
        public List<CostCenter> CostCenters { get; set; } = new();
    }
}