﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      throwConfigExceptions="false"
      internalLogLevel="info"
      internalLogFile="\[Log]\Shinsoft\DDI\internal-nlog-AspNetCore.txt">

	<!-- enable asp.net core layout renderers -->
	<extensions>
		<add assembly="NLog.Web.AspNetCore" />
	</extensions>

	<!-- the targets to write to -->
	<targets async="true">
		<!-- write logs to file  -->
		<target xsi:type="File" name="errorFile" fileName="\[Log]\Shinsoft\DDI\Errors\Error-${shortdate}.log"
				layout="${longdate} | ${message}  | ${logger} |  ${aspnet-MVC-Controller} | ${aspnet-MVC-Action} | ${event-properties:item=Operate} | ${event-properties:item=Input}  |  ${event-properties:item=Duration}  |${exception:format=tostring}  | ${stacktrace}" />

		<target xsi:type="Database" name="database"
				dbProvider="Microsoft.Data.SqlClient"
				commandType="StoredProcedure"
				commandText="[log].[sp_WriteLog]">
			<parameter name="@ID" layout="${event-properties:item=ID}" />
			<parameter name="@CompanyId" layout="${event-properties:item=CompanyId}" />
			<parameter name="@CompanyCode" layout="${event-properties:item=CompanyCode}" />
			<parameter name="@CompanyName" layout="${event-properties:item=CompanyName}" />
			<parameter name="@Category" layout="${event-properties:item=Category}" />
			<parameter name="@Level" layout="${level}" />
			<parameter name="@LogTime" layout="${date}" />
			<parameter name="@Logger" layout="${logger}" />
			<parameter name="@Platform" layout="${event-properties:item=Platform}" />
			<parameter name="@Program" layout="${event-properties:item=Program}" />
			<parameter name="@Operate" layout="${event-properties:item=Operate}" />
			<parameter name="@Job" layout="${event-properties:item=Job}" />
			<parameter name="@Message" layout="${message}" />
			<parameter name="@Duration" layout="${event-properties:item=Duration}" />
			<parameter name="@Remark" layout="${event-properties:item=Remark}" />
			<parameter name="@Culture" layout="${event-properties:item=Culture}" />
			<parameter name="@UserId" layout="${event-properties:item=UserId}" />
			<parameter name="@UserUniqueName" layout="${event-properties:item=UserUniqueName}" />
			<parameter name="@UserDisplayName" layout="${event-properties:item=UserDisplayName}" />
			<parameter name="@EmployeeId" layout="${event-properties:item=EmployeeId}" />
			<parameter name="@EmployeeName" layout="${event-properties:item=EmployeeName}" />
			<parameter name="@AgentId" layout="${event-properties:item=AgentId}" />
			<parameter name="@AgentName" layout="${event-properties:item=AgentName}" />
			<parameter name="@Controller" layout="${aspnet-MVC-Controller}" />
			<parameter name="@Action" layout="${aspnet-MVC-Action}" />
			<parameter name="@Method" layout="${aspnet-Request-Method}" />
			<parameter name="@Headers" layout="${aspnet-Request-Headers}" />
			<parameter name="@Url" layout="${aspnet-Request-Url}" />
			<parameter name="@IsAuthenticated" layout="${aspnet-User-IsAuthenticated}" />
			<parameter name="@QueryString" layout="${aspnet-Request-QueryString}" />
			<parameter name="@UserAgent" layout="${aspnet-Request-UserAgent}" />
			<parameter name="@Identity" layout="${aspnet-User-Identity}" />
			<parameter name="@Host" layout="${aspnet-Request-Host}" />
			<parameter name="@IP" layout="${aspnet-Request-IP}" />
			<parameter name="@TargetName" layout="${event-properties:item=TargetName}" />
			<parameter name="@TargetType" layout="${event-properties:item=TargetType}" />
			<parameter name="@TargetId" layout="${event-properties:item=TargetId}" />
			<parameter name="@ApiType" layout="${event-properties:item=ApiType}" />
			<parameter name="@Success" layout="${event-properties:item=Success}" />
			<parameter name="@Input" layout="${event-properties:item=Input}" />
			<parameter name="@OutHeaders" layout="${aspnet-Response-Headers}" />
			<parameter name="@Output" layout="${event-properties:item=Output}" />
			<parameter name="@InterfaceSite" layout="${event-properties:item=InterfaceSite}" />
			<parameter name="@InterfaceName" layout="${event-properties:item=InterfaceName}" />
			<parameter name="@InterfaceAddress" layout="${event-properties:item=InterfaceAddress}" />
			<parameter name="@InterfaceMethod" layout="${event-properties:item=InterfaceMethod}" />
			<parameter name="@InterfaceHeader" layout="${event-properties:item=InterfaceHeader}" />
			<parameter name="@InterfaceRequest" layout="${event-properties:item=InterfaceRequest}" />
			<parameter name="@InterfaceResponse" layout="${event-properties:item=InterfaceResponse}" />
		</target>

		<target xsi:type="Database" name="errorDatabase"
				dbProvider="Microsoft.Data.SqlClient"
				commandType="StoredProcedure"
				commandText="[log].[sp_WriteExceptionLog]">
			<parameter name="@ID" layout="${event-properties:item=ID}" />
			<parameter name="@CompanyId" layout="${event-properties:item=CompanyId}" />
			<parameter name="@CompanyCode" layout="${event-properties:item=CompanyCode}" />
			<parameter name="@CompanyName" layout="${event-properties:item=CompanyName}" />
			<parameter name="@Category" layout="${event-properties:item=Category}" />
			<parameter name="@Level" layout="${level}" />
			<parameter name="@LogTime" layout="${date}" />
			<parameter name="@Logger" layout="${logger}" />
			<parameter name="@Platform" layout="${event-properties:item=Platform}" />
			<parameter name="@Program" layout="${event-properties:item=Program}" />
			<parameter name="@Operate" layout="${event-properties:item=Operate}" />
			<parameter name="@Job" layout="${event-properties:item=Job}" />
			<parameter name="@Message" layout="${message}" />
			<parameter name="@Duration" layout="${event-properties:item=Duration}" />
			<parameter name="@Remark" layout="${event-properties:item=Remark}" />
			<parameter name="@Culture" layout="${event-properties:item=Culture}" />
			<parameter name="@UserId" layout="${event-properties:item=UserId}" />
			<parameter name="@UserUniqueName" layout="${event-properties:item=UserUniqueName}" />
			<parameter name="@UserDisplayName" layout="${event-properties:item=UserDisplayName}" />
			<parameter name="@EmployeeId" layout="${event-properties:item=EmployeeId}" />
			<parameter name="@EmployeeName" layout="${event-properties:item=EmployeeName}" />
			<parameter name="@AgentId" layout="${event-properties:item=AgentId}" />
			<parameter name="@AgentName" layout="${event-properties:item=AgentName}" />
			<parameter name="@Controller" layout="${aspnet-MVC-Controller}" />
			<parameter name="@Action" layout="${aspnet-MVC-Action}" />
			<parameter name="@Method" layout="${aspnet-Request-Method}" />
			<parameter name="@Headers" layout="${aspnet-Request-Headers}" />
			<parameter name="@Url" layout="${aspnet-Request-Url}" />
			<parameter name="@IsAuthenticated" layout="${aspnet-User-IsAuthenticated}" />
			<parameter name="@QueryString" layout="${aspnet-Request-QueryString}" />
			<parameter name="@UserAgent" layout="${aspnet-Request-UserAgent}" />
			<parameter name="@Identity" layout="${aspnet-User-Identity}" />
			<parameter name="@Host" layout="${aspnet-Request-Host}" />
			<parameter name="@IP" layout="${aspnet-Request-IP}" />
			<parameter name="@TargetName" layout="${event-properties:item=TargetName}" />
			<parameter name="@TargetType" layout="${event-properties:item=TargetType}" />
			<parameter name="@TargetId" layout="${event-properties:item=TargetId}" />
			<parameter name="@ApiType" layout="${event-properties:item=ApiType}" />
			<parameter name="@Success" layout="${event-properties:item=Success}" />
			<parameter name="@Input" layout="${event-properties:item=Input}" />
			<parameter name="@OutHeaders" layout="${aspnet-Response-Headers}" />
			<parameter name="@Output" layout="${event-properties:item=Output}" />
			<parameter name="@InterfaceSite" layout="${event-properties:item=InterfaceSite}" />
			<parameter name="@InterfaceName" layout="${event-properties:item=InterfaceName}" />
			<parameter name="@InterfaceAddress" layout="${event-properties:item=InterfaceAddress}" />
			<parameter name="@InterfaceMethod" layout="${event-properties:item=InterfaceMethod}" />
			<parameter name="@InterfaceHeader" layout="${event-properties:item=InterfaceHeader}" />
			<parameter name="@InterfaceRequest" layout="${event-properties:item=InterfaceRequest}" />
			<parameter name="@InterfaceResponse" layout="${event-properties:item=InterfaceResponse}" />
			<parameter name="@Exception" layout="${exception}" />
			<parameter name="@StackTrace" layout="${exception:StackTrace}" />
		</target>
	</targets>

	<!-- rules to map from logger name to target -->
	<rules>
		<logger name="Microsoft.*" minlevel="Info" writeTo="" final="true" />
		<logger name="Quartz.*" minlevel="Info" writeTo="" final="true" />
		<logger name="*" minlevel="Error" writeTo="errorFile" />
		<logger name="*" minlevel="Error" writeTo="errorDatabase" />
		<logger name="*" minlevel="Info" maxlevel="Warn" writeTo="database" />
	</rules>
</nlog>