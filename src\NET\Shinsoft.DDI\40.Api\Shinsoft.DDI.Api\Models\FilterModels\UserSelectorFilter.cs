﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class UserSelectorFilter
    {
        /// <summary>
        /// 关键词（编码,名称）
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(User), User.Columns.LoginName, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(User), User.Columns.DisplayName, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(User), User.Columns.Email, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(User), User.Columns.Mobile, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }
    }
}