﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public partial class Auth : IParent<Auth>, IOrder
    {
        #region IParent

        IComparable IParent.ID => this.ID;

        IComparable? IParent.ParentId => this.ParentId;

        #endregion IParent

        #region IOrder

        IComparable IOrder.Order => this.Ordinal;

        #endregion IOrder

        [NotMapped, JsonIgnore, XmlIgnore]
        public List<Auth> InvisibleChilren { get; set; } = [];
    }
}