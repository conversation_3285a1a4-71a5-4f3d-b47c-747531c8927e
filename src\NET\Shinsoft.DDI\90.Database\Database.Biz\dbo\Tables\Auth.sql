--权限
CREATE TABLE [dbo].[Auth] (
    [ID]                            UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_Auth_ID]  DEFAULT (NEWSEQUENTIALID()),
    [RootId]                        UNIQUEIDENTIFIER            NOT NULL,
    [ParentId]                      UNIQUEIDENTIFIER            NULL,
    [Uid]                           INT                         NOT NULL    IDENTITY (1, 1) ,
    [UidPath]                       VARCHAR (500)               NOT NULL,
    [EnumProgramFlags]              INT                         NOT NULL,
    [EnumType]                      INT                         NOT NULL,
    [EnumFlags]                     INT                         NOT NULL,
    [EnumAuthTagType]               INT                         NOT NULL,
    [Code]                          NVARCHAR(50)                NOT NULL,
    [Name]                          NVARCHAR(200)               NOT NULL,
    [Rank]                          INT                         NOT NULL,
    [Valid]                         BIT                         NOT NULL,
    [Ordinal]                       INT                         NOT NULL,
    [Remark]                        NVARCHAR(500)               NOT NULL,
    [Deleted]                       BIT                         NOT NULL,
    [Creator]                       NVARCHAR(50)                NULL,
    [CreateTime]                    DATETIME                    NULL,
    [LastEditor]                    NVARCHAR(50)                NULL,
    [LastEditTime]                  DATETIME                    NULL,
    CONSTRAINT [PK_Auth] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_Auth_Auth_00_Root] FOREIGN KEY ([RootId]) REFERENCES [dbo].[Auth] ([ID]),
    CONSTRAINT [FK_Auth_Auth_01_Parent_Children] FOREIGN KEY ([ParentId]) REFERENCES [dbo].[Auth] ([ID]),
);
GO

CREATE UNIQUE INDEX [IX_Auth_Uid] ON [dbo].[Auth]
(
	[Uid] ASC
);
GO

CREATE INDEX [IX_Auth_UidPath] ON [dbo].[Auth]
(
	[UidPath] ASC
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'权限',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Auth',
    @level2type = NULL,
    @level2name = NULL
