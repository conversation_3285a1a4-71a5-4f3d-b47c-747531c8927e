CREATE TABLE [dbo].[CostCenter] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_CostCenter_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [ParentId]                  UNIQUEIDENTIFIER            NULL,
    [Uid]                       INT                         NOT NULL IDENTITY (1, 1) ,
    [UidPath]                   VARCHAR (500)               NOT NULL,
    [SubCompanyId]              UNIQUEIDENTIFIER            NULL,
    [Rank]                      INT                         NOT NULL,
    [Code]                      NVARCHAR(50)                NOT NULL,
    [Name]                      NVARCHAR(50)                NOT NULL,
    [Valid]                     BIT                         NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_CostCenter] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_CostCenter_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_CostCenter_CostCenter_00_Parent_Children] FOREIGN KEY ([ParentId]) REFERENCES [dbo].[CostCenter] ([ID]),
    CONSTRAINT [FK_CostCenter_SubCompany_00_SubCompany] FOREIGN KEY ([SubCompanyId]) REFERENCES [dbo].[SubCompany] ([ID]),
);
GO
CREATE UNIQUE INDEX [IX_CostCenter_Uid] ON [dbo].[CostCenter]
(
	[Uid] ASC
);
GO

CREATE INDEX [IX_CostCenter_UidPath] ON [dbo].[CostCenter]
(
	[UidPath] ASC
);

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'成本中心',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CostCenter',
    @level2type = NULL,
    @level2name = NULL

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'唯一码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CostCenter',
    @level2type = N'COLUMN',
    @level2name = N'Uid'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'唯一码路径',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CostCenter',
    @level2type = N'COLUMN',
    @level2name = N'UidPath'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'分公司ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CostCenter',
    @level2type = N'COLUMN',
    @level2name = N'SubCompanyId'

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'等级',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CostCenter',
    @level2type = N'COLUMN',
    @level2name = N'Rank'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'编码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CostCenter',
    @level2type = N'COLUMN',
    @level2name = N'Code'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CostCenter',
    @level2type = N'COLUMN',
    @level2name = N'Name'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'有效性',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CostCenter',
    @level2type = N'COLUMN',
    @level2name = N'Valid'

GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'上级成本中心ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'CostCenter',
    @level2type = N'COLUMN',
    @level2name = N'ParentId'
