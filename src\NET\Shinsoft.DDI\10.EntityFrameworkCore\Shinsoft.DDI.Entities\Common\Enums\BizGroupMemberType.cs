﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace Shinsoft.DDI.Entities
{
    /// <summary>
    /// 业务组成员类型枚举
    /// 用于定义业务组中成员的不同类型
    /// </summary>
    public enum BizGroupMemberType
    {
        /// <summary>
        /// 无成员类型
        /// 默认值，表示未指定成员类型
        /// </summary>
        [Description("无")]
        None = 0,

        /// <summary>
        /// 员工
        /// </summary>
        [Description("员工")]
        Employee = 1,

        /// <summary>
        /// 岗位
        /// </summary>
        [Description("岗位")]
        Station = 2
    }
}
