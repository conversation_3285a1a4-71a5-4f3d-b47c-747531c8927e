CREATE TABLE [dbo].[EmployeeDelegate]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_EmployeeDelegate_ID]  DEFAULT (NEWSEQUENTIALID()),
    [CompanyId]                 UNIQUEIDENTIFIER            NOT NULL,
    [EmployeeId]                UNIQUEIDENTIFIER            NOT NULL,
    [AgentId]                   UNIQUEIDENTIFIER            NOT NULL,
    [StartDate]                 DATE                        NULL,
    [EndDate]                   DATE                        NULL,
    [Valid]				        BIT					        NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_EmployeeDelegate] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_EmployeeDelegate_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_EmployeeDelegate_Employee_00_Employee] FOREIGN KEY ([EmployeeId]) REFERENCES [dbo].[Employee] ([ID]),
    CONSTRAINT [FK_EmployeeDelegate_Employee_01_Agent] FOREIGN KEY ([AgentId]) REFERENCES [dbo].[Employee] ([ID]),
)
