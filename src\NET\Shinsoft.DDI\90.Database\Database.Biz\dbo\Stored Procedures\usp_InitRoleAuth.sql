CREATE PROCEDURE [dbo].[usp_InitRoleAuth]
    @CompanyCode NVARCHAR(50),
	@RoleCode NVARCHAR(50)
AS
    INSERT INTO dbo.RoleAuth
    (
        CompanyId,
        RoleId,
        AuthId,
        AllowAllTags
    )
    SELECT DISTINCT
        r.CompanyId,
	    r.ID AS [RoleId],
	    ca.AuthId,
	    CASE 
		    WHEN ca.EnumAuthTagType <> 0 THEN 1
		    ELSE NULL
	    END AS [AllowAllTags]
    FROM dbo.Role AS r
    LEFT JOIN dbo.vCompanyAuth AS ca ON ca.CompanyId = r.CompanyId
    LEFT JOIN dbo.RoleAuth AS ra ON ra.RoleId = r.ID AND ra.AuthId = ca.AuthId
    WHERE r.Deleted = 0
    AND ca.Valid = 1
    AND ca.CompanyCode = @CompanyCode
    AND r.Code = @RoleCode
    AND ra.ID IS NULL

RETURN 0
