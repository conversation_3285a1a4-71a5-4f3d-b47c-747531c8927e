﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public enum I18nCategory
    {
        None = 0,

        /// <summary>
        /// 枚举
        /// </summary>
        [Description("枚举")]
        Enum = 1,

        /// <summary>
        /// 枚举
        /// </summary>
        [Description("枚举")]
        Entity = 2,

        /// <summary>
        /// 验证规则
        /// </summary>
        [Description("验证规则")]
        Rule = 11,

        /// <summary>
        /// 消息
        /// </summary>
        [Description("消息")]
        Message = 12,

        /// <summary>
        /// UI
        /// </summary>
        [Description("UI")]
        UI = 13
    }
}
