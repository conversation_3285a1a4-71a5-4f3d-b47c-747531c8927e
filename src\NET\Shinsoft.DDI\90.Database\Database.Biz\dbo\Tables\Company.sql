﻿CREATE TABLE [dbo].[Company] (
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_Company_ID]  DEFAULT (NEWSEQUENTIALID()),
    [GroupId]                   UNIQUEIDENTIFIER            NOT NULL,
    [ParentId]                  UNIQUEIDENTIFIER            NULL,
    [Uid]                       INT                         NOT NULL    IDENTITY (1, 1) ,
    [UidPath]                   VARCHAR (500)               NOT NULL,
    [Rank]                      INT                         NOT NULL,
    [EnumType]                  INT                         NOT NULL,
    [Code]                      NVARCHAR(50)                NOT NULL,
    [Name]                      NVARCHAR(200)               NOT NULL,
    [ShortName]                 NVARCHAR(50)                NOT NULL,
    [StdCurrency]               NVARCHAR(10)                NOT NULL,
    [Valid]                     BIT                         NOT NULL,
    [Deleted]				    BIT					        NOT NULL,
    [Creator]				    NVARCHAR(50)		        NULL, 
    [CreateTime]			    DATETIME			        NULL, 
    [LastEditor]			    NVARCHAR(50)		        NULL, 
    [LastEditTime]			    DATETIME			        NULL, 
    CONSTRAINT [PK_Company] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_Company_Company_00_Group_Companies] FOREIGN KEY ([GroupId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_Company_Company_01_Parent_Children] FOREIGN KEY ([ParentId]) REFERENCES [dbo].[Company] ([ID])
);
GO

CREATE UNIQUE INDEX [IX_Company_Uid] ON [dbo].[Company]
(
	[Uid] ASC
);
GO

CREATE INDEX [IX_Company_UidPath] ON [dbo].[Company]
(
	[UidPath] ASC
);
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'公司',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Company',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'集团公司ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Company',
    @level2type = N'COLUMN',
    @level2name = N'GroupId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'母公司ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Company',
    @level2type = N'COLUMN',
    @level2name = N'ParentId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'唯一码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Company',
    @level2type = N'COLUMN',
    @level2name = N'Uid'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'等级',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Company',
    @level2type = N'COLUMN',
    @level2name = N'Rank'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Company',
    @level2type = N'COLUMN',
    @level2name = N'EnumType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'编码',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Company',
    @level2type = N'COLUMN',
    @level2name = N'Code'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'名称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Company',
    @level2type = N'COLUMN',
    @level2name = N'Name'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'简称',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Company',
    @level2type = N'COLUMN',
    @level2name = N'ShortName'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'唯一码路径',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Company',
    @level2type = N'COLUMN',
    @level2name = N'UidPath'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'本位币',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'Company',
    @level2type = N'COLUMN',
    @level2name = N'StdCurrency'
GO
