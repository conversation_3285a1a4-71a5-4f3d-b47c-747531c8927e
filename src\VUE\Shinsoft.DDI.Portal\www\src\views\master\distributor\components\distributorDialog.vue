<template>
  <el-dialog :title="isEdit ? '编辑收货方' : '新增收货方'" v-model="dialogVisible" width="900px" :close-on-click-modal="false"
    @close="handleClose">
    <el-form ref="distributorFormRef" :model="distributorForm" :rules="formRules" label-width="140px">
      <!-- 基本信息 -->
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="收货方名称" prop="name">
            <el-input v-model="distributorForm.name" placeholder="请输入收货方名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 授权码和收货方Code - 仅编辑时显示且不可编辑 -->
      <el-row :gutter="16" v-if="isEdit">
        <el-col :span="12">
          <el-form-item label="授权码" prop="sdrCode">
            <el-input v-model="distributorForm.sdrCode" placeholder="授权码" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收货方Code" prop="code">
            <el-input v-model="distributorForm.code" placeholder="收货方Code" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 收货方类型和省市县 - 放在一行 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="收货方类型" prop="receiverTypeCascader">
            <el-cascader v-model="distributorForm.receiverTypeCascader" :options="receiverTypeCascaderOptions"
              placeholder="请选择收货方类型" clearable @change="handleReceiverTypeChange" :props="{ expandTrigger: 'hover' }" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="省市县" prop="locationCascader">
            <el-cascader v-model="distributorForm.locationCascader" :options="locationCascaderOptions"
              placeholder="请选择省市县（可选择任意级别）" clearable @change="handleLocationChange" :props="{
                expandTrigger: 'hover',
                checkStrictly: true,
                emitPath: true
              }" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 上级单位 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="上级单位" prop="medicineGroupId">
            <el-input v-model="distributorForm.medicineGroupName" placeholder="请选择上级单位" readonly
              @click="handleSelectMedicineGroup">
              <template #suffix>
                <el-icon class="el-input__icon" style="cursor: pointer;" @click="handleSelectMedicineGroup">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 联系信息 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="telephone">
            <el-input v-model="distributorForm.telephone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电子邮箱" prop="eMail">
            <el-input v-model="distributorForm.eMail" placeholder="请输入电子邮箱" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 其他信息 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
            <el-input v-model="distributorForm.unifiedSocialCreditCode" placeholder="请输入统一社会信用代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮政编码" prop="postalCode">
            <el-input v-model="distributorForm.postalCode" placeholder="请输入邮政编码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="网络地址" prop="netAddress">
            <el-input v-model="distributorForm.netAddress" placeholder="请输入网络地址" />
          </el-form-item>
        </el-col>
        <!-- 停用时间 - 仅编辑时显示且不可修改 -->
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="停用时间" prop="stopTime">
            <el-date-picker v-model="distributorForm.stopTime" type="datetime" placeholder="停用时间"
              format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 医院等级和级别 - 仅当选择医疗机构类型时显示 -->
      <el-row :gutter="16" v-if="showHospitalFields">
        <el-col :span="12">
          <el-form-item label="医院等级" prop="hospitalGradeId">
            <el-select v-model="distributorForm.hospitalGradeId" placeholder="请选择医院等级" clearable>
              <el-option v-for="item in hospitalGradeList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="医院级别" prop="hospitalLevelId">
            <el-select v-model="distributorForm.hospitalLevelId" placeholder="请选择医院级别" clearable>
              <el-option v-for="item in hospitalLevelList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 详细地址 -->
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="distributorForm.address" type="textarea" :rows="3" placeholder="请输入详细地址" />
      </el-form-item>

      <!-- 备注 -->
      <el-form-item label="备注" prop="remark">
        <el-input v-model="distributorForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 上级单位选择弹框 -->
  <el-dialog v-model="medicineGroupDialogVisible" title="选择上级单位" width="900px" append-to-body>
    <div class="medicine-group-selector">
      <!-- 搜索框 -->
      <div class="search-bar" style="margin-bottom: 16px;">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-cascader v-model="medicineGroupSelectedCity" :options="medicineGroupCityList" placeholder="省份/城市"
              clearable @change="handleMedicineGroupCascaderChange" :props="{
                expandTrigger: 'hover',
                checkStrictly: true,
                emitPath: true
              }" />
          </el-col>
          <el-col :span="6">
            <el-input v-model="medicineGroupSearchForm.nameKeyword" placeholder="请输入名称" clearable>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-input v-model="medicineGroupSearchForm.codeKeyword" placeholder="请输入编码" clearable>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-button icon="Search" @click="handleMedicineGroupSearch">查询</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 表格 -->
      <el-table :data="medicineGroupList" height="350" @row-click="handleMedicineGroupRowClick" highlight-current-row
        style="cursor: pointer;">
        <el-table-column prop="name" label="收货方名称" width="200" />
        <el-table-column prop="code" label="Code" width="150" />
        <el-table-column prop="provinceName" label="省份" width="100" />
        <el-table-column prop="cityName" label="城市" width="100" />
        <el-table-column prop="receiverTypeName" label="收货方类型">
          <template #default="{ row }">
            {{ row.receiverTypeLevelOneName }}-{{ row.receiverTypeLevelTwoName }}-{{ row.receiverTypeLevelThreeName }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="{ row }">
            <el-button circle size="small" @click.stop="handleSelectMedicineGroupItem(row)">
              <el-icon>
                <CircleCheck />
              </el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div style="margin-top: 16px; display: flex; justify-content: flex-end;">
        <el-pagination v-model:current-page="medicineGroupPagination.pageIndex"
          v-model:page-size="medicineGroupPagination.pageSize" :total="medicineGroupPagination.total"
          :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleMedicineGroupPageSizeChange" @current-change="handleMedicineGroupPageChange" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="medicineGroupDialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage, linkEmits } from 'element-plus'
import { Search, Check, TopLeft } from '@element-plus/icons-vue'
import { receiverApi } from '@/api/receiverApi'
import { selectorApi } from '@/api/selectorApi'

export default {
  name: 'receiverDialog',
  components: {
    Search,
    Check
  },
  props: {
    // 对话框显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 记录ID，传入则为编辑模式，不传入则为新增模式
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    // 表单引用
    const distributorFormRef = ref(null)

    // 是否为编辑模式
    const isEdit = computed(() => !!props.recordId)

    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })

    // 医院字段显示控制
    const showHospitalFields = ref(false)

    // 表单数据
    const distributorForm = reactive({
      sdrCode: '',
      code: '',
      name: '',
      provinceId: null,
      cityId: null,
      countyId: null,
      receiverTypeId: null,
      unifiedSocialCreditCode: '',
      address: '',
      telephone: '',
      eMail: '',
      postalCode: '',
      netAddress: '',
      stopTime: null,
      hospitalGradeId: null,
      hospitalLevelId: null,
      medicineGroupId: null, // 上级单位ID
      medicineGroupName: '', // 上级单位名称
      remark: '',
      // 级联选择器辅助字段
      receiverTypeCascader: [],
      locationCascader: []
    })

    // 表单验证规则
    const formRules = reactive({
      name: [
        { required: true, message: '请输入收货方名称', trigger: ['blur', 'change'] },
        { max: 100, message: '收货方名称不能超过100个字符', trigger: ['blur', 'change'] }
      ],
      receiverTypeCascader: [
        { required: true, message: '请选择收货方类型', trigger: ['blur', 'change'] }
      ],
      locationCascader: [
        { required: true, message: '请至少选择省份', trigger: ['blur', 'change'] }
      ],
      telephone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: ['blur', 'change'] }
      ],
      eMail: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
      ],
      postalCode: [
        { pattern: /^\d{6}$/, message: '请输入正确的邮政编码', trigger: ['blur', 'change'] }
      ],
      remark: [
        { required: false, message: '请输入备注', trigger: ['blur', 'change'] },
        { max: 500, message: '备注不能超过500个字符', trigger: ['blur', 'change'] }
      ]
    })

    // 级联选择器数据
    const receiverTypeCascaderOptions = ref([])
    const locationCascaderOptions = ref([])
    // 医院等级和级别数据
    const hospitalGradeList = ref([])
    const hospitalLevelList = ref([])

    // 上级单位选择相关数据
    const medicineGroupDialogVisible = ref(false)
    const medicineGroupList = ref([])
    const medicineGroupSelectedCity = ref([]) // 级联选择器选中的省市
    const medicineGroupCityList = ref([])     // 省市级联选择器数据
    const medicineGroupSearchForm = reactive({
      provinceId: '',          // 省份ID
      cityId: '',              // 城市ID
      nameKeyword: '',         // 名称搜索关键词
      codeKeyword: ''          // 编码搜索关键词
    })
    const medicineGroupPagination = reactive({
      pageIndex: 1,
      pageSize: 10,
      total: 0
    })

    /**
     * 加载收货方类型选项
     */
    const loadReceiverTypeOptions = async () => {
      try {
        const response = await selectorApi.ReceiverTypeSelect()
        if (response.data) {
          receiverTypeCascaderOptions.value = response.data || []
        }
      } catch (error) {
        console.error('加载收货方类型选项失败:', error)
        // 确保在出错时也有默认值
        receiverTypeCascaderOptions.value = []
      }
    }

    /**
     * 加载省市县级联选项
     */
    const loadLocationOptions = async () => {
      try {
        const response = await selectorApi.ProvinceCityCountySelect()
        if (response.data) {
          locationCascaderOptions.value = response.data || []
        }
      } catch (error) {
        console.error('加载省市县选项失败:', error)
      }
    }

    /**
     * 加载上级单位省市级联选项
     */
    const loadMedicineGroupCityOptions = async () => {
      try {
        const response = await selectorApi.ProvinceCitySelect()
        if (response.data) {
          medicineGroupCityList.value = response.data || []
        }
      } catch (error) {
        console.error('加载上级单位省市选项失败:', error)
      }
    }

    /**
     * 加载医院等级选项
     */
    const loadHospitalGradeOptions = async () => {
      try {
        const response = await selectorApi.getDicts('HospitalGrade')
        if (response.data && response.data.success) {
          hospitalGradeList.value = response.data.data || []
        }
      } catch (error) {
        console.error('加载医院等级选项失败:', error)
      }
    }

    /**
     * 加载医院级别选项
     */
    const loadHospitalLevelOptions = async () => {
      try {
        const response = await selectorApi.getDicts('HospitalLevel')
        if (response.data && response.data.success) {
          hospitalLevelList.value = response.data.data || []
        }
      } catch (error) {
        console.error('加载医院级别选项失败:', error)
      }
    }

    /**
     * 收货方类型级联变化处理
     */
    const handleReceiverTypeChange = (value) => {

      if (value && value.length > 0) {

        if (receiverTypeCascaderOptions.value && Array.isArray(receiverTypeCascaderOptions.value)) {
          const receiverType = receiverTypeCascaderOptions.value.find(item => item.value === value[0])
          // 判断是否为医疗机构类型
          showHospitalFields.value = receiverType.code === '30'
        } else {
          showHospitalFields.value = false
        }
        // 设置收货方类型ID为最后一级的值
        distributorForm.receiverTypeId = value[value.length - 1]

        // 如果不是医疗机构，清空医院相关字段
        if (!showHospitalFields.value) {
          distributorForm.hospitalGradeId = null
          distributorForm.hospitalLevelId = null
        }
      } else {
        showHospitalFields.value = false
        distributorForm.receiverTypeId = null
        distributorForm.hospitalGradeId = null
        distributorForm.hospitalLevelId = null
      }
    }

    /**
     * 地理位置级联变化处理
     * 支持选择任意级别：一级（省份）、二级（城市）、三级（区县）
     */
    const handleLocationChange = (value) => {
      // 重置所有地理位置字段
      distributorForm.provinceId = null
      distributorForm.cityId = null
      distributorForm.countyId = null

      if (value && value.length > 0) {
        // 根据选择的级别设置对应的ID
        if (value.length >= 1) {
          distributorForm.provinceId = value[0] // 省份
        }
        if (value.length >= 2) {
          distributorForm.cityId = value[1] // 城市
        }
        if (value.length >= 3) {
          distributorForm.countyId = value[2] // 区县
        }
      }
    }

    /**
     * 重置表单
     */
    const resetForm = () => {
      // 重置表单数据
      Object.assign(distributorForm, {
        sdrCode: '',
        code: '',
        name: '',
        provinceId: null,
        cityId: null,
        countyId: null,
        receiverTypeId: null,
        unifiedSocialCreditCode: '',
        address: '',
        telephone: '',
        eMail: '',
        postalCode: '',
        netAddress: '',
        stopTime: null,
        hospitalGradeId: null,
        hospitalLevelId: null,
        medicineGroupId: null,
        medicineGroupName: '',
        remark: '',
        receiverTypeCascader: [],
        locationCascader: []
      })

      // 重置显示状态
      showHospitalFields.value = false

      // 清除表单验证
      nextTick(() => {
        setTimeout(() => {
          distributorFormRef.value?.clearValidate()
        }, 100)
      })
    }

    /**
     * 加载记录数据（编辑模式）
     */
    const loadRecordData = async (id) => {
      try {
        const response = await receiverApi.getReceiver(id)
        if (response.data && response.data.success) {
          const data = response.data.data          
          Object.assign(distributorForm, data);
          // 设置级联选择器的值
          if (data.receiverTypeId) {
            const receiverTypeArray = []
            if (data.receiverTypeLevelOneId) receiverTypeArray.push(data.receiverTypeLevelOneId)
            if (data.receiverTypeLevelTwoId) receiverTypeArray.push(data.receiverTypeLevelTwoId)
            if (data.receiverTypeId) receiverTypeArray.push(data.receiverTypeId)
            distributorForm.receiverTypeCascader = receiverTypeArray
          }

          if (data.provinceId || data.cityId || data.countyId) {
            const locationArray = []
            if (data.provinceId) locationArray.push(data.provinceId)
            if (data.cityId) locationArray.push(data.cityId)
            if (data.countyId) locationArray.push(data.countyId)
            distributorForm.locationCascader = locationArray
          }

          // 设置医院字段显示状态
          showHospitalFields.value = data.receiverTypeId && data.receiverTypeId.includes('medical')
        }  
      } catch (error) {
        console.error('加载记录数据失败:', error)    
      }
    }

    /**
     * 保存数据
     */
    const handleSave = async () => {
      try {
        // 表单验证
        const valid = await distributorFormRef.value?.validate()
        if (!valid) return        
        // 调用API保存数据
        let response
        if (isEdit.value) {
          // 编辑模式
          response = await receiverApi.editReceiver(distributorForm)
        } else {
          // 新增模式
          response = await receiverApi.addReceiver(distributorForm)
        }

        if (!response.data.success) {
          throw new Error(response.data.message || '保存失败')
        }

        ElMessage.success(isEdit.value ? '更新成功' : '保存成功')

        // 触发成功事件
        emit('success')

        // 关闭对话框
        handleClose()

      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      } finally {
        
      }
    }

    /**
     * 关闭对话框
     */
    const handleClose = () => {
      resetForm()
      emit('update:visible', false)
    }

    // 组件挂载时加载选项数据
    onMounted(() => {
      loadReceiverTypeOptions()
      loadLocationOptions()
      loadHospitalGradeOptions()
      loadMedicineGroupCityOptions()
      loadHospitalLevelOptions()
    })

    // 监听对话框显示状态
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        if (isEdit.value && props.recordId) {
          // 编辑模式，加载数据
          loadRecordData(props.recordId)
        } else {
          // 新增模式，重置表单
          resetForm()
        }

        // 延迟清除验证状态
        nextTick(() => {
          setTimeout(() => {
            distributorFormRef.value?.clearValidate()
          }, 100)
        })
      }
    })

    /**
     * 打开上级单位选择弹框
     */
    const handleSelectMedicineGroup = () => {
      medicineGroupDialogVisible.value = true
      // 重置搜索表单
      Object.assign(medicineGroupSearchForm, {
        provinceId: '',
        cityId: '',
        nameKeyword: '',
        codeKeyword: ''
      })
      medicineGroupSelectedCity.value = []
      medicineGroupPagination.pageIndex = 1
      loadMedicineGroupList()
    }

    /**
     * 加载上级单位列表
     */
    const loadMedicineGroupList = async () => {
      try {
        const params = {
          pageIndex: medicineGroupPagination.pageIndex,
          pageSize: medicineGroupPagination.pageSize
        }
        params.excludeId = props.recordId

        // 添加搜索条件
        if (medicineGroupSearchForm.provinceId) {
          params.provinceId = medicineGroupSearchForm.provinceId
        }
        if (medicineGroupSearchForm.cityId) {
          params.cityId = medicineGroupSearchForm.cityId
        }
        if (medicineGroupSearchForm.nameKeyword) {
          params.nameKeyword = medicineGroupSearchForm.nameKeyword
        }
        if (medicineGroupSearchForm.codeKeyword) {
          params.codeKeyword = medicineGroupSearchForm.codeKeyword
        }

        // 编辑模式下，排除当前记录
        if (isEdit.value && props.recordId) {
          params.excludeId = props.recordId
        }
        params.status = [10]
        const response = await receiverApi.queryReceiver(params)
        if (response.data && response.data.success) {
          medicineGroupList.value = response.data.datas || []
          medicineGroupPagination.total = response.data.total || 0
        }
      } catch (error) {
        console.error('加载上级单位列表失败:', error)
      }
    }

    /**
     * 上级单位级联选择器变化处理
     */
    const handleMedicineGroupCascaderChange = (value) => {
      if (value && value.length > 0) {
        medicineGroupSearchForm.provinceId = value[0] || ''
        medicineGroupSearchForm.cityId = value[1] || ''
      } else {
        medicineGroupSearchForm.provinceId = ''
        medicineGroupSearchForm.cityId = ''
      }

    }

    /**
     * 搜索上级单位
     */
    const handleMedicineGroupSearch = () => {
      medicineGroupPagination.pageIndex = 1
      loadMedicineGroupList()
    }

    /**
     * 分页大小改变
     */
    const handleMedicineGroupPageSizeChange = (size) => {
      medicineGroupPagination.pageSize = size
      medicineGroupPagination.pageIndex = 1
      loadMedicineGroupList()
    }

    /**
     * 页码改变
     */
    const handleMedicineGroupPageChange = (page) => {
      medicineGroupPagination.pageIndex = page
      loadMedicineGroupList()
    }

    /**
     * 表格行点击
     */
    const handleMedicineGroupRowClick = (row) => {
      handleSelectMedicineGroupItem(row)
    }

    /**
     * 选择上级单位
     */
    const handleSelectMedicineGroupItem = (row) => {
      distributorForm.medicineGroupId = row.id
      distributorForm.medicineGroupName = row.name
      medicineGroupDialogVisible.value = false

    }

    return {
      distributorFormRef,      
      isEdit,
      dialogVisible,
      showHospitalFields,
      distributorForm,
      formRules,
      receiverTypeCascaderOptions,
      locationCascaderOptions,
      hospitalGradeList,
      hospitalLevelList,
      // 上级单位选择相关
      medicineGroupDialogVisible,
      medicineGroupList,
      medicineGroupSelectedCity,
      medicineGroupCityList,
      medicineGroupSearchForm,
      medicineGroupPagination,
      handleSelectMedicineGroup,
      handleMedicineGroupCascaderChange,
      handleMedicineGroupSearch,
      handleMedicineGroupPageSizeChange,
      handleMedicineGroupPageChange,
      handleMedicineGroupRowClick,
      handleSelectMedicineGroupItem,
      handleReceiverTypeChange,
      handleLocationChange,
      handleSave,
      handleClose,
      resetForm,
      loadReceiverTypeOptions,
      loadLocationOptions,
      loadHospitalGradeOptions,
      loadHospitalLevelOptions
    }
  }
}
</script>
