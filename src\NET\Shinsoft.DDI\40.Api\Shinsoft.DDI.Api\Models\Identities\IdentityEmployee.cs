﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    [MapFromType(typeof(Employee), Reverse = false)]
    public class IdentityEmployee : IModel
    {
        public Guid ID { get; set; }

        public string Name => this.DisplayName;

        [JsonIgnore]
        public string DisplayName { get; set; } = string.Empty;

        [JsonIgnore]
        public string Email { get; set; } = string.Empty;

        public bool IsCurrent { get; set; }

        public bool IsAgent { get; set; }
    }
}