﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class SyncBll : BaseCompanyBll
    {
        #region Constructs

        public SyncBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public SyncBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public SyncBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public SyncBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs
    }
}