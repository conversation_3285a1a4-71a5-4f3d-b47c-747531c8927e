﻿CREATE TABLE [dbo].[ImportDataLog](
	[ID]  UNIQUEIDENTIFIER            NOT NULL  CONSTRAINT [Default_ImportDataLog_ID] DEFAULT (NEWSEQUENTIALID()),
	[ReceiverId]                UNIQUEIDENTIFIER            NOT NULL,
	[AttachmentId]              UNIQUEIDENTIFIER            NOT NULL,
	[FileName]					NVARCHAR(50)				NOT NULL,
	[Path]						NVARCHAR(50)				NOT NULL,
	[EnumDataTemplateType]		INT							NOT NULL,
	[EnumImportMode]			INT							NOT NULL,
	[RightCount]                INT                         NOT NULL,
	[WranningCount]				INT							NOT NULL,
    [ErrorCount]                INT                         NOT NULL,
    [TotalCount]                INT                         NOT NULL,
    [HandledCount]              INT                         NOT NULL,
	[ImportTime]				DATETIME					NOT NULL,
	[ErrorFileName]				NVARCHAR(50)				NULL,
	[ErrorFilePath]				NVARCHAR(50)				NULL,
	[EnumDistributorType]		INT							NOT NULL,
	[Deleted]                   BIT                         NOT NULL,
    [Creator]                   NVARCHAR(50)                NULL, 
    [CreateTime]                DATETIME                    NULL, 
    [LastEditor]                NVARCHAR(50)                NULL, 
    [LastEditTime]              DATETIME                    NULL, 
    CONSTRAINT [PK_ImportDataLog] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ImportDataLog_Receiver_00_Receiver] FOREIGN KEY ([ReceiverId]) REFERENCES [dbo].[Receiver] ([ID]),
    )
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'原始文件名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'FileName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'原始文件路径' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'Path'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'数据模板类型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'EnumDataTemplateType'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'导入类型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'EnumImportMode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'正确数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'RightCount'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'总数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'TotalCount'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'导入时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'ImportTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'异常文件名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'ErrorFileName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'异常文件路径' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'ErrorFilePath'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'Deleted'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'Creator'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最后修改人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'LastEditor'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最后修改时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog', @level2type=N'COLUMN',@level2name=N'LastEditTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'导入日志' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ImportDataLog'
GO
