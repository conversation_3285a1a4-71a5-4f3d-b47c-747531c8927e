﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace Shinsoft.DDI.Entities
{
    /// <summary>
    /// 附件类型枚举
    /// 用于定义系统中不同类型的附件分类
    /// </summary>
    public enum AttachmentType
    {
        /// <summary>
        /// 无附件类型
        /// 默认值，表示未指定附件类型
        /// </summary>
        [Description("无")]
        None = 0,

        /// <summary>
        /// 图片媒体类型
        /// 包括图片、视频等媒体文件
        /// </summary>
        [Description("媒体")]
        Image = 1,

        /// <summary>
        /// 文件附件类型
        /// 包括文档、表格等一般文件附件
        /// </summary>
        [Description("附件")]
        Attach = 3,
    }
}