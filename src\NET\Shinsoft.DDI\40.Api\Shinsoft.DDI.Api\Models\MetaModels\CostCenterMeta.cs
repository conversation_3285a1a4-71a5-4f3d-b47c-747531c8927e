﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class CostCenterMeta
    {
        [MapFromProperty(typeof(CostCenter), nameof(CostCenter.Text))]
        public string Text { get; set; } = string.Empty;

        [MapFromProperty(typeof(CostCenter), CostCenter.Foreigns.SubCompany, SubCompany.Columns.Name)]
        public string? SubCompanyName { get; set; }

        /// <summary>
        /// 父编码
        /// </summary>
        [Description("父编码")]
        [MapFromProperty(typeof(CostCenter), CostCenter.Foreigns.Parent, CostCenter.Columns.Code)]
        public string? ParentCode { get; set; }

        [MapFromProperty(typeof(CostCenter), CostCenter.Foreigns.Parent, CostCenter.Columns.Name)]
        public string? ParentName { get; set; }

        /// <summary>
        /// 父显示文字
        /// </summary>
        [MapFromProperty(typeof(CostCenter), CostCenter.Foreigns.Parent, nameof(CostCenter.Text))]
        public string? ParentText { get; set; }
    }
}
