﻿using Shinsoft.Core.Hosting;
using Shinsoft.Core.I18n;

namespace Shinsoft.DDI.Api.Providers
{
    public class I18nProvider : II18nProvider<Guid>
    {
        #region SysCache

        protected virtual SysCache SysCache => HostContext.GetRequiredService<SysCache>();

        #endregion SysCache

        protected virtual User? OperatorUser => MvcHttpContext.GetOperatorUser<User>();

        public virtual II18nTranslator GetTranslator(Guid? companyId = null)
        {
            if (companyId.IsEmpty())
            {
                companyId = this.OperatorUser?.OperatorCompanyId;
            }

            return companyId.HasValue
                ? CompanyCache.GetCompanyCache(companyId.Value)
                : this.SysCache;
        }

        public virtual string? OperatorCulture => this.OperatorUser?.Culture;

        public string Translate(string key, string? culture = null)
        {
            return this.Translate(key, null as Guid?, culture);
        }

        public string Translate(string group, string key, string? culture = null)
        {
            return this.Translate(group, key, null as Guid?, culture);
        }

        public string GetEnumDesc(Enum enumValue, string? culture = null)
        {
            return this.GetEnumDesc(enumValue, null, culture);
        }

        public string GetEnumDesc(Type enumType, int enumValue, string? culture = null)
        {
            return this.GetEnumDesc(enumType, enumValue, null, culture);
        }

        public string GetEnumDesc(Type enumType, string? enumName, string? culture = null)
        {
            return this.GetEnumDesc(enumType, enumName, null, culture);
        }

        public string Translate(string key, Guid? companyId, string? culture = null)
        {
            var lastDot = key.LastIndexOf('.');

            string group;

            if (lastDot < 0)
            {
                group = string.Empty;
            }
            else
            {
                group = key[..lastDot];
                key = key[(lastDot + 1)..];
            }

            return this.Translate(group, key, companyId, culture);
        }

        public string Translate(string group, string key, Guid? companyId, string? culture = null)
        {
            culture ??= this.OperatorCulture;
            var translator = this.GetTranslator(companyId);

            return translator.Translate(group, key, culture);
        }

        public string GetEnumDesc(Enum enumValue, Guid? companyId, string? culture = null)
        {
            var enumType = enumValue.GetType();
            var enumName = Enum.GetName(enumType, enumValue);

            return this.GetEnumDesc(enumType, enumName, companyId, culture);
        }

        public string GetEnumDesc(Type enumType, int enumValue, Guid? companyId, string? culture = null)
        {
            var enumName = Enum.GetName(enumType, enumValue);

            return this.GetEnumDesc(enumType, enumName, companyId, culture);
        }

        public string GetEnumDesc(Type enumType, string? enumName, Guid? companyId, string? culture = null)
        {
            if (enumName.IsEmpty())
            {
                return string.Empty;
            }
            else
            {
                var group = $"{nameof(I18ns.Enums)}.{enumType.Name}";

                return this.Translate(group, enumName!, companyId, culture);
            }
        }
    }
}
