CREATE VIEW [dbo].[VwRoleMember] AS

SELECT 
	rm.ID,
	rm.CompanyId,
	CASE rm.EnumType 
		WHEN 1 THEN u.DisplayName 
		WHEN 2 THEN e.DisplayName
		WHEN 3 THEN s.Name
		WHEN 4 THEN p.Name
		WHEN 5 THEN d.Name
		ELSE ''
	END AS [MemberName],
	rm.RoleId,
	rm.EnumType,
	rm.MemberId,
    u.ID AS [UserId],
    e.ID AS [EmployeeId],
    s.ID AS [StationId],
    p.ID AS [PositionId],
    d.ID AS [DepartmentID],
	rm.Creator,
	rm.CreateTime,
	rm.LastEditor,
	rm.LastEditTime
FROM dbo.RoleMember AS rm
INNER JOIN dbo.Company AS c ON c.ID = rm.CompanyId
LEFT JOIN dbo.Role AS r ON r.ID = rm.RoleId
LEFT JOIN dbo.[User] AS u ON rm.EnumType = 1 AND rm.MemberId = u.ID
LEFT JOIN dbo.Employee AS e ON rm.EnumType = 2 AND rm.MemberId = e.ID
LEFT JOIN dbo.Station AS s ON rm.EnumType = 3 AND rm.MemberId = s.ID
LEFT JOIN dbo.Department AS s_d ON s_d.ID = s.DepartmentId
LEFT JOIN dbo.Position AS s_p ON s_p.ID = s.PositionId
LEFT JOIN dbo.Position AS p ON rm.EnumType = 4 AND rm.MemberId = p.ID
LEFT JOIN dbo.Department AS d ON rm.EnumType = 5 AND rm.MemberId = d.ID
