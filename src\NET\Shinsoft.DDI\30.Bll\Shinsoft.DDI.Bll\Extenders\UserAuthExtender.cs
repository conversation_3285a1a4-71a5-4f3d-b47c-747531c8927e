﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public static class UserAuthExtender
    {
        public static CompanyCache GetCompanyCache(this User user)
        {
            return CompanyCache.GetCompanyCache(user.CurrentCompanyId);
        }

        public static bool HasAuth([NotNull] this User user, [NotNull] AuthAttribute authAttr)
        {
            var cc = CompanyCache.GetCompanyCache(user.CurrentCompanyId);

            var authCodes = authAttr.AuthCodes;

            var auths = cc.Auths.Where(p => p.EnumType == AuthType.Permission && authCodes.Contains(p.Code)).ToList();

            var authTags = authAttr.AuthTagCodes == null ? [] : cc.GetAuthTags([.. authAttr.AuthTagCodes]);

            var exist = true;

            foreach (var authCode in authCodes)
            {
                var auth = auths.FirstOrDefault(p => p.Code == authCode);

                if (auth == null)
                {
                    exist = false;
                }
                else if (auth.EnumAuthTagType == AuthTagType.None)
                {
                    if (!user.HasAuth(authCode))
                    {
                        exist = false;
                    }
                }
                else
                {
                    var authTagCodes = authTags.Where(p => p.EnumType == auth.EnumAuthTagType).Select(p => p.Code).ToList();

                    if (!user.HasAuth(authCode, authCodes))
                    {
                        exist = false;
                    }
                }

                if (!exist)
                {
                    break;
                }
            }

            return authAttr.Behavior == AuthBehavior.Allow ? exist : !exist;
        }

        public static bool HasAnyAuth([NotNull] this User user, [NotNull] IEnumerable<AuthAttribute> authAttrs)
        {
            return authAttrs.Any(authAttr => user.HasAuth(authAttr));
        }
    }
}