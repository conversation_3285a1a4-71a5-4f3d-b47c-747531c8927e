﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class UserFilter
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        [Description("角色ID")]
        public Guid? RoleId { get; set; }

        /// <summary>
        /// 关键词（编码,名称）
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(User), User.Columns.LoginName, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(User), User.Columns.DisplayName, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(User), User.Columns.Email, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(User), User.Columns.Mobile, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        [Description("类型")]
        [DynamicQueryColumn(typeof(User), User.Columns.EnumType, Operation = Operation.In)]
        public List<UserType>? Types { get; set; }

        /// <summary>
        /// 邮件
        /// </summary>
        [Description("邮件")]
        [DynamicQueryColumn(typeof(User), User.Columns.Email, Operation = Operation.StringIntelligence)]
        public string? Email { get; set; }

        /// <summary>
        /// 手机
        /// </summary>
        [Description("手机")]
        [DynamicQueryColumn(typeof(User), User.Columns.Mobile, Operation = Operation.StringIntelligence)]
        public string? Mobile { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        [DynamicQueryColumn(typeof(User), User.Columns.EnumStatus, Operation = Operation.In)]
        public List<UserStatus>? Statuses { get; set; }
    }
}