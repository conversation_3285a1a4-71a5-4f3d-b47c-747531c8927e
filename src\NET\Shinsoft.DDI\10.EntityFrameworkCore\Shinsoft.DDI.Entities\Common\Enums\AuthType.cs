﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace Shinsoft.DDI.Entities
{
    /// <summary>
    /// 权限类型枚举
    /// 用于定义系统中权限管理的不同类型
    /// </summary>
    public enum AuthType
    {
        /// <summary>
        /// 组
        /// </summary>
        [Description("组")]
        [EnumGroup(Visible = true)]
        Group = 0,

        /// <summary>
        /// 权限
        /// </summary>
        [Description("权限")]
        Permission = 1
    }
}
