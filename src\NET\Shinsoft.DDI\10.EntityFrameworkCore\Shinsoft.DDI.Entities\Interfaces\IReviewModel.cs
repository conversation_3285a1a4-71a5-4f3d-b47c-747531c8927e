﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public interface IReviewModel<TEntityModel>
        where TEntityModel : IReviewDataModel
    {
        public TEntityModel? OriData { get; set; }

        public TEntityModel? NewData { get; set; }

        public List<TEntityModel>? OriDatas { get; set; }

        public List<TEntityModel>? NewDatas { get; set; }
    }
}