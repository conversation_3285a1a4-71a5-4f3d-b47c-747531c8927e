﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public class LogBll : BaseLogBll
    {
        #region Constructs

        public LogBll(IUser? operatorUser = null)
           : base(operatorUser)
        {
        }

        public LogBll(string operatorUniqueName)
           : base(operatorUniqueName)
        {
        }

        public LogBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        public LogBll(IRepo bll)
            : base(bll)
        {
        }

        #endregion Constructs
    }
}