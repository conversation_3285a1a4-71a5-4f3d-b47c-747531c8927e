﻿using Microsoft.AspNetCore.Mvc;
using NLog.Filters;

namespace Shinsoft.DDI.Api.Controllers
{
    [ApiExplorerSettings(GroupName = "配置与监控")]
    public class ConfigController :  BaseApiController<ConfigBll>
    {
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询经销商配置信息")]
        public QueryResult<ReceiverClientQuery> QueryReceiverClient([FromQuery] ReceiverClientFilter filter)
        {
            var exps = this.NewExps<ReceiverClient>();

            return this.Repo.GetDynamicQuery<ReceiverClient, ReceiverClientQuery>(filter, exps);
        }

        /// <summary>
        /// 获取经销商配置信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取经销商配置信息")]
        public BizResult<ReceiverClientModel> GetReceiverClient([FromQuery, Required] Guid id)
        {
            var result = new BizResult<ReceiverClientModel>();

            var entity = this.Repo.Get<ReceiverClient>(id);

            if (entity == null)
            {
                result.Error("经销商配置信息不存在");
            }
            else
            {
                var model = entity.Map<ReceiverClientModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增经销商配置信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增经销商配置信息")]
        public BizResult<ReceiverClientModel> AddReceiverClient(ReceiverClientModel model)
        {
            var entity = model.Map<ReceiverClient>();

            var result = this.Repo.AddReceiverClient(entity);

            return result.Map<ReceiverClientModel>();
        }

        /// <summary>
        /// 编辑经销商配置信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "编辑经销商配置信息")]
        public BizResult<ReceiverClientModel> UpdateReceiverClient(ReceiverClientModel model)
        {
            var entity = model.Map<ReceiverClient>();

            var result = this.Repo.UpdateReceiverClient(entity);

            return result.Map<ReceiverClientModel>();
        }

        /// <summary>
        /// 删除经销商配置信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除经销商配置信息")]
        public BizResult DeleteReceiverClient(ReceiverClientModel model)
        {
            return this.Repo.DeleteReceiverClient(model.ID);
        }

        /// <summary>
        /// 获取需要映射的数据库列
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public List<ColumnMappingTemplateQuery> GetColumnMappingTemplates()
        {
            var entites = this.Repo.GetEntities<ColumnMappingTemplate>();

            var models = entites.Maps<ColumnMappingTemplateQuery>();

            return models;
        }
    }
}
