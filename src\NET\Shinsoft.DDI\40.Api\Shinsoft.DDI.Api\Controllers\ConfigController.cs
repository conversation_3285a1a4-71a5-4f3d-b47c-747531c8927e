﻿using Microsoft.AspNetCore.Mvc;
using NLog.Filters;

namespace Shinsoft.DDI.Api.Controllers
{
    [ApiExplorerSettings(GroupName = "配置与监控")]
    public class ConfigController :  BaseApiController<ConfigBll>
    {
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询经销商配置信息")]
        public QueryResult<ReceiverClientQuery> QueryReceiverClient([FromQuery] ReceiverClientFilter filter)
        {
            var exps = this.NewExps<ReceiverClient>();

            return this.Repo.GetDynamicQuery<ReceiverClient, ReceiverClientQuery>(filter, exps);
        }

        /// <summary>
        /// 获取经销商配置信息
        /// </summary>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取经销商配置信息")]
        public BizResult<ReceiverClientModel> GetReceiverClient([FromQuery, Required] Guid id)
        {
            var result = new BizResult<ReceiverClientModel>();

            var entity = this.Repo.Get<ReceiverClient>(id);

            if (entity == null)
            {
                result.Error("经销商配置信息不存在");
            }
            else
            {
                var model = entity.Map<ReceiverClientModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增经销商配置信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增经销商配置信息")]
        public BizResult<ReceiverClientModel> AddReceiverClient(ReceiverClientModel model)
        {
            var entity = model.Map<ReceiverClient>();

            // 构建列映射数据
            List<ColumnMapping> columnMappings = new List<ColumnMapping>();
            if (model.ColumnMappingTemplateModels?.Any() == true)
            {
                foreach (var templateModel in model.ColumnMappingTemplateModels)
                {
                    if (templateModel?.ID != null)
                    {
                        var columnMapping = new ColumnMapping
                        {
                            ReceiverId = entity.ID, // 修正：直接使用entity.ID
                            ColumnMappingTemplateId = templateModel.ID,
                            DbFiled = templateModel.ExDbFiled ?? string.Empty,
                            DateFormatType = templateModel.DateFormatType ?? string.Empty
                        };
                        columnMappings.Add(columnMapping);
                    }
                }
            }

            var result = this.Repo.AddReceiverClient(entity, columnMappings);

            return result.Map<ReceiverClientModel>();
        }

        /// <summary>
        /// 编辑经销商配置信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "编辑经销商配置信息")]
        public BizResult<ReceiverClientModel> UpdateReceiverClient([FromBody] ReceiverClientModel model)
        {
            var entity = model.Map<ReceiverClient>();

            // 构建列映射数据
            List<ColumnMapping> columnMappings = new List<ColumnMapping>();
            if (model.ColumnMappingTemplateModels?.Any() == true)
            {
                foreach (var templateModel in model.ColumnMappingTemplateModels)
                {
                    if (templateModel?.ID != null)
                    {
                        var columnMapping = new ColumnMapping
                        {
                            ReceiverId = entity.ID, // 修正：直接使用entity.ID
                            ColumnMappingTemplateId = templateModel.ID,
                            DbFiled = templateModel.ExDbFiled ?? string.Empty,
                            DateFormatType = templateModel.DateFormatType ?? string.Empty
                        };
                        columnMappings.Add(columnMapping);
                    }
                }
            }

            var result = this.Repo.UpdateReceiverClient(entity, columnMappings);

            return result.Map<ReceiverClientModel>();
        }

        /// <summary>
        /// 删除经销商配置信息
        /// </summary>
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除经销商配置信息")]
        public BizResult DeleteReceiverClient(ReceiverClientModel model)
        {
            return this.Repo.DeleteReceiverClient(model.ID);
        }

        /// <summary>
        /// 获取列映射模板
        /// </summary>
        /// <param name="receiverId">经销商ID，可选参数</param>
        /// <returns>列映射模板列表</returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取列映射模板")]
        public BizResult<List<ColumnMappingTemplateQuery>> GetColumnMappingTemplates([FromQuery] Guid? receiverId)
        {
            var result = new BizResult<List<ColumnMappingTemplateQuery>>();

            try
            {
                List<ColumnMappingTemplateQuery> models;

                if (receiverId.HasValue && receiverId.Value != Guid.Empty)
                {
                    // 获取指定经销商的列映射配置
                    var columnMappings = this.Repo.GetEntities<ColumnMapping>(a => a.ReceiverId == receiverId.Value);

                    if (columnMappings.Any())
                    {
                        // 返回该经销商已配置的映射模板，并包含具体的映射信息
                        models = columnMappings.Select(mapping =>
                        {
                            var template = mapping.ColumnMappingTemplate.Value().Map<ColumnMappingTemplateQuery>();
                            // 添加经销商特定的映射信息
                            template.ExDbFiled = mapping.DbFiled;
                            template.DateFormatType = mapping.DateFormatType;
                            return template;
                        }).ToList();
                    }
                    else
                    {
                        // 该经销商还没有配置，返回所有可用的模板
                        models = this.Repo.GetEntities<ColumnMappingTemplate>().Maps<ColumnMappingTemplateQuery>();
                    }
                }
                else
                {
                    // 没有指定经销商ID，返回所有可用的模板
                    models = this.Repo.GetEntities<ColumnMappingTemplate>().Maps<ColumnMappingTemplateQuery>();
                }

                result.Data = models;
            }
            catch (Exception ex)
            {
                result.Error($"获取列映射模板失败：{ex.Message}");
            }

            return result;
        }
    }
}
