﻿CREATE VIEW [dbo].[VwStation] AS

SELECT
	s.ID,
	s.CompanyId,
	p_s.ID AS [ParentId],
	s.DepartmentId,
	s.PositionId,
	s.<PERSON>,
	s.Valid,
	s.StartDate,
	s.End<PERSON>ate,
	s.<PERSON>,
	s.<PERSON><PERSON>,
	s.<PERSON>,
	s.Creator,
	s.CreateTime,
	s.<PERSON>Editor,
	s.LastEditTime
FROM dbo.Station AS s
INNER JOIN dbo.Department AS d ON d.ID = s.DepartmentId
INNER JOIN dbo.Position AS p ON p.ID = s.PositionId
LEFT JOIN dbo.Station AS p_s ON p_s.ID = s.ParentId
LEFT JOIN dbo.Department AS p_d ON p_d.ID = p_s.DepartmentId
LEFT JOIN dbo.Position AS p_p ON p_p.ID = p_s.PositionId

WHERE s.Deleted =0
AND s.Valid = 1				--岗位有效
AND d.Deleted = 0
AND d.Valid = 1				--部门有效
AND p.Deleted = 0			--职位有效
AND ISNULL(p_s.Deleted,0) = 0
AND ISNULL(p_s.Valid,1) = 1
AND ISNULL(p_d.Deleted,0) = 0
AND ISNULL(p_d.Valid,1) = 1
AND ISNULL(p_p.Deleted,0) = 0
