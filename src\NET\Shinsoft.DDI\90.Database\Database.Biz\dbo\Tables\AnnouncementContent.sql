CREATE TABLE [dbo].[AnnouncementContent]
(
	[ID]                    UNIQUEIDENTIFIER            NOT NULL,
    [CompanyId]             UNIQUEIDENTIFIER            NOT NULL,
	[Content]               NVARCHAR(MAX)               NOT NULL,
    CONSTRAINT [PK_AnnouncementContent] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_AnnouncementContent_Company_00_Company] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Company] ([ID]),
    CONSTRAINT [FK_AnnouncementContent_Announcement_00_Announcement_Content] FOREIGN KEY ([ID]) REFERENCES [dbo].[Announcement] ([ID]),
)
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'公告内容',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'AnnouncementContent',
    @level2type = NULL,
    @level2name = NULL;
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'内容',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'AnnouncementContent',
    @level2type = N'COLUMN',
    @level2name = N'Content'
