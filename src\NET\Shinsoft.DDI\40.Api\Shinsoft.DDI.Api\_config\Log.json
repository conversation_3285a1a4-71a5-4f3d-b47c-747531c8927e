﻿{
    "Platform": "Shinsoft.DDI",
    "Program": "Shinsoft.DDI.Api",
    "LogApi": true, //  API 日志 总开关，设置为 false 将不记录任何日志
    "LogApis": { // 按 API Type 类型设置是否要记录日志，仅当LogApi设置为true时有效，若未设置则读取LogAipAttribute内的设置（Attr无设置则默认记录）
        "Test": false,
        "Login": true,
        "Logout": true,
        "ChangeMyPassword": true,
        "SwitchMyIdentity": true,
        "SwitchMyRole": true,
        "SwitchToAgent": true,
        "Query": false,
        "Save": true,
        "Cache": false,
        "File": false,
        "Notice": false,
        "Sync": true,
        "Interface": true
    },
    "LogApiMasks": { // 日志记录Mask，优先使用本设置，其次读取LogApiAttribue的设置，都没有则默认为None
        "Login": 3,
        "SwitchMyIdentity": 2,
        "SwitchToAgent": 2,
        "ChangeMyPassword": 1
    },
    "BizResult": {
        "LogResultMessage": false, // 是否将BizResult.Message中的内容记录到Log.Message中
        "LogFailedApi": true, //在记录日志的情况下： 是否记录BizResult失败的 API 日志 总开关，设置为 false 将不记录输出结果为BizResult或BizResult<T>的日志
        "LogFailedApis": { // 按 API Type 类型设置是否要记录失败日志，仅当LogFailedApi设置为true时有效
            "Test": false,
            "Login": true,
            "Logout": true,
            "ChangeMyPassword": true,
            "SwitchMyIdentity": true,
            "SwitchMyRole": true,
            "SwitchToAgent": true,
            "Query": false,
            "Save": true,
            "Cache": false,
            "File": false,
            "Notice": false,
            "Sync": true,
            "Interface": true
        }
    },
    "LogTargetNameToMessage": [
    ]
}
