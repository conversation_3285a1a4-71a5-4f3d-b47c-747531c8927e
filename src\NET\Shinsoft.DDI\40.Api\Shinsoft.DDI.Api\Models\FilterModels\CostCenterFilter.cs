﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class CostCenterFilter
    {
        /// <summary>
        /// 关键词（编码,名称）
        /// </summary>
        [Description("关键词")]
        [DynamicQueryColumn(typeof(CostCenter), CostCenter.Columns.Code, Operation = Operation.StringIntelligence)]
        [DynamicQueryColumn(typeof(CostCenter), CostCenter.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Keywords { get; set; }

        /// <summary>
        /// 父ID
        /// </summary>
        [Description("父ID")]
        [DynamicQueryColumn(typeof(CostCenter), CostCenter.Columns.ParentId, Operation = Operation.Equal)]
        public Guid? ParentId { get; set; }

        /// <summary>
        /// 编码
        /// </summary>
        [Description("编码")]
        [DynamicQueryColumn(typeof(CostCenter), CostCenter.Columns.Code, Operation = Operation.StringIntelligence)]
        public string? Code { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        [DynamicQueryColumn(typeof(CostCenter), CostCenter.Columns.Name, Operation = Operation.StringIntelligence)]
        public string? Name { get; set; }

        /// <summary>
        /// 有效性
        /// </summary>
        [Description("有效性")]
        [DynamicQueryColumn(typeof(CostCenter), CostCenter.Columns.Valid, Operation = Operation.Equal)]
        public bool? Valid { get; set; }
    }
}
