﻿namespace Shinsoft.DDI.Api.Models
{
    [MapFromType(typeof(CompanyCfg), Reverse = false)]
    public class EmployeeCfg : IModel
    {
        [MapFromProperty(typeof(CompanyCfg), CompanyCfg.Columns.EnumLoginNameType)]
        public LoginNameType LoginNameType { get; set; }

        [JsonIgnore]
        [MapFromProperty(typeof(CompanyCfg), CompanyCfg.Columns.EmailSuffix)]
        public string? EmailSuffixJson { get; set; }

        [MapFromIgnore]
        public List<string> EmailSuffix => this.EmailSuffixJson.IsEmpty() ? [] : this.EmailSuffixJson!.DeserializeJson<List<string>>()!;
    }
}
