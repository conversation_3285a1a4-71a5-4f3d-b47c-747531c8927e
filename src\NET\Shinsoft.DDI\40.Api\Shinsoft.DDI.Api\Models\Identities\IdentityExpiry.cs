﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Shinsoft.Core;

namespace Shinsoft.DDI.Api.Models
{
    public struct IdentityExpiry : IIdentityExpiry<ApiIdentityKey>
    {
        public IdentityExpiry(ApiIdentityKey identityKey, DateTime? expiry = null)
        {
            this.IdentityKey = identityKey;
            this.Expiry = expiry;
        }

        public IdentityExpiry(IIdentityKey identityKey, DateTime? expiry = null)
        {
            if (identityKey is ApiIdentityKey key)
            {
                this.IdentityKey = key;
            }
            else
            {
                this.IdentityKey = new ApiIdentityKey
                {
                    UserId = identityKey.UserId,
                    EmployeeId = identityKey.EmployeeId,
                    AgentId = identityKey.AgentId,
                    RoleId = identityKey.RoleId,
                };
            }

            this.Expiry = expiry;
        }

        public ApiIdentityKey IdentityKey { get; set; }

        public DateTime? Expiry { get; set; }
    }
}
