﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    public partial class ReviewIndexFilter
    {
        /// <summary>
        /// 审阅类型
        /// </summary>
        [DynamicQueryColumn(typeof(ReviewIndex), ReviewIndex.Columns.EnumReviewType, Operation = Operation.Equal)]
        public ReviewType? ReviewType { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [DynamicQueryColumn(typeof(ReviewIndex), ReviewIndex.Columns.EnumReviewStatus, Operation = Operation.In)]
        public List<ReviewStatus>? EnumReviewStatuses { get; set; }
    }
}