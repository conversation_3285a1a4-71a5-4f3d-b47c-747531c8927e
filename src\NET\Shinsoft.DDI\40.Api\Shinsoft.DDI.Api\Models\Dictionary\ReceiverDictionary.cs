using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api.Models
{
    /// <summary>
    /// 收货方字典类
    /// 定义收货方相关的字典数据，包括导出列定义等
    /// </summary>
    public class ReceiverDictionary
    {
        /// <summary>
        /// 导出收货方信息的列定义
        /// </summary>
        public static Dictionary<string, string> ExportReceiverColumns
        {
            get
            {
                return new Dictionary<string, string>
                {
                    {"收货方编码", "Code"},
                    {"收货方名称", "Name"},
                    {"地址", "Address"},
                    {"电话", "Telephone"},
                    {"邮件", "EMail"},
                    {"统一社会信用代码", "UnifiedSocialCreditCode"},
                    {"状态", "EnumStatusDesc"},
                };
            }
        }
    }
}