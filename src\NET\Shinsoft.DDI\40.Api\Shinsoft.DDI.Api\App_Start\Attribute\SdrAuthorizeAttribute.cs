﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false, Inherited = true)]
    public class SdrAuthorizeAttribute : Attribute
    {
        public SdrAuthorizeAttribute(bool enable = true)
        {
            this.Enable = enable;
        }

        public bool Enable { get; set; } = true;
    }
}