<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useEnumStoreHook } from "@/store/modules/enum";
import { useUserStoreHook } from "@/store/modules/user";
import { ElMessage, FormInstance } from "element-plus";
import { allowEdit } from "@/utils/auth";
import { announcementApi } from "@/api/announcement";
import Epclose from "@iconify-icons/ep/close-bold";
import Epselect from "@iconify-icons/ep/select";

import { useI18n } from "vue-i18n";
import moment from "moment";
const { t } = useI18n();
const tt = t;

defineOptions({
  name: "announcement:edit"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return `${state.model.id ? t("operate.edit") : t("operate.add")} ${tt("Entity.Announcement._Entity")}`;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      enumFlags: 0
    }
  },
  // 枚举定义
  enums: {
    importantList: []
  },
  isShowList: [
    {
      label: t("operate.yes"),
      value: true
    },
    {
      label: t("operate.no"),
      value: false
    }
  ],
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  },
  // 按钮权限
  btn: {
    save: computed(() => {
      return state.model.id
        ? allowEdit(state.model, 2 | 4 | 16 | 64 | 128) &&
            userStore.hasAnyAuth(["Announcement:Manage", "Announcement:Manage:Edit"])
        : userStore.hasAnyAuth(["Announcement:Manage", "Announcement:Manage:Add"]);
    })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  model: cfg.default.model as Record<string, any>
});

const vaildStartTime = (rule, value, callback) => {
  if (value && state.model.endTime && moment(value) > moment(state.model.endTime)) {
    callback(new Error(tt("Rule.Announcement.EndTimeEarlierStartTime")));
  } else {
    callback();
  }
};
const vaildEndTime = (rule, value, callback) => {
  if (value && state.model.startTime && moment(value) < moment(state.model.startTime)) {
    callback(new Error(tt("Rule.Announcement.EndTimeEarlierStartTime")));
  } else {
    callback();
  }
};

/**
 * 验证规则
 */
const rules = {
  form: {
    subject: [
      { required: true, message: tt("Rule.Announcement.Subject:Required"), trigger: "blur" },
      { max: 50, message: tt("Rule.Announcement.Subject:Length"), trigger: "blur" }
    ],
    enumImportant: [
      { required: true, message: tt("Rule.Announcement.EnumImportant:Required"), trigger: "change" }
    ],
    content: [
      {
        required: true,
        message: tt("Rule.Announcement.AnnouncementContent:Required"),
        trigger: "blur"
      }
    ],
    startTime: [
      {
        validator: vaildStartTime,
        trigger: "change"
      }
    ],
    endTime: [
      {
        validator: vaildEndTime,
        trigger: "change"
      }
    ]
  }
};

/**
 * 存储
 */
const userStore = useUserStoreHook();
const enumStore = useEnumStoreHook();

/**
 * 当前组件ref
 */
const formRef = ref<FormInstance>();
const dateRange = ref<(string | null)[]>([null, null]);

/**
 * 初始化组件
 */
const init = () => {
  initState();
  initImportant();
  if (state.model.id) {
    announcementApi
      .GetAnnouncement(state.model.id)
      .then(res => {
        state.model = res.data;
      })
      .finally(() => {
        cfg.loading.btn = false;
      });
  } else {
    state.model.isShow = true;
  }
};

const initImportant = async () => {
  return new Promise<void>(resolve => {
    enumStore.getEnumInfos("Important").then(enumInfos => {
      cfg.enums.importantList = enumInfos;
      resolve();
    });
  });
};

/**
 * 初始化state数据
 */
const initState = () => {
  state.model = cfg.default.model;
};

/**
 * 清除state数据
 */
const clearState = () => {
  initState();
  formRef.value.clearValidate();
  formRef.value.resetFields();
};

/**
 * 编辑数据
 */
const edit = () => {
  cfg.loading.btn = true;

  state.model.companyId = userStore.currentCompanyId;
  formRef.value.validate((valid, fields) => {
    if (valid) {
      if (state.model.id) {
        // 仅提示
        announcementApi
          .UpdateAnnouncement(state.model)
          .then(res => {
            if (res.success) {
              refresh("编辑成功", "success");
            }
          })
          .finally(() => {
            cfg.loading.btn = false;
          });
      } else {
        announcementApi
          .AddAnnouncement(state.model)
          .then(res => {
            if (res.success) {
              refresh("新增成功", "success");
            }
          })
          .finally(() => {
            cfg.loading.btn = false;
          });
      }
    } else {
      cfg.loading.btn = false;
    }
  });
};

/**
 * 按钮事件：【保存】
 */
const save = async () => {
  edit();
};

/**
 * 处理日期变化的函数
 */
const handleDateChange = (value: [string, string]) => {
  state.model.startTime = value ? value[0] : null;
  state.model.endTime = value ? value[1] : null;
};

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string) => {
  cfg.dialog.visible = true;
  state.model.id = id;
  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        v-loading="cfg.loading.form"
        :rules="rules.form"
        :model="state.model"
        label-position="right"
        :label-width="formLabelWidth"
        class="el-dialog-form"
      >
        <el-row :gutter="formGutter">
          <el-col :span="formColSpan">
            <el-form-item prop="subject" :label="tt('Entity.Announcement.Subject')">
              <el-input
                v-model="state.model.subject"
                clearable
                show-word-limit
                :placeholder="tt('Entity.Announcement.Subject')"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="enumImportant" :label="tt('Entity.Announcement.EnumImportant')">
              <el-select
                v-model="state.model.enumImportant"
                :placeholder="tt('Entity.Announcement.EnumImportant')"
              >
                <el-option
                  v-for="item in cfg.enums.importantList"
                  :key="item.value"
                  :label="item.text"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="startTime" :label="tt('Entity.Announcement.StartTime')">
              <el-date-picker
                v-model="state.model.startTime"
                type="datetime"
                :placeholder="tt('Entity.Announcement.StartTime')"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                style="width: 100%"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="endTime" :label="tt('Entity.Announcement.EndTime')">
              <el-date-picker
                v-model="state.model.endTime"
                type="datetime"
                :placeholder="tt('Entity.Announcement.EndTime')"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                style="width: 100%"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="importance" :label="tt('Entity.Announcement.IsShow')">
              <el-select
                v-model="state.model.isShow"
                :placeholder="tt('Entity.Announcement.IsShow')"
              >
                <el-option
                  v-for="(item, index) in cfg.isShowList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="content" :label="tt('Entity.Announcement.Content')">
              <el-input
                v-model="state.model.content"
                type="textarea"
                clearable
                show-word-limit
                :placeholder="tt('Entity.Announcement.Content')"
                maxlength="1000"
                :rows="5"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div>
          <el-button
            class="save"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(Epselect)"
            @click="save()"
          >
            {{ t("operate.save") }}
          </el-button>
          <el-button class="close" :icon="useRenderIcon(Epclose)" @click="close()">
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="css">
.is-not-error > .el-input__wrapper {
  box-shadow: #dcdfe6 !important;
}
</style>
